# -*- coding: UTF-8 -*-

"""
@create 邱立楷@2022.04.22
"""
import argparse
import json
import logging.handlers
import os
import sys
import threading
import traceback

from flask import Flask, request, abort

from basics import config
from basics.util import logger
from basics.util.config import PY_LOG_DIR
from basics.util.error import JsonrpcError
from basics.util.exception_handling import global_exception_handle
from basics.util.top_router import get_method_result

# 日志初始化
# 日志默认打到文件
format = logging.Formatter(
    '[%(process)s][%(threadName)s][%(asctime)s][%(levelname)s] %(message)s')
logger.setLevel(logging.INFO)
# logger.setLevel(logging.DEBUG)

file = logging.handlers.TimedRotatingFileHandler(
    filename=os.path.join(PY_LOG_DIR, 'lazyOne.log'), encoding='utf-8',
    when='H', backupCount=7 * 24
)
file.setFormatter(format)
logger.addHandler(file)

# 全局异常捕获
sys.excepthook = global_exception_handle
# # 全局信号监听
# for sig in signal.Signals:
#     if sig in [signal.SIGKILL, signal.SIGSTOP]:
#         continue
#     signal.signal(sig, global_signal_handler)

app = Flask(__name__)

# 配置文件路径的默认值（可选，根据实际需求设置）
DEFAULT_CONFIG_PATH = 'profile.json'
DEFAULT_PORT = 9966

@app.route("/", methods=['POST'])
def index():
    """ 统一接口 """
    try:
        jsonrpc = request.json["jsonrpc"]
        id = request.json["id"]
        method = request.json["method"]
        params = request.json["params"]

        logger.info('method: ' + method)
        logger.debug('params: ' + json.dumps(params))
        threading.current_thread().name = f'id-{id}'

        res = {
            "jsonrpc": jsonrpc,
            "id": id,
            "error": None
        }
        logger.info("start to solve case: " + str(res['id']))
        try:
            logger.info("input method:{}".format(method))
            logger.info("input params:{}".format(json.dumps(params, ensure_ascii=False)))
            res = get_method_result(jsonrpc, id, method, params)
            
        except JsonrpcError as error:
            res["error"] = {
                "code": error.code,
                "message": error.message,
                "data": error.data
            }
        except Exception as error:
            res["error"] = {
                "code": -32603,
                "message": "Internal error",
                "data": traceback.format_exc()
            }

        if res["error"] is not None:
            logger.error('Jsonrpc response: ' + json.dumps(res["error"]))
        logger.info("finish to solve case: " + str(res['id']))
        # logger.debug("response: {}".format(json.dumps(res)))
        return res
    except JsonrpcError as error:
        errmsg = '[Internal error] code:%s message:%s data:%s' % (
            error.code, error.message, str(error.data))
        logger.error(errmsg)
        abort(500, errmsg)
    except Exception as error:
        errmsg = '[Internal error] ' + traceback.format_exc()
        logger.info(errmsg)
        abort(500, errmsg)


if __name__ == '__main__':
    logger.info("lazy-dom local server sys.argv: {}".format(sys.argv))

    parser = argparse.ArgumentParser(description='启动 Lazy-DOM 本地服务器')
    parser.add_argument('-p', '--port', type=int, default=DEFAULT_PORT,
                        help='服务器端口号（默认：{}）'.format(DEFAULT_PORT))
    parser.add_argument('-c', '--config', type=str, default=DEFAULT_CONFIG_PATH,
                        help='配置文件路径（默认：{}）'.format(DEFAULT_CONFIG_PATH))

    args = parser.parse_args()

    # 获取port和config，没有就用default
    port = args.port
    config_path = args.config.strip()

    try:
        # 加载配置
        config.load_config(config_path)
        print("config: ", config.config)
    except Exception as e:
        logger.error("加载配置出错: " + str(e))

    logger.info("lazy-dom local server start")

    # 启动服务器
    app.run(host="127.0.0.1", port=port)
