"""
用例级修复
"""
import datetime
import json
import os
import time

import cv2
import requests

from apps.auto_case_transform.device import Device
from apps.case_repair.step_repair.repair_step import repair_step
from apps.case_repair.utils.err_code import ErrCode
from basics import config
from basics.util.image import url_to_image_path


def get_report_from_url(report_url):
    """
    从报告地址获取一个用力自动化执行报告内容
    """
    return requests.get(report_url).json()


def run_step(device, action_info):
    """
    执行一个用例步骤
    """
    print(f'真机执行步骤：{action_info}')
    res = device.run_step(action_info)
    # print(res)
    if res['status'] != 0:
        print(f'执行失败，结果为{res}')
        raise Exception(f'执行失败，结果为{res}')
    interval = action_info.get('common', {}).get('stepInterval', 3)
    print(f'等待{interval}秒')
    time.sleep(interval)

def prepare_env(device, action_info):
    """
    准备用例修复的执行环境
    1. 下载用例截图
    2. 获取回放截图
    """
    print('准备环境')
    tmp_dir = os.getcwd() + '/env'
    if not os.path.exists(tmp_dir):
        os.makedirs(tmp_dir)

    case_img_url = action_info['params']['deviceInfo']['screenshot']
    case_img_name = case_img_url.split('/')[-1]
    old_img_path = os.path.join(tmp_dir, case_img_name)
    print(f'params: {case_img_url} {old_img_path}')
    if os.path.exists(old_img_path):
        os.remove(old_img_path)
    url_to_image_path(case_img_url, old_img_path)

    replay_img_url = device.screenshot(needUploadBos=True)['screenshot']
    replay_img_name = replay_img_url.split('/')[-1]
    new_img_path = os.path.join(tmp_dir, replay_img_name)
    if os.path.exists(new_img_path):
        os.remove(new_img_path)
    url_to_image_path(replay_img_url, new_img_path)

    print(f'环境准备完成，图片路径：{old_img_path}, {new_img_path}')
    return old_img_path, new_img_path


def draw_rects_by_action_info(img_path, action_info):
    """
    根据action_info中的信息绘制矩形框
    """
    def scale_rect(rect, scale):
        """
        将矩形的坐标和大小进行缩放
        """
        for k, v in rect.items():
            rect[k] = int(v * scale)
        return
    imgcv = cv2.imread(img_path)
    find_nodes = action_info['params']['findInfo']['findNode']
    scale = action_info['params']['deviceInfo']['screenSize']['scale']
    print(scale)
    for find_node in find_nodes:
        rect = find_node['detailFeature']['rect']
        scale_rect(rect, scale)
        x1, y1 = rect['x'], rect['y']
        x2, y2 = rect['x'] + rect['w'], rect['y'] + rect['h']
        cv2.rectangle(imgcv, (x1, y1), (x2, y2), (0, 0, 255),
                      thickness=5)
    return imgcv

def save_repair_result(old_action_info, new_action_info, old_img_path, new_img_path):
    """
    保存修复结果
    """

    print('保存修复结果')
    base_dir = os.path.join('./result', datetime.datetime.now().strftime('%Y%m%d%H%M%S'))
    if not os.path.exists(base_dir):
        os.makedirs(base_dir)
    res_dir = os.path.join(base_dir, str(len(os.listdir(base_dir))))
    if not os.path.exists(res_dir):
        os.makedirs(res_dir)
    try:
        old_imgcv = draw_rects_by_action_info(old_img_path, old_action_info)
        cv2.imwrite(os.path.join(res_dir, 'old_img.png'), old_imgcv)
    except Exception as e:
        print(f'绘制失败，错误信息：{e}')
    new_imgcv = draw_rects_by_action_info(new_img_path, new_action_info)
    cv2.imwrite(os.path.join(res_dir, 'new_img.png'), new_imgcv)

    with open(os.path.join(res_dir, 'old_action_info.json'), 'w') as f:
        json.dump(old_action_info, f, ensure_ascii=False, indent=4)
    with open(os.path.join(res_dir, 'new_action_info.json'), 'w') as f:
        json.dump(new_action_info, f, ensure_ascii=False, indent=4)

def do_repair_step(device, action_info):
    """
    执行修复步骤操作
    """
    old_img_path, new_img_path = prepare_env(device, action_info)
    new_action_info = None
    try:
        # 执行修复步骤
        print(f'第一次执行修复步骤')
        new_action_info = repair_step(old_img_path, new_img_path, action_info)
        return new_action_info
    except Exception as e:
        if e.args[0] == ErrCode.POPUP_DISTURB:
            print(f'检测到弹窗, 点击弹窗')
            device.click_popup()
            print(f'重新准备环境')
            old_img_path, new_img_path = prepare_env(device, action_info)
            print(f'第二次执行修复步骤')
            new_action_info = repair_step(old_img_path, new_img_path, action_info, need_click_popup=True)
            return new_action_info
        else:
            raise e
    finally:
        if new_action_info is not None:
            save_repair_result(action_info, new_action_info, old_img_path, new_img_path)

def generate_debug_info(new_action_info, old_action_info):
    """
    保存失败后的调试信息
    """
    print('保存调试信息')
    base_dir = os.path.join('./debug', datetime.datetime.now().strftime('%Y%m%d%H%M%S'))
    if not os.path.exists(base_dir):
        os.makedirs(base_dir)
    res_dir = os.path.join(base_dir, str(len(os.listdir(base_dir))))
    if not os.path.exists(res_dir):
        os.makedirs(res_dir)

    with open(os.path.join(res_dir, 'old_action_info.json'), 'w') as f:
        json.dump(old_action_info, f, ensure_ascii=False, indent=4)
    with open(os.path.join(res_dir, 'new_action_info.json'), 'w') as f:
        json.dump(new_action_info, f, ensure_ascii=False, indent=4)

def repair_from_report(report, device):
    """
    从执行报告中修复用例
    """
    def process_step(step):
        """
        """
        try:
            run_step(device, step['stepInfo'])
        except Exception as e:
            step_desc = step['stepDesc']
            print(f'{node_name} {step_desc} 执行失败，错误信息{e}, 判断是否是batdom步骤')
            if step['stepInfo'].get('type', None) in (2, 5, 6, 7, 8, 9):
                print(f'{node_name} {step_desc} 是batdom步骤，开始修复')
                new_action_info = do_repair_step(device, step['stepInfo'])
                try:
                    run_step(device, new_action_info)
                except Exception as e:
                    print(f'{node_name} {step["stepDesc"]} 修复成功但真机回放失败，错误信息{e}')
                    generate_debug_info(new_action_info, step['stepInfo'])
            else:
                print(f'{node_name} {step_desc} 不是batdom步骤，直接抛出异常')
                raise e
    for case_node in report:
        node_name = case_node['nodeName']
        for step in case_node['step']:
            step_desc = step['stepDesc']
            print(f'执行：{node_name} - {step_desc}')
            if step['stepInfo'].get('type', None) == 1 and step['stepInfo']['params']['type'] == 'runTemplate':
                print(f'{node_name} {step_desc} 是测试片段，进入到测试片段内部执行')
                template_steps = step['stepInfo']['params']['params']['step']
                for template_step in template_steps:
                    process_step(template_step)
            else:
                process_step(step)
    print('执行完成, 用例完成修复')

if __name__ == '__main__':

    config.load_config('/Users/<USER>/batdom_vers/10.1.0/core/profile.json')
    report_url = ('https://bj.bcebos.com/newmvp/lazycloud/135377-result-1730024126568-ad423355-068f-4d41-9576-f5fe7fa73915.json')
    dv = Device(device_id=2712, retry=1)
    report = get_report_from_url(report_url)
    repair_from_report(report, device=dv)

if __name__ == '__main__1':
    imgcv = draw_rects_by_action_info(
        img_path="/Users/<USER>/batdom_vers/10.1.0/core/apps/case_repair/step_repair/tmp/13/old_img.png",
        action_info=json.load(open("/Users/<USER>/batdom_vers/10.1.0/core/apps/case_repair/step_repair/tmp/13"
                                   "/old_action_info.json")))
    cv2.imshow('img', imgcv)
    cv2.waitKey(0)

if __name__ == '__main__1':
    url_to_image_path('https://bj.bcebos.com/newmvp/lazy-one/167927--1-1715827795982-71c21910-914a-4400-a09e'
                      '-4d552bc48807.jpg', save_path='/Users/<USER>/batdom_vers/10.1.0/core/apps/case_repair/step_repair/tmp/167927--1-1715827795982-71c21910-914a-4400-a09e-4d552bc48807.jpg')
