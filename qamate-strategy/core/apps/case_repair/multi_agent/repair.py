#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@File    : repair.py
<AUTHOR> <PERSON><PERSON><PERSON><PERSON>(renya<PERSON><PERSON>@baidu.com)
@Time    : 2024/10/8 16:51
@Desc    : 
"""
import copy
import json
import logging.handlers
import os
import uuid

import cv2

from apps.app_agent.utils.big_model import QAMateLLM
from apps.app_agent.utils.chat_message import ChatMessage
from apps.app_agent.utils.draw import draw_rect_with_index
from apps.case_repair.multi_agent.chat import repair_prompt
from apps.case_repair.utils.err_code import ErrCode
from apps.case_repair.utils.image_process import get_image_with_grid, upload_case_image
from basics.util import logger
from basics.util.config import CACHE_DIR, PY_LOG_DIR

format = logging.Formatter(
    '[%(process)s][%(asctime)s][%(levelname)s] %(message)s')
logger.setLevel(logging.INFO)
# logger.setLevel(logging.DEBUG)

file = logging.handlers.TimedRotatingFileHandler(
    filename=os.path.join(PY_LOG_DIR, 'lazyRepair.log'), encoding='utf-8',
    when='D', backupCount=3
)
file.setFormatter(format)
logger.addHandler(file)


def scale_rect(rect, scale, reduce=False):
    """

    :param rect:
    :param scale:
    :param reduce:  默认为方法
    :return:
    """
    new_rect = {}
    if reduce:
        new_rect['x'] = int(rect['x'] / scale)
        new_rect['y'] = int(rect['y'] / scale)
        new_rect['w'] = int(rect['w'] / scale)
        new_rect['h'] = int(rect['h'] / scale)
    else:
        new_rect['x'] = int(rect['x'] * scale)
        new_rect['y'] = int(rect['y'] * scale)
        new_rect['w'] = int(rect['w'] * scale)
        new_rect['h'] = int(rect['h'] * scale)

    return new_rect

def multi_find_node_match(old_find_node, new_ele):
    """
    将旧的find_node信息映射到新的元素
    """
    old_type = old_find_node['detailFeature']['type']
    new_type = new_ele['type']
    new_find_node = {
        'detailFeature': {
            'ext': new_ele['ext'],
            'rect': new_ele['rect'],
            'matchType': old_find_node['detailFeature']['matchType'],
            'type': new_ele['type']
        },
        'featureFlag': old_find_node['featureFlag'],
        'actionNode': old_find_node['actionNode'],
        'id': new_ele['debug']['id'],
        'parents': new_ele['debug']['parents']
    }

    if old_type == new_type:
        return new_find_node
    elif old_find_node['detailFeature']['type'] == "Icon" and new_ele['type'] == "Text":
        return new_find_node
    elif old_find_node['detailFeature']['type'] == "Component" and new_ele['type'] == "Text":
        return new_find_node
    elif old_find_node['detailFeature']['type'] == "Text" and new_ele['type'] == "Icon":
        return new_find_node
    elif old_find_node['detailFeature']['type'] == "Text" and new_ele['type'] == "Component":
        return new_find_node
    elif old_find_node['detailFeature']['type'] == "Component" and new_ele['type'] == "Icon":
        return new_find_node
    elif old_find_node['detailFeature']['type'] == "Icon" and new_ele['type'] == "Component":
        return new_find_node
    else:
        raise Exception(ErrCode.UNSUPPORTED_FIND_NODE_CONVERT)


class RepairAgent(object):
    """
    用例修复
    """

    def __init__(self, old_action_info: dict, old_image_path: str, new_image_path: str):
        """

        :param old_action_info:
        :param old_image_path:
        :param new_image_path:
        """
        self.old_action_info = old_action_info
        self.old_image_path = old_image_path
        self.new_image_path = new_image_path

        self.temp_dir = os.path.join(CACHE_DIR, str(uuid.uuid4()))
        self.find_node_img_path = None
        self.find_node_infos = []
        self.new_grid_img_path = None

        # self.model_name = "gpt-4o"

    def params_check(self):
        """

        :return:
        """
        find_node_num = len(self.old_action_info['params']['findInfo']['widgetInfo']['findNode'])
        if find_node_num > 5:
            raise Exception("定位元素的个数为:{}，超过5个不进行修复".format(find_node_num))

    def mk_dir(self):
        """

        :return:
        """
        if not os.path.exists(self.temp_dir):
            os.mkdir(self.temp_dir)
        logger.info("temp_dir: {}".format(self.temp_dir))

    def analysis_find_node_desc(self, id, node_detail):
        """

        :param node_detail:
        :return:
        """
        if 'Text' in node_detail['type']:
            desc = node_detail['ext']['text'] if len(node_detail['ext']['text']) < 15 else (
                    node_detail['ext']['text'][:15] + "...")
            desc = "'{}'".format(desc)
            type = "text"
        elif node_detail['type'] == "Icon":
            desc = "『{}』".format(node_detail['ext']['name'].replace("_beta", "")) + " icon"
            type = "icon"
        elif node_detail['type'] == "Component":
            desc = "input_box" if "comment_box" in node_detail['ext']['name'] else node_detail['ext']['name']
            if node_detail['ext']['name'] == "icon":
                type = "icon"
            else:
                type = "element"
        else:
            logger.warning("未知的操作类型：{}".format(node_detail['type']))
            type = "未知"
            desc = "未知"

        node_desc = "id:{}, 类型:{}, 描述:{}".format(id, type, desc)

        return node_desc

    def preprocess_image_dir(self):
        """
        预处理图片数据（可视化）
        :return:
        """
        self.mk_dir()
        # TODO: 原始find_node['detailFeature']格式需要兼容 历史版本

        ele_rects = []
        for idx, find_node in enumerate(self.old_action_info['params']['findInfo']['widgetInfo']['findNode']):
            node_rect = scale_rect(find_node['detailFeature']['rect'],
                                   self.old_action_info['params']['recordInfo']['deviceInfo']['screenSize']['scale'],
                                   reduce=False)
            ele_rects.append(node_rect)
            try:
                node_desc = self.analysis_find_node_desc(idx, find_node['detailFeature'])
            except Exception as e:
                logger.error("解析定位元素信息发生错误:{}".format(e))
                node_desc = "id:{}, 类型:未知, 描述:未知".format(idx)

            find_node_info = {
                "node_info": find_node,
                "rect": node_rect,
                "node_desc": node_desc
            }
            self.find_node_infos.append(find_node_info)

        self.find_node_img_path = self.temp_dir + "/old_with_ele.jpg"
        old_img = cv2.imread(self.old_image_path)
        img_with_find_node = draw_rect_with_index(old_img, result=ele_rects, output_path=self.find_node_img_path,
                                                  line_width=5)

        self.new_grid_img_path = os.path.join(self.temp_dir, "new_grid_img.jpg")
        dom_info, dom, ele_desc, ele_id_map = get_image_with_grid(self.new_image_path, self.new_grid_img_path,
                                                                  bat_version='v11.0.0')
        return dom_info, dom, ele_desc, ele_id_map

    def get_new_device_info(self):
        """
        生成新的device_info
        """
        h, w = cv2.imread(self.new_grid_img_path).shape[:2]
        dev_info = copy.deepcopy(self.old_action_info['params']['recordInfo']['deviceInfo'])
        dev_info['screenSize']['width'] = w
        dev_info['screenSize']['height'] = h
        dev_info['screenSize']['scale'] = 1
        new_url = upload_case_image(self.new_image_path)
        dev_info['screenshot'] = new_url
        return dev_info

    def format_new_action_info(self, node_pairs, dom, dom_info):
        """
        生成新的action_info
        """
        new_action_info = copy.deepcopy(self.old_action_info)
        new_action_info['params']['recordInfo']['deviceInfo'] = self.get_new_device_info()
        new_action_info['params']['recordInfo']['dom'] = dom
        new_action_info['params']['recordInfo']['domInfo'] = dom_info
        new_find_nodes = []
        for np in node_pairs:
            old_find_node, new_ele = np
            new_find_nodes.append(multi_find_node_match(old_find_node, new_ele))
        if len(new_find_nodes) <= 1:
            raise Exception("多控件修复后必须是两个以上控件")
        new_action_info['params']['findInfo']['widgetInfo']['findNode'] = new_find_nodes
        return new_action_info


    def multi_element_repair(self):
        """
        多控件修复
        :return:
        """

        repair_ele_ids = {}

        # 检查
        # self.params_check()

        # 前置处理
        dom_info, dom, ele_desc, ele_id_map = self.preprocess_image_dir()

        # 构建prompt
        # chat_list = init_ai_tester_chat()
        record_ele_desc = "\n".join([item['node_desc'] for item in self.find_node_infos])
        repair_prompt_str = repair_prompt(dom_desc=ele_desc, find_node_desc=record_ele_desc)
        logger.info(repair_prompt_str)
        image_paths = [self.find_node_img_path, self.new_grid_img_path]
        # chat_list = add_user_info_to_chat_list(chat_list, repair_prompt_str, image_paths=image_paths)
        msg = ChatMessage()
        msg.add_user_message(repair_prompt_str, image_paths=image_paths)
        llm_ans = QAMateLLM().chat(msg, response_format="json_object")
        llm_res = json.loads(llm_ans).get('result')

        for item in llm_res:
            repair_ele_ids[item['record_element_id']] = item['replay_element_id']
            logger.info("{} -> {}".format(item['record_element_id'], item['replay_element_id']))

        # for i in range(len(self.find_node_img_path)):
        #     chat_list = init_ai_tester_chat()
        #     node_desc = self.find_node_descs[i]
        #     repair_prompt_str = repair_prompt(dom_desc=ele_desc, find_node_desc=node_desc)
        #     logger.info(repair_prompt_str)
        #     find_img_path = self.find_node_img_path[i]
        #     image_paths = [find_img_path, self.new_grid_img_path]
        #     chat_list = add_user_info_to_chat_list(chat_list, repair_prompt_str, image_paths=image_paths)
        #     llm_ans = chat_with_gpt(chat_list=chat_list, model_name=self.model_name, temperature=0.2,
        #                             response_format="json_object")
        #     ele_id = json.loads(llm_ans).get("element_id")

        logger.info("修复结果元素映射为：{}".format(repair_ele_ids))
        node_pairs = []
        for idx, find_node_info in enumerate(self.find_node_infos):
            if idx in repair_ele_ids:
                old_find_node = find_node_info['node_info']
                ele_id = repair_ele_ids[idx]
                if ele_id >= 0:
                    new_ele = ele_id_map[ele_id]
                    node_pairs.append((old_find_node, new_ele))

        return self.format_new_action_info(node_pairs, dom, dom_info)



if __name__ == '__main__':
    o_i = "/Users/<USER>/data/用例生成/用例修复/样式变化/多控件/控件减少/3/old.jpg"
    n_i = "/Users/<USER>/data/用例生成/用例修复/样式变化/多控件/控件减少/3/new.jpg"
    ac_i = "/Users/<USER>/data/用例生成/用例修复/样式变化/多控件/控件减少/3/action_info.json"
    with open(ac_i, 'rb') as f:
        old_action_info = json.load(f)
    repair = RepairAgent(old_action_info, o_i, n_i)
    res = repair.multi_element_repair()
