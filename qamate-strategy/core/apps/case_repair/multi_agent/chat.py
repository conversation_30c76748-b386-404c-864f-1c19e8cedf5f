#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@File    : prompt.py
<AUTHOR> <PERSON><PERSON><PERSON><PERSON>(renya<PERSON><PERSON>@baidu.com)
@Time    : 2024/9/19 15:48
@Desc    : 
"""


def init_ai_tester_chat():
    """

    :return:
    """
    chat_list = []
    system_prompt = "你是一个具有App自动化测试经验的AI手机操作助手。使用中文回答后续的问题。"
    chat_list.append(["system", [{"type": "text", "text": system_prompt}]])
    return chat_list


def repair_prompt1(dom_desc="", find_node_desc=""):
    """

    :return:
    """

    if find_node_desc:
        node_desc_str = "[\n{}\n]".format(find_node_desc)
    else:
        node_desc_str = ""

    if dom_desc:
        dom_desc_str = ("为了帮助你更好地理解 回放截图(第二张图片)的内容，我们通过其他能力得到每个框选元素的补充信息供你参考，"
                        "id是元素数字id与图片中的数字id相对应，类型表示元素类型，描述是元素的描述或文字内容\n")
        dom_desc_str += dom_desc
        dom_desc_str += "\n这些信息不一定准确，仅供参考，你需要结合截图来理解。"
    else:
        dom_desc_str = ""

    prompt = f"""&& 背景 &&
我正在进行App自动化测试，前期通过录制过程，录制了自动化测试用例，当前正在进行用例回放。

&& 图片信息 &&
第一张图片是录制阶段的设备截图，红框为点击的UI元素。{node_desc_str}
第二张图片是当前回放阶段的设备截图，截图中的交互式UI元素都用数字标签标记(从0开始)，每个交互元素用红色框框选出，数字id都位于元素的左上角。
{dom_desc_str}

&& 任务 &&
你需要判断 录制阶段的点击元素是否在 回放阶段截图中，如果在，给出点击元素在回放截图中的数字id，保证回归步骤与录制步骤达到相同的操作目的。
你需要先分析录制阶段点击元素的操作目的，然后思考在回放阶段达到相同操作目的需要点击哪个元素。

&& 注意事项 &&
1.如果回放截图中存在多个元素能达到与录制阶段相同的测试目的时：
    a.选择与录制阶段点击元素(红框元素)在页面中相对位置接近的element，例如均处于页面左上角。
    b.选取最为稳定(不经常变化，最好为图标)的element来保证自动化测试的稳定性。
2.如果回放截图不存在元素能达到与录制阶段相同的测试目的时(或者拿不准时)，将element_id值输出为-1。
"""

    output = """"
&& 输出格式 &&
输出应该包含两个部分, element_id 和 reason，以合法的json格式输出：
{
    "element_id": int  // 整数，输出点击元素在回放截图中的数字id，如果回放页面不存在点击元素，则element_id = -1。
    "reason": str // 给出原因
}
"""
    return prompt + output


def repair_prompt(dom_desc="", find_node_desc=""):
    """

    :return:
    """

    if find_node_desc:
        node_desc_str = "{}".format(find_node_desc)
    else:
        node_desc_str = ""

    if dom_desc:
        dom_desc_str = ("为了帮助你更好地理解 回放截图(第二张图片)的内容，我们通过其他能力得到每个框选元素的补充信息供你参考，"
                        "id是元素数字标签与图片中的数字标签相对应，类型表示元素类型，描述是元素的描述或文字内容\n")
        dom_desc_str += dom_desc
        dom_desc_str += "\n这些信息不一定准确，仅供参考，你需要结合截图来理解。"
    else:
        dom_desc_str = ""

    prompt = f"""&& 背景 &&
我正在进行App自动化测试，前期通过录制过程，录制了自动化测试用例，当前正在进行用例回放。

&& 图片信息 &&
第一张图片是录制阶段的设备截图，红框为期望校验(存在性校验)的多个UI元素，在红框的左上角标记了数字标签。每个元素的描述如下：
{node_desc_str}
第二张图片是当前回放阶段的设备截图，截图中的交互式UI元素都用数字标签标记(从0开始)，每个交互元素用红色框框选出，数字标签都位于元素的左上角。
{dom_desc_str}

&& 任务 &&
你需要判断 录制阶段 的校验元素在 回放截图中是否存在，如果在，给出点击元素在回放截图中的数字id。
你需要一个个元素进行分析，先分析录制阶段校验元素的含义，然后思考在回放阶段是否有相同含义的UI元素。

&& 注意事项 &&
1.如果回放截图中存在多个元素能达到与录制阶段相同的测试目的时：
    a.选择与录制阶段校验元素(红框元素)在页面中相对位置接近的element，例如均处于页面左上角。
    b.选取最为稳定(不经常变化，最好为图标)的element来保证自动化测试的稳定性。
2.如果回放截图不存在元素能达到与录制阶段相同的测试目的时(或者拿不准时)，将replay_element_id值输出为-1。
3.输出item的数量与录制元素的数量保持一致。
"""

    output = """"
&& 输出格式 &&
输出result为List[dict]，每一个录制元素对应一个dict，每个dict有三个key分别为: 'record_element_id', 'replay_element_id','reason'，以合法的json格式输出：
{
    "result": [
        {
            'record_element_id': int  // 录制截图中的元素id,
            'replay_element_id': int // 回放截图中的元素id, 如果回放页面不存在录制元素，则 replay_element_id = -1。
            'reason': str // 给出原因（分析过程）
        },
        ...
    ]
}
"""
    return prompt + output