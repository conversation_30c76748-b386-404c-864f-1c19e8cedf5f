import json
import os
import random
import shutil

from apps.case_repair.img_cmp.comparators import Gpt4oComparator

def sim_test():
    out_dir = './output'
    comparator = Gpt4oComparator()
    for dir_name in os.listdir(out_dir):
        abs_dir = os.path.join(out_dir, dir_name)
        print(abs_dir)
        old_img_path = os.path.join(abs_dir, 'old.png')
        new_img_path = os.path.join(abs_dir, 'new.png')
        # print(old_img_path, new_img_path)
        res = comparator.compare(old_img_path, new_img_path)
        print(abs_dir, res)

def diff_test():
    out_dir = './output'
    diff_out_dir = './diff_out'
    comparator = Gpt4oComparator()
    for i in range(20):
        a, b = random.sample(os.listdir(out_dir), 2)
        abs_dir_a = os.path.join(out_dir, a)
        abs_dir_b = os.path.join(out_dir, b)
        diff_dir = os.path.join(diff_out_dir, str(len(os.listdir(diff_out_dir))))
        os.makedirs(diff_dir, exist_ok=True)
        img1 = os.path.join(abs_dir_a, 'new.png')
        shutil.copy(img1, os.path.join(diff_dir, 'a.png'))
        img2 = os.path.join(abs_dir_b, 'new.png')
        shutil.copy(img2, os.path.join(diff_dir, 'b.png'))
        res = comparator.compare(img1, img2)
        with open(os.path.join(diff_dir, 'res.json'), 'w') as f:
            json.dump(res, f, indent=4, ensure_ascii=False)



if __name__ == '__main__':
    diff_test()