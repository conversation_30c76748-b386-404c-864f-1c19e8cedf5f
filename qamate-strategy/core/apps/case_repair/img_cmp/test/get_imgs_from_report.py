import json
import os

import requests


def download_image(url, filename):
    """
    从指定的URL下载图片，并保存到指定的文件名。
    """
    # 发送HTTP GET请求
    response = requests.get(url)

    # 确保HTTP请求成功
    if response.status_code == 200:
        # 打开一个文件用于写入。'wb'模式表示写入二进制文件
        with open(filename, 'wb') as file:
            # 写入从URL获取的内容（即图片数据）
            file.write(response.content)

        print(f"图片已经成功保存到: {filename}")
    else:
        print(f"无法下载图片，HTTP状态码为: {response.status_code}")

if __name__ == '__main__':
    res = requests.get('https://bj.bcebos.com/newmvp/lazycloud/6468445-result-1727071796814-f807a2c3-5e97-4956-b380-470861353de3.json').json()
    print(res)
    out_dir = './output'
    os.makedirs(out_dir, exist_ok=True)

    for node in res:
        for step in node['step']:
            old_img = json.loads(step['actionInfo']).get('params', {}).get('deviceInfo', {}).get('screenshot', None)
            if old_img is None or not old_img.startswith('http'):
                continue
            new_img = step.get('result', {}).get('data', {}).get('screenshot', None)
            if new_img is None or not new_img.startswith('http'):
                continue
            new_dir = os.path.join(out_dir, str(len(os.listdir(out_dir))))
            os.makedirs(new_dir, exist_ok=True)
            download_image(old_img, os.path.join(new_dir, 'old.png'))
            download_image(new_img, os.path.join(new_dir, 'new.png'))
