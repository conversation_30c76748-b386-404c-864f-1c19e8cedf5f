"""
@File    : prompt.py
"""


def init_ai_tester_chat():
    """

    :return:
    """
    chat_list = []
    system_prompt = "你是一个具有App自动化测试经验的AI手机操作助手。使用中文回答后续的问题。"
    chat_list.append(["system", [{"type": "text", "text": system_prompt}]])
    return chat_list


def compare_prompt1():
    """
    :return:
    """
    prompt = """&& 背景 &&
我正在进行App自动化测试，测试过程中，需要判断两张手机页面截图是否属于同一个场景。

&& 图片信息 &&
输入两张图片，分别是预期页面截图和当前页面截图

&& 任务 &&
你需要先观察页面中所有图标，大致的布局，然后判断，这两张图片是否属于同一个场景

&& 注意事项 &&
1. 你的判断条件必须尽可能宽松，也就是说两个页面只要存在少量的图标或者相似，即使其他组件、功能、内容不同，也属于同一个场景
2. 一定不允许以"内容不同"作为不同场景的判断原因
3. 如果是不同场景，必须在原因中给出哪些功能和布局不同

&& 输出格式 &&
输出应该包含两个部分, result 和 reason，以合法的json格式输出：
{
    "result": bool  // 布尔值，true表示是同一个场景，false表示不是同一个场景。
    "reason": str // 字符串，给出原因，用中文描述
}
"""
    return prompt
