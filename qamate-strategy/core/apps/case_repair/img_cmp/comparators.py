import json
from abc import ABC, abstractmethod

from apps.app_agent.utils.openaiGPT import chat_with_gpt
from apps.app_agent.utils.pandaGPT import add_user_info_to_chat_list
from apps.case_repair.img_cmp.chat import init_ai_tester_chat, compare_prompt1


class Comparator(ABC):
    """
    对比两张ui截图是否是同一个场景
    """
    def __init__(self, *args):
        pass

    @abstractmethod
    def compare(self, img_path1, img_path2):
        pass


class Gpt4oComparator(Comparator):
    """
    图片相似度对比
    """

    def __init__(self):
        super().__init__()
        self.model_name = "gpt-4o"

    """
    基于Gpt4o的比较器
    """
    def compare(self, img_path1, img_path2):
        chat_list = init_ai_tester_chat()
        repair_prompt_str = compare_prompt1()
        # print(repair_prompt_str)
        image_paths = [img_path1, img_path2]
        chat_list = add_user_info_to_chat_list(chat_list, repair_prompt_str, image_paths=image_paths)
        llm_ans = chat_with_gpt(chat_list=chat_list, model_name=self.model_name, temperature=0.2,
                                response_format="json_object")
        llm_res = json.loads(llm_ans)
        # print('llm res', llm_res)
        return llm_res

if __name__ == '__main__':
    comparator = Gpt4oComparator()
    comparator.compare(
        "/Users/<USER>/lab/样式变化/多控件/布局样式变化/3/new.jpg",
        "/Users/<USER>/lab/样式变化/多控件/布局样式变化/3/old.jpg")