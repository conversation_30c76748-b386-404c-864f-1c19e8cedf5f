"""
单步骤修复能力
"""

import json
import os.path
import uuid

import cv2

from apps.auto_case_transform.batdom_upgrade.upgrader_to_10 import UpgraderTo10
from apps.auto_case_transform.utils.utils import batdom_replay_by_path_new
from apps.auto_case_transform.visual_strategy.base_visual_detect import VisualDetect
from apps.case_repair.multi_agent.repair import RepairAgent as MultiRepairAgent
from apps.case_repair.single_agent.repair import RepairAgent as SingleRepairAgent
from apps.case_repair.utils.err_code import ErrCode
from basics import config
from basics.util import logger
from basics.util.config import CACHE_DIR
from basics.util.image import Image
from features.cv_services.popup_detect import popup_detect


def pre_check(old_img_path, new_img_path, action_info):
    """
    :param old_img_path: 旧截图（建模时的截图）
    :param new_img_path: 新截图（回放时的截图）
    :param action_info: 动作信息
    1. 校验是否为『控件不存在』操作
    2. 校验控件数量是否超过5个
    3. 校验是否为相似页面
    4. 校验是否为弹窗干扰
    """
    action_type = action_info.get('params', {}).get('actionInfo', {}).get('type', None)
    if action_type == 'absence':
        raise Exception(ErrCode.ABSENCE_ACTION)
    # find_nodes = action_info.get('params', {}).get('findInfo', {}).get('findNode', [])
    # if len(find_nodes) > 5:
    #     raise Exception(ErrCode.OVER_5_NODES)

    r = popup_detect({'image_path': new_img_path})
    if r['data']['hit'] is True:
        raise Exception(ErrCode.POPUP_DISTURB)


def check_step(old_img_path, new_img_path, action_info, os_type=2, page_source=None):
    """
    检查用例能否在某个截图上回放成功
    """
    vision_info = VisualDetect(image_path=new_img_path).main()
    # batdom_replay_by_path_new_v2(img_path=new_img_path,
    #                              step_info=action_info['params'],
    #                              case_img_path=old_img_path,
    #                              page_source=page_source,
    #                              os_type=os_type,
    #                              vision_info=vision_info)
    batdom_replay_by_path_new(img_path=new_img_path,
                                 step_info=action_info['params'],
                                 case_img_path=old_img_path,
                                 page_source=page_source,
                                 os_type=os_type,
                                 vision_info=vision_info)


def is_multi_widget(action_info):
    """
    判断当前控件定位步骤是否是多控件定位
    """
    find_nodes = action_info.get('params', {}).get('findInfo', {}).get('widgetInfo', {}).get('findNode', [])
    return len(find_nodes) > 1


def do_repair(old_img_path, new_img_path, action_info):
    """
    执行基于大模型的修复操作
    """
    if is_multi_widget(action_info):
        repair = MultiRepairAgent(action_info, old_img_path, new_img_path)
        res = repair.multi_element_repair()
        logger.info(res)
        return res
    else:
        repair = SingleRepairAgent(action_info, old_img_path, new_img_path)
        res = repair.single_element_repair()
        logger.info(res)
        return res


def optimize_step(action_info):
    """
    优化用例步骤
    """
    return action_info


def add_extra_settings(new_action_info, need_click_popup):
    """
    给用例增加一些额外的设置参数，例如：通用弹窗点除
    """
    if need_click_popup is True:
        if 'common' not in new_action_info:
            new_action_info['common'] = {}
        new_action_info['common']['commonAlertClear'] = need_click_popup


def repair_step(old_img_path, new_img_path, action_info, need_click_popup=False):
    """
    :param old_img_path: 旧截图（建模时的截图）
    :param new_img_path: 新截图（回放时的截图）
    :param action_info: 动作信息
    """
    pre_check(old_img_path, new_img_path, action_info)
    logger.info("=" * 10 + "前置检查通过" + "=" * 10)
    new_action_info = UpgraderTo10(action_info).upgrade()
    logger.info("=" * 10 + "Step升级完成" + "=" * 10)
    try:
        check_step(old_img_path, new_img_path, new_action_info)
        # 如果升级后直接可以在新图回放成功，说明是用例过旧导致的失败，直接返回最新的用例
        logger.info("=" * 10 + "升级后回放成功，直接返回" + "=" * 10)
        return new_action_info
    except Exception as e:
        logger.info("=" * 10 + "升级后回放失败，修复用例" + "=" * 10)

    # 执行用例修复
    new_action_info = do_repair(old_img_path, new_img_path, new_action_info)
    opt_action_info = optimize_step(new_action_info)
    add_extra_settings(opt_action_info, need_click_popup)
    try:
        check_step(new_img_path, new_img_path, opt_action_info)
        logger.info("=" * 10 + "修复后回放成功" + "=" * 10)
        return opt_action_info
    except Exception as e:
        logger.info("=" * 10 + "修复后回放失败" + "=" * 10)
        raise e


def repair_step_by_info(action_info, new_img_path, force_repair=False):
    """
    """
    new_action_info = UpgraderTo10(action_info).upgrade()
    logger.info("=" * 10 + "Step升级完成" + "=" * 10)

    img_url = new_action_info['params']['recordInfo']['deviceInfo']['screenshot']
    suffix = img_url.split('.')[-1]
    img_path = str(os.path.join(CACHE_DIR, str(uuid.uuid4().hex) + '.' + suffix))
    Image(url=img_url).save_image(img_path)
    old_img_path = img_path
    new_img_path = new_img_path
    if force_repair is False:
        # 如果不是强制修复，那么只做升级
        try:
            check_step(old_img_path, new_img_path, new_action_info)
            # 如果升级后直接可以在新图回放成功，说明是用例过旧导致的失败，直接返回最新的用例
            logger.info("=" * 10 + "升级后回放成功，直接返回" + "=" * 10)
            return new_action_info
        except Exception as e:
            logger.info("=" * 10 + "升级后回放失败，修复用例" + "=" * 10)
    else:
        logger.info("force_repair参数为true，执行强制修复")

    # 执行用例修复
    new_action_info = do_repair(old_img_path, new_img_path, new_action_info)
    opt_action_info = optimize_step(new_action_info)
    # add_extra_settings(opt_action_info, need_click_popup)
    try:
        check_step(new_img_path, new_img_path, opt_action_info)
        logger.info("=" * 10 + "修复后回放成功" + "=" * 10)
        return opt_action_info
    except Exception as e:
        logger.info("=" * 10 + "修复后回放失败" + "=" * 10)
        raise e


if __name__ == '__main__':
    config.load_config('/Users/<USER>/batdom_vers/10.1.0/core/profile.json')

    base_dir = '/Users/<USER>/Downloads/样式变化/单控件/样式变化/2'
    with open(f'/Users/<USER>/batdom_vers/10.1.0/core/tests/t2.json', 'r') as f:
        action_info = json.load(f)
    # upg = Upgrader(old_act).upgrade()
    # repair_step(f"{base_dir}/old.jpg",
    #             f"{base_dir}/new.jpg",
    #             action_info=action_info)
    repair_step_by_info(action_info,
                        "/Users/<USER>/Downloads/下载-0.png")

if __name__ == '__main__1':
    imgcv = cv2.imread("/Users/<USER>/Downloads/7672186-1748616090809-4d7f860f-d697-40d2-9ce0-d5c9a466a000.jpg")
    imgcv = cv2.rectangle(imgcv, (32, 722), (32 + 1139, 722 + 1142), (0, 0, 255), thickness=5)
    cv2.imshow("test", imgcv)
    cv2.waitKey(0)
