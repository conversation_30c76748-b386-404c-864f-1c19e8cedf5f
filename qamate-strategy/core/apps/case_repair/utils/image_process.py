#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@File    : image_process.py
<AUTHOR> <PERSON><PERSON><PERSON><PERSON>(renya<PERSON><PERSON>@baidu.com)
@Time    : 2024/10/8 10:40
@Desc    : 
"""
import json
from datetime import datetime
from uuid import uuid4

import cv2

from apps.auto_case_transform.utils.utils import batdom_record_by_path_new
from apps.app_agent.utils.draw import draw_rect_with_index
from basics.util import logger
from basics import config
import os
from basics.util.bos_utils import BosUtil, upload_image


# config.load_config('/Users/<USER>/Workspace/Project/lazyOne/qamate-strategy/core/profile.json')
# config.load_config('/profile.json')


def get_image_with_grid(image_path, grid_path, bat_version="v10.1"):
    """
    :param image_path:
    :param grid_path:  # 存储地址
    :return:
    """

    dom_info, dom = batdom_record_by_path_new(image_path, bat_version=bat_version)
    ele_id_map = {}

    # ele_list = [item['rect'] for item in dom['children']]
    ele_list = []
    for item in dom['children']:
        if item['type'] == 'TextArea':
            for child in item['children']:
                ele_list.append(child)
        else:
            ele_list.append(item)

    rect_list = [ele['rect'] for ele in ele_list]

    imgcv = cv2.imread(image_path)
    grid_image = draw_rect_with_index(imgcv, rect_list, output_path=grid_path)

    ele_desc = "[\n"
    for idx, ele in enumerate(ele_list):
        t, d = dom_ele_2_desc(ele)
        ele_desc += "id:{}, 类型:{}, 描述: {}\n".format(idx, t, d)
        ele_id_map[idx] = ele
    ele_desc += "]"

    return dom_info, dom, ele_desc, ele_id_map


def dom_ele_2_desc(dom_ele: dict):
    """

    :param dom_ele:
    :return:
    """
    if 'Text' in dom_ele['type']:
        desc = dom_ele['ext']['text'] if len(dom_ele['ext']['text']) < 15 else dom_ele['ext']['text'][:15] + "..."
        desc = "'{}'".format(desc)
        type = "text"
    elif dom_ele['type'] == "Icon":
        desc = "『{}』".format(dom_ele['ext']['name'].replace("_beta", "")) + " icon"
        type = "icon"
    elif dom_ele['type'] == "Component":
        desc = "input_box" if "comment_box" in dom_ele['ext']['name'] else dom_ele['ext']['name']
        if dom_ele['ext']['name'] == "icon":
            type = "icon"
        else:
            type = "element"
    else:
        logger.warning("未知的操作类型：{}".format(dom_ele['type']))
        desc = "未知"
        type = "未知"
    return type, desc


def upload_case_image(image_path):
    def gen_key(file_path):
        time_stamp = int(datetime.now().timestamp() * 1000)
        _, file_extension = os.path.splitext(file_path)
        file_name = str(uuid4()) + file_extension
        key = os.path.join("lazy-one", "0-0-{}-{}".format(time_stamp, file_name))
        return key

    return upload_image(image_path, gen_key(image_path))


if __name__ == '__main__':
    # img_path = "/Users/<USER>/.lazyOne/localServer/cache/0d91d883-ef45-4159-beb9-5b4f9fd0d13c/old_with_ele.jpg"
    # grid_path = "/Users/<USER>/.lazyOne/localServer/cache/0d91d883-ef45-4159-beb9-5b4f9fd0d13c/old_with_ele_g.jpg"
    # a, b, c, d = get_image_with_grid(img_path, grid_path)
    # print(c)
    upload_case_image('/Users/<USER>/Downloads/8aa8e4f5f5b9878e6246b8f912a1d8e8.png')
