"""
错误码
"""
from enum import Enum


class ErrCode(Enum):
    ABSENCE_ACTION = (10001, "控件不存在校验，不支持修复")
    OVER_5_NODES = (10002, "超过5个节点，不支持修复")
    POPUP_DISTURB = (10003, "弹窗遮挡，不支持修复")
    DIFF_PAGE = (10004, "页面不一致, 不支持修复")
    UNSUPPORTED_FIND_NODE_CONVERT = (10005, "旧的findNode类型不支持映射到新类型")

    def __init__(self, code, msg):
        self.code = code
        self.msg = msg


if __name__ == '__main__':
    try:
        raise Exception(ErrCode.ABSENCE_ACTION)
    except Exception as e:
        print(e.args[0].code)
