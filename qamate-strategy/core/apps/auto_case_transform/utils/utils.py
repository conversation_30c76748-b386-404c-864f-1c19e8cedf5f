# !/usr/bin/env python3
# -*- coding: UTF-8 -*-
#
# Copyright (c) 2024 Baidu.com, Inc. All Rights Reserved
#
"""

一些工具

Authors: <AUTHORS>
Date:    2024/05/09
"""
import copy
import os
import uuid
from io import BytesIO

import requests
from PIL import Image

from apps.auto_case_transform.entity.trans import EleObj
from apps.auto_case_transform.exception.exception import CoreException, CoreErrCode
from apps.auto_case_transform.visual_strategy.base_visual_detect import VisualDetect
from basics.util import logger
from basics.util.config import CACHE_DIR
from basics.util.image import url_to_image_path
from features.record_replay.v9_4.multi_syne_record import MultiSyneRecordExecute
from features.record_replay.v9_4.multi_syne_replay import MultiSyneReplayExecute
from features.record_replay.v9_4.single_feature_record import SingleFeatureRecordExecute
from features.record_replay.v9_4.single_feature_replay import SingleFeatureReplayExecute
from features.router import main_router


def batdom_record(img_url, model_type, os_type, find_type, vision_info=None, page_source=None):
    """
    得到 当前版本的建模内容
    :param img_url:
    :param model_type:
    :param os_type:
    :param find_type:    0：单控件  1：多控件
    :param vision_info
    :param page_source:
    :return:
    """
    # 下载图片
    temp_dir = os.path.join(CACHE_DIR, 'temp')
    if not os.path.exists(temp_dir):
        os.makedirs(temp_dir)

    img_name = str(uuid.uuid4()) + ".jpg"
    img_path = os.path.join(temp_dir, img_name)
    url_to_image_path(img_url=img_url, save_path=img_path)

    new_dom_info, dom = batdom_record_by_path(img_path, model_type, os_type, find_type, vision_info, page_source)

    return new_dom_info, dom


def batdom_record_by_path(img_path, model_type, os_type, find_type, vision_info=None, page_source=None):
    """
    得到 当前版本的建模内容
    :param img_path:
    :param model_type:
    :param os_type:
    :param find_type:    0：单控件  1：多控件
    :param vision_info
    :param page_source:
    :return:
    """

    if not vision_info:
        vision_info = VisualDetect(image_path=img_path).main()

    # 获取建模结果
    bat_params = {
        "os_type": os_type,
        "model_type": model_type,
        "image_path": img_path,
        "vision_info": vision_info,
        "root_elements": page_source
    }

    if find_type == 1:
        tool = MultiSyneRecordExecute()
    elif find_type == 0:
        tool = SingleFeatureRecordExecute()
    else:
        raise Exception("find_type={},不存在的建模类型".format(find_type))

    dom_res = tool.main(bat_params)

    # 删除文件
    # os.remove(img_path)

    new_dom_info = dom_res['data']['info']
    dom = dom_res['data']['dom']

    return new_dom_info, dom


def batdom_replay(img_url, case_img_url, model_type, os_type, find_type, step_info, vision_info=None, page_source=None):
    """

    :param img_url: 回放页面的图片链接
    :param case_img_url: 录制页面的图片链接
    :param model_type:
    :param os_type:
    :param find_type:
    :param step_info:
    :param vision_info:
    :param page_source:
    :return:
    """

    temp_dir = os.path.join(CACHE_DIR, 'temp')
    if not os.path.exists(temp_dir):
        os.makedirs(temp_dir)

    # 下载回放图片
    img_name = str(uuid.uuid4()) + ".jpg"
    img_path = os.path.join(temp_dir, img_name)
    url_to_image_path(img_url=img_url, save_path=img_path)

    # 下载录制图片
    case_img_name = str(uuid.uuid4()) + ".jpg"
    case_img_path = os.path.join(temp_dir, case_img_name)
    url_to_image_path(img_url=case_img_url, save_path=case_img_path)

    match_nodes = batdom_replay_by_path(img_path, case_img_path,
                                        model_type=model_type, os_type=os_type, find_type=find_type,
                                        step_info=step_info, vision_info=vision_info,
                                        page_source=page_source)

    return match_nodes


def batdom_replay_by_path(img_path, case_img_path, model_type, os_type, find_type, step_info, vision_info=None,
                          page_source=None):
    """
    通过 图片路径 回放
    :param img_path:  回放页面的图片路径
    :param case_img_path:  录制页面的图片路径
    :param model_type:
    :param os_type:
    :param find_type:
    :param step_info:
    :param vision_info:
    :param page_source:
    :return:
    """

    if not vision_info:
        vision_info = VisualDetect(image_path=img_path).main()

    # 获取建模结果
    bat_params = {
        "os_type": os_type,
        "model_type": model_type,
        "image_path": img_path,
        "case_image_path": case_img_path,
        "vision_info": vision_info,
        "root_elements": page_source,
        "step_info": step_info
    }

    if find_type == 1:
        tool = MultiSyneReplayExecute()
    elif find_type == 0:
        tool = SingleFeatureReplayExecute()
    else:
        raise Exception("find_type={},不存在的建模类型".format(find_type))

    pattern_res = tool.main(bat_params)
    if pattern_res['code'] == 0:
        match_nodes = pattern_res['data']['match_nodes']
    else:
        # with open("err_replay_params.json", "w") as fi:
        #     json.dump(bat_params, fi, ensure_ascii=False)
        raise Exception("回放匹配失败，err_code:{}, err_mes：{}".format(pattern_res['code'], pattern_res['msg']))

    return match_nodes


def find_dom_ele_by_id(id, dom_node):
    """
    通过id查询 录制元素信息
    :param id:
    :param dom_node:
    :return:
    """

    node_id = dom_node['debug']['id']
    if id == node_id:
        return dom_node

    if dom_node.get("children"):
        for c_node in dom_node.get("children"):
            res = find_dom_ele_by_id(id, c_node)
            if res is not None:
                return res
    return None


def find_node_to_ele_obj(node_info_feature, imgcv, scale=1, node_params=None):
    """
    step_info 中的 findNode (或dom中的children) 转为 GuiObj
    :param node_info_feature:
    :param imgcv: 原图
    :param scale: 缩放比例
    :return:
    """
    # 默认值
    index = 0
    text = ""

    if node_info_feature['type'] == "Component":
        name = node_info_feature['ext'].get('name', None)
        if name is None:
            name = node_info_feature['debug']['name']
    elif node_info_feature['type'] == "Text":
        name = "text"
        text = node_info_feature['ext']['text']
        index = node_info_feature['ext'].get("index", 0)
    elif node_info_feature['type'] == "TextArea":
        name = "text"
        text = node_info_feature['ext']['text']
        index = node_info_feature['ext'].get("index", 0)
    elif node_info_feature['type'] == "Icon":
        name = node_info_feature['ext'].get('name', None)
        index = node_info_feature['ext'].get("index", None)
        if name is None:
            name = node_info_feature['debug']['name']
            index = node_info_feature['debug'].get('index', 0)
    elif node_info_feature['type'] == "DFE":
        name = "dfe"
    elif node_info_feature['type'] == "DOE":
        name = "doe"
    elif node_info_feature['type'] == "DomElement":
        name = "doe"
    else:
        raise Exception("当前类型：{}，是不支持转化的操作元素类型，跳过".format(node_info_feature['type']))
        # logger.warning("当前类型：{}，是不支持转化的操作元素类型，跳过".format(node_info_feature['type']))

    left = int(node_info_feature['rect']['x'] * scale)
    top = int(node_info_feature['rect']['y'] * scale)
    width = int(node_info_feature['rect']['w'] * scale)
    height = int(node_info_feature['rect']['h'] * scale)
    rect_img = imgcv[top: top + height, left: left + width]

    gui_obj = EleObj(name=name, left=left, top=top, width=width, height=height, index=index,
                     word=text, type=node_info_feature['type'], case_info=node_info_feature,
                     imgcv=rect_img)
    return gui_obj


def parse_text_match_type(find_node):
    """
    解析匹配方式
    :param find_node:
    :return:
    """
    # 见于 type = 2/5/6
    if 'detailDetection' in find_node:
        # 文本全部包含 - xuzhejun
        if find_node['detailDetection'][1] == 2:
            return 1
        elif find_node['detailDetection'][1] == 1:
            return 1
        else:
            return 0
    # 见于 type = 7
    elif 'detailFeature' in find_node:
        return find_node['detailFeature']['matchType']
    else:
        raise Exception("未知的匹配类型")


def parse_element_ext_info(element: EleObj):
    """
    解析 元素中的 ext 信息
    :param element:
    :return:
    """
    if element.type == "Icon":
        ext = {
            "name": element.name,
            "index": element.index
        }
    elif element.type == "Component":
        ext = {
            "name": element.name
        }
    elif "Text" in element.type:
        word = element.word if not element.include_text else element.include_text
        ext = {
            "text": word,
            "index": element.index
        }
    elif "DFE" in element.type:
        ext = element.case_info['ext']
    elif "DOE" in element.type:
        ext = element.case_info['ext']
    else:
        raise Exception("未涉及的元素类型：{}".format(element.type))
    ext.update(element.case_info['ext'])
    return ext


def get_os_name_by_os_type(os_type):
    """
    得到系统类型
    :param os_type:
    :return:
    """
    os_map = {
        1: "android",
        2: "ios"
    }
    return os_map[os_type]


def scale_dom(dom_info, scale):
    """
    对给定的DOM信息进行缩放处理。

    Args:
        dom_info (Union[dict, list]): DOM信息，可以是单个字典对象或字典对象的列表。
        scale (float): 缩放比例。

    Returns:
        Union[dict, list]: 经过缩放处理后的DOM信息，与输入类型相同。

    """
    arr = []
    if type(dom_info) is dict:
        arr.append(dom_info)
    elif type(dom_info) is list:
        arr = dom_info
    for dom in arr:
        dom['rect']['x'] = int(dom['rect']['x'] / scale)
        dom['rect']['y'] = int(dom['rect']['y'] / scale)
        dom['rect']['w'] = int(dom['rect']['w'] / scale)
        dom['rect']['h'] = int(dom['rect']['h'] / scale)
        if 'children' in dom:
            scale_dom(dom['children'], scale)
    return dom_info


def download_img(img_url, img_path=None):
    """
    下载图片
    """
    image_name = img_url.split("/")[-1]
    if img_path is None:
        tmp_dir = os.path.join(os.getcwd(), ".tmp")
        if not os.path.exists(tmp_dir):
            os.makedirs(tmp_dir)
        img_path = os.path.join(tmp_dir, "{}.png".format(image_name))
    img_bytes = requests.get(img_url).content
    img = Image.open(BytesIO(img_bytes))
    img.save(img_path)
    return img_path


def batdom_record_new(img_url, os_type=2, vision_info=None, page_source=None):
    """
    得到最新版本的建模内容
    :param img_url:
    :param model_type:
    :param os_type:
    :param find_type:    0：单控件  1：多控件
    :param vision_info
    :param page_source:
    :return:
    """
    # 下载图片
    temp_dir = os.path.join(CACHE_DIR, 'temp')
    if not os.path.exists(temp_dir):
        os.makedirs(temp_dir)

    img_name = str(uuid.uuid4()) + ".jpg"
    img_path = os.path.join(temp_dir, img_name)
    url_to_image_path(img_url=img_url, save_path=img_path)

    new_dom_info, dom = batdom_record_by_path_new(img_path, os_type, page_source, vision_info)
    return new_dom_info, dom


def get_bat_main_version(bat_version):
    """
    获取batdom的主版本号
    """
    if bat_version is None:
        raise Exception('bat_version is None')
    if bat_version.startswith('v'):
        v = bat_version[1:].split('.')[0]
        return int(v)
    raise Exception('unknown bat_version {}'.format(bat_version))


def batdom_replay_by_path_new(img_path, case_img_path, step_info, os_type=2, page_source=None, vision_info=None):
    """
    batdom10以上的回放函数
    """
    v = step_info.get('recordInfo', {}).get('version', None)
    if v is not None and get_bat_main_version(v) >= 11:
        return batdom_replay_by_path_new_v2(img_path=img_path, case_img_path=case_img_path, step_info=step_info,
                                            os_type=os_type, page_source=page_source, vision_info=vision_info)
    step_info = copy.deepcopy(step_info)
    if vision_info is None:
        vision_info = VisualDetect(image_path=img_path, use_tab=False).main()

    use_page_source = page_source is not None

    icon_r = vision_info['icon_info']
    beta_r = vision_info['beta_info']
    ele_r = vision_info['ele_info']
    ocr_r = vision_info['ocr_info']
    body = {
        "jsonrpc": "xxx",
        "id": 0,
        "method": "v4.record_replay.replay",
        "params": [
            {
                "module_name": "replay_widget_model",
                "module_info": {
                    "widget_info": {
                        "ocr_info": {"used": True, "data": ocr_r},
                        "icon_info": {"used": True, "data": icon_r},
                        "ele_info": {"used": True, "data": ele_r},
                        "beta_info": {"used": True, "data": beta_r},
                        "dom_info": {"used": use_page_source, "data": page_source if use_page_source else {}}
                    },
                    "os_type": os_type,
                    "step_info": step_info,
                    "image_path": img_path,
                    "case_image_path": case_img_path
                },
            }
        ]
    }
    replay_res = call_main_route(body)
    if replay_res['code'] == 0:
        return replay_res['data']['match_nodes']
    else:
        logger.error(replay_res)
        raise CoreException(CoreErrCode.REPLAY_FAILED)


def batdom_record_by_path_new(img_path, os_type=2, page_source=None, vision_info=None, bat_version=None):
    """
    得到最新版本的建模内容
    batdom10.0+的建模函数，不再区分单、多控件
    """
    if bat_version is not None and get_bat_main_version(bat_version) >= 11:
        return batdom_record_by_path_new_v2(img_path=img_path, os_type=os_type, page_source=page_source,
                                            vision_info=vision_info, bat_version=bat_version)

    if vision_info is None:
        vision_info = VisualDetect(image_path=img_path, use_tab=False).main()

    use_page_source = page_source is not None

    icon_r = vision_info['icon_info']
    beta_r = vision_info['beta_info']
    ele_r = vision_info['ele_info']
    ocr_r = vision_info['ocr_info']
    body = {
        "jsonrpc": "xxx",
        "id": 0,
        "method": "v4.record_replay.record",
        "params": [
            {
                "module_name": "record_widget_model",
                "module_info": {
                    "widget_info": {
                        "ocr_info": {"used": True, "data": ocr_r},
                        "icon_info": {"used": True, "data": icon_r},
                        "ele_info": {"used": True, "data": ele_r},
                        "beta_info": {"used": True, "data": beta_r},
                        "dom_info": {"used": use_page_source, "data": page_source["dom"] if use_page_source else {}}
                    },
                    "os_type": os_type,
                    "image_path": img_path
                },
            }
        ]
    }

    if isinstance(bat_version, str) and bat_version.startswith("v"):
        body["params"][0]['version'] = bat_version

    dom_res = call_main_route(body)
    new_dom_info = dom_res['data']['info']
    dom = dom_res['data']['dom']
    return new_dom_info, dom


def batdom_replay_by_path_new_v2(img_path, case_img_path, step_info, os_type=2, page_source=None, vision_info=None):
    """
    batdom11以上的回放函数，因为用例格式≠策略入参格式，所以需要单独的逻辑
    """

    def get_input_step(step_info):
        input_step = {
            "findInfo": step_info["findInfo"]["widgetInfo"],
            "recordInfo": {
                "name": step_info["recordInfo"]["name"],
                "used_widget_list": step_info["recordInfo"]["used_widget_list"],
                "version": step_info["recordInfo"]["version"],
                "recordResult": step_info["recordInfo"]["dom"]
            }
        }
        return input_step

    step_info = get_input_step(step_info)
    # step_info = copy.deepcopy(step_info)
    if vision_info is None:
        vision_info = VisualDetect(image_path=img_path, use_tab=False).main()

    use_page_source = page_source is not None

    icon_r = vision_info['icon_info']
    beta_r = vision_info['beta_info']
    ele_r = vision_info['ele_info']
    ocr_r = vision_info['ocr_info']
    body = {
        "jsonrpc": "xxx",
        "id": 0,
        "method": "record_replay.replay",
        "params": [
            {
                "module_name": "replay_widget_model_v2",
                "module_info": {
                    "widget_info": {
                        "ocr_info": {"used": True, "data": ocr_r},
                        "icon_info": {"used": True, "data": icon_r},
                        "ele_info": {"used": True, "data": ele_r},
                        "beta_info": {"used": True, "data": beta_r},
                        "dom_info": {"used": use_page_source, "data": page_source if use_page_source else {}}
                    },
                    "os_type": os_type,
                    "step_info": step_info,
                    "image_path": img_path,
                    "case_image_path": case_img_path
                },
            }
        ]
    }
    replay_res = call_main_route(body)
    if replay_res['code'] == 0:
        return replay_res['data']['match_nodes']
    else:
        logger.error(replay_res)
        raise CoreException(CoreErrCode.REPLAY_FAILED)


def batdom_record_by_path_new_v2(img_path, os_type=2, page_source=None, vision_info=None, bat_version=None):
    """
    得到最新版本的建模内容
    batdom11.0+的建模函数，不再区分单、多控件
    """
    if vision_info is None:
        vision_info = VisualDetect(image_path=img_path, use_tab=False).main()

    use_page_source = page_source is not None

    icon_r = vision_info['icon_info']
    beta_r = vision_info['beta_info']
    ele_r = vision_info['ele_info']
    ocr_r = vision_info['ocr_info']
    body = {
        "jsonrpc": "xxx",
        "id": 0,
        "method": "record_replay.record",
        "params": [
            {
                "module_name": "record_widget_model_v2",
                "module_info": {
                    "widget_info": {
                        "ocr_info": {"used": True, "data": ocr_r},
                        "icon_info": {"used": True, "data": icon_r},
                        "ele_info": {"used": True, "data": ele_r},
                        "beta_info": {"used": True, "data": beta_r},
                        "dom_info": {"used": use_page_source, "data": page_source["dom"] if use_page_source else {}}
                    },
                    "os_type": os_type,
                    "image_path": img_path
                },
            }
        ]
    }

    if isinstance(bat_version, str) and bat_version.startswith("v"):
        body["params"][0]['version'] = bat_version

    dom_res = call_main_route(body)
    new_dom_info = dom_res['data']['info']
    dom = dom_res['data']['dom']
    return new_dom_info, dom


def call_main_route(body):
    """
    {
        "jsonrpc": "xxx",
        "id": 0,
        "method": "v3.record_replay.single_feature_record",
        "params": [
            {
                "os_type": 2,
                "model_type": 0,
                "image_path": "/xxx/xxx.png",
                "vision_info": {}
            }
        ]
    }
    """
    return main_router(body["method"], body["params"][0])


if __name__ == '__main__':
    pass
