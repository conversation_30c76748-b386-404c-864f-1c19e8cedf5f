# !/usr/bin/env python3
# -*- coding: UTF-8 -*-
#
# Copyright (c) 2024 Baidu.com, Inc. All Rights Reserved
#
"""


Authors: <AUTHORS>
Date:    2024/05/07
"""

import json
import requests
import time

from basics.image.ui.webapi import get_popup_from_openapi
from basics.util import logger
from apps.app_agent.utils.qamate_web_util import QamateWebUtil
from apps.auto_case_transform.exception.exception import CoreErrCode, CoreException

QAMATE_BASE_URL = 'https://qamate.baidu-int.com'


class Device:
    """
    设备操控
    """

    def __init__(self, device_id, retry=3):
        """
        用于设备操控
        """
        self.device_id = device_id
        self.retry = retry

        # 初始化设备信息
        self.size_info = self.size()
        self.rotation_info = self.screen_rotation()

    def request(self, action_info, timeout=120):
        """
        发送请求至云控系统
        :return:
        """
        try_count = 0
        while try_count < self.retry:
            try_count += 1
            try:
                result = requests.post(
                    url=QAMATE_BASE_URL + "/lazydorking/device/action",
                    data=json.dumps(
                        {
                            "deviceId": self.device_id,
                            "timeout": timeout,
                            "actionInfo": json.dumps(action_info, ensure_ascii=False),
                        },
                        ensure_ascii=False,
                    ).encode('utf-8'),
                    headers={"Content-Type": "application/json; charset=UTF-8"},
                    timeout=180,
                )
                action_res = json.loads(result.content)
                if 0 != action_res["code"]:
                    raise Exception("action error: " + action_res["msg"])

                action_result = json.loads(action_res["data"]["actionResult"])
                if 'status' in action_result and action_result['status'] != 0:
                    raise Exception("驱动设备执行出现错误，status:{}, "
                                    "err_msg:{},"
                                    "screenshot:{}".format(action_result['status'], action_result['msg'],
                                                           self.screenshot().get('screenshot')))

                return json.loads(action_res["data"]["actionResult"])
            except Exception as e:
                logger.error("请求云控出现错误:{}，重试...".format(e))
                if try_count == self.retry:
                    raise CoreException(CoreErrCode.DEVICE_ERROR, detail=str(e))
                time.sleep(2)

    def size(self):
        """
        获取屏幕尺寸
        :return:
        """
        action_info = {"type": "device.screensize", "params": {}}
        return self.request(action_info)

    def screen_rotation(self):
        """
        获取屏幕旋转比例
        :return:
        """
        action_info = {"type": "device.rotation", "params": {}}
        rotation_info = self.request(action_info)
        return rotation_info['rotation']

    def screenshot(self, needUploadBos=True):
        """
        获取屏幕截图
        :return:
        """
        action_info = {
            "type": "device.screenshot",
            "params": {"needUploadBos": needUploadBos},
        }
        return self.request(action_info)

    def current(self):
        """
        获取设备当前状态
        :return:
        """
        action_info = {"type": "device.current", "params": {}}
        return self.request(action_info)

    def source(self):
        """
        获取 device source
        :return:
        """
        action_info = {"type": "dom.pagesource", "params": {}}
        return self.request(action_info)

    def ui_dom(self):
        """
        获取 UIDom
        :return:
        """
        action_info = {"type": "dom.uidom", "params": {}}
        return self.request(action_info)

    def bat_dom(self):
        """
        获取 BatDom
        :return:
        """
        action_info = {"type": "dom.batdom", "params": {}}
        return self.request(action_info)

    def tap(self, x, y):
        """
        设备点击
        :return:
        """
        action_info = {"type": "action.tap", "params": {"x": x, "y": y}}
        return self.request(action_info)

    def scroll(self, pointer, delta):
        """
        设备滑动
        :return:
        """
        action_info = {
            "type": "action.swipe",
            "params": {
                "startX": pointer["x"],
                "startY": pointer["y"],
                "targetX": pointer["x"] + delta["deltaX"],
                "targetY": pointer["y"] + delta["deltaY"],
            },
        }
        return self.request(action_info)

    def input(self, x, y, value):
        """
        设备输入
        :return:
        """
        action_info = {
            "type": "action.input",
            "params": {
                "x": x,
                "y": y,
                "text": value,
            },
        }
        return self.request(action_info)

    def close_app(self, packageName):
        """
        关闭 APP
        :return:
        """
        action_info = {
            "type": "action.closeApp",
            "params": {
                "packageName": packageName,
            },
        }
        return self.request(action_info)

    def launch_app(self, packageName, activity="", coldStart=True):
        """
        开启 APP
        :return:
        """
        action_info = {
            "type": "action.launchApp",
            "params": {
                "isColdStart": coldStart,
                "packageName": packageName,
                "activity": activity,
            },
        }
        return self.request(action_info)

    def install_app(self, installerPath, packageName):
        """
        安装 APP
        :return:
        """
        action_info = {
            "type": "action.installApp",
            "params": {
                "packageName": packageName,
                "fileLink": installerPath,
            },
        }
        return self.request(action_info)

    def scheme(self, scheme):
        """
        调起 scheme
        :return:
        """
        action_info = {
            "type": "action.scheme",
            "params": {
                "scheme": scheme,
            },
        }
        return self.request(action_info)

    def clear_alert(self):
        """
        清空当前的系统弹窗
        :return:
        """
        action_info = {"type": "action.clearAlert", "params": {}}
        return self.request(action_info)

    def crash(self, need_upload=False):
        """
        获取设备崩溃信息
        :return:
        """
        action_info = {"type": "device.crash", "params": {"needUpload": need_upload}}
        return self.request(action_info)

    def device_info(self, os_type):
        """
        设备信息
        :return:
        """
        dv_size = self.size()
        dv_rotation = self.screen_rotation()
        dv_screenshot = self.screenshot()

        info = {
            "type": os_type,
            "screenSize": {
                "width": dv_size['width'],
                "height": dv_size['height'],
                "scale": dv_size['scale'],
                "rotation": dv_rotation
            },
            "screenshot": dv_screenshot['screenshot']
        }
        return info

    def run_step(self, step_info, **kwargs):
        """
        单步骤执行
        :param step_info:
        :return:
        """
        action_info = {
            "type": "step.run",
            "params": {
                "stepInfo": step_info
            }
        }
        if 'type' in step_info['params'] and step_info['params']['type'] == "launchApp":
            os_type = kwargs.get('os_type', None)
            module_id = kwargs.get('module_id', None)
            if not os_type or not module_id:
                return self.request(action_info)
            self.__replenish_launch_app(step_info, os_type=os_type, module_id=module_id)

        return self.request(action_info)

    def __replenish_launch_app(self, step_info, os_type, module_id):
        """
        启动App
        :param step_info:
        :param os_type:
        :param module_id:
        :return:
        """
        pack_id = step_info['params']['params']['id']
        module_pack_list = QamateWebUtil(os_type=os_type, module_id=module_id).get_app_package_list()
        pack_name = ""

        for pack_info in module_pack_list:
            if pack_info['appId'] == pack_id:
                pack_name = pack_info['packageList'][0]
        if not pack_name:
            raise Exception("module_id={}未找到appId={}的packageName".format(module_id, pack_id))

        step_info["params"]["params"]["packageName"] = pack_name
        return step_info

    def __get_run_template_case_info(self, temp_id, module_id, os_type):
        """
        得到测试片段case信息
        :return:
        """
        qwu = QamateWebUtil(os_type=os_type, module_id=module_id)
        module_temp_lists = qwu.get_template_info()
        temp_info = None
        for module_temp in module_temp_lists:
            if module_temp['templateId'] == temp_id:
                temp_info = module_temp
                break
        if not temp_info:
            raise Exception("未查询到template_id：{}的模板信息".format(temp_id))

        temp_case_list = qwu.get_node_step_list(case_node_id=temp_info['caseNodeId'])
        return temp_case_list

    def run_step_compatibility(self, step_info, module_id, os_type):
        """
        兼容 模板类型的运行
        :param step_info:
        :param version_id:
        :return:
        """

        exec_res = None
        if step_info["type"] == 1 and step_info["params"]["type"] == "runTemplate":
            logger.info("设备运行测试片段模板")
            temp_case_list = self.__get_run_template_case_info(temp_id=step_info['params']['params']['id'],
                                                               module_id=module_id, os_type=os_type)
            for temp_step in temp_case_list:
                exec_res = self.run_step(temp_step['stepInfo'], os_type=os_type, module_id=module_id)

        else:
            exec_res = self.run_step(step_info, os_type=os_type, module_id=module_id)

        return exec_res

    def click_popup(self):
        """
        通用弹窗点除
        """

        def get_scale():
            """
            获取缩放比例
            """
            dev_info = self.device_info(os_type=None)
            scl = dev_info['screenSize']['scale']
            return scl

        scale = get_scale()
        time.sleep(1)
        for i in range(10):
            # 由于弹窗检测不稳定，暂时加多次重试解决
            sc = self.screenshot(needUploadBos=False)
            sc = sc['screenshot'].split(',')[1]
            r = get_popup_from_openapi(sc, product_ids=[0])
            print(r)
            if r['code'] == 0 and len(r['data']['popupList']) > 0 and r['data']['hit'] is True:
                rect = r['data']['rect']
                x, y = int(rect['x'] + rect['w'] / 2), int(rect['y'] + rect['h'] / 2)
                res = self.tap(int(x / scale), int(y / scale))
                print('点除弹窗 res: ', res)
                continue
            else:
                break
        time.sleep(1)


if __name__ == '__main__':
    # step_info = {'common': {'commonAlertClear': False, 'stepInterval': 2}, 'desc': '执行【打开App并登录】测试片段',
    #              'params': {'params': {'id': 8576}, 'type': 'runTemplate'}, 'type': 1}
    s = {'desc': '冷启动【百度极速版安卓】', 'type': 1, 'common': {'stepInterval': 2, 'commonAlertClear': False},
         'params': {'type': 'launchApp', 'params':
             {'id': 350, 'appName': '百度极速版安卓', 'activity': 'com.baidu.searchbox.SplashActivity',
              'isColdStart': True, 'packageName': 'com.baidu.searchbox.lite'}}}
    dv = Device(device_id=1285)
    # dv_info = dv.device_info("Android")
    # source = dv.source()
    res = dv.screenshot()
    print(res)
