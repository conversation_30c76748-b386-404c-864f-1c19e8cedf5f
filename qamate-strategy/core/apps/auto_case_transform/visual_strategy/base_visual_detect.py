# !/usr/bin/env python3
# -*- coding: UTF-8 -*-
#
# Copyright (c) 2024 Baidu.com, Inc. All Rights Reserved
#
"""
视觉检测结果获取

Authors: <AUTHORS>
Date:    2024/04/25
"""

import threading

from apps.auto_case_transform.visual_strategy.basic_ui_detect.element_detect import element_detect
from apps.auto_case_transform.visual_strategy.basic_ui_detect.icon_detect import beta_icon_detect
from apps.auto_case_transform.visual_strategy.basic_ui_detect.icon_detect import icon_detect
from apps.auto_case_transform.visual_strategy.basic_ui_detect.ocr import ocr
from apps.auto_case_transform.visual_strategy.basic_ui_detect.tab_detect import tab_detect


class VisualDetect(object):
    """
    视觉检测
    """

    def __init__(self, image_path, icon_conf=0.3, element_conf=0.5, tab_conf=0.5, use_tab=True):
        """

        :param image_path:
        :param icon_conf:
        :param element_conf:
        :param tab_conf:
        """
        self.image_path = image_path

        # 各种参数
        self.icon_conf = icon_conf
        self.element_conf = element_conf
        self.tab_conf = tab_conf

        self.detect_res = {}
        self.use_tab = use_tab

    def get_icon_result(self):
        """
        得到icon检测结果
        :return:
        """
        icon_res = icon_detect(self.image_path, threshold=self.icon_conf)
        self.detect_res['icon_info'] = icon_res
        # return icon_res

    def get_beta_icon_result(self):
        """
        得到beta_icon检测结果
        :return:
        """
        icon_res = beta_icon_detect(self.image_path, threshold=self.icon_conf)
        self.detect_res['beta_info'] = icon_res
        # return icon_res

    def get_element_result(self):
        """
        得到element检测结果
        :return:
        """
        element_res = element_detect(self.image_path, threshold=self.element_conf)
        self.detect_res['ele_info'] = element_res
        # return element_res

    def get_tab_result(self):
        """
        得到tab检测结果
        :return:
        """
        tab_res = tab_detect(self.image_path, threshold=self.tab_conf)
        self.detect_res['tab_info'] = tab_res
        # return tab_res

    def get_ocr_result(self):
        """
        得到OCR检测结果
        :return:
        """
        ocr_res = ocr(self.image_path)
        self.detect_res['ocr_info'] = ocr_res
        # return ocr_res

    def mul_thr_det(self):
        """

        :return:
        """
        threads = []

        funcs = [self.get_icon_result, self.get_beta_icon_result, self.get_element_result,
                 self.get_ocr_result]
        if self.use_tab:
            funcs.append(self.get_tab_result)

        # try:
        for func in funcs:
            thread = threading.Thread(target=func)
            threads.append(thread)
            thread.start()

        for thread in threads:
            thread.join()

    def main(self):
        """

        :return:
        """
        self.mul_thr_det()
        return self.detect_res


if __name__ == '__main__':
    img_pa = "/Users/<USER>/data/页面理解-通用卡片/新增物料/1-24/IMG_0093.PNG"
    res = VisualDetect(img_pa).main()
    print(res)