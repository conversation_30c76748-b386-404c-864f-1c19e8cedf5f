# !/usr/bin/env python3
# -*- coding: UTF-8 -*-
################################################################################
#
# Copyright (c) 2024 Baidu.com, Inc. All Rights Reserved
#
################################################################################
"""
获取 ICON 检测结果信息

Authors: <AUTHORS>
Date:    2024/04/24
"""

from features.cv_services.icon_detect import beta_detect
from features.cv_services.icon_detect import icon_detect as row_icon_detect


def icon_detect(image_path, threshold=0.3):
    """

    :param image_path:
    :param threshold:
    :return:
    """
    params = {
        "image_path": image_path,
        "threshold": threshold
    }
    res = row_icon_detect(params)
    return res.get("dom_tree")


def beta_icon_detect(image_path, threshold=0.3):
    """

    :param image_path:
    :param threshold:
    :return:
    """
    params = {
        "image_path": image_path,
        "threshold": threshold
    }
    res = beta_detect(params)
    return res.get("dom_tree")


if __name__ == '__main__':
    from basics import config
    config.load_config('/Users/<USER>/baidu/qamate-strategy/core/profile.json')
    img_pa = "/Users/<USER>/Downloads/图片/1.jpg"
    # res = icon_detect(img_pa, threshold=0.3)
    res = beta_icon_detect(img_pa, threshold=0.3)
    print(res)