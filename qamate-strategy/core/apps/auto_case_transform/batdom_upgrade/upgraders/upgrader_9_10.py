"""
升级到最新版本用例，暂未使用
"""
from apps.auto_case_transform.batdom_upgrade.upgraders.upgrader_9_9 import Upgrader9To9
from apps.auto_case_transform.exception.exception import CoreException, CoreErrCode
from basics.util import logger


class Upgrader9To10(Upgrader9To9):
    """
    type 9 -> type 10 类型转换
    """
    def convert_9to10(self, step_info):
        """
        """
        step_params = step_info["params"]
        step_info['type'] = 10
        fp = step_params["findParams"]
        fi = step_params["findInfo"]
        screen_count = fi.get("screenCount", 1)
        del fi["screenCount"]
        scroll_direction = fi.get("scrollDirection", 1)
        del fi["scrollDirection"]
        scroll_ratio = fi.get("scrollRatio", 1)
        del fi["scrollType"]
        if scroll_ratio == 1:
            scroll_ratio = 50
        elif scroll_ratio == 2:
            scroll_ratio = 25
        elif scroll_ratio == 3:
            scroll_ratio = 80
        else:
            scroll_ratio = 50
        scroll_type = fp.get("scrollType", 1)

        new_find_params = {
            "scroll": {
                "scrollType": scroll_type,
                "scrollDirection": scroll_direction,
                "scrollRatio": scroll_ratio,
                "screenCount": screen_count
            },
            "retry": {
                "times": fp.get("times", 1),
                "interval": fp.get("interval", 1)
            },
            "before": fp.get("before", {"wait", 0}),
            "until": fp.get("until", {"enable": False, "times": 1, "interval": 2000}),
            "allowFail": fp.get("allowFail", False),
        }

        new_pattern = {
            "recordInfo": {
                "dom": step_params["dom"],
                "name": step_params["domInfo"]["name"],
                "version": 'v11.0.0',
                "used_widget_list": step_params["domInfo"]["used_widget_list"],
                "deviceInfo": step_params["deviceInfo"]
            },
            "findType": step_params["findType"],
            "findInfo": {
                "widgetInfo": step_params["findInfo"]
            },
            "findParams": new_find_params,
            "actionInfo": step_params["actionInfo"]
        }
        step_info['params'] = new_pattern
        return step_info

    def _check_type(self):
        """
        检查类型
        """
        if self.type != 9:
            raise CoreException(CoreErrCode.INPUT_STEP_TYPE_ERROR)

    def upgrade(self):
        """
        """
        logger.info('升级到batdom11')
        self._check_type()
        new_step_info = super().upgrade()
        return self.convert_9to10(new_step_info)
