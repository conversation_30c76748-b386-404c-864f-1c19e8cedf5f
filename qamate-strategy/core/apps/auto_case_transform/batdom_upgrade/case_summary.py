"""
统计某个目录节点下所有用例的分布

步骤：

batdomvx: []

"""
import json

import requests

from apps.auto_case_transform.exception.exception import CoreErrCode, CoreException


def load_template_steps(template_id):
    template_query_url = 'https://qamate.baidu-int.com/lazyone/template/case/getCase'
    param = {
        "templateId": template_id,
    }
    cookies = {
        'lazyone_token': 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.E5FEA98991E1EACF7EE801292C6150F8',
    }
    res = requests.post(template_query_url, json=param, cookies=cookies).json()
    if res['errno'] != 0:
        print(f'获取测试片段步骤失败：{res}')
        raise CoreException(CoreErrCode.QUERY_CASE_TEMPLATE_FAILED, res['msg'])
    return res['data']

def traversal_dom_steps(nodes: list, pop_set=None, temp_set=None):
    """
    遍历所有的batdom步骤
    """
    if pop_set is None:
        pop_set = set()
    if temp_set is None:
        temp_set = set()
    for node in nodes:
        for step in node.get('step', []):
            action_info = json.loads(step['actionInfo'])
            a_type = action_info['type']
            if a_type in (2, 5, 6, 7, 8, 9):
                # 视觉步骤
                yield step
            if a_type == 1 and False:
                p_type = action_info['params']['type']
                if p_type == 'clearPop':
                    print(f'步骤{step["id"]}是弹窗模板')
                    pop_list = action_info['params']['params']['popList']
                    for template_id in pop_list:
                        if template_id in pop_set:
                            print("重复弹窗模板")
                            continue
                        else:
                            pop_set.add(template_id)
                        pop_steps = load_template_steps(template_id=template_id)
                        for pop_step in pop_steps:
                            pop_action_info = json.loads(pop_step['actionInfo'])
                            pop_a_type = pop_action_info['type']
                            if pop_a_type in (2, 5, 6, 7, 8, 9):
                                # 视觉步骤
                                yield pop_step
                elif p_type == 'runTemplate':

                    template_id = action_info['params']['params']['id']
                    if template_id in temp_set:
                        print("重复测试片段")
                        continue
                    else:
                        temp_set.add(template_id)
                    tp_steps = load_template_steps(template_id=template_id)
                    for tp_step in tp_steps:
                        tp_action_info = json.loads(tp_step['actionInfo'])
                        tp_a_type = tp_action_info['type']
                        if tp_a_type in (2, 5, 6, 7, 8, 9):
                            # 视觉步骤
                            yield tp_step
        yield from traversal_dom_steps(node.get('child', []), pop_set=pop_set, temp_set=temp_set)




def stat_step(space_id: int, dir_node_id: int, os_type: int, version_id: int):


    dom_map = {}
    list_template_url = 'https://qamate.baidu-int.com/lazyone/template/getTemplate'
    body = {
        'os': os_type,
        'spaceId': space_id,
        'versionId': version_id,
        'withStep': True
    }
    cookies = {
        'lazyone_token': 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.E5FEA98991E1EACF7EE801292C6150F8',
    }
    rj = requests.post(list_template_url, json=body, cookies=cookies).json()
    for template in rj['data']:
        for step in template['step']:
            action_info = json.loads(step['actionInfo'])
            a_type = action_info['type']
            if a_type in (2, 5, 6, 7, 8, 9):
                dom_ver = f'type:{action_info["type"]}'
                # print(dom_ver)
                if dom_ver not in dom_map:
                    dom_map[dom_ver] = []
                dom_map[dom_ver].append(step)


    tree_query_url = 'https://qamate.baidu-int.com/lazyone/tree/query'
    param = {
        "nodeId": dir_node_id,
        "osType": os_type,
        "versionId": version_id,
        "withStep": True,
        "withRichText": False
    }
    res = requests.post(tree_query_url, json=param).json()
    # print(res)
    for i, step in enumerate(traversal_dom_steps(res['data']['node'])):
        print(i, step['id'])
        try:
            action_info = json.loads(step['actionInfo'])
            # dom_ver = action_info['params'].get('domInfo', {}).get('version', None)
            # if dom_ver is None:
            atype = action_info['type']
            dom_ver = f'type:{atype}'
            if dom_ver not in dom_map:
                dom_map[dom_ver] = []
            dom_map[dom_ver].append(step)
        except:
            print(step)

    for k, v in dom_map.items():
        print(k, len(v))

if __name__ == '__main__':
    stat_step(space_id=23, dir_node_id=68084, os_type=1, version_id=492)