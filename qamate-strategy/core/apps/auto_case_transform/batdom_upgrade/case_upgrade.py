"""
case级用例升级：
1. 拉取用例树
2. 组装用例
3. 数据采集
4. 用例转换
5. 真机执行
6. 步骤/模板更新
"""
import json
import os.path
import shutil
import traceback
import typing

import requests

from apps.auto_case_transform.batdom_upgrade.upgrader import Upgrader
from apps.auto_case_transform.device import Device
from apps.auto_case_transform.exception.exception import CoreException, CoreErrCode
from basics import config


class CaseManager:
    """
    用例查询
    """

    def __init__(self):
        """
        init
        """
        self.token = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.E5FEA98991E1EACF7EE801292C6150F8'
        self.base_url = 'https://qamate.baidu-int.com'
        self.tree_query_url = self.base_url + '/lazyone/tree/query'
        self.template_query_url = self.base_url + '/lazyone/template/case/getCase'
        self.step_update_url = self.base_url + '/lazyone/step/update'
        self.template_update_url = self.base_url + '/lazyone/template/case/update'
        self.template_list_url = self.base_url + '/lazyone/template/getTemplate'

    def update_step(self, action_info):
        pass

    def load_template_steps(self, template_id):
        """
        获取模板步骤
        """
        param = {
            "templateId": template_id,
        }
        cookies = {
            'lazyone_token': self.token,
        }
        res = requests.post(self.template_query_url, json=param, cookies=cookies).json()
        if res['errno'] != 0:
            print(f'获取测试片段步骤失败：{res}')
            raise CoreException(CoreErrCode.QUERY_CASE_TEMPLATE_FAILED, res['msg'])
        return res['data']

    def load_case_tree(self, case_root_id, os_type, dir_node_id, version_id):
        """
        获取用例树
        {
            "index": 2,
            "nodeId": 259141,
            "parentId": 0,
            "nodeType": 0,
            "creatorId": 173,
            "priority": 0,
            "nodeVersionType": 2,
            "modifyUser": "zhangkaili05",
            "creator": null,
            "nodeText": "纯听流",
            "extra": null,
            "templateIds": [],
            "tagList": [],
            "modifyTime": 1719546738000,
            "linkInfo": [],
            "relation": [],
            "richText": null,
            "forkNodeId": null,
            "nodeSource": 0,
            "adoptStatus": null,
            "tempNodeType": null,
            "metric": {
                "auto": 0,
                "halfAuto": 0,
                "manual": 2
            },
            "child": []
        }
        """
        trees = self.load_dir_node_trees(os_type, dir_node_id, version_id)
        root = [node for node in trees if node['nodeId'] == case_root_id][0]
        return root

    def load_dir_node_trees(self, os_type, dir_node_id, version_id):
        param = {
            "nodeId": dir_node_id,
            "osType": os_type,
            "versionId": version_id,
            "withStep": True,
            "withRichText": False
        }
        res = requests.post(self.tree_query_url, json=param).json()
        return [node for node in res['data']['node']]

    def update_case_step_info(self, node_id, step_id, os_type, action_info):
        """
        更新非模板步骤信息
        "id": 1, // 步骤标识
        "nodeId": 1, // 节点标识
        "osType": 2 // 1-android 2-ios
        "actionInfo": "{}", // 步骤信息
        """
        body = {
            "id": step_id,
            "nodeId": node_id,
            "osType": os_type,
            "actionInfo": json.dumps(action_info, ensure_ascii=False)
        }
        cookies = {
            'lazyone_token': self.token,
        }
        r = requests.post(self.step_update_url, json=body, cookies=cookies).json()
        if r['errno'] != 0:
            raise CoreException(err_code=CoreErrCode.UPDATE_ERROR, detail=r['msg'])
        print(f"非模板步骤{step_id}升级成功")

    def update_template_step_info(self, template_id, step_id, action_info):
        """
        更新模板步骤信息
        """
        body = {
            "id": step_id,
            "templateId": template_id,
            "actionInfo": json.dumps(action_info, ensure_ascii=False)
        }
        cookies = {
            'lazyone_token': self.token,
        }
        r = requests.post(self.template_update_url, json=body, cookies=cookies).json()
        if r['errno'] != 0:
            raise CoreException(err_code=CoreErrCode.UPDATE_ERROR, detail=r['msg'])
        print(f"模板步骤{step_id}升级成功")

    def list_template(self, os_type, space_id, version_id):
        body = {
            'os': os_type,
            'spaceId': space_id,
            'versionId': version_id,
            'withStep': True
        }
        cookies = {
            'lazyone_token': self.token,
        }
        rj = requests.post(self.template_list_url, json=body, cookies=cookies).json()
        if rj['errno'] != 0:
            raise CoreException(err_code=CoreErrCode.LIST_TEMPLATE_FAILED, detail=rj['msg'])
        return rj['data']


class CaseTreeUpgrader:

    def __init__(self, space_id, tree_query_param, device_id):
        """
        """
        self.case_manager = CaseManager()
        self.root_id = tree_query_param['case_root_id']
        self.device_id = device_id
        self.device = Device(device_id)
        self.os_type = tree_query_param['os_type']
        self.version_id = tree_query_param['version_id']
        self.space_id = space_id
        self.case_tree = self.case_manager.load_case_tree(case_root_id=self.root_id, os_type=self.os_type,
                                                          dir_node_id=tree_query_param['dir_node_id'],
                                                          version_id=tree_query_param['version_id'])
        self.node_status = {}
        self.step_upgrade_cache = {}
        self.template_upgrade_cache = {}
        self.pop_up_upgrade_cache = {}

    def _need_page_source(self, old_action_info):
        """
        分析旧步骤是否使用了pagesource
        """
        find_nodes = old_action_info.get('params', {}).get('findInfo', {}).get('findNode', [])
        for find_node in find_nodes:
            # print(find_node)
            if find_node.get('detailFeature', {}).get('type', '') in ['DFE', 'DOE']:
                return True
        return False

    def _do_upgrade(self, old_action_info):
        """
        升级步骤
        """
        print(f'升级步骤：{old_action_info["desc"]}')
        need_ps = self._need_page_source(old_action_info)

        try:
            return Upgrader(old_action_info=old_action_info).upgrade()
        except CoreException as e:
            print(e)
            ex = e
        except Exception as e:
            print(e)
            raise e
        try:
            if need_ps:
                # FIXME
                #  临时关闭新pagesource转化，只做视觉转化
                raise CoreException(CoreErrCode.UNSUPPORTED_UPGRADE)

                # 如果纯视觉转化失败，再尝试采集page_source和新截图，在新截图上升级
                new_img_url = self.device.screenshot(needUploadBos=True)['screenshot']
                new_ps = self.device.source()['dom']
                return Upgrader(old_action_info=old_action_info,
                                new_img_url=new_img_url,
                                new_page_source=new_ps).upgrade()
        except Exception as e:
            print(e)
            raise e
        if ex is not None:
            raise ex

    def _get_case_list(self):
        case_list = []
        case_path = []

        def traversal_tree_case(root, cl: typing.List, cp: typing.List):
            cp.append(root)
            if len(root['child']) == 0:
                cl.append(cp[:])
            for child in root['child']:
                traversal_tree_case(child, cl, cp)
            cp.remove(root)

        traversal_tree_case(self.case_tree, case_list, case_path)
        return case_list

    def _process_popup_step(self, params):
        """
        递归每个弹窗模板
        对每个步骤递归执行升级，但不真机验证，允许上传
        直到递归执行结束后，才对整个弹窗点除步骤执行真机回放，而且不检查是否成功
        """
        pop_list = params['popList']
        for template_id in pop_list:
            steps = self.case_manager.load_template_steps(template_id=template_id)
            for step in steps:
                self._process_one_step(step, pop_up=True)

    def _process_case_template(self, params):
        """
        处理测试片段模板
        """
        template_id = params['id']
        steps = self.case_manager.load_template_steps(template_id=template_id)
        # print(steps)
        for step in steps:
            self._process_one_step(step, template=True)

    def _dev_replay_check(self, action_info):
        # FIXME
        #  临时关闭真机回放
        return True

        print(f'真机执行：{action_info["desc"]}')
        run_res = self.device.run_step(action_info)
        # print(run_res)
        if run_res['status'] < 0:
            print(run_res)
            raise CoreException(CoreErrCode.DEVICE_REPLAY_CHECK_FAILED, param=run_res)

    def _on_upgrade_failed(self, step, e, old_action_info, pop_up, template):
        # 步骤执行失败的生命周期函数
        print("{} failed".format(step))
        step_id = step['id']
        node_id = step.get('nodeId', None)
        template_id = step.get('templateId', None)
        if node_id is not None:
            node_dir = os.path.join("output/node", str(node_id))
        else:
            node_dir = os.path.join("output/template", str(template_id))
        if not os.path.exists(node_dir):
            os.makedirs(node_dir)
        step_dir = os.path.join(node_dir, str(step_id))
        if not os.path.exists(step_dir):
            os.makedirs(step_dir)
        old_action_path = os.path.join(step_dir, "old_action_info.json")
        with open(old_action_path, 'w') as f:
            json.dump(old_action_info, f, indent=4, ensure_ascii=False)
        ex_path = os.path.join(step_dir, "exception.txt")
        with open(ex_path, 'w') as f:
            f.write(str(e))

    def _on_dev_replay_failed(self, step, e, old_action_info, new_action_info, pop_up, template):
        # 真机回放失败的生命周期函数
        step_id = step['id']
        node_id = step.get('nodeId', None)
        template_id = step.get('templateId', None)
        if node_id is not None:
            node_dir = os.path.join("output/node", str(node_id))
        else:
            node_dir = os.path.join("output/template", str(template_id))

        screenshot = e.param.get('data', {}).get('extra', {}).get('screenshot', None)
        if not os.path.exists(node_dir):
            os.makedirs(node_dir)
        step_dir = os.path.join(node_dir, str(step_id))
        if not os.path.exists(step_dir):
            os.makedirs(step_dir)
        old_action_path = os.path.join(step_dir, "old_action_info.json")
        with open(old_action_path, 'w') as f:
            json.dump(old_action_info, f, indent=4, ensure_ascii=False)
        new_action_path = os.path.join(step_dir, "new_action_info.json")
        with open(new_action_path, 'w') as f:
            json.dump(new_action_info, f, indent=4, ensure_ascii=False)
        ex_path = os.path.join(step_dir, "exception.txt")
        with open(ex_path, 'w') as f:
            f.write(str(e))

        if screenshot is not None:
            screenshot_path = os.path.join(step_dir, "screenshot.png")
            shutil.copy(screenshot, screenshot_path)

    def _is_step_new(self, step) -> bool:
        """
        判断一个step是否是最新版本
        """
        action_info = json.loads(step['actionInfo'])
        a_type = action_info['type']
        if a_type in (2, 5, 6, 7, 8, 9):
            dom_ver = json.loads(step['actionInfo']).get('params', {}).get('domInfo', {}).get('version', '')
            print('dom ver', dom_ver)
            return 'v10.1' in dom_ver
        elif a_type == 1:
            print(f'步骤{step["id"]}是非视觉步骤，判断是否是测试片段或弹窗模板')
            p_type = action_info['params']['type']
            if p_type == 'clearPop':
                pop_list = action_info['params']['params']['popList']
                for template_id in pop_list:
                    steps = self.case_manager.load_template_steps(template_id=template_id)
                    # print(steps)
                    for step in steps:
                        if not self._is_step_new(step):
                            return False
            elif p_type == 'runTemplate':
                template_id = action_info['params']['params']['id']
                steps = self.case_manager.load_template_steps(template_id=template_id)
                for step in steps:
                    if not self._is_step_new(step):
                        return False
            # 其他情况默认是true
            return True

    def _on_upgrade_success(self, step, new_action_info, pop_up, template):
        """
        升级成功后的处理逻辑
        """
        # 这里加一个最新版本校验，已经是最新版本的，不做写入
        if self._is_step_new(step):
            step_id = step['id']
            print(f'{step_id}, pop: {pop_up}, template: {template}, 已经是最新版本, 跳过更新')
            return

        if pop_up:
            self.pop_up_upgrade_cache[step['id']] = new_action_info
        elif template:
            self.template_upgrade_cache[step['id']] = new_action_info
        else:
            self.step_upgrade_cache[step['id']] = new_action_info

        if template or pop_up:
            # 属于测试片段模板步骤或弹窗模板步骤
            print("更新测试片段步骤，{}", step)
            step_id = step['id']
            template_id = step['templateId']
            self.case_manager.update_template_step_info(template_id=template_id,
                                                        step_id=step_id,
                                                        action_info=new_action_info)
        else:
            # 普通步骤的更新
            print("更新普通步骤，{}", step)
            dev_os = new_action_info['params']['deviceInfo']['type']
            self.case_manager.update_case_step_info(node_id=step['nodeId'],
                                                    step_id=step['id'],
                                                    os_type=self.os_type,
                                                    action_info=new_action_info)

    def _process_one_step(self, step, pop_up=False, template=False):
        """
        处理一个步骤
        :param step:
        :param pop_up: 是否是弹窗中的步骤
        :param run_template: 是否是测试片段中的步骤
        step (keys): ['id', 'nodeId', 'type', 'index', 'actionType', 'screenshot', 'createdTime', 'state', 'extra',
        'actionInfo']
        """
        # print(step.keys())
        action_info = json.loads(step['actionInfo'])
        a_type = action_info['type']
        allow_fail = action_info.get('params', {}).get('findParams', {}).get('allowFail', False)

        # 判断是否需要升级
        if a_type in (2, 5, 6, 7, 8, 9):
            print(f'步骤{step["id"]}是视觉步骤，开始升级：{action_info["params"]}')
            # new_action_info = Upgrader(old_action_info=action_info).upgrade()
            if pop_up and step['id'] in self.pop_up_upgrade_cache:
                print(f'弹窗{step["id"]}已经升级，直接使用缓存')
                new_action_info = self.pop_up_upgrade_cache[step['id']]
            elif template and step['id'] in self.template_upgrade_cache:
                print(f'测试片段{step["id"]}已经升级，直接使用缓存')
                new_action_info = self.template_upgrade_cache[step['id']]
            elif step['id'] in self.step_upgrade_cache:
                print(f'步骤{step["id"]}已经升级，直接使用缓存')
                new_action_info = self.step_upgrade_cache[step['id']]
            else:
                try:
                    new_action_info = self._do_upgrade(action_info)
                except Exception as e:
                    self._on_upgrade_failed(step, e, action_info, pop_up=pop_up, template=template)
                    raise e
            if not pop_up:
                # 真机回放验证
                try:
                    self._dev_replay_check(new_action_info)
                    self._on_upgrade_success(step, new_action_info, pop_up=pop_up, template=template)
                    return
                except CoreException as e:
                    if e.err_code == CoreErrCode.DEVICE_REPLAY_CHECK_FAILED:
                        self._on_dev_replay_failed(step, e, action_info, new_action_info, pop_up=pop_up,
                                                   template=template)
                    #  判断是不是阻塞步骤，如果是非阻塞步骤，则真机回放失败不阻塞后续执行
                    if allow_fail:
                        print(e)
                        print(f'步骤{step["id"]}真机回放失败，但允许失败，继续转化')
                        self._on_upgrade_success(step, new_action_info, pop_up=pop_up, template=template)
                        return
                    else:
                        raise e
                except Exception as e:
                    print(e)
                    raise e
        elif a_type == 1:
            print(f'步骤{step["id"]}是非视觉步骤，判断是否是测试片段或弹窗模板')
            p_type = action_info['params']['type']
            if p_type == 'clearPop':
                print(f'步骤{step["id"]}是弹窗模板')
                self._process_popup_step(action_info['params']['params'])
                self._dev_replay_check(action_info)
            elif p_type == 'runTemplate':
                print(f'步骤{step["id"]}是测试片段')
                # 对测试片段中的步骤逐个进行转化，立刻进行真机回放验证，并上传
                self._process_case_template(action_info['params']['params'])
            else:
                print(f'步骤{step["id"]}不是弹窗模板或测试片段，不升级直接执行')
                self._dev_replay_check(action_info)
        elif a_type == 3:
            print(f'步骤{step["id"]}是人工操作，不进行真机回放')
        else:
            print(f'步骤{step["id"]}不是需要升级的步骤，不升级直接执行')
            self._dev_replay_check(action_info)

    def _is_case_new(self, case) -> bool:
        """
        检查用例所有视觉节点（包括弹窗）是否已经最新
        """
        for node in case:
            for step in node['step']:
                if not self._is_step_new(step):
                    return False
        return True

    def upgrade_all_template(self):
        """
        先更新所有：
        1. 测试片段用例 type 1
        2. 弹窗模板 type 0
        3. 设备初始化模板 type 2
        """
        templates = self.case_manager.list_template(os_type=self.os_type, space_id=self.space_id,
                                                    version_id=self.version_id)
        for template in templates:
            t_type = template['type']
            for step in template['step']:
                print(f'节点{template["id"]}步骤{step["id"]}开始转化')
                try:
                    if t_type == 0:
                        self._process_one_step(step, pop_up=True)
                    elif t_type == 1:
                        self._process_one_step(step, template=True)
                    elif t_type == 2:
                        self._process_one_step(step, template=True)
                except Exception as e:
                    traceback.print_exc()


    def upgrade_all(self):
        """
        升级用例树
        """
        self.upgrade_all_template()
        case_list = self._get_case_list()
        for i, case in enumerate(case_list):
            if self._is_case_new(case):
                print(f"case {i} 的步骤已经全部更新完成，跳过")
                continue
            # 遍历每个case
            for node in case:
                node_text = node['nodeText']
                print(f'节点{node_text}开始转化')
                # 遍历case的每个节点

                for step in node['step']:
                    # 遍历节点的每个步骤
                    print(f'节点{node["nodeId"]}步骤{step["id"]}开始转化')
                    try:
                        # 由于转化过程不使用真机回放，改成有一个步骤失败，后续步骤仍然可以转化
                        self._process_one_step(step)
                    except Exception as e:
                        # print(e)
                        traceback.print_exc()


if __name__ == '__main__':
    config.load_config('/Users/<USER>/batdom_vers/case_trans/core/profile.json')
    # os_type=2, dir_node_id=26286, version_id=263
    # case_root_list = [267041, 266681, 266940, 267221, 266780, 266616, 267218, 267036, 267063]
    # case_root_list = [266780, 266616, 267218, 267036, 267063]
    # case_root_list = [267041]

    os_type = 1
    dir_node_id = 68084
    version_id = 492
    space_id = 23
    device_id = 386

    cm = CaseManager()
    trees = cm.load_dir_node_trees(version_id=version_id, dir_node_id=dir_node_id, os_type=os_type)
    case_root_list = [tree['nodeId'] for tree in trees]

    flag = False
    for case_root in case_root_list:
        tree_query_param = {
            "case_root_id": case_root,
            "os_type": os_type,
            "dir_node_id": dir_node_id,
            "version_id": version_id
        }
        ctu = CaseTreeUpgrader(space_id=space_id, tree_query_param=tree_query_param, device_id=device_id)  # 215676
        # ctu = CaseTreeUpgrader(root_id=215676, os="ios", device_id=386)
        if not flag:
            ctu.upgrade_all_template()
            flag = True
        ctu.upgrade_all()
