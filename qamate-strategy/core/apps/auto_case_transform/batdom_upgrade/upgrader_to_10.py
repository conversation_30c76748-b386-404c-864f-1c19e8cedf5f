"""
任意版本步骤升级到batdom11（type=10）
"""

from apps.auto_case_transform.batdom_upgrade.upgrader import Upgrader
from apps.auto_case_transform.batdom_upgrade.upgraders.upgrader_9_10 import Upgrader9To10
from basics.util import logger


class UpgraderTo10(Upgrader):
    """
    任意版本步骤升级到batdom11（type=10）
    """
    def __init__(self, old_action_info, old_page_source=None, new_img_url=None, new_page_source=None):
        self.need_upgrade = True
        if old_action_info.get('type', -1) == 10:
            logger.info("已经是最新版本用例，无需升级")
            self.need_upgrade = False
            self.old_action_info = old_action_info
            return
        super().__init__(old_action_info, old_page_source, new_img_url, new_page_source)
    def upgrade(self):
        """
        升级
        """
        if self.need_upgrade is False:
            return self.old_action_info
        new_step_info = super().upgrade()
        return Upgrader9To10(new_step_info).upgrade()