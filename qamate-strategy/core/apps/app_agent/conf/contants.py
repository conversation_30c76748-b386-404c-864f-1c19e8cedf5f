#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@File    : contants.py
<AUTHOR> <PERSON><PERSON><PERSON><PERSON>(renyan<PERSON>@baidu.com)
@Time    : 2024/8/6 15:08
@Desc    : 
"""
# wenxin
WENXIN_AK = "MsgrqIRdLre0j0s0EB7P77uH"
WENXIN_SK = "WCqX45S4TsBzVLOegbmgnOfB8jv52Tmz"

# 千帆API
QIANFAN_API = "bce-v3/ALTAK-iC1khuStayW18uWflStL1/05f65a6944e26ccd50093832a62c5dbc2acb393a"

# EP QIANFAN
QIANFAN_AGENT_BU = "ep"
QIANFAN_AGENT_TOKEN = "b1e16ra3-e11e-g42c-guan-2e9da723cnv5"

# qwen
QWEN_API = "sk-e743057c3c334c8895d2b6068e052d1b"

# zhipu
ZHIPU_API = "59fda096d0f0d88c356c5c5e9f71a215.e5DQmamTeLdikiPV"

# GPT-用例生成
GENERATE_API_KEY = ("************************************************************************************************"
                    "JXDq6E76V4H-UZZuG1d7nat-7idQbZE5Evueh6nb1fYHgP_PFFwJHm2-r-itdLCI0e0A")

# GPT-用例修复
REPAIR_API_KEY = ("***************************************************************************************************"
                  "lMQ0G412TY9qwRb4dfhpbJG5WzS7Q1NMwj6H3w7rWF3zjTxrw4ApFI1WjQeVsRcAA")

# GPT-默认
DEFAULT_API_KEY = ("*********************************************************************************************"
                   "XmupjVng9pcaAqQx_gQHxXe0ve5RglxxniMR1QA")
OPENAI_ORGANIZATION_ID = "org-dQ2ixj8J6OAOAv179aiVAzPC"
