#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@File    : check.py
<AUTHOR> <PERSON><PERSON><PERSON><PERSON>(renyan<PERSON>@baidu.com)
@Time    : 2024/8/13 14:45
@Desc    : 
"""

import json

from apps.app_agent.utils.wenxin_util import WenXinUtils
from apps.app_agent.utils.pandaGPT import image_to_base64
from apps.app_agent.old.agent_wenxin.chat_wenxin import *
from basics.util import logger


class CheckerWenxin(object):
    """
    文心checker
    """
    def __init__(self):
        """
        初始化
        """
        self.wx_client = WenXinUtils()
        
    def understand_case(self, mrd_list):
        """
        理解页面
        :param mrd:
        :return:
        """
        prompt = understand_case_prompt(mrd_list)
        llm_anw = self.wx_client.get_llm_result(prompt)

        # 解析结果
        step_str = llm_anw.split("### 测试步骤 ###")[1]
        step_raw_list = step_str.split("\n")
        step_list = [i.strip() for i in step_raw_list if i.strip()]
        return step_list
    
    def format_batdom(self, batdom):
        """
        格式化batdom
        :param batdom:
        :return:
        """
        formated_dom = []
        for dom in batdom["dom"]["children"]:
            if dom["rect"]["y"] <= 150:
                continue
            formated_iterm = {
                "id": dom["debug"]["id"],
                "left": dom["rect"]["x"],
                "top": dom["rect"]["y"],
                "width": dom["rect"]["w"],
                "height": dom["rect"]["h"],
            }
            
            if dom["type"] == "TextArea" or dom["type"] == "Text":
                formated_iterm["type"] = "含有文本的元素"
                formated_iterm["text"] = dom["ext"]["text"]
            elif dom["type"] == "Icon":
                formated_iterm["type"] = "{}图标".format(dom["ext"]["cname"])
            elif dom["type"] == "Component":
                formated_iterm["type"] = "{}组件".format(dom["ext"]["cname"])
                if dom["ext"]["name"] in ["search_box", "comment_box"]:
                    formated_iterm["type"] = "输入框组件"
            else:
                continue
            formated_dom.append(str(formated_iterm))

        return formated_dom

    
    def understand_judge_page(self, case_step, batdom):
        """
        理解&并且判断页面是否可以执行操作
        :param case_step:
        :return:
        """
        # 翻译batdom
        formated_dom = self.format_batdom(batdom)

        prompt = understand_page_content_prompt(case_step, formated_dom)
        logger.info("understand_judge_page prompt:\n{}".format(prompt))
        llm_anw = self.wx_client.get_llm_result(prompt)
        judge_desc = llm_anw.split("### 判断 ###")[1].strip()
        if judge_desc.find("yes") != -1:
            return True
        else:
            return False
    
    def generate_action_desc(self, case_step, batdom, action_history=[]):
        """
        理解页面
        :param case_step:
        :return:
        """
        # 翻译batdom
        formated_dom = self.format_batdom(batdom)

        prompt = generate_step_desc_prompt(case_step, formated_dom, action_history)
        logger.info("generate_action_desc prompt:\n{}".format(prompt))
        llm_anw = self.wx_client.get_llm_result(prompt)

        action_content = llm_anw.split("### 操作 ###")[1].split("###")[0].strip()
        action_desc = llm_anw.split("### 操作描述 ###")[1].split("###")[0].strip()
        return action_content, action_desc
    
    def judge_action_finish(self, case_step, action_history=[]):
        """
        理解页面
        :param case_step:
        :return:
        """
        prompt = judge_step_finished_prompt(case_step, action_history)
        logger.info("judge_action_finish prompt:\n{}".format(prompt))
        llm_anw = self.wx_client.get_llm_result(prompt)

        output_content = llm_anw.split("### 输出 ###")[1].split("###")[0].strip()
        if "yes" in output_content:
            return True
        else:
            return False
        
    def trans_llm_action_func(self, func_str):
        """
        翻译llm结果
        :param llm_anw:
        :return:
        """
        func_dict = {
            "action": None
        }

        # 点击
        if func_str.find("tap") >= 0:
            func_dict["action"] = "tap"
            id_str = func_str.split("tap(")[1].split(")")[0].strip()
            try:
                func_dict["id"] = int(id_str)
            except:
                return False, func_dict
            return True, func_dict
        
        # 输入
        if func_str.find("input") >= 0:
            func_dict["action"] = "input"
            id_str = func_str.split("input(")[1].split(",")[0].strip()
            try:
                func_dict["id"] = int(id_str)
            except:
                return False, func_dict
            text_str = func_str.split("input(")[1].split(",")[1].split(")")[0].strip()
            func_dict["text"] = text_str.replace("'", "").replace('"', "")
            return True, func_dict
        
        # 滑动
        if func_str.find("swipe") >= 0: 
            func_dict["action"] = "swipe"
            direction_str = func_str.split("swipe(")[1].split(")")[0].strip()
            if direction_str not in ["left", "right", "up", "down"]:
                return False, func_dict
            func_dict["direction"] = direction_str
            return True, func_dict
        
        # 长按
        if func_str.find("long_press") >= 0:
            func_dict["action"] = "long_press"
            id_str = func_str.split("long_press(")[1].split(")")[0].strip()
            try:
                func_dict["id"] = int(id_str)
            except:
                return False, func_dict
            return True, func_dict
        
        # 存在
        if func_str.find("assert_exist") >= 0:
            func_dict["action"] = "nope"
            id_str = func_str.split("assert_exist(")[1].split(")")[0].strip()
            try:
                func_dict["id"] = int(id_str)
            except:
                return False, func_dict
            return True, func_dict
        
        logger.error("func_str:{} 未定义".format(func_str))
        return False, func_dict
        
    def generate_action_info(self, execute_step, action_history, raw_img_path, grid_img_path):
        """
        理解页面
        :param case_step:
        :return:
        """
        content = [{
            "type": "text",
            "text": prompt_text
        }]
        base64_image = image_to_base64(img_path)
        img_cont = {
            "type": "image_url",
            "image_url": {
                "url": f"data:image/jpeg;base64,{base64_image}",
                "detail": img_quality
            }
        }
        content.append(img_cont)


        prompt = generate_step_info_prompt(case_step, formated_dom)
        logger.info("generate_action_info prompt:\n{}".format(prompt))
        llm_anw = self.wx_client.get_llm_result(prompt)
        func_str = llm_anw.split("### 具体操作函数 ###")[1].strip()
        trans_flag, func_dict = self.trans_llm_action_func(func_str)
        return trans_flag, func_dict

if __name__ == "__main__":
    checker = CheckerWenxin()
    checker.understand_case("")
