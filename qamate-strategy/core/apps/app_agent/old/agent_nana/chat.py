#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@File    : prompt.py
<AUTHOR> <PERSON><PERSON><PERSON><PERSON>(renya<PERSON><PERSON>@baidu.com)
@Time    : 2024/8/9 15:48
@Desc    : 
"""


def init_app_tester_chat():
    """

    :return:
    """
    chat_list = []
    system_prompt = "你是一个具有App自动化测试经验的AI手机操作助手，你需要帮我操作手机并且完成页面的功能点验证"
    chat_list.append(["system", [{"type": "text", "text": system_prompt}]])
    return chat_list


def get_action_and_check_prompt(instruction, summary, dom_desc, error_flag=False):
    """

    :param instruction:
    :param summary:
    :param dom_desc:
    :param error_flag:
    :return:
    """

    if not error_flag:
        reflect_str = ""
    else:
        reflect_str = "## 上次操作反思 ##\n你的上一步操作并没有达到你的操作目的，你需要反思并在这次操作中进行修正"

    if dom_desc:
        dom_desc_str = "为了帮助你更好地理解这张截图的内容，我们通过其他能力得到每个框选元素的补充信息，这些信息id与标签数字是对应的，type表示元素类型，desc是元素的描述\n"
        dom_desc_str += dom_desc
        dom_desc_str += "\n请注意，这些信息不一定准确。你需要结合截图来理解。"
    else:
        dom_desc_str = ""

    prompt = f"""
## 背景 ##
当前有一份人工编写的手工测试用例，我需要将此手工测试用例转为自动化测试用例。
你需要根据本份测试用例帮助我操作手机来到达特定的页面完成测试过程并完成测试，测试用例为：{instruction}。
原始的测试用例是脑图格式，我按层次结构使用'->'链接，原始用例中存在部分与自动化执行无关的汇总性记录或表达，忽略这部分内容，目标是达到最终的测试目的。

## 截图信息 ##
本轮对话的图片是最新的手机截图。截图中的交互式UI元素都用数字标签标记，标签从0开始，每个交互元素用红色框框选出，数字标签都位于元素的左上角。
{dom_desc_str}

{reflect_str}

## 操作函数 ##
1. tap(element: int)
这个函数用于点击智能手机屏幕上显示的UI元素。
"element" 是分配给智能手机屏幕上显示的UI元素的数字标签；如果想要点击的UI元素在页面中存在，但是没有分配标签，则element=-1；如果要点击的UI元素在页面中不存在，则element=-2。
例如：tap(5)，表示点击标有数字5的UI元素。

2. input(element: int, text_input: str)
这个函数用于在输入字段/框中插入文本输入。
"element" 同上。
"text_input" 是你想插入的字符串，必须用双引号括起来。
例如：input(10, "Hello, world!")，表示在10元素上输入"Hello, world"。通常对搜索框、输入框等元素才使用此函数。

3. swipe(direction: str)
这个函数用于滑动智能手机屏幕上显示的UI元素，通常是滚动视图或滑动条。默认为滑动半个屏幕的距离。
"direction" 是表示四个方向之一的字符串：up, down, left, right。"direction" 必须用双引号括起来。
例如：swipe("up")，表示将页面向上滑动。

4. long_press(element: int)
这个函数用于长按智能手机屏幕上显示的UI元素。
"element" 同上。
例如：long_press(8), 表示长按标有数字8的UI元素。

5. finsh()
如果你认为已经到达需求描述待测试，不需要进一步操作，你可以选择此操作来终止操作过程。

## 验证函数 ##
1. assert_exist(element: int, content: str)
根据测试用例，需要在本页面需要校验『存在』的元素。
"element" 是分配给智能手机屏幕上显示的UI元素的数字标签；如果想要点击的UI元素在页面中存在，但是没有分配标签，则element=-1；如果要点击的UI元素在页面中不存在，则element=-2。
"content"是校验点内容。如果当前页面不存在校验点，则 content = "无"。
例如：assert_exist(4, "存在点赞按钮")，需要验证当前页面需要元素4存在; assert_exist(-1, "无")，本页面暂无需要验证的校验点。
能够验证多个校验点，则分多行输出。

2. assert_not_exist(element: int, content: str)
根据测试用例，需要在下一个页面校验『不存在』的元素。
"element" 同上。
"content"是校验点内容。如果在下一个页面没有校验不存在的校验点，则 content = "无"。
例如：assert_not_exist(5, "弹窗消失")，需要校验下一个页面不应该存在弹窗; assert_not_exist(-1, "无")，下一个页面没有校验不存在的校验点。
能够验证多个校验点，则分多行输出。

## 任务 ##
现在，根据我给出的信息，你需要思考并调用完成任务所需的函数。你过去执行此任务的操作总结为：{summary}

## 输出要求 ##
你的输出固定的包括以下六个部分，格式如下：
### 思考 ###
思考在之前操作中已经完成的要求以及在下一次操作中需要完成的要求。
### 行动 ###
调用具有正确参数的函数以继续任务，行动只能从上述『操作函数』中选择一个，不能输出验证函数内容。直接给出函数及参数，不要包含其他内容。
### 操作描述 ###
请根据你的思考生成一个简短的自然语言描述，用于解释操作中的动作。对行动的内容，生成一个简短的自然语言描述
### 校验 ###
调用需要校验的函数保障测试，校验只能从上述『验证函数』选择一个，不能输出操作函数内容。直接给出函数及参数，不要包含其他内容。
### 总结 ###
将你过去的行动和你最新的行动总结成一两句话。不要在总结中包含数字标签。
### 对应测试用例 ###
上述操作及验证是对应到[测试用例]哪个节点，只能输出一个节点。例如原始输出的测试用例为 A -> B -> C, 上述操作是针对完成 B 描述的，则输出 B。

可能需要交互很多次才能达成最终的目标，但是你一次只能采取一个行动，所以请直接调用函数。element的数字使用最新截图的信息。
"""
    return prompt


def get_check_prompt(check_template, exec_desc):
    """
    最终功能校验prompt
    :param check_template: 校验模板
    :param exec_desc: 操作描述
    :return:
    """
    check_prompt = f"""
&& 角色 &&
你是一个具有丰富App自动化测试经验的测试智能助手

&& 背景 &&
当前有一份自动化测试用例的执行数据，我将执行过程中的每一步操作和截图按顺序传给你了，图片中的每一步操作位置都使用红框标注，箭头表示页面滑动操作中手指在设备的滑动轨迹。
这份自动化执行数据，可能覆盖到了校验点，也可能没有覆盖到。

&& 任务 &&
你需要根据手机执行过程中的【操作】和【操作后的跳转结果页面】判断【校验点】是否校验通过。你需要一步步思考。
校验点如下：
{check_template}

&& 操作记录 &&
这些操作记录与截图是一一对应的，操作记录如下：
{exec_desc}

&& 输出格式 &&
每一条校验点都需要固定的输出三个方面信息：内容，结论，原因。格式如下：
校验点1
内容： 输出校验点的内容，与输入的校验点一致
结论： 验证是否通过，只能是 True 表示验证通过，False 表示验证不通过。
原因： 得到此结论的原因是什么。
"""
    return check_prompt


def get_task_decompose_prompt(mrd):
    """
    需求任务拆解
    :param mrd:
    :return:
    """
    decomposer_prompt = f"""
&& 角色 &&
你是一个具有丰富App自动化测试经验的测试智能助手

&& 任务 &&
当前有一份人工编写的手工测试用例，你需要判断该测试用例是否可以转为通过手机设备执行的自动化测试用例，如果可以转化，需要将测试任务分解为交互和检查两个正交的子任务。
原始的测试用例是脑图格式，为了方便你理解，我做了格式转化，按层次结构使用'->'链接，测试用例为：{mrd}

&& 输出格式 &&
你的输出固定的包括以下三部分，格式如下：
### 能否转化 ###
判断是否可以转为通过手机设备执行的自动化测试用例，输出只能是 True 或者 False
### 执行任务 ###
引导设备执行的任务，如果存在多个执行任务，用中文中的"；"分隔
### 校验任务 ###
需要进行校验的任务，如果存在多个执行任务，用中文中的"；"分隔

&& 样例 &&
输入：
# 发送内容为"你好"的评论并点赞
## 检查是否正确显示
## 点赞按钮是否变为红色

输出：
### 能否转化 ###
True
### 执行任务 ###
发送内容为"你好"的评论并点赞
### 校验任务 ###
校验评论发送后是否正确显示；校验点赞后点赞按钮是否变为红色
"""
    return decomposer_prompt


def can_manual_case_2_auto_case(mrd):
    """

    :param mrd:
    :return:
    """
    prompt = f"""
&& 角色 &&
你是一个具有丰富App测试经验的测试智能助手

&& 任务 &&
当前有一份人工编写的手工测试用例，你需要判断该测试用例是否可以转为通过手机设备执行的自动化测试用例。重点关注其自动化执行的可能性，无需关注验证点。
原始的测试用例是脑图格式，为了方便你理解，我做了格式转化，按层次结构使用'->'链接。手工用例为：{mrd}

&& 判断标准 &&
这里给出部分标准参考：
1. 稳定性：经常变化的页面不适合转化，频繁地更改自动化脚本会导致高维护成本。
2. 前置条件：前置条件过多的不适合转化。
3. 数据依赖：测试用例依赖于特定数据或需要动态数据，不适合转化。
4. 输入用例不是正常的是测试用例，不能转化。
5. 需要用到微信等其它APP的测试用例，不能转化。
等等

&& 注意 &&
原始用例中可能存在部分与自动化执行无关的汇总性记录或表达，忽略这部分内容，目标是判断能转为稳定的自动化用例，达到最终的测试目的。
"""
    output = """
&& 输出 &&
输出应该包含两个部分，result 和 reason，以json的格式输出。
{
    "result": bool  // 能否转
    "reason": string  // 原因
}
"""
    return prompt + output


def get_reflect_check(instruction: str, history: list):
    """
    反思过程
    :param instruction:
    :param history:
    :return:
    """
    operation_des = ""
    for idx, item in enumerate(history):
        operation_des += "步骤-{}: [思考：{}；操作动作：{}]\n".format(idx + 1, item['thought'], item['action_dec'])

    prompt = f"""
&& 角色 &&
你是一个具有丰富App自动化测试经验的测试智能助手

&& 背景 &&
当前有一份人工编写的手工测试用例，我当前在操作手机来到达特定的页面完成测试过程，但是不确定操作过程是否正确。
我每进行一步操作都会来询问你，所以我当前的操作不一定是最终步骤。

&& 任务 &&
你需要检查下你最新的操作执行是否正确，来保证我能够进行正常测试。我会提供给你，为了完成测试用例，历史的操作、思考以及操作截图。
截图与历史操作是一一对应的，截图会比操作多一步，最后一张截图 表示 最后一步操作后的页面变化。
截图中的每一步操作位置都使用红框标注，箭头表示页面滑动操作中手指在设备的滑动轨迹。

&& 测试用例 &&
{instruction}

&& 历史操作 &&
为了完成用户指令的要求，你已经执行了一系列操作。这些操作如下：
{operation_des}

&& 响应要求 &&
现在你需要根据历史执行记录和截图判断【最后一步操作】的结果是否符合对“操作思考”的预期？
A: “操作动作”的结果符合我对“操作思考”的预期。
B: “操作动作”的结果导致了错误的页面，我需要返回到前一个页面。
C: “操作动作”未产生任何变化。
"""
    output = """
&& 输出 &&
输出应该包含两个部分，result 和 reason，以json的格式输出。
{
    "result": string  // "A" or "B" or "C"
    "reason": string  // 原因
}
"""
    return prompt + output


def amend_manual_case(mrd):
    """
    修正手工用例
    :param mrd:
    :return:
    """
    prompt = f"""
&& 角色 &&
你是一个具有丰富App自动化测试经验的测试智能助手

&& 任务 &&
当前有一份人工编写的手工测试用例，你需要判断该测试用例是否可以转为通过手机设备执行的自动化测试用例，如果可以转化，给出方便自动化测试里的用例，舍弃掉原始用例中与自动化执行无关的汇总性记录或表达。
原始的测试用例是脑图格式，为了方便你理解，我做了格式转化，按层次结构使用'->'链接，测试用例为：{mrd}

&& 判断标准 &&
这里给出部分标准参考：
1. 前置条件：前置条件过多或过于苛刻的不适合转化。
2. 数据依赖：测试用例依赖于特定数据或需要动态数据，不适合转化。
"""
    output = """
&& 输出要求 &&
仅能输出一条优化后的用例。你的输出固定的包括以下三部分，无需输出其他内容，格式如下：
### 能否转化 ###
判断是否可以转为通过手机设备执行的自动化测试用例，输出只能是 True 或者 False
### 优化后的用例 ###
优化后的测试用例，也通过"->"链接
### 原因 ###
给出具体原因
{
    "can_transform": bool // 一个布尔值，表示是否可以转化
    "optimized_test_cases": string // 优化后的测试用例，也通过"->"链接
    "reason": string // 给出具体原因
}

&& 例子 &&
测试用例：
首页及对话->对话交互优化8条->支持引用功能->通用->引用功能基本使用->测试步骤->用户长按历史回复内容气泡->长按历史回复内容时，弹出菜单面板并包含“引用”选项

输出：
{
    "can_transform": true
    "optimized_test_cases": "首页及对话->用户长按历史回复内容气泡->长按历史回复内容时，弹出菜单面板并包含'引用'选项"
    "reason": "对话交互优化8条->支持引用功能->通用->引用功能基本使用与核心测试步骤、核心校验无关"
}
"""
    return prompt + output