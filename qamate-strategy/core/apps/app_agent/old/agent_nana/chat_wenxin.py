#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@File    : prompt.py
<AUTHOR> <PERSON><PERSON><PERSON><PERSON>(renyan<PERSON>@baidu.com)
@Time    : 2024/8/9 15:48
@Desc    : 
"""
import json


def tran_batdom_content(batdom):
    """

    :param batdom_content:
    :return:
    """
    dom_elements = batdom["dom"]["children"]
    dom_list = []
    for dom in dom_elements:
        dom_info = {}
        if dom["rect"]["y"] <= 150:
            continue
        if dom["type"] == "TextArea" or dom["type"] == "Text":
            dom_info = {
                "type": "文本",
                "text": dom["ext"]["text"],
                "rect": [dom["rect"]["x"], dom["rect"]["y"], dom["rect"]["w"], dom["rect"]["h"]],
                "id": dom["debug"]["id"]
            }
        elif dom["type"] == "Icon":
            dom_info = {
                "type": "{}图标".format(dom["ext"]["cname"]),
                "rect": [dom["rect"]["x"], dom["rect"]["y"], dom["rect"]["w"], dom["rect"]["h"]],
                "id": dom["debug"]["id"]
            }
        elif dom["type"] == "Component":
            dom_info = {
                "type": "{}组件".format(dom["ext"]["name"]),
                "rect": [dom["rect"]["x"], dom["rect"]["y"], dom["rect"]["w"], dom["rect"]["h"]],
                "id": dom["debug"]["id"]
            }
            if dom["ext"]["name"] in ["search_box", "comment_box"]:
                dom_info["type"] = "输入框组件"
        elif dom["type"] == "DFE":
            dom_info = {
                "type": "{}系统组件".format(dom["ext"]["feature"]["system_type"]["data"]),
                "content": dom["ext"]["feature"]["content"]["data"],
                "rect": [dom["rect"]["x"], dom["rect"]["y"], dom["rect"]["w"], dom["rect"]["h"]],
                "id": dom["debug"]["id"]
            }
        else:
            continue

        dom_content = str(dom_info)
        dom_list.append(dom_content)       

    batdom_content = "\n".join(dom_list)
    return batdom_content


def init_app_tester_chat():
    """

    :return:
    """
    chat_list = []
    system_prompt = "你是一个具有App自动化测试经验的AI手机操作助手，你需要帮我操作手机并且完成页面的功能点验证"
    chat_list.append(["system", [{"type": "text", "text": system_prompt}]])
    return chat_list


def get_wenxin_judge_prompt(case_total, case_step):
    """

    :param instruction:
    :param summary:
    :param dom_desc:
    :param error_flag:
    :return:
    """
    prompt = f"""
## 背景 ##
当前有一份人工编写的手工测试用例，测试的对象是一个手机上的APP的功能，需要你最终根据本份手工测试用例帮助我操作手机来到达特定的页面完成测试过程并完成测试。一份人工编写的测试用例有若干个节点组成，每个节点可能会描述一个可以执行的测试步骤、一个可以进行校验的测试步骤、描述一个详细功能的测试步骤，或者只是一个简单的总结归纳性的描述，所以需要你来进行一下判断，当前输入的节点是一个测试步骤、功能描述还是一个总结性的描述。

## 测试用例 ##
现在需要进行判断的完整的测试用例为：
{case_total}

## 测试节点 ##
当前需要你来帮助判断的测试节点为：
{case_step}

## 输出要求 ##
你的输出固定的包括以下2个部分，格式如下：
### 思考 ###
思考在当前节点的是否是一个抽象总结性的描述，给出你的理由
### 判断 ###
如果是一个功能性的描述，则只需要输出『type_1』，如果是一个测试步骤，则只需要输出『type_2』，如果是一个抽象总结性的描述或者其它情况，则只需要输出『type_0』。请注意只需要输出数字即可，不需要输出多余的内容。
"""
    return prompt


def get_wenxin_action_and_check_prompt(case, summary, batdom, error_flag=False):
    """

    :param instruction:
    :param summary:
    :param dom_desc:
    :param error_flag:
    :return:
    """
    batdom_content = tran_batdom_content(batdom)

    prompt = f"""
## 背景 ##
当前有一份人工编写的手工测试用例，测试的对象是一个手机上的APP的相关功能。你需要根据这份手工测试用例帮助我操作手机来到达特定的页面完成测试过程并完成测试。

## 测试用例 ##
现在需要进行测试的完整的测试用例为：
{case}

## 已经执行了的操作 ##
为了测试当前的测试步骤，已经进行了这些操作：
{summary}

## 手机页面的实时信息 ##
这里的输入是根据手机截图进行建模的结果，得到的UI元素信息的格式是json格式的字符串，每行代表一个元素的信息，具体内容如下：
{batdom_content}
以上完成全部元素的描述。

每个json字段的含义是：
id: 元素在页面中的唯一标识，必定存在
type: 元素类型，必定存在
rect: 元素在页面中的位置信息，必定存在，格式为[x, y, width, height]，元素的位置信息是相对于手机屏幕左上角的位置
text: 元素上的文字信息，可能不存在
content: 系统组件的名称或者内容，可能不存在

## 操作函数 ##
你可以在页面上进行这些类型的操作：
1. tap(id: int)
这个函数用于点击智能手机屏幕上显示的UI元素。
"id" 返回需要进行操作元素的id；如果要点击的UI元素在页面中不存在，则id=-2。
例如：tap(5)，表示点击id等于5的UI元素。

2. input(id: int, text_input: str)
这个函数用于在输入字段/框中插入文本输入。
"id" 同上。
"text_input" 是你想插入的字符串，必须用双引号括起来。
例如：input(10, "Hello, world!")，表示在id等于10的元素上输入"Hello, world"。通常对搜索框、输入框等元素才使用此函数。

3. swipe(direction: str)
这个函数用于在智能手机屏幕上进行滑动操作，通常是滚动视图或滑动条。默认为滑动半个屏幕的距离。
"direction" 是表示四个方向之一的字符串：up, down, left, right。"direction" 必须用双引号括起来。其中up表示从下往上滑动，down表示从上往下滑动，left表示从右往左滑动，right表示从左往右滑动。
例如：swipe("up")，表示将页面从下往上滑动。

4. long_press(id: int)
这个函数用于长按智能手机屏幕上显示的UI元素。
"id" 同上。
例如：long_press(8), 表示长按id等于8的UI元素。

## 验证函数 ##
你可以对页面元素做这些类型的校验和验证：
1. assert_exist(id: int, content: str)
根据测试用例，需要在本页面需要校验『存在』的元素。
"id" 返回需要进行验证元素的id；如果没有需要校验的元素，则id=-1，如果要验证的UI元素在页面中不存在，则id=-2。
"content"是校验点内容。如果当前页面不存在校验点，则 content = "无"。
例如：assert_exist(4, "存在点赞按钮")，需要验证当前页面需要元素id等于4存在; assert_exist(-1, "无")，本页面暂无需要验证的校验点。

2. assert_not_exist(id: int, content: str)
根据测试用例，需要在下一个页面校验『不存在』的元素。
"id" 同上。
"content"是校验点内容。如果在下一个页面没有校验不存在的校验点，则 content = "无"。
例如：assert_not_exist(5, "弹窗消失")，需要校验下一个页面不应该存在弹窗; assert_not_exist(-1, "无")，下一个页面没有校验不存在的校验点。

## 任务 ##
如背景中所说，一份测试用例需要进行一系列的操作来完成。现在请你基于测试用例，输出全部的测试过程。由于需要一系列的操作来完成测试，但是每次只能输出最多一个行动和一个验证，所以请结合上述『已经执行了的操作』，给出现在应该进行的行动和验证。并且按照『输出要求』中规定的格式进行输出。同时按照要求完成『总结』中内容，用来给下一次执行进行参考。

## 输出要求 ##
你的输出固定的包括以下5个部分，格式如下：
### 思考 ###
思考在之前操作中已经完成的要求以及在这次操作中需要完成的要求。
### 操作描述 ###
请根据你的思考生成一个简短的自然语言描述，用于解释本次操作的内容。
### 行动 ###
你可以根据需要从上述『操作函数』中选择一个来进行执行本次的操作，并在这个模块进行输出，请不要在这个部分输出验证函数内容。输出格式上，直接给出操作函数及参数即可，请不要包含其他内容。请注意，一定只给出最多一个行动。
### 校验 ###
如果有需要的话，你可以根据需要从上述『验证函数』中选择一个来进行执行本次的校验，并在这个模块进行输出，不能输出操作函数内容。直接给出函数及参数，不要包含其他内容。请注意，一定只给出最多一个校验。
### 总结 ###
将你这次的行动总结成一两句话，不要在总结中包含数字标签，为你后续的操作提供已经执行了哪些步骤的参考。


另外，可能需要交互很多次才能达成最终的目标，但是你一次只能采取一个行动，所以请直接调用函数。id的数字使用最新截图的信息。
"""
    return prompt


def get_wenxin_action_and_check_step_prompt(case_total, case_step, summary, batdom, error_flag=False):
    """

    :param instruction:
    :param summary:
    :param dom_desc:
    :param error_flag:
    :return:
    """
    batdom_content = tran_batdom_content(batdom)

    prompt = f"""
## 背景 ##
当前有一份人工编写的手工测试用例，测试的对象是一个手机上的APP的相关功能。你需要根据这份手工测试用例帮助我操作手机来到达特定的页面完成测试过程并完成测试。其中一份手工测试用例包含多个测试步骤，每个测试步骤又需要进行一系列的操作来完成。

## 测试用例 ##
现在需要进行测试的完整的测试用例为：
{case_total}
以上测试用例完成描述。

## 当前测试步骤 ##
一条手工测试用例往往需要包括多个测试步骤，当前需要你来帮助实现的测试步骤为：
{case_step}

## 已经执行了的操作 ##
为了测试当前的测试步骤，已经进行了这些操作：
{summary}

## 手机页面的实时信息 ##
这里的输入是根据手机截图进行建模的结果，得到的UI元素信息的格式是json格式的字符串，每行代表一个元素的信息，具体内容如下：
{batdom_content}
以上完成全部元素的描述。

每个json字段的含义是：
id: 元素在页面中的唯一标识，必定存在
type: 元素类型，必定存在
rect: 元素在页面中的位置信息，必定存在，格式为[x, y, width, height]，元素的位置信息是相对于手机屏幕左上角的位置
text: 元素上的文字信息，可能不存在
content: 系统组件的名称或者内容，可能不存在

## 操作函数 ##
你可以在页面上进行这些类型的操作：
1. tap(id: int)
这个函数用于点击智能手机屏幕上显示的UI元素。
"id" 返回需要进行操作元素的id；如果要点击的UI元素在页面中不存在，则id=-2。
例如：tap(5)，表示点击id等于5的UI元素。

2. input(id: int, text_input: str)
这个函数用于在输入字段/框中插入文本输入。
"id" 同上。
"text_input" 是你想插入的字符串，必须用双引号括起来。
例如：input(10, "Hello, world!")，表示在id等于10的元素上输入"Hello, world"。通常对搜索框、输入框等元素才使用此函数。

3. swipe(direction: str)
这个函数用于在智能手机屏幕上进行滑动操作，通常是滚动视图或滑动条。默认为滑动半个屏幕的距离。
"direction" 是表示四个方向之一的字符串：up, down, left, right。"direction" 必须用双引号括起来。其中up表示从下往上滑动，down表示从上往下滑动，left表示从右往左滑动，right表示从左往右滑动。
例如：swipe("up")，表示将页面从下往上滑动。

4. long_press(id: int)
这个函数用于长按智能手机屏幕上显示的UI元素。
"id" 同上。
例如：long_press(8), 表示长按id等于8的UI元素。

5. finsh()
如果你认为已经到达需求描述待测试，不需要进一步操作，你可以选择此操作来终止操作过程。

## 验证函数 ##
你可以对页面元素做这些类型的校验和验证：
1. assert_exist(id: int, content: str)
根据测试用例，需要在本页面需要校验『存在』的元素。
"id" 返回需要进行验证元素的id；如果没有需要校验的元素，则id=-1，如果要验证的UI元素在页面中不存在，则id=-2。
"content"是校验点内容。如果当前页面不存在校验点，则 content = "无"。
例如：assert_exist(4, "存在点赞按钮")，需要验证当前页面需要元素id等于4存在; assert_exist(-1, "无")，本页面暂无需要验证的校验点。
能够验证多个校验点，则分多行输出。

2. assert_not_exist(id: int, content: str)
根据测试用例，需要在下一个页面校验『不存在』的元素。
"id" 同上。
"content"是校验点内容。如果在下一个页面没有校验不存在的校验点，则 content = "无"。
例如：assert_not_exist(5, "弹窗消失")，需要校验下一个页面不应该存在弹窗; assert_not_exist(-1, "无")，下一个页面没有校验不存在的校验点。
能够验证多个校验点，则分多行输出。

## 任务 ##
如背景中所说，一份测试用例包含多个测试步骤，每个测试步骤又需要进行一系列的操作来完成。现在请你结合完整的测试用例作为参考，输出如何完成当前测试步骤的全部测试过程。由于每个测试步骤，需要一系列的操作来执行，但是每次只能输出最多一个行动和一个验证，所以请结合上述『已经执行了的操作』，给出现在应该进行的行动和验证。并且按照『输出要求』中规定的格式进行输出。同时按照要求完成『总结』中内容，用来给下一次执行进行参考。

## 输出要求 ##
你的输出固定的包括以下5个部分，格式如下：
### 思考 ###
思考在之前操作中已经完成的要求以及在这次操作中需要完成的要求。
### 操作描述 ###
请根据你的思考生成一个简短的自然语言描述，用于解释本次操作的内容。
### 行动 ###
你可以根据需要从上述『操作函数』中选择一个来进行执行本次的操作，并在这个模块进行输出，请不要在这个部分输出验证函数内容。输出格式上，直接给出操作函数及参数即可，请不要包含其他内容。请注意，一定只给出最多一个行动。
### 校验 ###
如果有需要的话，你可以根据需要从上述『验证函数』中选择一个来进行执行本次的校验，并在这个模块进行输出，不能输出操作函数内容。直接给出函数及参数，不要包含其他内容。请注意，一定只给出最多一个校验。
### 总结 ###
将你这次的行动总结成一两句话，不要在总结中包含数字标签，为你后续的操作提供已经执行了哪些步骤的参考。


另外，可能需要交互很多次才能达成最终的目标，但是你一次只能采取一个行动，所以请直接调用函数。id的数字使用最新截图的信息。
"""
    return prompt


def get_wenxin_judge_finish_prompt(case_total, case_step, summary):
    """

    :param instruction:
    :param summary:
    :param dom_desc:
    :param error_flag:
    :return:
    """
    prompt = f"""
## 背景 ##
当前有一份人工编写的手工测试用例，测试的对象是一个手机上的APP的功能，测试用例包含多个测试步骤，每个测试步骤又需要进行一系列的操作来完成。

## 测试用例 ##
现在需要进行测试的完整的测试用例为：
{case_total}
以上测试用例完成描述。

## 当前测试步骤 ##
一条手工测试用例往往需要包括多个测试步骤，当前正在进行执行的测试步骤为：
{case_step}

## 已经执行了的操作 ##
为了测试当前的测试步骤，已经进行了这些操作：
{summary}

## 任务 ##
现在需要做一个判断，根据『已经执行了的操作』中的列出的操作列表，判断这些操作是否已经完成了对『当前测试步骤』的验证，可以对『测试用例』中的下一个步骤进行操作了。

## 输出要求 ##
你的输出固定的包括以下2个部分，格式如下：
### 思考 ###
思考『已经执行了的操作』中的列出的操作列表是否已经完成了对『当前测试步骤』的验证
### 结论 ###
如果你判断『已经执行了的操作』中的列出的操作列表已经完成了对『当前测试步骤』的验证，请输出『yes』，否则请输出『no』。
"""
    return prompt


def get_check_prompt(check_template, exec_desc):
    """
    最终功能校验prompt
    :param check_template: 校验模板
    :param exec_desc: 操作描述
    :return:
    """
    check_prompt = f"""
&& 角色 &&
你是一个具有丰富App自动化测试经验的测试智能助手

&& 背景 &&
当前有一份自动化测试用例的执行数据，我将执行过程中的每一步操作和截图按顺序传给你了，图片中的每一步操作位置都使用红框标注，箭头表示页面滑动操作中手指在设备的滑动轨迹。
这份自动化执行数据，可能覆盖到了校验点，也可能没有覆盖到。

&& 任务 &&
你需要根据手机执行过程中的【操作】和【操作后的跳转结果页面】判断【校验点】是否校验通过。你需要一步步思考。
校验点如下：
{check_template}

&& 操作记录 &&
这些操作记录与截图是一一对应的，操作记录如下：
{exec_desc}

&& 输出格式 &&
每一条校验点都需要固定的输出三个方面信息：内容，结论，原因。格式如下：
校验点1
内容： 输出校验点的内容，与输入的校验点一致
结论： 验证是否通过，只能是 True 表示验证通过，False 表示验证不通过。
原因： 得到此结论的原因是什么。
"""
    return check_prompt


def get_task_decompose_prompt(mrd):
    """
    需求任务拆解
    :param mrd:
    :return:
    """
    decomposer_prompt = f"""
&& 角色 &&
你是一个具有丰富App自动化测试经验的测试智能助手

&& 任务 &&
当前有一份人工编写的测试用例，你需要判断该测试用例是否可以转为通过手机设备执行的自动化测试用例，如果可以转化，需要将测试任务分解为交互和检查两个正交的子任务。
原始人工编写的测试用例是脑图格式，为了方便理解，我将它转了为markdown格式，测试用例为：{mrd}

&& 输出格式 &&
你的输出固定的包括以下三部分，格式如下：
### 能否转化 ###
判断是否可以转为通过手机设备执行的自动化测试用例，输出只能是 True 或者 False
### 执行任务 ###
引导设备执行的任务，如果存在多个执行任务，用中文中的"；"分隔
### 校验任务 ###
需要进行校验的任务，如果存在多个执行任务，用中文中的"；"分隔

&& 样例 &&
输入：
# 发送内容为"你好"的评论并点赞
## 检查是否正确显示
## 点赞按钮是否变为红色

输出：
### 能否转化 ###
True
### 执行任务 ###
发送内容为"你好"的评论并点赞
### 校验任务 ###
校验评论发送后是否正确显示；校验点赞后点赞按钮是否变为红色
"""
    return decomposer_prompt


def can_manual_case_2_auto_case(mrd):
    """

    :param mrd:
    :return:
    """
    prompt = f"""
&& 角色 &&
你是一个具有丰富App测试经验的测试智能助手

&& 任务 &&
当前有一份人工编写的测试用例，你需要判断该测试用例是否可以转为通过手机设备执行的自动化测试用例。重点关注其自动化执行的可能性，无需关注验证点。
原始的测试用例是脑图格式，为了方便你理解，我做了格式转化，按层次结构使用'->'链接。手工用例为：{mrd}

&& 判断标准 &&
这里给出部分标准参考：
1. 稳定性：经常变化的页面不适合转化，频繁地更改自动化脚本会导致高维护成本。
2. 前置条件：前置条件过多的不适合转化。
3. 数据依赖：测试用例依赖于特定数据或需要动态数据，不适合转化。
4. 输入用例不是正常的是测试用例，不能转化。
等等

&& 注意 &&
原始用例中可能存在部分与自动化执行无关的汇总性记录或表达，忽略这部分内容，目标是判断能转为稳定的自动化用例，达到最终的测试目的。
"""
    output = """
&& 输出 &&
输出应该包含两个部分，result 和 reason，以json的格式输出。
{
    "result": bool  // 能否转
    "reason": string  // 原因
}
"""
    return prompt + output


def get_reflect_check(instruction: str, history: list):
    """
    反思过程
    :param instruction:
    :param history:
    :return:
    """
    operation_des = ""
    for idx, item in enumerate(history):
        operation_des += "步骤-{}: [思考：{}；操作动作：{}]\n".format(idx + 1, item['thought'], item['action_dec'])

    prompt = f"""
&& 角色 &&
你是一个具有丰富App自动化测试经验的测试智能助手

&& 背景 &&
当前有一份人工编写的测试用例，我当前在操作手机来到达特定的页面完成测试过程，但是不确定操作过程是否正确。
我每进行一步操作都会来询问你，所以我当前的操作不一定是最终步骤。

&& 任务 &&
你需要检查下你最新的操作执行是否正确，来保证我能够进行正常测试。我会提供给你，为了完成测试用例，历史的操作、思考以及操作截图。
截图与历史操作是一一对应的，截图会比操作多一步，最后一张截图 表示 最后一步操作后的页面变化。
截图中的每一步操作位置都使用红框标注，箭头表示页面滑动操作中手指在设备的滑动轨迹。

&& 测试用例 &&
{instruction}

&& 历史操作 &&
为了完成用户指令的要求，你已经执行了一系列操作。这些操作如下：
{operation_des}

&& 响应要求 &&
现在你需要根据历史执行记录和截图判断【最后一步操作】的结果是否符合对“操作思考”的预期？
A: “操作动作”的结果符合我对“操作思考”的预期。
B: “操作动作”的结果导致了错误的页面，我需要返回到前一个页面。
C: “操作动作”未产生任何变化。
"""
    output = """
&& 输出 &&
输出应该包含两个部分，result 和 reason，以json的格式输出。
{
    "result": string  // "A" or "B" or "C"
    "reason": string  // 原因
}
"""
    return prompt + output


def understand_case_prompt(mrd_list, executed_step=[]):
    """

    :param instruction:
    :param summary:
    :param dom_desc:
    :param error_flag:
    :return:
    """

    mrd_str = "\n".join(mrd_list)
    executed_str = "无" if len(executed_step) == 0 else "\n".join(executed_step)
    prompt = f"""
&& 角色 &&
你是一个具有丰富App测试经验的测试智能助手

&& 任务 &&
当前有一份人工编写的手工测试用例，请理解这份用例，并且拆分成多个可以执行的测试步骤。注意，这份测试用例当中可能存在部分与测试执行无关的汇总性记录或表达，忽略这部分内容。

&& 测试用例 &&
手工测试用例如下，每行为一个用例的节点：
{mrd_str}

&& 已经完成执行的步骤 &&
这些步骤已经完成了执行：
{executed_str}

&& 输出要求 &&
你的输出固定的包括以下2个部分，格式如下：
### 思考 ###
通过理解这份手工测试用例和已经完成执行了的步骤，思考如何继续将其分解成多个可以执行的测试步骤，请给出思考过程。
### 测试步骤 ###
输出分解后的测试步骤，如果存在多个测试步骤，请通过换行的方式输出，每行输出一个测试步骤。在个部分中请只要输出步骤即可，不需要输出其它总结归纳信息或者思考过程。
"""
    return prompt


def understand_page_content_prompt(case_step, dom_list):
    """

    :param instruction:
    :param summary:
    :param dom_desc:
    :param error_flag:
    :return:
    """
    page_info = "\n".join(dom_list)
    prompt = f"""
&& 角色 &&
你是一个具有丰富App测试经验的测试智能助手

&& 任务 &&
现在会给你输入一个测试步骤和一个手机上的APP页面信息，需要你判断下，当前测试步骤，是否可以在这个页面上进行执行。

&& 测试步骤 &&
{case_step}

&& 页面信息的输入方式 &&
当前手机的页面内容的输入方式，主要是给出识别到的每个页面元素的类型、位置、内容、宽高等信息，每行给出一个元素的相关信息，格式为json格式，每个字段的含义如下：
id: 元素在页面中的唯一标识，必定存在
type: 元素的类型，必定存在
left: 元素离页面中的左边界的距离，必定存在
top: 元素离页面中的上边界的距离，必定存在
width: 元素在页面中的宽度，必定存在
height: 元素在页面中的高度，必定存在
text: 元素中包含的文字信息，可能不存在

&& 页面信息 &&
以下是按照『页面信息的输入方式』中描述的json格式给出的页面信息，每行给出一个元素的相关信息：
{page_info}

你的输出固定的包括以下3个部分，格式如下：
### 页面理解 ###
根据页面信息，请你先理解下这个页面包含哪些内容和功能，并给出你的理解结果。
### 思考 ###
基于对页面的理解和输入的测试步骤，思考下是否可以在当前页面上执行测试步骤中需要进行的操作，并给出你的思考结果。
### 判断 ###
通过思考，请你给出判断，是否可以在当前页面上执行测试步骤中需要进行的操作，如果你判断可以，请输出"yes"，否则请输出"no"。
"""
    return prompt

def generate_step_desc_prompt(case_step, dom_list, history_list=[]):
    """

    :param instruction:
    :param summary:
    :param dom_desc:
    :param error_flag:
    :return:
    """
    page_info = "\n".join(dom_list)
    history_action = "暂无历史操作"
    if len(history_list) > 0:
        history_action = "\n".join(history_list)

    prompt = f"""
&& 角色 &&
你是一个具有丰富App测试经验的测试智能助手

&& 任务 &&
现在会给你输入一个测试步骤、一个手机上的APP页面信息，以及为了完成这个测试步骤，已经在这个APP上执行过的操作。需要你根据这些信息给出下一步的操作。

&& 测试步骤 &&
{case_step}

&& 页面信息的输入方式 &&
当前手机的页面内容的输入方式，主要是给出识别到的每个页面元素的类型、位置、内容、宽高等信息，每行给出一个元素的相关信息，格式为json格式，每个字段的含义如下：
id: 元素在页面中的唯一标识，必定存在
type: 元素的类型，必定存在
left: 元素离页面中的左边界的距离，必定存在
top: 元素离页面中的上边界的距离，必定存在
width: 元素在页面中的宽度，必定存在
height: 元素在页面中的高度，必定存在
text: 元素中包含的文字信息，可能不存在

&& 页面信息 &&
以下是按照『页面信息的输入方式』中描述的json格式给出的页面信息，每行给出一个元素的相关信息：
{page_info}

&& 历史操作 &&
以下是为了完成测试步骤，已经在这个APP上执行过的操作，每行一个操作：
{history_action}

&& 操作类型 &&
可以用来选择饿的操作类型包括以下6种，每行一个，包括操作名称和操作内容描述：
点击操作：点击某个页面元素
输入操作：在某个页面元素中输入特定内容
滑动操作：在页面中进行滑动
长按操作：在某个页面元素上长按
存在验证操作：判断某个页面元素存在
不存在验证操作：判断某个页面元素不存在

你的输出固定的包括以下4个部分，格式如下：
### 页面理解 ###
根据页面信息，请你先理解下这个页面包含哪些内容和功能，并给出你的理解结果。
### 思考 ###
基于对页面的理解、输入的测试步骤和已经完成执行的历史操作，思考下一步的操作，并给出你的思考结果。注意，只需要下一步，不需要全部的操作。
### 操作 ###
通过思考，请你给出下一步的操作内容，注意，只需要下一步，不需要全部的操作。以及请直接输出操作内容即可。
### 操作描述 ###
请你描述下你给出的操作，用来记录为下一次交互的时候『历史操作』中的参考信息。
"""
    return prompt

def judge_step_finished_prompt(case_step, history_list):
    """

    :param instruction:
    :param summary:
    :param dom_desc:
    :param error_flag:
    :return:
    """
    history_action = "暂无历史操作"
    if len(history_list) > 0:
        history_action = "\n".join(history_list)

    prompt = f"""
&& 角色 &&
你是一个具有丰富App测试经验的测试智能助手

&& 任务 &&
现在会给你输入一个测试步骤，以及为了完成这个测试步骤，已经在这个APP上执行过的操作。需要你根据这些信息判断，当前的操作是否已经执行完了这个测试步骤。

&& 测试步骤 &&
{case_step}

&& 已完成的操作 &&
以下是为了完成测试步骤，已经在这个APP上执行过的操作，每行一个操作：
{history_action}

&& 操作类型 &&
可以用来选择饿的操作类型包括以下5种，每行一个，包括操作名称和操作内容描述：
点击操作：点击某个页面元素
输入操作：在某个页面元素中输入特定内容
滑动操作：在页面中进行滑动
长按操作：在某个页面元素上长按
存在验证操作：判断某个页面元素存在

你的输出固定的包括以下2个部分，格式如下：
### 思考 ###
基于对测试步骤的理解和已经完成执行的历史操作，思考是否已经完成了这个测试步骤的执行。
### 输出 ###
通过思考，给出你的判断，如果已经完成了这个测试步骤的执行，请只输出"yes"，否则请只输出"no"。
"""
    return prompt


def generate_step_info_prompt(step_action, dom_list):
    """

    :param instruction:
    :param summary:
    :param dom_desc:
    :param error_flag:
    :return:
    """
    page_info = "\n".join(dom_list)

    prompt = f"""
&& 角色 &&
你是一个具有丰富App测试经验的测试智能助手

&& 任务 &&
现在会给你输入一个具体的操作内容、一个手机上的APP页面信息和一系列具体的操作函数。需要你根据操作内容和页面信息，从一系列操作函数中，选择出对应的操作函数。并从页面信息中，得到这个操作函数需要的参数信息。

&& 操作内容 &&
{step_action}

&& 页面信息的输入方式 &&
当前手机的页面内容的输入方式，主要是给出识别到的每个页面元素的类型、位置、内容、宽高等信息，每行给出一个元素的相关信息，格式为json格式，每个字段的含义如下：
id: 元素在页面中的唯一标识，必定存在
type: 元素的类型，必定存在
left: 元素离页面中的左边界的距离，必定存在
top: 元素离页面中的上边界的距离，必定存在
width: 元素在页面中的宽度，必定存在
height: 元素在页面中的高度，必定存在
text: 元素中包含的文字信息，可能不存在

&& 页面信息 &&
以下是按照『页面信息的输入方式』中描述的json格式给出的页面信息，每行给出一个元素的相关信息：
{page_info}

&& 操作函数 &&
1. tap(id: int)
用法：这个函数用于执行点击操作，具体作用是点击手机屏幕上显示的指定元素。
参数说明："id" 表示需要进行点击操作元素的id。例如：tap(5)，表示点击id等于5的页面元素。

2. input(id: int, text_input: str)
用法：这个函数用于执行输入操作，具体作用是在目标页面元素中输入指定的文本。
参数说明："id" 表示需要进行输入文本的元素的id。"text_input" 是你想插入的字符串，必须用双引号括起来。例如：input(10, "Hello, world!")，表示在id等于10的元素中输入"Hello, world"。

3. swipe(direction: str)
用法：这个函数用于在手机屏幕上执行滑动操作。
参数说明："direction" 是个枚举类型的字符串，用于表示滑动方向：up, down, left, right。"direction" 必须用双引号括起来。其中up表示从下往上滑动，down表示从上往下滑动，left表示从右往左滑动，right表示从左往右滑动。例如：swipe("up")，表示将页面从下往上滑动。

4. long_press(id: int)
用法：这个函数用于执行长按操作，具体作用是长按手机屏幕上显示的指定元素。
参数说明："id" 表示需要进行长按操作元素的id。例如：long_press(8), 表示长按id等于8的页面元素。

5. assert_exist(id: int)
用法：这个函数用于执行存在验证操作，具体作用是判断指定的页面元素是否在当前页面中存在。
参数说明："id" 表示需要进行验证存在的元素的id。例如：assert_exist(4)，需要验证当前页面需要元素id等于4存在。

你的输出固定的包括以下4个部分，格式如下：
### 页面理解 ###
根据页面信息，请你先理解下这个页面包含哪些内容和功能，并给出你的理解结果。
### 思考 ###
基于对页面的理解、输入的操作内容和操作函数列表，思考如何从操作函数列表中，选择出对应的操作函数；如何从页面信息中，得到这个操作函数需要的参数信息。请给出思考过程。
### 具体操作函数 ###
通过思考，请你给出具体的操作函数以及对应的参数，注意，一次只能给出一个操作函数。以及请直接输出带有具体参数的操作函数即可。
"""
    return prompt


if __name__ == '__main__':
    pass