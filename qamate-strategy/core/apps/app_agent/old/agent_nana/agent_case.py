#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@Desc    :   和gpt/文心等大模型交互的封装
@File    :   agent_case.py
@Time    :   2024-09-05
<AUTHOR>   x<PERSON><PERSON><PERSON>@baidu.com
"""

class AgentCase(object):
    """
    和gpt/文心等大模型交互的封装
    """
    def __init__(self):
        """
        初始化部分
        """
        pass

    def run(self, text: str) -> str:
        """
        执行部分
        """
        return "hello world"

if __name__ == "__main__":
    agent = AgentCase()
    print(agent.run("你好"))
