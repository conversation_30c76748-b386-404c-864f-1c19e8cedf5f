#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@File    : image_process.py
<AUTHOR> <PERSON><PERSON><PERSON><PERSON>(<EMAIL>)
@Time    : 2024/8/9 10:40
@Desc    : 
"""
import json

import cv2
import numpy as np

from apps.auto_case_transform.utils.utils import batdom_record_by_path_new
from apps.app_agent.utils.draw import draw_rect_with_index
from basics.util import logger
from basics import config

config.load_config('/Users/<USER>/Workspace/Project/lazyOne/qamate-strategy/core/profile.json')
# config.load_config('/Users/<USER>/baidu/qamate-strategy/core/profile.json')


def get_image_with_grid(image_path, grid_path):
    """

    :param image_path:
    :param grid_path:  # 存储地址
    :return:
    """

    dom_info, dom = batdom_record_by_path_new(image_path)
    ele_list = [item['rect'] for item in dom['children']]

    imgcv = cv2.imread(image_path)
    grid_image = draw_rect_with_index(imgcv, ele_list, output_path=grid_path)

    ele_desc = "[\n"
    for idx, ele in enumerate(dom['children']):
        t, d = dom_ele_2_desc(ele)
        ele_desc += "id:{}, type: {}, desc: {}\n".format(idx, t, d)
    ele_desc += "]"

    return dom_info, dom, ele_desc


def restructure_image_with_batdom(image_path, grid_path):
    """
    切割图片，并且完成重组
    """
    dom_info, dom = batdom_record_by_path_new(image_path)
    ele_list = [item['rect'] for item in dom['children']]
    imgcv = cv2.imread(image_path)

    # 字体设置
    font = cv2.FONT_HERSHEY_SIMPLEX
    font_scale = 1
    font_color = (0, 0, 0)  # 黑色字体
    thickness = 2
    separator_thickness = 5
    index_width = 50
    vertical_padding = 10
    spacing_between_index_and_image = 5

    # 存储切割的子图片，添加序号和间隔
    sub_images = []
    for idx, element in enumerate(ele_list):
        # 每个 element 是一个字典，包含位置 (x, y) 和尺寸 (width, height)
        x, y, width, height = element['x'], element['y'], element['w'], element['h']

        # 从原始图片中切割出子图片
        sub_image = imgcv[y: y + height, x: x + width]

        # 创建一个新的图片区域用于显示序号，设置为白色背景
        index_image = np.ones((sub_image.shape[0], index_width, 3), dtype=np.uint8) * 255

        # 在序号区域上写上序号
        text = str(idx + 1)  # 序号从1开始
        text_size = cv2.getTextSize(text, font, font_scale, thickness)[0]
        text_x = (index_image.shape[1] - text_size[0]) // 2
        text_y = (index_image.shape[0] + text_size[1]) // 2
        cv2.putText(index_image, text, (text_x, text_y), font, font_scale, font_color, thickness)

        # 创建一个黑色的竖线，作为序号和图片之间的间隔
        vertical_separator = np.zeros((sub_image.shape[0], spacing_between_index_and_image, 3), dtype=np.uint8)

        # 将序号图片、竖线和子图片横向拼接
        sub_image_with_index = np.hstack((index_image, vertical_separator, sub_image))

        # 在子图片上下添加白色的空白区域
        sub_image_with_padding = np.pad(
            sub_image_with_index,
            ((vertical_padding, vertical_padding), (0, 0), (0, 0)),
            mode='constant', constant_values=255
        )

        # 将结果存储
        sub_images.append(sub_image_with_padding)

    # 计算新图片的宽度（子图片与序号区域的最大宽度）和总高度（所有子图片高度的总和加上分隔线）
    max_width = max(sub_image.shape[1] for sub_image in sub_images)
    total_height = sum(sub_image.shape[0] for sub_image in sub_images) + (separator_thickness * (len(sub_images) - 1))

    # 创建一个空白的（白色背景）新图片，尺寸为最大宽度和总高度
    new_image = np.ones((total_height, max_width, 3), dtype=np.uint8) * 255

    # 将子图片逐行粘贴到新图片上，并添加黑色分隔线
    current_height = 0
    for i, sub_image in enumerate(sub_images):
        # 粘贴子图片
        new_image[current_height:current_height + sub_image.shape[0], 0:sub_image.shape[1]] = sub_image
        current_height += sub_image.shape[0]

        # 如果不是最后一行，则添加黑色分隔线
        if i < len(sub_images) - 1:
            new_image[current_height:current_height + separator_thickness, :] = (0, 0, 0)  # 黑色分隔线
            current_height += separator_thickness

    # 保存生成的图片
    cv2.imwrite(grid_path, new_image)
    print(f"New image saved as {grid_path}")
    print(f"New image size is {new_image.shape[0]}*{new_image.shape[1]}")
    print(f"Raw image size is {imgcv.shape[0]}*{imgcv.shape[1]}")


def dom_ele_2_desc(dom_ele: dict):
    """

    :param dom_ele:
    :return:
    """
    if 'Text' in dom_ele['type']:
        desc = dom_ele['ext']['text'] if len(dom_ele['ext']['text']) < 15 else dom_ele['ext']['text'][:15] + "..."
        type = "text"
    elif dom_ele['type'] == "Icon":
        desc = dom_ele['ext']['cname'].replace("_beta", "") + "控件"
        type = "icon"
    elif dom_ele['type'] == "Component":
        desc = "输入框" if dom_ele['ext']['cname'] == "评论框" else dom_ele['ext']['cname']
        if dom_ele['ext']['name'] == "icon":
            type = "icon"
        else:
            type = "component"
    else:
        logger.warning("未知的操作类型：{}".format(dom_ele['type']))
        desc = ""
        type = ""
    return type, desc


if __name__ == '__main__':
    img_path = "/Users/<USER>/.lazyOne/localServer/cache/754ef7da-ca10-49d5-8450-855509c6ecd2/0.jpg"
    grid_path = "/Users/<USER>/.lazyOne/localServer/cache/754ef7da-ca10-49d5-8450-855509c6ecd2/0_grid.jpg"
    get_image_with_grid(img_path, grid_path)
