#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@File    : function_call_text.py
<AUTHOR> <PERSON><PERSON><PERSON><PERSON>(renya<PERSON><PERSON>@baidu.com)
@Time    : 2025/4/16 14:46
@Desc    : 
"""

import openai
import json

from apps.app_agent.conf.contants import GENERATE_API_KEY, OPENAI_ORGANIZATION_ID


# 模拟天气查询函数（这里其实你可以用 requests 去调用真实 API）
def get_weather(location: str, unit: str = "celsius"):
    # 模拟返回
    return {
        "location": location,
        "temperature": "22",
        "unit": unit,
        "description": "Sunny"
    }


# 定义函数 schema（告诉 LLM 有这样一个可调用的函数）
functions = [
    {
        "name": "get_weather",
        "description": "获取某地的天气信息",
        "parameters": {
            "type": "object",
            "properties": {
                "location": {
                    "type": "string",
                    "description": "城市名，比如 Beijing"
                },
                "unit": {
                    "type": "string",
                    "enum": ["celsius", "fahrenheit"],
                    "description": "温度单位"
                }
            },
            "required": ["location"]
        }
    }
]

client = openai.OpenAI(organization=OPENAI_ORGANIZATION_ID, api_key=GENERATE_API_KEY)
# 向 LLM 发送消息
response = client.chat.completions.create(
    model="gpt-4o",
    messages=[
        {"role": "user", "content": "北京今天天气怎么样？"}
    ],
    functions=functions,
    function_call="auto",  # 允许自动决定是否调用函数
)

# 检查是否触发了函数调用
message = response.choices[0].message

if message.function_call:
    print("LLM 想调用函数:", message.function_call.name)
    print("调用参数:", message.function_call.arguments)

    # 解析参数
    function_name = message.function_call.name
    arguments = json.loads(message.function_call.arguments)

    # 手动调用我们定义的函数
    if function_name == "get_weather":
        function_response = get_weather(**arguments)

        # 再把结果返回给 LLM 让它组织语言回答
        second_response = client.chat.completions.create(
            model="gpt-4o",
            messages=[
                {"role": "user", "content": "北京今天天气怎么样？"},
                message,
                {
                    "role": "function",
                    "name": function_name,
                    "content": json.dumps(function_response)
                }
            ]
        )

        print("\n最终回复：", second_response.choices[0].message.content)
