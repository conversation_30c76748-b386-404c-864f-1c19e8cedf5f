#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@Desc    :   
@File    :   save.py
@Time    :   2024-08-13
<AUTHOR>   x<PERSON><PERSON><PERSON>@baidu.com
"""
import json
import requests
from basics.util import logger

class AICaseSaveHandler(object):
    """
    生成用例的保存类
    """
    def __init__(self, version_id, os_type, guide_type=None):
        """
        初始化
        """
        self.version_id = version_id
        self.os_type = os_type

        self.network_retry_num = 3
        self.lazyone_cookie = "lazyone_token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJsYXp5b25lIiwiZXhwIjoxN" + \
            "zI1MzcwNTI0LCJpYXQiOjE3MjQ3NjU3MjQsInVzZXIiOiJ4dXpoZWp1biJ9.Eh31fXUwKC7Hk5aszB6AyWZSMx9E6FSoVOMTZ1dpjQQ"
        
        self.node_tree = None
        self.update_version_tree()

        self.last_node_id = None
        
        self.mrd_list = []
        self.mrd_node_info = {}

        self.guide_type = guide_type

    def init_mrd_list(self, new_mrd_list):
        """
        初始化mrd列表
        """
        self.mrd_list = new_mrd_list[:]
        for mrd in self.mrd_list:
            self.mrd_node_info[mrd] = {
                "node_id": None,
                "step_ids": [],
            }

    def update_version_tree(self):
        """
        更新版本对应的文件目录
        """
        for _ in range(self.network_retry_num):
            try:
                res = requests.post(
                    "https://qamate.baidu-int.com/lazyone/node/tree",
                    data=json.dumps({
                        "versionId": self.version_id,
                        "osType": self.os_type,
                        "withStep": False
                    }),
                    headers={
                        "Content-Type": "application/json",
                        "Cookie":self.lazyone_cookie
                    }, 
                    timeout=3
                )
                logger.info("add_node res: {}".format(res.text))
                result = json.loads(res.text)
                if result["errno"] != 0:
                    raise Exception("add_node error")
                self.node_tree = result["data"]["node"]
                return True
            except:
                pass
        return False
    
    def search_goal_node(self, node_tree, node_id):
        """
        搜索目标节点
        """        
        if node_id is None:
            return None

        for node in node_tree:
            if node["nodeId"] == node_id:
                return node
        
            if "child" in node and len(node["child"]) > 0:
                ret = self.search_goal_node(node["child"], node_id)
                if ret is not None:
                    return ret
        return None

    def add_node(self, desc, parent_id=None):
        """
        添加节点
        """
        # 先判断desc在parent_id下是否存在
        parent_node = self.search_goal_node(self.node_tree, parent_id)
        if parent_node is None:
            check_nodes = self.node_tree
        else:
            check_nodes = parent_node["child"]

        for node in check_nodes:
            if node["nodeText"] == desc:
                return node["nodeId"], False
        

        for _ in range(self.network_retry_num):
            try:
                res = requests.post(
                    "https://qamate.baidu-int.com/lazyone/node/create",
                    data=json.dumps({
                        "text": desc, 
                        "osType": self.os_type,
                        "versionId": self.version_id,
                        "parentId": parent_id
                    }),
                    headers={
                        "Content-Type": "application/json",
                        "Cookie":self.lazyone_cookie
                    }, 
                    timeout=3
                )
                logger.info("add_node res: {}".format(res.text))
                result = json.loads(res.text)
                if result["errno"] != 0:
                    raise Exception("add_node error")
                
                # 更新版本对应的文件目录
                self.update_version_tree()
                return result["data"]["id"], True
            except:
                pass
        return False, False
    
    def format_step_info(self, action_info):
        """
        生成标准格式的步骤信息
        """
        default_step_info = {
            "caseVersion": "v1.0.0",
            "desc": "测试",
            "type": 1,
            "params": {
                    "type": "wait",
                    "params": {
                    "seconds": 1
                }
            },
            "common": {
                "commonAlertClear": True,
                "stepInterval": 2
            }
        }
    
    def add_step(self, node_id, action_info, action_type):
        """
        添加步骤
        """
        for _ in range(self.network_retry_num):
            try:
                res = requests.post(
                    "https://qamate.baidu-int.com/lazyone/step/create",
                    data=json.dumps({
                        "nodeId": node_id,
                        "osType": self.os_type,
                        "actionInfo": json.dumps(action_info),
                        "actionType": action_type
                    }),
                    headers={
                        "Content-Type": "application/json",
                        "Cookie":self.lazyone_cookie
                    }, 
                    timeout=3
                )
                logger.info("add_step res: {}".format(res.text))
                result = json.loads(res.text)
                if result["errno"] != 0:
                    raise Exception("add_step error")
                return result["data"]["id"]
            except:
                pass
        return False
    
    def add_image(self, step_id, screen_url):
        """
        添加步骤
        """
        for _ in range(self.network_retry_num):
            try:
                res = requests.post(
                    "https://qamate.baidu-int.com/lazyone/step/report/screenshot",
                    data=json.dumps({
                        "id": step_id,
                        "screenshot": screen_url
                    }),
                    headers={
                        "Content-Type": "application/json",
                        "Cookie":self.lazyone_cookie
                    }, 
                    timeout=3
                )
                logger.info("add_step res: {}".format(res.text))
                result = json.loads(res.text)
                if result["code"] != 0:
                    raise Exception("add_step error")
                return True
            except:
                pass
        return False

    def save_pre_operation_nodes(self, mrd_list, mrd_index, action_infos):
        """
        保存前置步骤
        初期挂载逻辑为：mrd_index 对应的节点中挂载对应的全部步骤，其它mrd为空节点
        """
        # 获取真实的节点列表
        real_mrd_list = mrd_list[: mrd_index + 1]

        # 最后一个节点之前都是空节点
        parent_id = None
        for node_desc in real_mrd_list[:-1]:
            parent_id, _ = self.add_node(node_desc, parent_id)
        
        # 添加最后一个节点，作为有效节点
        node_id, is_new = self.add_node(real_mrd_list[-1], parent_id)
        if is_new is True:
            for action_info in action_infos:
                self.add_step(node_id, action_info, action_info["params"]["type"])
        
        self.last_node_id = node_id

    def map_params_action_info(self, action_res):
        """
        将action_res转换为步骤中的action_info
        """
        action_info = {}
        if action_res["action"] == "tap":
            action_info = {
                "type": "tap",
                "params": {}
            }
        elif action_res["action"] == "input":
            action_info = {
                "type": "input",
                "params": {
                    "text": action_res["input_text"]
                }
            }
        elif action_res["action"] == "long_press":
            action_info = {
                "type": "tap",
                "params": {
                    "duration": 1000
                }
            }
        elif action_res["action"] == "nope":
            action_info = {
                "type": "nope",
                "params": {}
            }
        elif action_res["action"] == "absence":
            action_info = {
                "type": "absence",
                "params": {}
            }
        else:
            raise Exception("action_res type error")
        return action_info
    
    def search_goal_node_by_elementid(self, element_id, dom):
        """
        通过element_id查找节点
        """
        path_summary = [0]
        goal_node = None

        # 临时逻辑，特殊处理TextArea
        if self.guide_type is None:
            goal_node = dom["children"][element_id]
            path_summary.append(goal_node["debug"]["id"])
            if goal_node["type"] == "TextArea":
                goal_node = goal_node["children"][0]
                path_summary.append(goal_node["debug"]["id"])
        else:
            for node in dom["children"]:
                if node["debug"]["id"] == element_id:
                    goal_node = node
                    path_summary.append(element_id)
                    break
        
        logger.info("goal_node: {}".format(goal_node))
        # 根据goal_node 规范结果
        find_info = {
            "screenCount": 1,
            "findType": 0,
            "findNode": [{
                "detailFeature": {
                    "ext": goal_node["ext"],
                    "rect": goal_node["rect"],
                    "matchType": 1,
                    "type": goal_node["type"]
                },
                "featureFlag": True,
                "actionNode": True,
                "id": goal_node["debug"]["id"]
            }],
            "modelType": 2,
            "chosenTag": ["visual", "single"],
            "findPath": [],
            "mergeOCR": False
        }
        return find_info, path_summary
    
    def scale_down_rect(self, rect, scale):
        """
        根据scale缩放rect
        """
        rect["x"] = int(rect["x"] / scale)
        rect["y"] = int(rect["y"] / scale)
        rect["w"] = int(rect["w"] / scale)
        rect["h"] = int(rect["h"] / scale)
    
    def format_step_dom_by_scale(self, dom, device_info):
        """
        根据scale缩放dom
        """
        scale = device_info["screenSize"]["scale"]
        # 根节点
        self.scale_down_rect(dom["rect"], scale)

        # 第一层
        for node in dom["children"]:
            self.scale_down_rect(node["rect"], scale)
            # 第二层
            if "children" in node:
                for child_node in node["children"]:
                    self.scale_down_rect(child_node["rect"], scale)
    
    def get_action_info_from_action_res(self, batdom, action_res, action_dec):
        """
        从action_res中提取出action_info
        """
        action_info = {
            "caseVersion": "v1.0.0",
            "desc": action_dec,
            "type": -1,
            "params": {},
            "common": {
                "commonAlertClear": True,
                "stepInterval": 2
            }
        }

        if action_res["action"] == "swipe":
            # swipe 特殊处理
            direction = -1
            if action_res["direction"] == "up":
                direction = 1
            elif action_res["direction"] == "down":
                direction = 2
            elif action_res["direction"] == "right":
                direction = 3
            elif action_res["direction"] == "left":
                direction = 4
            action_info["type"] = 1
            action_info["params"] = {
                "type": "swipe",
                "params": {
                    "direction": direction,
                    "times": 1,
                    "interval": 1,
                    "duration": 500
                }
            }
            action_type = action_info["params"]["type"]
            return action_info, action_type
        else:
            # tap,long_press,input,assert_exist可以统一处理
            find_params = {
                "allowFail": False,
                "times": 0,
                "before": {
                    "wait": 0
                },
                "interval": 1000,
                "until": {
                    "times": 1,
                    "enable": False,
                    "interval": 2000
                }
            }

            find_info, path_summary = self.search_goal_node_by_elementid(
                action_res["id"], batdom["dom"]
            )
            params = {
                "dom": batdom["dom"],
                "domInfo": batdom["info"],
                "findType": 0,
                "findInfo": find_info,
                "actionInfo": self.map_params_action_info(action_res),
                "findParams": find_params,
                "pathSummary": path_summary,
                "deviceInfo": batdom["deviceInfo"]
            }
            action_info["type"] = 9
            action_info["params"] = params
            action_type = action_info["params"]["actionInfo"]["type"]

        return action_info, action_type
    
    def format_action_assert(self, action_assert):
        """
        格式化对齐一下action_assert和action_res
        """
        if action_assert["element_id"] < 0:
            return False
        action_assert["action"] = "nope" if action_assert["is_not"] is True else "absence"
        return True
    
    def get_mrd_index(self, mrd_iterm):
        """
        获取mrd对应的list id
        """
        try:
            index = self.mrd_list.index(mrd_iterm)
        except ValueError:
            index = len(self.mrd_list) - 1
        
        # 判断下这个index之后的mrd是否已经添加了步骤，如果有，则放到最后一个添加了步骤的节点里
        for i in range(index + 1, len(self.mrd_list)):
            if len(self.mrd_node_info[self.mrd_list[i]]["step_ids"]) == 0:
                pass
            else:
                index = i
        return index
    
    def save_auto_generate_nodes_without_save(self, mrd_item, record_batdom, action_res, action_assert, action_dec):
        """
        保存自动生成步骤，但是进行用例保存
        转化且保存生成步骤
        有两种情况，要判断是否存在断言步骤
        如果断言步骤存在，需要判断是断言存在还是不存在
        如果是存在性校验:先判断存在，再执行步骤
        如果是不存在性校验：先执行步骤，再判断不存在
        """
        # 根据scale缩放dom
        self.format_step_dom_by_scale(record_batdom["dom"], record_batdom["deviceInfo"])

        action_info = None
        action_type = None
        # 先判断是否没有执行动作
        if action_res["action"] == "assert_exist" or action_res["action"] == "assert_not_exist":
            pass
        else:
            # 结合batdom和action_res生成action_info
            action_info, action_type = self.get_action_info_from_action_res(
                record_batdom, action_res, action_dec
            )

        # 获取断言节点
        # 先做一个格式对齐
        need_assert = self.format_action_assert(action_assert)
        assert_info = None
        assert_type = None
        if need_assert is True:
            assert_info, assert_type = self.get_action_info_from_action_res(
                record_batdom, action_assert, action_assert["content"]
            )

        # 添加 & 执行步骤
        action_info_list = []
        # 逻辑为：
        # 没有操作路径，则直接添加验证步骤
        if action_info is None:
            if assert_info is None:
                return action_info_list
            
            action_info_list.append({
                "action": assert_info,
                "act_type": assert_type
            })
            return action_info_list

        # 没有assert，直接添加和执行action_info
        if assert_info is None:
            action_info_list.append({
                "action": action_info,
                "act_type": action_type
            })
            return action_info_list
        # 存在 assert，则进行判断
        if assert_type == "nope":
            # 如果是存在性校验:先判断存在，再执行步骤
            action_info_list.append({
                "action": assert_info,
                "act_type": assert_type
            })
            action_info_list.append({
                "action": action_info,
                "act_type": action_type
            })
            return action_info_list
        else:
            # 如果是不存在性校验：先执行步骤，再判断不存在
            action_info_list.append({
                "action": action_info,
                "act_type": action_type
            })
            action_info_list.append({
                "action": assert_info,
                "act_type": assert_type
            })
            return action_info_list
        return action_info_list
    
    def trans_generate_nodes(self, record_batdom, action_func, action_dec):
        """
        保存自动生成步骤，但是进行用例保存
        转化且保存生成步骤
        有两种情况，要判断是否存在断言步骤
        如果断言步骤存在，需要判断是断言存在还是不存在
        如果是存在性校验:先判断存在，再执行步骤
        如果是不存在性校验：先执行步骤，再判断不存在
        """
        # 根据scale缩放dom
        self.format_step_dom_by_scale(record_batdom["dom"], record_batdom["deviceInfo"])

        action_info = None
        action_type = None
        
        # 结合batdom和action_res生成action_info
        action_info, action_type = self.get_action_info_from_action_res(
            record_batdom, action_func, action_dec
        )

        return action_info, action_type
    
    def save_total_actions(self, actions):
        """
        统一进行保存测试步骤
        """
        # 添加节点
        for mrd in self.mrd_list:
            if self.mrd_node_info[mrd]["node_id"] is not None:
                # 已经添加过了，直接退出
                self.last_node_id = self.mrd_node_info[mrd]["node_id"]
                continue
            
            node_id, _ = self.add_node(mrd, self.last_node_id)
            self.last_node_id = node_id
            self.mrd_node_info[mrd]["node_id"] = node_id
        
        # 把所有步骤添加到最后一个节点中：
        for each_action in actions:
            logger.info("insert action_info: {}".format(type(each_action)))
            _ = self.add_step(
                self.last_node_id, each_action["action"], each_action["type"]
            )
        
    def save_auto_generate_nodes(self, mrd_item, record_batdom, action_res, action_assert, action_dec):
        """
        转化且保存生成步骤
        有两种情况，要判断是否存在断言步骤
        如果断言步骤存在，需要判断是断言存在还是不存在
        如果是存在性校验:先判断存在，再执行步骤
        如果是不存在性校验：先执行步骤，再判断不存在
        """
        # 添加节点
        for mrd in self.mrd_list:
            if self.mrd_node_info[mrd]["node_id"] is not None:
                # 已经添加过了，直接退出
                break
            
            node_id, _ = self.add_node(mrd, self.last_node_id)
            self.last_node_id = node_id
            self.mrd_node_info[mrd]["node_id"] = node_id

        # 获取要在哪个节点插入步骤
        mrd_index = self.get_mrd_index(mrd_item)
        goal_node_id = self.mrd_node_info[self.mrd_list[mrd_index]]["node_id"]

        # 根据scale缩放dom
        self.format_step_dom_by_scale(record_batdom["dom"], record_batdom["deviceInfo"])

        action_info = None
        action_type = None
        # 先判断是否没有执行动作
        if action_res["action"] == "assert_exist" or action_res["action"] == "assert_not_exist":
            pass
        else:
            # 结合batdom和action_res生成action_info
            action_info, action_type = self.get_action_info_from_action_res(
                record_batdom, action_res, action_dec
            )

        # 获取断言节点
        # 先做一个格式对齐
        need_assert = self.format_action_assert(action_assert)
        assert_info = None
        assert_type = None
        if need_assert is True:
            assert_info, assert_type = self.get_action_info_from_action_res(
                record_batdom, action_assert, action_assert["content"]
            )

        # 添加 & 执行步骤
        action_info_list = []
        # 逻辑为：
        # 没有操作路径，则直接添加验证步骤
        if action_info is None:
            if assert_info is None:
                return action_info_list
            
            step_id = self.add_step(goal_node_id, assert_info, assert_type)
            action_info_list.append(assert_info)
            self.mrd_node_info[self.mrd_list[mrd_index]]["step_ids"].append(step_id)
            return action_info_list

        # 没有assert，直接添加和执行action_info
        if assert_info is None:
            step_id = self.add_step(goal_node_id, action_info, action_type)
            action_info_list.append(action_info)
            self.mrd_node_info[self.mrd_list[mrd_index]]["step_ids"].append(step_id)
            return action_info_list
        # 存在 assert，则进行判断
        if assert_type == "nope":
            # 如果是存在性校验:先判断存在，再执行步骤
            step_id = self.add_step(goal_node_id, assert_info, assert_type)
            action_info_list.append(assert_info)
            self.mrd_node_info[self.mrd_list[mrd_index]]["step_ids"].append(step_id)

            step_id = self.add_step(goal_node_id, action_info, action_type)
            action_info_list.append(action_info)
            self.mrd_node_info[self.mrd_list[mrd_index]]["step_ids"].append(step_id)
            return action_info_list
        else:
            # 如果是不存在性校验：先执行步骤，再判断不存在
            step_id = self.add_step(goal_node_id, action_info, action_type)
            action_info_list.append(action_info)
            self.mrd_node_info[self.mrd_list[mrd_index]]["step_ids"].append(step_id)

            step_id = self.add_step(goal_node_id, assert_info, assert_type)
            action_info_list.append(assert_info)
            self.mrd_node_info[self.mrd_list[mrd_index]]["step_ids"].append(step_id)
            return action_info_list
        
        return action_info_list
