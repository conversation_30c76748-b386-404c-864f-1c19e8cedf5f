#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@File    : check.py
<AUTHOR> <PERSON><PERSON><PERSON><PERSON>(renya<PERSON><PERSON>@baidu.com)
@Time    : 2024/8/13 14:45
@Desc    : 
"""

import json

from apps.app_agent.old.agent_wenxin.chat import get_check_prompt, get_task_decompose_prompt, amend_manual_case
from apps.app_agent.utils.wenxin_util import WenXinUtils
from apps.app_agent.utils.pandaGPT import chat_gpt_4_vision_answer_by_image_path
from apps.app_agent.old.agent_wenxin.chat import can_manual_case_2_auto_case


def check_decomposer(m_case_list: list):
    """
    校验点拆解
    :return:
    """
    m_case = "->".join(m_case_list)
    decompose_prompt = get_task_decompose_prompt(m_case)

    yiyan = WenXinUtils()
    llm_ans = yiyan.get_llm_result(prompt_message=decompose_prompt)

    can_trans = llm_ans.split("### 能否转化 ###")[-1].split("### 执行任务 ###")[0].replace(" ", "").strip()
    exec_task = llm_ans.split("### 执行任务 ###")[-1].split("### 校验任务 ###")[0].replace(" ", "").strip()
    check_task = llm_ans.split("### 校验任务 ###")[-1].strip()

    exec_task_list = exec_task.replace(";", "；").strip().split("；")
    check_task_list = check_task.replace(";", "；").strip().split("；")

    decompose_result = {
        "can_trans": True if can_trans == "True" else False,
        "exec_task_list": exec_task_list,
        "check_task_list": check_task_list
    }

    return decompose_result


def optimize_manual_case(mrd):
    """
    对手工用例进行优化，
    :param mrd:
    :return: A -> B -> C
    """
    prompt = amend_manual_case(mrd)
    res = WenXinUtils().get_llm_result(prompt, response_format="json_object")
    res = json.loads(res)
    return res


def judge_manual_2_auto_case(mrd):
    """
    判断能够转为自动化case
    :param mrd:  A -> B -> C
    :return:
    """
    prompt = can_manual_case_2_auto_case(mrd)

    res = WenXinUtils().get_llm_result(prompt, response_format="json_object")
    res = json.loads(res)
    return res


def chat_wenxin_with_batdom(prompt):
    """
    判断能够转为自动化case
    :param mrd:
    :return:
    """
    res = WenXinUtils().get_llm_result(prompt)
    # res = json.loads(res)
    return res


def check_task_by_mllm(check_list, exec_info):
    """

    :return:
    """
    check_template = ""
    for idx, check in enumerate(check_list):
        check_template += "校验点{}：".format(idx + 1) + "{}\n".format(check)

    exec_img_paths = []
    exec_desc = ""
    for idx, item in enumerate(exec_info):
        exec_img_paths.append(item['img_path'])
        exec_desc += "步骤{}：".format(idx + 1) + "{}\n".format(item['desc'])

    check_prompt = get_check_prompt(check_template, exec_desc)
    llm_check_res = chat_gpt_4_vision_answer_by_image_path(check_prompt, img_paths=exec_img_paths)

    # 结果解析
    check_res = []
    for section in llm_check_res.replace(':', '：').strip().split("\n校验点"):
        content = section.split("内容：")[-1].split("\n")[0]
        result = section.split("结论：")[-1].split("\n")[0]
        reason = section.split("原因：")[-1].split("\n")[0]

        item = {
            "content": content,
            "check_res": True if result == "True" else False,
            "reason": reason
        }
        check_res.append(item)

    return check_res


def demo(m_case_list, exec_info):
    """

    :param m_case_list:
    :param exec_info:
    :return:
    """
    decompose_result = check_decomposer(m_case_list)
    check_list = decompose_result['check_task_list']
    check_res = check_task_by_mllm(check_list, exec_info)
    print(check_res)


def experience_case_optimize():
    """

    :return:
    """
    from apps.app_agent.old.agent.tool import get_mrd_list_by_caseid
    mrd_list = get_mrd_list_by_caseid(70720)

    for idx, mrd in enumerate(mrd_list[:1]):
        mrd = "->".join(mrd)
        print(idx, mrd)
        optimize_manual_case(mrd)


if __name__ == '__main__':
    mrd = ["助手tab和历史tab可点击切换、可手势横划切换"]
    # exec_info = [
    #     {
    #         "desc": "点击历史tab以验证标签切换功能",
    #         "img_path": "/Users/<USER>/.lazyOne/localServer/cache/625b19ce-81e5-41c4-ac93-31719f53b122/0_exec.jpg"
    #     },
    #     {
    #         "desc": "点击助手tab，以验证标签的返回切换功能。",
    #         "img_path": "/Users/<USER>/.lazyOne/localServer/cache/625b19ce-81e5-41c4-ac93-31719f53b122/1_exec.jpg"
    #     },
    #     {
    #         "desc": "通过向左滑动屏幕来尝试切换到历史tab",
    #         "img_path": "/Users/<USER>/.lazyOne/localServer/cache/625b19ce-81e5-41c4-ac93-31719f53b122/2_exec.jpg"
    #     },
    #     {
    #         "desc": "finsh",
    #         "img_path": "/Users/<USER>/.lazyOne/localServer/cache/625b19ce-81e5-41c4-ac93-31719f53b122/3.jpg"
    #     }
    # ]
    #
    # demo(mrd, exec_info)

    # mrd_s = "普通对话（文字+语音+图片）->对话内容在测试当中使用下面query->文字提问->会话类型->文生代码->用python给我写一段冒泡排序算法".split("->")
    # mrd_s = "首页及对话（普通对话+语音电话对话）（97）-> 对话交互优化 8 条->支持引用功能->通用->引用功能基本使用->测试步骤->"
    # "用户确认引用该条对话并编辑新的问题发送。->用户能够成功发送包含引用内容的新问题。->引用内容点击无响应->"
    # "不支持查看全部内容，生成的回答内容与引用内容相关"
    # optimize_manual_case_agent(mrd_s)

    # from apps.app_agent.agent.tool import get_mrd_list_by_caseid
    # mrd_list = get_mrd_list_by_caseid(70720)
    # for idx, item in enumerate(mrd_list):
    #     mrd_s = "->".join(item)
    #     print(idx)
    #     print(mrd_s)
    #     res = optimize_manual_case_agent(mrd_s)
    experience_case_optimize()
