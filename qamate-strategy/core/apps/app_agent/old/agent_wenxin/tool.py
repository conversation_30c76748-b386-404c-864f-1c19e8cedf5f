#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@Desc    :   
@File    :   tool.py
@Time    :   2024-08-15
<AUTHOR>   x<PERSON><PERSON><PERSON>@baidu.com
"""
import json
import requests


def trans_casenode_to_mrd_list(case_node, parents_list, mrd_list):
    """
    将case_node转换为mrd_list
    """
    # 先复制
    new_parensts_list = parents_list[:]
    new_parensts_list.append(case_node["nodeName"])

    if "child" in case_node and len(case_node["child"]) > 0:
        for child in case_node["child"]:
            trans_casenode_to_mrd_list(child, new_parensts_list, mrd_list)
    else:
        mrd_list.append(new_parensts_list)    


def get_mrd_list_by_caseid(case_id):
    """
    根据case_id获取mrd_list
    """
    res = requests.get(
        "https://qamate.baidu-int.com/lazymind/common/simple/brain?caseId={}".format(case_id),
        headers={
            "Content-Type": "application/json"
        }, 
        timeout=3
    )
    case_nodes = json.loads(res.text)["data"]["caseNode"][0]["child"]
    mrd_list = []
    for case_node in case_nodes:
        trans_casenode_to_mrd_list(case_node, [], mrd_list)
    return mrd_list


if __name__ == '__main__':
    case_id = 70720
    mrd_list = get_mrd_list_by_caseid(case_id)
    print(len(mrd_list))
    # for mrd in mrd_list:
    #     print(mrd)
