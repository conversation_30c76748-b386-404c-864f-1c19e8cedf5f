#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@File    : guide.py
<AUTHOR> <PERSON><PERSON><PERSON><PERSON>(renyan<PERSON>@baidu.com)
@Time    : 2024/8/8 17:07
@Desc    : 
"""
import sys

sys.path.append("/Users/<USER>/Workspace/Project/lazyOne/qamate-strategy/core")

import os
import json
import uuid
import logging.handlers
import traceback

from apps.app_agent.old.agent_wenxin.image_process import get_image_with_grid
from apps.app_agent.multi_agent.preroute_agent.case_search import CaseKnowledgeRetrieve
from apps.app_agent.old.agent_wenxin.check_wenxin import CheckerWenxin
from basics.util.image import Image
from apps.auto_case_transform.device import Device
from apps.app_agent.conf.device import DEVICE_ID
from basics.util import logger
from basics.util.config import CACHE_DIR
from apps.app_agent.old.agent_wenxin.save import AICaseSaveHandler
from basics.util.config import PY_LOG_DIR

format = logging.Formatter(
    '[%(process)s][%(asctime)s][%(levelname)s] %(message)s')
# logger.setLevel(logging.ERROR)
logger.setLevel(logging.INFO)

file = logging.handlers.TimedRotatingFileHandler(
    filename=os.path.join(PY_LOG_DIR, 'lazyGuide.log'), encoding='utf-8',
    when='D', backupCount=3
)
file.setFormatter(format)
logger.addHandler(file)


class ExecuteInfo(object):
    """
    """

    def __init__(self, raw_img_path: str, grid_img_path: str, batdom_record: dict, prompt: str, llm_ans: str,
                 action_res: dict, exec_img_path: str, history):
        """

        :param raw_img_path:
        :param grid_img_path:
        :param batdom_record:
        :param prompt:
        :param llm_ans:
        """
        self.raw_img_path = raw_img_path
        self.grid_img_path = grid_img_path
        self.batdom_record = batdom_record
        self.prompt = prompt
        self.llm_ans = llm_ans
        self.action_res = action_res
        self.exec_img_path = exec_img_path
        self.history = history


class AppTestAgent(object):
    """

    """

    def __init__(self, mrd_list, knowledge_version_id, os_type, case_save_version_id):
        """

        :param mrd_list:  例如：["首页及对话", "对话页", "助手tab和历史tab可点击切换、可手势横划切换"]
        :param knowledge_version_id:
        :param os_type:
        """
        self.mrd_list = mrd_list
        self.knowledge_version_id = knowledge_version_id
        self.os_type = os_type

        self.temp_dir = os.path.join(CACHE_DIR, str(uuid.uuid4()))

        self.dv = Device(DEVICE_ID)

        self.new_mrd_list = None
        self.retrieve_res = None
        self.execute_record = []  # 执行记录
        self.batdom_list = []

        self.ai_case_save_handler = AICaseSaveHandler(case_save_version_id, os_type, "wenxin")
        self.ai_client = CheckerWenxin()


    def mk_dir(self):
        """

        :return:
        """
        if not os.path.exists(self.temp_dir):
            os.mkdir(self.temp_dir)
        logger.info("temp_dir: {}".format(self.temp_dir))

    def _current_image(self, image_path):
        """
        获取当前截图
        :param image_path:
        :return:
        """
        img_url = self.dv.screenshot()['screenshot']
        image = Image(url=img_url)
        image.save_image(image_path)
        return image

    def _current_image_record_and_grid(self, image_path, grid_path):
        """
        页面建模 + 画框
        :param image_path: 原图存储地址
        :param grid_path: 带有gird图存储地址
        :return:
        """
        img_url = self.dv.screenshot()['screenshot']
        # source = self.dv.source()
        image = Image(url=img_url)
        image.save_image(image_path)

        # dom_info, dom, dom_desc = get_image_with_grid(image_path, grid_path, source)
        dom_info, dom, dom_desc = get_image_with_grid(image_path, grid_path)
        batdom_record_res = {
            "info": dom_info,
            "dom": dom,
            "dom_desc": dom_desc,
            "deviceInfo": {
                "screenSize": {
                    "rotation": self.dv.rotation_info,
                    "width": self.dv.size_info["width"],
                    "height": self.dv.size_info["height"],
                    "scale": self.dv.size_info["scale"]
                },
                "screenshot": img_url,
                "type": "ios" if self.os_type == 2 else "android"
            }
        }

        return batdom_record_res

    def case_retrieve_and_run(self):
        """
        前置准备：知识检索+执行 用于冷启
        :return:
        """
        case_retrieve_tool = CaseKnowledgeRetrieve(self.knowledge_version_id, self.os_type)
        retrieve_res = case_retrieve_tool.main(self.mrd_list)
        self.new_mrd_list = retrieve_res['new_mrd_list']
        self.retrieve_res = retrieve_res

        # 执行前置步骤
        action_infos = []
        logger.info("执行前置case步骤, case_name: {}".format(retrieve_res['knowledge_case']['nodeText']))
        case_name = retrieve_res['knowledge_case']['nodeText']
        
        # return case_name
    
        for step_info in retrieve_res['knowledge_case']['step']:
            action_info = json.loads(step_info['actionInfo'])
            self.dv.run_step(action_info)
            action_infos.append(action_info)

        # 执行成功的话，将执行的步骤挂载到手自一体的节点中去
        # 初期挂载逻辑为：mrd_index 对应的节点中挂载对应的全部步骤，其它mrd为空节点
        self.ai_case_save_handler.save_pre_operation_nodes(
            retrieve_res["mrd_list"], retrieve_res["mrd_index"], action_infos
        )
        return case_name

    def guide_by_mllm(self, case_name):
        """
        大模型引导执行
        : return
            res_flag: 1 正常生成；0 判断转为自动化；-1 转化失败
        """
        # 一次性集中保存的操作列表
        total_actions = []

        # 需求输入
        self.ai_case_save_handler.init_mrd_list(self.new_mrd_list)
        new_mrd = "->".join(self.new_mrd_list)
        logger.info("需求更新为：{}".format(new_mrd))
        
        # 将输入的待转化需求，转化成测试步骤列表
        execute_step_list = self.ai_client.understand_case(self.new_mrd_list)
        logger.info("execute_step_list: {}".format(execute_step_list))
        action_history = []
        idx = 0
        
        for execute_step in execute_step_list:
            max_step_num = 10
            logger.info("execute_step: {}".format(execute_step))
            for step_index in range(max_step_num):
                logger.info("step_index: {}".format(step_index))

                # 获取当前步骤的截图
                idx += 1
                raw_img_path = os.path.join(self.temp_dir, "{}.jpg".format(idx))
                grid_img_path = os.path.join(self.temp_dir, "{}_grid.jpg".format(idx))
                cur_batdom_record = self._current_image_record_and_grid(raw_img_path, grid_img_path)

                # 判断当前页面是否可以执行
                page_judge_flag = self.ai_client.understand_judge_page(execute_step, cur_batdom_record)
                logger.info("page_judge_flag: {}".format(page_judge_flag))
                if page_judge_flag is False:
                    break
                
                # 生成对应的执行操作 --> 自然语言
                action_content, action_desc = self.ai_client.generate_action_desc(
                    execute_step, cur_batdom_record, action_history
                )
                action_history.append(action_desc)
                logger.info("action_content: {}".format(action_content))
                logger.info("action_desc: {}".format(action_desc))

                # 根据执行操作生成具体的操作函数 --> 自动化操作
                trans_flag, action_func = self.ai_client.generate_action_info(action_content, cur_batdom_record)
                logger.info("trans_flag: {}".format(trans_flag))
                logger.info("action_func: {}".format(action_func))
                if trans_flag is False:
                    continue

                # 转化成Qamate的执行操作
                action_qamate = self._action_res_2_qamate_action_info(
                    cur_batdom_record, action_func, action_desc
                )
                logger.info("action_qamate: {}".format(action_qamate))

                total_actions.append(action_qamate)

                # 判断当前步骤是否执行完成
                finish_flag = self.ai_client.judge_action_finish(execute_step, action_history)
                logger.info("judge_action_finish: {}".format(finish_flag))
                if finish_flag is True:
                    break
        
        if len(total_actions) > 0:
            self.ai_case_save_handler.save_total_actions(total_actions)
            return True
        else:
            return False

    def _action_res_2_qamate_action_info(
            self, cur_batdom_record, action_res, action_dec):
        """
        check 点解析
        :param check:
        :return:
        """
        # 转为 qamate 的数据格式
        action_info, action_type = self.ai_case_save_handler.trans_generate_nodes(
            cur_batdom_record, action_res, action_dec
        )

        # 并执行
        action_res = self.dv.run_step(action_info)
        logger.info("action_res: {}".format(action_res))
        save_info = {
            "action": action_info,
            "type": action_type,
            "desc": action_dec
        }
        return save_info

    def main(self):
        """
        :return:
        """
        # 前置操作
        self.mk_dir()
        case_name = self.case_retrieve_and_run()
        # case_name = "进入对话页"

        # 执行
        res_flag = self.guide_by_mllm(case_name)
        return res_flag


if __name__ == '__main__':
    mrd_list = [
        ["对话页", "助手tab和历史tab可点击切换、可手势横划切换"],
        ["对话页", "主对话/二级页对话均支持下拉查看历史"],
        ["对话页", "欢迎语逻辑调整（9条）", "功能", "地图相关的历史数据", "输入去故宫怎么走", "正常展示欢迎语和地图历史内容"],
    ]
    # mrd_list = get_mrd_list_by_caseid(70720)
    no = -1
    exe_num = 0
    for mrd in mrd_list:
        try:
            # no += 1
            # if no <= 0:
            #     continue
            # exe_num += 1
            logger.info("{}: mrd 执行: {}".format(no, mrd))
            # if exe_num >= 10:
                # break
            
            t = AppTestAgent(mrd, 502, 2, 949).main()
            logger.info("=" * 20)
        except:
            logger.error("mrd 执行异常: {}".format(mrd))
            logger.error("错误信息: {}".format(traceback.format_exc()))
        # break
