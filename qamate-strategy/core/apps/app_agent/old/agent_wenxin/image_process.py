#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@File    : image_process.py
<AUTHOR> <PERSON><PERSON><PERSON><PERSON>(<EMAIL>)
@Time    : 2024/8/9 10:40
@Desc    : 
"""
import json

import cv2

from apps.auto_case_transform.utils.utils import batdom_record_by_path_new
from apps.app_agent.utils.draw import draw_rect_with_index
from basics.util import logger
from basics import config

config.load_config('/Users/<USER>/Workspace/Project/lazyOne/qamate-strategy/core/profile.json')
# config.load_config('/Users/<USER>/baidu/qamate-strategy/core/profile.json')


def get_image_with_grid(image_path, grid_path):
    """

    :param image_path:
    :param grid_path:  # 存储地址
    :return:
    """

    dom_info, dom = batdom_record_by_path_new(image_path)
    ele_list = [item['rect'] for item in dom['children']]

    imgcv = cv2.imread(image_path)
    grid_image = draw_rect_with_index(imgcv, ele_list, output_path=grid_path)

    ele_desc = "[\n"
    for idx, ele in enumerate(dom['children']):
        t, d = dom_ele_2_desc(ele)
        ele_desc += "id:{}, type: {}, desc: {}\n".format(idx, t, d)
    ele_desc += "]"

    return dom_info, dom, ele_desc


def dom_ele_2_desc(dom_ele: dict):
    """

    :param dom_ele:
    :return:
    """
    if 'Text' in dom_ele['type']:
        desc = dom_ele['ext']['text'] if len(dom_ele['ext']['text']) < 15 else dom_ele['ext']['text'][:15] + "..."
        type = "text"
    elif dom_ele['type'] == "Icon":
        desc = dom_ele['ext']['cname'].replace("_beta", "") + "控件"
        type = "icon"
    elif dom_ele['type'] == "Component":
        desc = "输入框" if dom_ele['ext']['cname'] == "评论框" else dom_ele['ext']['cname']
        if dom_ele['ext']['name'] == "icon":
            type = "icon"
        else:
            type = "component"
    else:
        logger.warning("未知的操作类型：{}".format(dom_ele['type']))
        desc = ""
        type = ""
    return type, desc


if __name__ == '__main__':
    img_path = "/Users/<USER>/.lazyOne/localServer/cache/754ef7da-ca10-49d5-8450-855509c6ecd2/0.jpg"
    grid_path = "/Users/<USER>/.lazyOne/localServer/cache/754ef7da-ca10-49d5-8450-855509c6ecd2/0_grid.jpg"
    get_image_with_grid(img_path, grid_path)
