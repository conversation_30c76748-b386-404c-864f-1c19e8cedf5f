#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@File    : check.py
<AUTHOR> <PERSON><PERSON><PERSON><PERSON>(renya<PERSON><PERSON>@baidu.com)
@Time    : 2024/8/30 16:31
@Desc    : 
"""

import json

from apps.app_agent.old.agent2.chat import amend_manual_case
from apps.app_agent.old.agent2.chat import process_prompt
from apps.app_agent.old.agent2.chat import recheck_retrieve_prompt
from apps.app_agent.utils.wenxin_util import WenXinUtils
from apps.app_agent.utils.openaiGPT import chat_with_gpt
from basics.util import logger
from apps.app_agent.utils.exception import AgentErrCode, AgentException

MAX_COUNT = 3


def optimize_manual_case(mrd, app_info, summary):
    """
    对手工用例进行优化，
    :param mrd:
    :param app_info:
    :param summary: 已完成内容为
    :return: A -> B -> C
    """
    prompt = amend_manual_case(mrd, app_info, summary)
    # res = chat_with_gpt(prompt, response_format="json_object")
    num = 0
    while num < MAX_COUNT:
        num += 1
        try:
            res = WenXinUtils().get_llm_result(prompt, response_format="json_object")
            res = json.loads(res)
            return res
        except Exception as e:
            logger.error(e)
            logger.error("output error, try again, left %d" % num)
    raise AgentException(err_code=AgentErrCode.LLM_UNEXPECTED_OUPUT)


def process_summary_by_wenxin(mrd, history):
    """
    判断 执行进度
    :param mrd:
    :param history:
    :return:
    """
    prompt = process_prompt(mrd, history)
    logger.info("进度总结prompt:{}".format(prompt))
    num = 0
    while num < MAX_COUNT:
        num += 1
        try:
            res = WenXinUtils().get_llm_result(prompt, temperature=0.1, response_format="json_object")
            res = json.loads(res)
            return res
        except Exception as e:
            logger.error(e)
            logger.error("output error, try again, left %d" % num)
    raise AgentException(err_code=AgentErrCode.LLM_UNEXPECTED_OUPUT)


def recheck_retrieve_by_llm(retrieve_result):
    """

    :param retrieve_result:
    :return:
    """
    mrd = "->".join(retrieve_result['mrd_list'])
    auto_case = retrieve_result['knowledge_case']['nodeName']
    # replace_case = retrieve_result['mrd_list'][retrieve_result['mrd_index']]
    new_case_list = [auto_case]
    new_case_list.extend(retrieve_result['new_mrd_list'])
    new_case = "->".join(new_case_list)

    prompt = recheck_retrieve_prompt(mrd=mrd, new_case=new_case)
    # logger.info("检索验证prompt：{}".format(prompt))
    num = 0
    while num < MAX_COUNT:
        num += 1
        try:
            res = WenXinUtils().get_llm_result(prompt, temperature=0.01, response_format="json_object")
            res = json.loads(res)
            return res
        except Exception as e:
            logger.error(e)
            logger.error("output error, try again, left %d" % num)
    raise AgentException(err_code=AgentErrCode.LLM_UNEXPECTED_OUPUT)
