#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@File    : guide.py
<AUTHOR> <PERSON><PERSON><PERSON><PERSON>(renya<PERSON><PERSON>@baidu.com)
@Time    : 2024/8/8 17:07
@Desc    : 
"""
import sys
import time

sys.path.append("/Users/<USER>/Workspace/Project/lazyOne/qamate-strategy/core")

import os
import json
import uuid
import re
import logging.handlers
import traceback
import difflib

import cv2
import numpy as np

from apps.app_agent.utils.pandaGPT import add_user_info_to_chat_list, add_assistant_info_to_chat_list
from apps.app_agent.utils.openaiGPT import chat_with_gpt
# from apps.app_agent.utils.pandaGPT import chat_gpt_with_image as chat_with_gpt
from apps.app_agent.old.agent.image_process import get_image_with_grid
from apps.app_agent.utils.draw import draw_arrow, draw_rect
from apps.app_agent.multi_agent.preroute_agent.case_search import CaseKnowledgeRetrieve
from apps.app_agent.knowledge_search.knowledge_search_for_text_list import KnowledgeSearchForTextList
from apps.app_agent.old.agent2.chat import (init_app_tester_chat, get_action_prompt, get_reflect_check,
                                        process_prompt)
from apps.app_agent.old.agent2.check import optimize_manual_case
from basics.util.image import Image
from apps.auto_case_transform.device import Device
from apps.app_agent.conf.device import DEVICE_ID
from basics.util import logger
from basics.util.config import CACHE_DIR
# from apps.app_agent.agent.tool import get_mrd_list_by_caseid
from apps.app_agent.utils.qamate_case_handle import QamateCaseHandle
from basics.util.config import PY_LOG_DIR
from apps.app_agent.conf.app_note import APP_NOTE
from apps.app_agent.conf.contants import GENERATE_API_KEY
from basics.image.ui.image_process import image_to_base64
from basics.image.ui.webapi import get_popup_from_openapi

format = logging.Formatter(
    '[%(process)s][%(asctime)s][%(levelname)s] %(message)s')
logger.setLevel(logging.INFO)
# logger.setLevel(logging.DEBUG)

TEMP_DIR = os.path.join(PY_LOG_DIR, 'lazyGuide.log')

file = logging.handlers.TimedRotatingFileHandler(
    filename=TEMP_DIR, encoding='utf-8',
    when='D', backupCount=3
)
file.setFormatter(format)
logger.addHandler(file)


def scale_dom(dom_info, scale):
    """
    对给定的DOM信息进行缩放处理。

    Args:
        dom_info (Union[dict, list]): DOM信息，可以是单个字典对象或字典对象的列表。
        scale (float): 缩放比例。

    Returns:
        Union[dict, list]: 经过缩放处理后的DOM信息，与输入类型相同。

    """
    arr = []
    if type(dom_info) is dict:
        arr.append(dom_info)
    elif type(dom_info) is list:
        arr = dom_info
    for dom in arr:
        dom['rect']['x'] = int(dom['rect']['x'] / scale)
        dom['rect']['y'] = int(dom['rect']['y'] / scale)
        dom['rect']['w'] = int(dom['rect']['w'] / scale)
        dom['rect']['h'] = int(dom['rect']['h'] / scale)
        if 'children' in dom:
            scale_dom(dom['children'], scale)
    return dom_info


class ExecuteInfo(object):
    """
    """

    def __init__(self, raw_img_path: str, grid_img_path: str, batdom_record: dict, prompt: str, llm_ans: str,
                 action_res: dict, exec_img_path: str, history):
        """

        :param raw_img_path:
        :param grid_img_path:
        :param batdom_record:
        :param prompt:
        :param llm_ans:
        """
        self.raw_img_path = raw_img_path
        self.grid_img_path = grid_img_path
        self.batdom_record = batdom_record
        self.prompt = prompt
        self.llm_ans = llm_ans
        self.action_res = action_res
        self.exec_img_path = exec_img_path
        self.history = history


class AppTestAgent(object):
    """

    """

    def __init__(self, mrd_list, mrd_info, knowledge_node_id, os_type, product_module_id, app_name):
        """

        :param mrd_list:  例如：["首页及对话", "对话页", "助手tab和历史tab可点击切换、可手势横划切换"]
        :param knowledge_version_id:
        :param os_type:
        """
        self.mrd_list = mrd_list
        self.mrd_info_list = mrd_info

        self.knowledge_version_id = knowledge_node_id
        self.os_type = os_type
        self.module_id = product_module_id

        if os_type == 2:
            self.os_name = "ios"
        else:
            self.os_name = "android"

        self.model_name = "gpt-4o"
        self.reduce_ratio = 1  # 屏幕的缩小比例

        self.temp_dir = os.path.join(CACHE_DIR, str(uuid.uuid4()))
        self.app_info = self._get_app_info(app_name)
        # self.dv = None
        self.dv = Device(DEVICE_ID)

        self.new_mrd_list = None
        self.retrieve_res = None
        self.execute_record = []  # 执行记录
        self.new_mrd_retrieve = None

        self.qamate_case_hander = None
        if self.qamate_case_hander is None:
            self.qamate_case_hander = QamateCaseHandle(os_type)

    def _get_app_info(self, app_name):
        """

        :param app_name:
        :return:
        """
        if app_name in APP_NOTE:
            return {
                "name": app_name,
                "desc": APP_NOTE[app_name]
            }
        else:
            logger.warning("APP_NOTE 没有{}信息".format(app_name))
            return {}

    def mk_dir(self):
        """

        :return:
        """
        if not os.path.exists(self.temp_dir):
            os.mkdir(self.temp_dir)
        logger.info("temp_dir: {}".format(self.temp_dir))

    def _current_image(self, image_path):
        """
        获取当前截图
        :param image_path:
        :return:
        """
        img_url = self.dv.screenshot()['screenshot']
        image = Image(url=img_url)
        image.imgcv = cv2.resize(image.imgcv, (0, 0), fx=self.reduce_ratio, fy=self.reduce_ratio)
        image.save_image(image_path)
        return image

    def _resize_image(self, img_path):
        """
        缩放图片，节省token
        :param img_path:
        :return:
        """
        imgcv = cv2.imread(img_path)
        if self.reduce_ratio != 1:
            self.reduce_ratio = round(500 / imgcv.shape[1], 3)
        resized_img = cv2.resize(imgcv, (0, 0), fx=self.reduce_ratio, fy=self.reduce_ratio)
        cv2.imwrite(img_path, resized_img)

    def _check_popup_action(self, image_path):
        """
        检查是否有弹窗
        :param image_path:
        :param grid_path:
        :return:
        """
        click_num = 0
        while click_num < 5:
            # 根据截图判断是否有弹窗
            img64 = image_to_base64(image_path)
            res = get_popup_from_openapi(img64, [0, self.module_id])

            try:
                popup_flag = res["data"]["hit"]
            except:
                popup_flag = False

            if popup_flag is False:
                return

            click_x = res["data"]["rect"]["x"] + int(res["data"]["rect"]["w"] * 0.5)
            click_y = res["data"]["rect"]["y"] + int(res["data"]["rect"]["h"] * 0.5)

            click_x /= self.dv.size_info["scale"]
            click_y /= self.dv.size_info["scale"]

            self.dv.tap(click_x, click_y)
            time.sleep(5)
            click_num += 1

            # 重新进行截图
            img_url = self.dv.screenshot()['screenshot']
            image = Image(url=img_url)
            image.save_image(image_path)

    def _current_image_record_and_grid(self, image_path, grid_path):
        """
        页面建模 + 画框
        :param image_path: 原图存储地址
        :param grid_path: 带有gird图存储地址
        :return:
        """
        img_url = self.dv.screenshot()['screenshot']
        image = Image(url=img_url)
        image.save_image(image_path)

        # 判断是否有弹窗
        self._check_popup_action(image_path=image_path)

        dom_info, dom, dom_desc, id_ele_map = get_image_with_grid(image_path, grid_path)

        batdom_record_res = {
            "info": dom_info,
            "dom": dom,
            "dom_desc": dom_desc,
            "id_ele_map": id_ele_map,
            "deviceInfo": {
                "screenSize": {
                    "rotation": self.dv.rotation_info,
                    "width": self.dv.size_info["width"],
                    "height": self.dv.size_info["height"],
                    "scale": self.dv.size_info["scale"]
                },
                "screenshot": img_url,
                "type": "ios" if self.os_type == 2 else "android"
            }
        }

        self._resize_image(image_path)
        self._resize_image(grid_path)

        return batdom_record_res

    def _reflect_by_similarity(self, image1, image2):
        """
        通过图片是否相同进行初步判断
        :param image1:
        :param image2:
        :return:
        """

        img1 = cv2.imread(image1)
        img2 = cv2.imread(image2)

        # 顶部去除
        dis = int(img1.shape[0] * 0.04)
        img1 = img1[dis:, ]
        img2 = img2[dis:, ]

        if np.array_equal(img1, img2):
            return True
        return False

    def _reflect_guide_result(self, chat_list, history: list, img_paths: list):
        """

        :param mrd:
        :param history:
        :param img_paths:
        :return:
        """
        reflect_prompt = get_reflect_check(history=history)
        logger.info("reflect_prompt:{}".format(reflect_prompt))
        chat_list = add_user_info_to_chat_list(chat_list=chat_list, prompt_text=reflect_prompt,
                                               image_paths=img_paths, img_quality="auto")
        reflect_res = chat_with_gpt(chat_list=chat_list, model_name=self.model_name, api_key=GENERATE_API_KEY,
                                    response_format='json_object')
        reflect_res = json.loads(reflect_res)
        return reflect_res

    def _amend_lm_out_mrd_item(self, mrd_item):
        """
        对模型输出的 对应测试用例 做一个修正
        :param mrd_item:  模型输出的
        :return:
        """
        if len(self.new_mrd_list) == 1:
            return self.new_mrd_list[0]

        max_sim = 0
        res = mrd_item

        for item in self.new_mrd_list:
            matcher = difflib.SequenceMatcher(None, item, mrd_item)
            similarity = matcher.ratio()
            if similarity > max_sim:
                max_sim = similarity
                res = item
        return res

    def _map_lm_out_mrd_item(self, model_mrd_item):
        """
        输出映射
        :param model_mrd_item:
        :return:
        """

        if len(self.new_mrd_list) == 1:
            return self.new_mrd_list[0]

        if not self.new_mrd_retrieve:
            self.new_mrd_retrieve = KnowledgeSearchForTextList(self.new_mrd_list)

        retrieve_res = self.new_mrd_retrieve.knowledge_search_for_text_list(query=model_mrd_item, top_k=1,
                                                                            score_threshold=-1.0)
        map_res = retrieve_res[0]['document'].page_content

        return map_res

    def case_retrieve_and_run(self):
        """
        前置准备：知识检索+执行 用于冷启
        :return:
        """
        case_retrieve_tool = CaseKnowledgeRetrieve(self.knowledge_version_id, self.os_type,
                                                   recheck=True, score_threshold=0.1)
        retrieve_res = case_retrieve_tool.main(self.mrd_list)
        self.new_mrd_list = retrieve_res['new_mrd_list']
        self.retrieve_res = retrieve_res

        # return

        # 执行前置步骤
        step_infos = []
        logger.info("执行前置case步骤, case_name: {}".format(retrieve_res['knowledge_case']['nodeName']))

        # TODO:暂时先不处理beforeAll和afterAll类型的步骤

        for step_info in retrieve_res['knowledge_case']['extra']['stepInfo'][self.os_name]:
            action_info = step_info['stepInfo']
            run_res = self.dv.run_step_compatibility(action_info, os_type=self.os_type, module_id=self.module_id)
            logger.info("执行结果: {}".format(run_res))
            time.sleep(2)
            step_infos.append(step_info)

        # 执行成功的话，将执行的步骤挂载到手自一体的节点中去
        # 初期挂载逻辑为：mrd_index 对应的节点中挂载对应的全部步骤，其它mrd为空节点
        self.qamate_case_hander.save_pre_operation_nodes(
            # retrieve_res["mrd_list"], retrieve_res["mrd_index"],
            self.mrd_info_list, step_infos
        )

    def _self_reflect(self, idx, chat_list, history):
        """
        自我反思
        :return:
        """
        # 自我反思
        new_img_path = os.path.join(self.temp_dir, "{}.jpg".format(idx + 1))
        self._current_image(new_img_path)
        # img_paths = [self.execute_record[-1]['exec_img_path']]
        # img_paths.append(new_img_path)
        img_paths = [new_img_path]

        # 先通过图片是否相同判断
        # if self._reflect_by_similarity(img_paths[0], img_paths[1]):
        #     reflect_res = {
        #         "result": False,
        #         "reason": "操作后页面未发生变化，你需要反思并在下一次操作中进行修正。如果有弹窗需要先行点除，如果没有可能需要进行后续的操作。"
        #     }
        #     logger.info("反思结果：{}".format(reflect_res))
        #     return reflect_res

        reflect_res = self._reflect_guide_result(chat_list=chat_list, history=history, img_paths=img_paths)

        if reflect_res['result']:
            logger.info("执行结果符合预期")
        else:
            logger.info("Reflect answer is {}, 不符合预期".format(reflect_res['result']))
        return reflect_res

    def _process_summary_by_gpt(self, idx, mrd, history):
        """
        判断 执行进度
        :param mrd:
        :param history:
        :return:
        """
        # new_img_path = os.path.join(self.temp_dir, "{}.jpg".format(idx + 1))
        # if not os.path.isfile(new_img_path):
        #     self._current_image(new_img_path)
        img_paths = [self.execute_record[-1]['exec_img_path']]
        # img_paths.append(new_img_path)

        prompt = process_prompt(mrd, history)
        logger.info("进度总结prompt:{}".format(prompt))

        chat_list = add_user_info_to_chat_list(chat_list=[], prompt_text=prompt,
                                               image_paths=img_paths, img_quality="auto")
        process_res = chat_with_gpt(chat_list=chat_list, model_name=self.model_name, api_key=GENERATE_API_KEY,
                                    response_format='json_object')
        res = json.loads(process_res)
        return res

    def guide_by_mllm(self):
        """
        大模型引导执行
        : return
            res_flag: 1 正常生成；0 判断转为自动化；-1 转化失败
        """

        res_flag = -1

        new_mrd = "->".join(self.new_mrd_list)
        logger.info("需求第一次更新，更新为：{}".format(new_mrd))
        summary = "已经完成 {} 操作".format(self.retrieve_res['knowledge_case']['nodeName'])

        # 转化验证
        optm_res = optimize_manual_case(mrd=new_mrd, app_info=self.app_info, summary=summary)
        latest_mrd = optm_res['optimized_test_cases']
        logger.info("需求第二次更新，更新为：{}".format(latest_mrd))

        # if not optm_res['can_transform']:
        #     res_flag = 0
        #     logger.warning("本条测试用例无法转为自动化用例，原因为：{}".format(optm_res['reason']))
        #     return res_flag

        completed_contents = ""
        history = []
        reflect_flag = True,
        reflect_cont = ""

        for idx in range(10):
            logger.info("=" * 10 + "start {}".format(idx) + "=" * 10)
            raw_img_path = os.path.join(self.temp_dir, "{}.jpg".format(idx))
            grid_img_path = os.path.join(self.temp_dir, "{}_grid.jpg".format(idx))
            exec_img_path = os.path.join(self.temp_dir, "{}_exec.jpg".format(idx))
            cur_batdom_record = self._current_image_record_and_grid(raw_img_path, grid_img_path)

            chat_list = init_app_tester_chat()
            prompt = get_action_prompt(
                mrd=new_mrd, latest_mrd=latest_mrd,
                dom_desc=cur_batdom_record['dom_desc'],
                history=history,
                completed_contents=completed_contents,
                app_info=self.app_info,
                pre_step=self.retrieve_res['knowledge_case']['nodeName']
            )
            logger.info("action_prompt: {}".format(prompt))
            chat_list = add_user_info_to_chat_list(
                chat_list, prompt_text=prompt, image_paths=[grid_img_path], img_quality="auto"
            )
            llm_ans = chat_with_gpt(
                chat_list=chat_list, model_name=self.model_name, temperature=0.2, api_key=GENERATE_API_KEY
            )
            chat_list = add_assistant_info_to_chat_list(chat_list=chat_list, ans_text=llm_ans)

            thought = llm_ans.split("### 思考 ###")[-1].split("### 行动 ###")[0].replace(" ", "").strip()
            action = llm_ans.split("### 行动 ###")[-1].split("### 行动描述 ###")[0].replace(" ", "").strip()
            action_dec = llm_ans.split("### 行动描述 ###")[-1].split("### 总结 ###")[0].replace(" ", "").strip()
            summary = llm_ans.split("### 总结 ###")[-1].split("### 对应测试用例 ###")[0].replace(" ", "").strip()
            mrd_item = llm_ans.split("### 对应测试用例 ###")[-1].replace(" ", "").strip().split("->")[-1]
            # 调整 ### 对应测试用例 ###
            # mrd_item = self._amend_lm_out_mrd_item(mrd_item)
            mrd_item = self._map_lm_out_mrd_item(mrd_item)

            # 操作解析
            action_res = self._mllm_action_parse(action)
            # action_assert = self._mllm_assert_parse(check)
            if action_res['action'] == 'tap' and action_res['element_id'] == -1:
                raise Exception("期望点击的元素未建模出来，元素描述为：{}".format(action_dec))

            if action_res['error']:
                logger.info("舍弃本次结果，重新执行本次 Action Agent")
                continue

            history.append({
                "thought": thought,
                "action": action,
                "action_dec": action_dec,
                "summary": summary,
                "mrd_item": mrd_item
            })

            # 执行图片可视化
            if action_res['action'] not in ('finsh', 'unknown'):
                if action_res['action'] == "swipe":
                    draw_arrow(image_path=raw_img_path, direction=action_res['direction'],
                               output_path=exec_img_path, reduce_size=self.reduce_ratio)
                elif action_res['action'] == "assert_not_exist" or (
                        action_res['action'] == "assert_exist" and action_res['element_id'] == -1):
                    exec_img_path = raw_img_path
                else:
                    ele_idx = action_res['element_id']
                    # bat_ele = cur_batdom_record['dom']['children'][ele_idx]
                    bat_ele = cur_batdom_record['id_ele_map'][ele_idx]
                    exe_ele_rect = {
                        "x": int(bat_ele['rect']['x'] * self.reduce_ratio),
                        "y": int(bat_ele['rect']['y'] * self.reduce_ratio),
                        "w": int(bat_ele['rect']['w'] * self.reduce_ratio),
                        "h": int(bat_ele['rect']['h'] * self.reduce_ratio)
                    }
                    draw_rect(image_path=raw_img_path, rect=exe_ele_rect, output_path=exec_img_path)
            else:
                exec_img_path = raw_img_path

            self.execute_record.append(
                ExecuteInfo(raw_img_path=raw_img_path, grid_img_path=grid_img_path,
                            batdom_record=cur_batdom_record['dom'],
                            prompt=prompt, llm_ans=llm_ans, action_res=action_res, exec_img_path=exec_img_path,
                            history=history).__dict__)

            if "finsh" in action or "unknown" in action:
                logger.info("=====测试完成=====：{}".format("->".join(self.new_mrd_list)))
                res_flag = 1
                break

            # 转为qamate 执行用例步骤，并执行
            self._action_res_2_qamate_action_info(
                mrd_item, self.mrd_info_list, cur_batdom_record, action_res, action_dec
            )

            # 自我反思
            if "assert" in action:
                reflect_flag = True,
                reflect_cont = ""
            else:
                reflect_res = self._self_reflect(idx=idx, chat_list=chat_list, history=history)
                reflect_flag = reflect_res["result"]
                reflect_cont = reflect_res["reason"]
            history[-1]['reflect_flag'] = reflect_flag
            history[-1]['reflect_cont'] = reflect_cont

            # if reflect_flag:
                # 是否完成 和 内容总结
                # process_res = process_summary_by_wenxin(new_mrd, history)
            process_res = self._process_summary_by_gpt(idx, mrd=new_mrd, history=history)
            finsh_flag = process_res['result']
            completed_contents = process_res['completed_contents']
            if finsh_flag:
                logger.info("=====测试完成=====：{}".format("->".join(self.new_mrd_list)))
                res_flag = 1
                break

        return res_flag

    def _mllm_action_parse(self, act: str):
        """
        模型输出解析
        :param action:
        :return:
        """

        action_res = {
            "error": False,
            "action": "",
            "element_id": -1,
            "input_text": "",
            "direction": "",
            "content": ""
        }

        act_name = act.split("(")[0]
        action_res['action'] = act_name

        if act_name == "tap":
            params = re.findall(r"tap\((.*?)\)", act)[0]
            element_id = int(params.strip())
            action_res['element_id'] = element_id
        elif act_name == "input":
            params = re.findall(r"input\((.*?)\)", act)[0]
            element_id = int(params.split(",")[0].strip())
            input_text = params.split(",")[1].strip().replace('"', '')
            action_res['element_id'] = element_id
            action_res['input_text'] = input_text
        elif act_name == "swipe":
            params = re.findall(r'swipe\("([^"]+)"\)', act)[0]
            swip_direction = params.strip()
            action_res['direction'] = swip_direction
        elif act_name == "long_press":
            params = re.findall(r"long_press\((.*?)\)", act)[0]
            element_id = int(params.strip())
            action_res['element_id'] = element_id
        elif "finsh" in act:
            params = re.findall(r'finsh\("([^"]+)"\)', act)[0]
            content = params.strip()
            logger.warning("执行结束,{}".format(content))
        elif act_name == "assert_exist":
            params = re.findall(r"assert_exist\((.*?)\)", act)[0]
            element_id = int(params.split(",")[0].strip())
            content = params.split(",")[1].strip().replace('"', '')
            action_res['element_id'] = element_id
            action_res['content'] = content
        elif act_name == "assert_not_exist":
            params = re.findall(r'assert_not_exist\("([^"]+)"\)', act)[0]
            action_res['content'] = params.strip()
        else:
            # raise AgentException(err_code=AgentErrCode.UNKNOWN_ACTION_TYPE)
            logger.error("预期外的操作类型：{}".format(act_name))
            action_res['error'] = True

        return action_res

    def _mllm_assert_parse(self, check):
        """

        :param check:
        :return:
        """
        assert_res = {
            "error": False,
            "is_not": True,  # 校验是否存在
            "element_id": -1,  # 元素序号
            "content": ""  # 校验描述
        }

        if "assert_exist" in check:
            assert_res["is_not"] = True
            params = re.findall(r"assert_exist\((.*?)\)", check)[0]
            assert_res['element_id'] = int(params.split(",")[0].strip())
            assert_res['content'] = params.split(",")[1].strip().replace('"', '')
        elif "assert_not_exist" in check:
            assert_res["is_not"] = False
            params = re.findall(r"assert_not_exist\((.*?)\)", check)[0]
            assert_res['element_id'] = int(params.split(",")[0].strip())
            assert_res['content'] = params.split(",")[1].strip().replace('"', '')
        else:
            # raise AgentException(err_code=AgentErrCode.UNKNOWN_CHECK_TYPE)
            logger.error("预期外的验证类型：{}".format(check))
            assert_res['error'] = True

        return assert_res

    def _action_res_2_qamate_action_info(self, mrd_item, mrd_info_list, cur_batdom_record, action_res, action_dec):
        """
        check 点解析
        :param check:
        :return:
        """
        # 转为 qamate 的数据格式
        action_info_list = self.qamate_case_hander.save_auto_generate_nodes(
            mrd_item, mrd_info_list, cur_batdom_record, action_res, action_dec
        )

        # 并执行
        for action_info in action_info_list:
            run_res = self.dv.run_step_compatibility(action_info["stepInfo"],
                                                     os_type=self.os_type,
                                                     module_id=self.module_id)
            logger.info("执行结果: {}".format(run_res))
            time.sleep(5)

    def main(self):
        """
        :return:
        """
        # 前置操作
        self.mk_dir()
        self.case_retrieve_and_run()

        # 执行
        res_flag = self.guide_by_mllm()
        return res_flag


if __name__ == '__main__':
    os_type = 2
    qamate_case_hander = QamateCaseHandle(os_type)
    mrd_list1, mrd_info_list1 = qamate_case_hander.get_manual_case_by_case_root_id(
        17462666, product_id=21, goal_labels=["可进行自动化用例生成且规范化"]
    )
    mrd_list2, mrd_info_list2 = qamate_case_hander.get_manual_case_by_case_root_id(
        17454344, product_id=21, goal_labels=["可进行自动化用例生成且规范化"]
    )
    mrd_list = mrd_list2 + mrd_list1
    mrd_info_list = mrd_info_list2 + mrd_info_list1

    print("共{}条case待生成".format(len(mrd_list)))
    # mrd_list = random.sample(mrd_list, k=10)

    for no in range(len(mrd_list)):
        # if no > 3:
        #     continue
        try:
            mrd = mrd_list[no][1:]
            mrd_info = mrd_info_list[no][1:]
            logger.info("----------------------{}: mrd 执行: {}----------------------".format(no, mrd))
            t = AppTestAgent(
                mrd_list=mrd,
                mrd_info=mrd_info,
                knowledge_node_id=14819335,
                os_type=2,
                app_name="NewApp",
                product_module_id=21
            ).main()
        except:
            logger.error("mrd 执行异常: {}".format(mrd))
            logger.error("错误信息: {}".format(traceback.format_exc()))
