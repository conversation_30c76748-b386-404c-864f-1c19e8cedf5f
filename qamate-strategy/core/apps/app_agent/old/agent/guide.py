#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@File    : guide.py
<AUTHOR> <PERSON><PERSON><PERSON><PERSON>(<EMAIL>)
@Time    : 2024/8/8 17:07
@Desc    : 
"""
import sys

sys.path.append("/Users/<USER>/Workspace/Project/lazyOne/qamate-strategy/core")

import os
import json
import uuid
import re
import logging.handlers
import traceback
import difflib

from apps.app_agent.utils.pandaGPT import chat_gpt_with_image, add_user_info_to_chat_list, \
    add_assistant_info_to_chat_list, chat_gpt_4_vision_answer_by_image_path
from apps.app_agent.old.agent.image_process import get_image_with_grid
from apps.app_agent.utils.draw import draw_arrow, draw_rect
from apps.app_agent.multi_agent.preroute_agent.case_search import CaseKnowledgeRetrieve
from apps.app_agent.old.agent.chat import init_app_tester_chat, get_action_and_check_prompt, get_reflect_check
from apps.app_agent.old.agent.check import judge_manual_2_auto_case
from apps.app_agent.utils.exception import AgentErrCode, AgentException
from basics.util.image import Image
from apps.auto_case_transform.device import Device
from apps.app_agent.conf.device import DEVICE_ID
from basics.util import logger
from basics.util.config import CACHE_DIR
from apps.app_agent.old.agent.save import AICaseSaveHandler
from apps.app_agent.old.agent.tool import get_mrd_list_by_caseid
from basics.util.config import PY_LOG_DIR

format = logging.Formatter(
    '[%(process)s][%(asctime)s][%(levelname)s] %(message)s')
# logger.setLevel(logging.INFO)
logger.setLevel(logging.DEBUG)

file = logging.handlers.TimedRotatingFileHandler(
    filename=os.path.join(PY_LOG_DIR, 'lazyGuide.log'), encoding='utf-8',
    when='D', backupCount=3
)
file.setFormatter(format)
logger.addHandler(file)


class ExecuteInfo(object):
    """
    """

    def __init__(self, raw_img_path: str, grid_img_path: str, batdom_record: dict, prompt: str, llm_ans: str,
                 action_res: dict, exec_img_path: str, history):
        """

        :param raw_img_path:
        :param grid_img_path:
        :param batdom_record:
        :param prompt:
        :param llm_ans:
        """
        self.raw_img_path = raw_img_path
        self.grid_img_path = grid_img_path
        self.batdom_record = batdom_record
        self.prompt = prompt
        self.llm_ans = llm_ans
        self.action_res = action_res
        self.exec_img_path = exec_img_path
        self.history = history


class AppTestAgent(object):
    """

    """

    def __init__(self, mrd_list, knowledge_version_id, os_type, case_save_version_id):
        """

        :param mrd_list:  例如：["首页及对话", "对话页", "助手tab和历史tab可点击切换、可手势横划切换"]
        :param knowledge_version_id:
        :param os_type:
        """
        self.mrd_list = mrd_list
        self.knowledge_version_id = knowledge_version_id
        self.os_type = os_type

        self.temp_dir = os.path.join(CACHE_DIR, str(uuid.uuid4()))

        self.dv = Device(DEVICE_ID)

        self.new_mrd_list = None
        self.retrieve_res = None
        self.execute_record = []  # 执行记录
        self.batdom_list = []

        self.ai_case_save_handler = AICaseSaveHandler(case_save_version_id, os_type)

    def mk_dir(self):
        """

        :return:
        """
        if not os.path.exists(self.temp_dir):
            os.mkdir(self.temp_dir)
        logger.info("temp_dir: {}".format(self.temp_dir))

    def _current_image(self, image_path):
        """
        获取当前截图
        :param image_path:
        :return:
        """
        img_url = self.dv.screenshot()['screenshot']
        image = Image(url=img_url)
        image.save_image(image_path)
        return image

    def _current_image_record_and_grid(self, image_path, grid_path):
        """
        页面建模 + 画框
        :param image_path: 原图存储地址
        :param grid_path: 带有gird图存储地址
        :return:
        """
        img_url = self.dv.screenshot()['screenshot']
        image = Image(url=img_url)
        image.save_image(image_path)

        dom_info, dom, dom_desc = get_image_with_grid(image_path, grid_path)
        batdom_record_res = {
            "info": dom_info,
            "dom": dom,
            "dom_desc": dom_desc,
            "deviceInfo": {
                "screenSize": {
                    "rotation": self.dv.rotation_info,
                    "width": self.dv.size_info["width"],
                    "height": self.dv.size_info["height"],
                    "scale": self.dv.size_info["scale"]
                },
                "screenshot": img_url,
                "type": "ios" if self.os_type == 2 else "android"
            }
        }

        return batdom_record_res

    def _reflect_guide_result(self, mrd: str, history: list, img_paths: list):
        """

        :param mrd:
        :param history:
        :param img_paths:
        :return:
        """
        reflect_prompt = get_reflect_check(instruction=mrd, history=history)
        reflect_res = chat_gpt_4_vision_answer_by_image_path(reflect_prompt, img_paths, response_format='json_object')
        reflect_res = json.loads(reflect_res)
        return reflect_res

    def _amend_lm_out_mrd_item(self, mrd_item):
        """
        对模型输出的 对应测试用例 做一个修正
        :param mrd_item:  模型输出的
        :return:
        """
        if len(self.new_mrd_list) == 1:
            return self.new_mrd_list[0]

        max_sim = 0
        res = mrd_item

        for item in self.new_mrd_list:
            matcher = difflib.SequenceMatcher(None, item, mrd_item)
            similarity = matcher.ratio()
            if similarity > max_sim:
                max_sim = similarity
                res = item
        return res

    def case_retrieve_and_run(self):
        """
        前置准备：知识检索+执行 用于冷启
        :return:
        """
        case_retrieve_tool = CaseKnowledgeRetrieve(self.knowledge_version_id, self.os_type)
        retrieve_res = case_retrieve_tool.main(self.mrd_list)
        self.new_mrd_list = retrieve_res['new_mrd_list']
        self.retrieve_res = retrieve_res

        # 执行前置步骤
        action_infos = []
        logger.info("执行前置case步骤, case_name: {}".format(retrieve_res['knowledge_case']['nodeText']))
        for step_info in retrieve_res['knowledge_case']['step']:
            action_info = json.loads(step_info['actionInfo'])
            self.dv.run_step(action_info)
            action_infos.append(action_info)

        # 执行成功的话，将执行的步骤挂载到手自一体的节点中去
        # 初期挂载逻辑为：mrd_index 对应的节点中挂载对应的全部步骤，其它mrd为空节点
        self.ai_case_save_handler.save_pre_operation_nodes(
            retrieve_res["mrd_list"], retrieve_res["mrd_index"], action_infos
        )

    def guide_by_mllm(self):
        """
        大模型引导执行
        : return
            res_flag: 1 正常生成；0 判断转为自动化；-1 转化失败
        """

        res_flag = -1

        # 感觉这里需要改下，按照步骤粒度对齐 —— 喆
        self.ai_case_save_handler.init_mrd_list(
            self.new_mrd_list
        )

        new_mrd = "->".join(self.new_mrd_list)
        logger.info("需求更新为：{}".format(new_mrd))

        # 转化验证
        can_tras_res = judge_manual_2_auto_case(new_mrd)
        logger.info("mrd{}，能否转化：{}，原因是：{}".format(self.new_mrd_list, can_tras_res['result'],
                                                         can_tras_res['reason']))
        if not can_tras_res['result']:
            res_flag = 0
            return res_flag

        # summary = ""
        summary = "已经完成 {} 操作".format(self.retrieve_res['knowledge_case']['nodeText'])
        chat_list = init_app_tester_chat()

        history = []
        error_flag = False

        for idx in range(10):
            print("=" * 10, "start {}".format(idx), "=" * 10)
            raw_img_path = os.path.join(self.temp_dir, "{}.jpg".format(idx))
            grid_img_path = os.path.join(self.temp_dir, "{}_grid.jpg".format(idx))
            exec_img_path = os.path.join(self.temp_dir, "{}_exec.jpg".format(idx))
            cur_batdom_record = self._current_image_record_and_grid(raw_img_path, grid_img_path)

            prompt = get_action_and_check_prompt(new_mrd, summary, dom_desc=cur_batdom_record['dom_desc'],
                                                 error_flag=error_flag)
            chat_list = add_user_info_to_chat_list(chat_list, prompt_text=prompt, image_paths=[grid_img_path],
                                                   img_quality="auto")
            llm_ans = chat_gpt_with_image(chat_list=chat_list, temperature=0.1)
            chat_list = add_assistant_info_to_chat_list(chat_list=chat_list, ans_text=llm_ans)

            thought = llm_ans.split("### 思考 ###")[-1].split("### 行动 ###")[0].replace(" ", "").strip()
            action = llm_ans.split("### 行动 ###")[-1].split("### 操作描述 ###")[0].replace(" ", "").strip()
            action_dec = llm_ans.split("### 操作描述 ###")[-1].split("### 校验 ###")[0].replace(" ", "").strip()
            check = llm_ans.split("### 校验 ###")[-1].split("### 总结 ###")[0].replace(" ", "").strip()
            summary = llm_ans.split("### 总结 ###")[-1].split("### 对应测试用例 ###")[0].replace(" ", "").strip()
            mrd_item = llm_ans.split("### 对应测试用例 ###")[-1].replace(" ", "").strip()
            # 调整 ### 对应测试用例 ###
            mrd_item = self._amend_lm_out_mrd_item(mrd_item)

            history.append({
                "thought": thought,
                "action": action,
                "action_dec": action_dec,
                "check": check,
                "summary": summary,
                "mrd_item": mrd_item
            })

            # 操作解析
            action_res = self._mllm_action_parse(action)
            action_assert = self._mllm_assert_parse(check)

            # 执行图片可视化
            if action_res['action']:
                if action_res['action'] == "swipe":
                    draw_arrow(image_path=raw_img_path, direction=action_res['direction'], output_path=exec_img_path)
                else:
                    ele_idx = action_res['element_id']
                    bat_ele = cur_batdom_record['dom']['children'][ele_idx]
                    draw_rect(image_path=raw_img_path, rect=bat_ele['rect'], output_path=exec_img_path)

            if "finsh" in action:
                exec_img_path = raw_img_path

            self.execute_record.append(
                ExecuteInfo(raw_img_path=raw_img_path, grid_img_path=grid_img_path,
                            batdom_record=cur_batdom_record['dom'],
                            prompt=prompt, llm_ans=llm_ans, action_res=action_res, exec_img_path=exec_img_path,
                            history=history).__dict__)

            if "finsh" in action:
                print("=" * 10, "DONE", "=" * 10)
                res_flag = 1
                break

            # 转为qamate 执行用例步骤，并执行
            self._action_res_2_qamate_action_info(
                mrd_item, cur_batdom_record, action_res, action_assert, action_dec
            )

            # 自我反思
            new_img_path = os.path.join(self.temp_dir, "{}.jpg".format(idx + 1))
            self._current_image(new_img_path)
            img_paths = []
            for item in self.execute_record:
                img_paths.append(item['exec_img_path'])
            img_paths.append(new_img_path)
            reflect_res = self._reflect_guide_result(mrd=new_mrd, history=history, img_paths=img_paths)

            # if reflect_res['result'] == "A":
            #     logger.info("Reflect answer is A, 符合预期")
            #     error_flag = False
            # elif reflect_res['result'] == "B":
            #     logger.info("Reflect answer is B, 不符合预期, 应返回上一步操作")
            #     error_flag = True
            #     # TODO：返回上一步
            # else:
            #     logger.info("Reflect answer is {}, 不符合预期".format(reflect_res['result']))
            #     error_flag = True
        return res_flag

    def _mllm_action_parse(self, act: str):
        """
        模型输出解析
        :param action:
        :return:
        """

        action_res = {
            "action": "",
            "element_id": -1,
            "input_text": "",
            "direction": ""
        }

        act_name = act.split("(")[0]
        action_res['action'] = act_name

        if act_name == "tap":
            params = re.findall(r"tap\((.*?)\)", act)[0]
            element_id = int(params.strip())
            action_res['element_id'] = element_id
        elif act_name == "input":
            params = re.findall(r"input\((.*?)\)", act)[0]
            element_id = int(params.split(",")[0].strip())
            input_text = params.split(",")[1].strip().replace('"', '')
            action_res['element_id'] = element_id
            action_res['input_text'] = input_text
        elif act_name == "swipe":
            params = re.findall(r'swipe\("([^"]+)"\)', act)[0]
            swip_direction = params.strip()
            action_res['direction'] = swip_direction
        elif act_name == "long_press":
            params = re.findall(r"long_press\((.*?)", act)[0]
            element_id = int(params.strip())
            action_res['element_id'] = element_id
        elif act_name == "assert_exist" or act_name == "assert_not_exist":
            element_id = -1
        elif "finsh" in act:
            logger.info("执行结束")
        else:
            raise AgentException(err_code=AgentErrCode.UNKNOWN_ACTION_TYPE)

        return action_res

    def _mllm_assert_parse(self, check):
        """

        :param check:
        :return:
        """
        assert_res = {
            "is_not": True,  # 校验是否存在
            "element_id": -1,  # 元素序号
            "content": ""  # 校验内容
        }

        if "assert_exist" in check:
            assert_res["is_not"] = True
            params = re.findall(r"assert_exist\((.*?)\)", check)[0]
            assert_res['element_id'] = int(params.split(",")[0].strip())
            assert_res['content'] = params.split(",")[1].strip().replace('"', '')
        elif "assert_not_exist" in check:
            assert_res["is_not"] = False
            params = re.findall(r"assert_not_exist\((.*?)\)", check)[0]
            assert_res['element_id'] = int(params.split(",")[0].strip())
            assert_res['content'] = params.split(",")[1].strip().replace('"', '')
        else:
            raise AgentException(err_code=AgentErrCode.UNKNOWN_CHECK_TYPE)

        return assert_res

    def _action_res_2_qamate_action_info(self, mrd_item, cur_batdom_record, action_res, action_assert, action_dec):
        """
        check 点解析
        :param check:
        :return:
        """
        # 转为 qamate 的数据格式
        action_info_list = self.ai_case_save_handler.save_auto_generate_nodes(
            mrd_item, cur_batdom_record, action_res, action_assert, action_dec
        )

        # 并执行
        for action_info in action_info_list:
            self.dv.run_step(action_info)

    def main(self):
        """
        :return:
        """
        # 前置操作
        self.mk_dir()
        self.case_retrieve_and_run()

        # 执行
        res_flag = self.guide_by_mllm()
        return res_flag


if __name__ == '__main__':
    mrd_list = [
        # ["首页及对话   （普通对话+语音电话对话）（97）", "对话页", "助手tab和历史tab可点击切换、可手势横划切换"],
        ["首页及对话（普通对话+语音电话对话）（97）", "对话页", "欢迎语逻辑调整（9条）", "功能",
         "地图相关的历史数据", "输入去故宫怎么走", "正常展示欢迎语和地图历史内容"]
    ]
    mrd_list = get_mrd_list_by_caseid(70720)
    no = -1
    exe_num = 0
    for mrd in mrd_list:
        try:
            no += 1
            # if no <= 1:
            #     continue
            exe_num += 1
            logger.info("{}: mrd 执行: {}".format(no, mrd))
            # if exe_num >= 10:
                # break
            # t = AppTestAgent(mrd, 502, 2, 517).main()
            t = AppTestAgent(mrd, 502, 2, 909).main()
        except:
            logger.error("mrd 执行异常: {}".format(mrd))
            logger.error("错误信息: {}".format(traceback.format_exc()))
        break
