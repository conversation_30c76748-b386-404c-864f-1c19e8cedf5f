#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@File    : try.py
<AUTHOR> <PERSON><PERSON><PERSON><PERSON>(<EMAIL>)
@Time    : 2024/8/15 18:48
@Desc    : 
"""

import json
import difflib
import cv2

from apps.app_agent.utils.wenxin_util import WenXinUtils
from apps.app_agent.agent.chat import can_manual_case_2_auto_case

a = ['live', 'add', 'camera', 'headset', 'baidu', 'back_home', 'video', 'mine', 'microphone', 'menu_shu',
     'menu_heng', 'menu', 'arrow_left', 'arrow_right', 'arrow_up', 'arrow_down', 'edit', 'zan',
     'comment', 'collect', 'transmit', 'share', 'find', 'download', 'delete', 'dislike', 'setting',
     'close', 'mute', 'home', 'wechat', 'friend_circle', 'tip_off', 'scan', 'button_switch', 'play', 'alarm',
     'like', 'refresh', 'red_bag', 'trumpet', 'weibo', 'AI', 'more', 'solid_gesture', 'lock', 'unlock',
     'camera_switch',
     'play_prev', 'play_next', 'filter', 'gift', 'previous', 'next', 'pause', 'shopping_cart', 'landscape',
     'fullscreen']

b = [
    'button_switch', 'solid_gesture', 'lock', 'camera_switch', 'play_prev',
    'play_next', 'filter', 'gift', 'next', 'delete', 'pause', 'shopping_cart',
    'landscape', 'AI', 'live', 'previous', 'weibo', 'fullscreen',
    'friend_circle', 'tip_off', 'wechat', 'more', 'scan',
    'mute', 'alarm', 'setting', 'trumpet', 'download', 'back_home',
    'dislike', 'red_bag', 'arrow_up', 'refresh', 'headset', 'camera', 'video',
    'share', 'like', 'mine', 'menu_heng', 'edit', 'home', 'microphone', 'transmit',
    'menu', 'baidu', 'find', 'play', 'menu_shu', 'collect', 'arrow_down', 'add',
    'zan', 'comment', 'close', 'arrow_right', 'arrow_left', "unlock"
]

# set1 = set(a)
# set2 = set(b)
#
# # 使用对称差集找出不同元素
# difference = set1.symmetric_difference(set2)
#
# # 将结果转换回列表（如果需要的话）
# difference_list = list(difference)
# print(len(a), len(b))
# print(len(set1), len(set2))
# print(difference_list)
#
# m = dict()
# for i in range(43):
#     print("name:{}, old_id:{}, new_id:{}".format(a[i], i, b.index(a[i])))
#     m[i] = b.index(a[i])
#
# print(m)

