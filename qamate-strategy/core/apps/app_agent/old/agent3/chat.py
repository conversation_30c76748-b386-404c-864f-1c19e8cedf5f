#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@File    : prompt.py
<AUTHOR> <PERSON><PERSON><PERSON><PERSON>(<EMAIL>)
@Time    : 2024/8/19 15:10
@Desc    : 
"""

from typing import List


def init_app_tester_chat():
    """

    :return:
    """
    chat_list = []
    system_prompt = ("你是一个具有App自动化测试经验的AI手机操作助手，你需要帮我操作手机并且完成页面的功能点验证。\n"
                     "你拥有基本的自动化测试常识，例如：在输入操作后需要跟发送操作；设备执行过程出现意料之外的弹窗、提示框、权限弹窗等需要先点除；"
                     "菜单或设置页面可能需要滑动查找某些功能等。")
    chat_list.append(["system", [{"type": "text", "text": system_prompt}]])
    return chat_list


def recheck_retrieve_prompt(mrd, new_case):
    """
    检索检查
    :return:
    """
    prompt = f"""
&& 角色 &&
你是一名有丰富App自动化测试经验的AI助手。

&& 任务 &&
我正在根据一条手工测试用例编写自动化测试用例，我对原始用例进行了部分修改，你需要判断『修改后的用例』是否正确。

&& 手工用例 &&
## 原始手工用例：【{mrd}】
需注意，忽略原始用例中与自动化执行无关的描述或汇总性内容，专注于测试目标。

## 修改后的用例：【{new_case}】

&& 判断标准 &&
判断是否正确的标准是：修改后的用例，用例路径完整，没有改变原始测试用例的自动化路径，能够满足后续手工用例的正常生成自动化用例。"""
    output = """
&& 输出格式 &&
以json格式输出，输出固定的包括以下两部分，无需输出其他内容：
{
    "result": bool // 一个布尔值，表示是否正确
    "reason": string // 原因
}"""
    return prompt + output


def amend_manual_case(mrd, app_info, summary):
    """
    修正手工用例
    :param mrd:
    :param app_info:
    :param summary:
    :return:
    """
    if app_info:
        app_info_str = "&& 背景 &&\n当前正在测试的App为{}，简介：{}".format(app_info['name'], app_info['desc'])
    else:
        app_info_str = ""

    prompt = f"""
&& 角色 &&
你是一个具有丰富App自动化测试经验的测试智能助手

{app_info_str}

&& 任务 &&
当前有一份人工编写的手工测试用例，你需要判断该测试用例是否可以转为通过手机设备执行的自动化测试用例，如果可以转化，将原始的用例进行优化，要求输出简洁、逻辑清晰。
转化用例需注意：舍弃掉原始用例中与自动化执行无关的汇总性记录或表达。
原始的测试用例是脑图格式，为了方便你理解，我做了格式转化，按层次结构使用'->'链接，测试用例为：{mrd}

&& 已完成内容 &&
手机设备已经完成：{summary}
优化的用例，从完成内容往后生成，无需包含已完成内容。

&& 判断标准 &&
这里给出能否转化为自动化测试用例的部分标准参考：
1. 前置条件：前置条件过多或过于苛刻的不适合转化，例如：无网络、弱网条件下、未登录、历史看过的情况下、历史收藏过的情况下、历史点赞过的情况下、需要命中**实验等。
2. 数据依赖：测试用例依赖于特定数据、动态数据或语音输入等，不适合转化。
3. 复杂交互：如果完成测试的操作过于复杂不适合转化。
4. 短暂出现信息：我们的当前的自动化执行速度较慢，无法捕捉到短时间停留的信息，例如：页面toast。因此也不适合转为自动化。

&& 用例优化要求 &&
1. 舍弃掉原始用例中与自动化执行无关的汇总性记录或表达。
2. 优化后的用例要合乎逻辑，输出简洁、逻辑清晰，是一串关键的执行链路。
"""
    output = """
&& 输出要求 &&
你的输出固定的包括以下三部分，无需输出其他内容，以json格式输出：
{
    "can_transform": bool // 一个布尔值，表示是否可以转化
    "optimized_test_cases": string // 优化后的测试用例步骤，也通过"->"链接。仅能输出一条用例。
    "reason": string // 给能够转化和优化测试用例步骤的具体原因
}"""

    return prompt + output


def get_action_desc_prompt(latest_mrd: str, app_info: dict, history: List[dict],
                           completed_contents="", pre_step=""):
    """

    :param mrd:
    :param latest_mrd:
    :param app_info:
    :param history:
    :param completed_contents:
    :param pre_step:
    :return:
    """

    # App简介
    if app_info:
        app_info_str = "当前正在测试的App为{}，{}".format(app_info['name'], app_info['desc'])
    else:
        app_info_str = ""

    # 操作历史
    if len(history) == 0:
        operation_history_str = ""
        if pre_step:
            operation_history_str += "前置已完成操作：{}\n前置步骤均已执行，后续步骤均在前置操作后顺序执行。\n".format(
                pre_step)
    else:
        operation_history_str = "## 操作历史 ## \n为了完成用户指令的要求，已经执行了一系列操作。这些操作信息如下：\n"
        if pre_step:
            operation_history_str += "前置已完成操作：{}\n前置步骤均已执行，后续步骤均在前置操作后顺序执行。\n".format(
                pre_step)
        for idx, item in enumerate(history):
            operation_history_str += "步骤 {}: [实际操作：{}\n操作后的思考：{}]\n".format(idx + 1, item['action_desc'],
                                                                                        item['reflect_cont'])
        operation_history_str += "请注意：操作历史是在此之前已经执行过的操作，无需重复执行!!\n"

    # 进度
    if not completed_contents:
        completed_contents_str = ""
    else:
        completed_contents_str = "## 进度思考及反思 ##\n根据历史操作，进度思考：{}\n".format(completed_contents)

    prompt = f"""## 背景 ##
你需要协助完成一项手机App测试任务。{app_info_str}
你需要根据本份测试用例、当前页面截图和操作历史帮助我操作手机来到达特定的页面完成测试过程并完成测试，

## 测试用例 ##
测试用例为：【{latest_mrd}】。

## 截图信息 ##
图片是当前手机所在页面的截图。
{operation_history_str}
{completed_contents_str}

## 任务说明 ##
基于当前截图、历史操作和目标任务，给出合适的操作，继续执行任务。优化后的测试用例为大致路径，具体要如何操作以当前页面截图为主，目标是达成最终原始用例的测试目标。
你可以进行的操作有：点击、输入、滑动、长按、校验。对于具体的操作控件要给出在页面中的大体位置。
点击：表示你要点击的元素，例如：点击页面左下角的点赞按钮。
输入：表示你要在哪里输入什么内容，例如：在页面下方的输入框中输入"你好"。
滑动：表示滑动页面的手势，例如：由下往上滑动页面(达到 页面下滑 的效果),由右向左滑动(达到 页面右滑 的效果)。
长按：表示你要长按的元素，例如：长按页面左下角的图片。
校验：表示你要校验什么，例如：验证菜单中存在点赞按钮 或 验证当前视频正常播放 或 验证页面中不存在点赞按钮。

## 注意事项 ##
1. 一定需要结合历史操作，可能需要交互很多次才能达成最终的目标，本次对话是其中一次交互。
2. 如果原始测试用例中存在明确的操作步骤，按顺序参考执行; 如果原始用例中明确用""或『』等标注的关键字，生成步骤时需注意生成的正确性。
3. 忽略原始用例中与自动化执行无关的描述或汇总性内容，专注于测试目标。
4. 操作执行的过程中可能出现很多意料之外的弹窗、提示框、权限弹窗等，你需要先进行点除，然后进行后续的操作。
5. 自动化测试最后一步通常是校验步骤，如果是校验页面可以用页面中的某个固定元素代替。
6. 注意操作的页面的前景和后景，例如：弹窗，下拉框等是前景内容，大多数操作需要在前景框中操作。
7. 在一些上下滑动的场景，当页面没有你需要的目标时，可能需要滑动页面，查找元素。例如：对话页、设置页、列表页等。
8. 给出的操作一定是基于当前截图的可以进行的操作，不要给出基于当前截图无法完成的动作。

## 输出要求 ##
你的输出固定的包括以下四个部分，格式如下：
### 思考 ###
基于 截图、历史操作、测试用例 分析当前所在的页面和基于当前页面需要完成什么操作来达到目的。
### 行动描述 ###
简要描述本次行动中的内容。例如：在主对话页，点击**（或者校验了***；或向*方向滑动了）。每次仅能生成单个动作。
### 总结 ###
将你过去的行动和你本次的行动总结成一两句话。不要在总结中包含数字标签。
### 对应测试用例 ###
上述操作或验证是对应到[测试用例]哪个节点，只能输出一个节点。例如原始输出的测试用例为 A -> B -> C, 上述操作是针对完成 B 描述的，则输出 B。"""
    return prompt


def get_action_prompt(dom_desc: str, action_desc: str):
    """

    :param dom_desc:
    :param action_desc:
    :return:
    """
    # 页面元素
    if dom_desc:
        dom_desc_str = "为了帮助你更好地理解这张截图的内容，我们通过其他能力得到每个框选元素的补充信息供你参考，标签是元素数字标签与图片中的数字标签相对应，类型表示元素类型，描述是元素的描述或文字内容\n"
        dom_desc_str += dom_desc
        dom_desc_str += "\n这些信息不一定准确，仅供参考，一切以截图为准!\n"
    else:
        dom_desc_str = ""

    prompt = f"""
&& 你的角色 &&
你是一个具有App自动化测试经验的AI手机操作助手

&& 你的任务 &&
你需要根据【动作描述】给出具体的操作函数和参数内容。如果想要点击的UI元素在页面中不存在或没有分配标签，则id输出 -1。
图片是当前手机所在页面的截图，截图中的交互式UI元素都用数字标签标记(从0开始)，每个交互元素用红色框框选出，数字标签都位于元素的左上角。
{dom_desc_str}

&& 操作函数 &&
你可以使用以下操作函数继续执行任务：
1. tap(element: int)
这个函数用于点击智能手机屏幕上显示的UI元素。
例如：tap(5)，表示点击标有数字5的UI元素。

2. input(element: int, text_input: str)
这个函数用于文本输入。需确保input函数中的element对应的是输入框或搜索框元素。
例如：input(10, "Hello, world!")，表示在10元素的输入框中上输入"Hello, world"。

3. swipe(direction: str)
这个函数用于滑动智能手机屏幕，表示手指在屏幕上的滑动轨迹。默认为滑动半个屏幕的距离。
"direction" 是表示四个方向之一的字符串：up, down, left, right。"direction" 必须用双引号括起来。
up表示由下往上滑动，达到 向下翻页 的效果；down表示由上往下滑动，达到 向上翻页 的效果（或查看历史信息）；
left表示由右向左滑动，达到 向右切换页面 的效果；right表示由左向右滑动，达到 向左切换页面 的效果。
例如：swipe("up")，表示将页面向上滑动。

4. long_press(element: int)
这个函数用于长按智能手机屏幕上显示的UI元素。
例如：long_press(8), 表示长按标有数字8的UI元素。

5. assert_exist(element: int, content: str)
根据测试用例，需要在本页面需要校验『存在』的元素，只校验测试用例中提到的必要的校验点。也可以是校验某项功能或特殊场景等，例如：校验**功能正常，此时element置为-1。
"element" 明确要在此页面校验存在的元素id。如果需要验证**页面，可以用验证页面中稳定的关键字替代，通常是页面title、标签等。
"content" 是校验目的，如果element != -1，需要体现出具体的元素信息。
例如：assert_exist(4, "验证存在点赞按钮")，需要验证当前页面需要元素4存在。assert_exist(-1, "校验视频正常播放")，表示验证当前视频正常播放。

6. assert_not_exist(content: str)
根据测试用例，需要在下一个页面校验『不存在』。只校验测试用例中提到的必要的校验点。
"content"是校验目的。
例如：assert_not_exist("验证页面不存在弹窗")，需要校验下一个页面不应该存在弹窗。

&& 动作描述 &&
{action_desc}
"""
    output = """&& 输出 &&
结果以json的格式输出：
{
    "content": str  // 给出具体的操作函数和参数内容，每次仅能生成单个操作函数。
}
"""
    return prompt + output


def get_action_prompt_v1(latest_mrd: str, app_info: dict,
                         dom_desc: str, history: List[dict], completed_contents="",
                         reflect_flag=True, reflect_content="", pre_step=""):
    """

    :param instruction:
    :param summary:
    :param dom_desc:
    :param history: 包含 思考+动作
    :return:
    """

    # App简介
    if app_info:
        app_info_str = "当前正在测试的App为{}，{}".format(app_info['name'], app_info['desc'])
    else:
        app_info_str = ""

    # 页面元素
    if dom_desc:
        dom_desc_str = "为了帮助你更好地理解这张截图的内容，我们通过其他能力得到每个框选元素的补充信息供你参考，标签是元素数字标签与图片中的数字标签相对应，类型表示元素类型，描述是元素的描述或文字内容\n"
        dom_desc_str += dom_desc
        dom_desc_str += "\n这些信息不一定准确，仅供参考，你需要结合截图来理解。\n"
    else:
        dom_desc_str = ""

    # 操作历史
    if len(history) == 0:
        operation_history_str = ""
    else:
        operation_history_str = "## 操作历史 ## \n为了完成用户指令的要求，已经执行了一系列操作。这些操作信息如下：\n"
        if pre_step:
            operation_history_str += "前置已完成操作：{}\n前置步骤均已执行，后续步骤均在前置操作后顺序执行。\n".format(
                pre_step)
        for idx, item in enumerate(history):
            operation_history_str += "步骤 {}: [实际操作：{}\n操作后的思考：{}]\n".format(idx + 1, item['action_desc'],
                                                                                        item['reflect_cont'])
        operation_history_str += "请注意：操作历史是在此之前已经执行过的操作，无需重复执行!!\n"

    # 进度
    if not completed_contents:
        completed_contents_str = ""
    else:
        completed_contents_str = "## 进度思考及反思 ##\n根据历史操作，进度思考：{}\n".format(completed_contents)

    # 错误反馈
    # if not reflect_flag:
    #     reflect_str = "最近一步操作反思：{}\n关于你的上一步操作反思仅供参考，如果操作错误需在这次操作中进行修正".format(
    #         reflect_content)
    #     completed_contents_str += reflect_str

    prompt = f"""## 背景 ##
你需要协助完成一项手机App测试任务。{app_info_str}
你需要根据本份测试用例、当前页面截图和操作历史帮助我操作手机来到达特定的页面完成测试过程并完成测试，

## 测试用例 ##
测试用例为：【{latest_mrd}】。

## 截图信息 ##
图片是当前手机所在页面的截图。截图中的交互式UI元素都用数字标签标记(从0开始)，每个交互元素用红色框框选出，数字标签都位于元素的左上角。
{dom_desc_str}
{operation_history_str}
{completed_contents_str}

## 操作函数 ##
你可以使用以下操作函数继续执行任务：
1. tap(element: int)
这个函数用于点击智能手机屏幕上显示的UI元素。
"element" 是分配给智能手机屏幕上显示的UI元素的数字标签（可参考文字中页面元素的补充信息）；如果想要点击的UI元素在页面中不存在或没有分配标签，则element=-1。
例如：tap(5)，表示点击标有数字5的UI元素。

2. input(element: int, text_input: str)
这个函数用于文本输入。需确保input函数中的element对应的是输入框或搜索框元素。
"element" 同上。
"text_input" 是你想输入的字符串，必须用双引号括起来。
例如：input(10, "Hello, world!")，表示在10元素的输入框中上输入"Hello, world"。

3. swipe(direction: str)
这个函数用于滑动智能手机屏幕，表示手指在屏幕上的滑动轨迹。默认为滑动半个屏幕的距离。
"direction" 是表示四个方向之一的字符串：up, down, left, right。"direction" 必须用双引号括起来。
up表示由下往上滑动，达到 向下翻页 的效果；down表示由上往下滑动，达到 向上翻页 的效果（或查看历史信息）；
left表示由右向左滑动，达到 向右切换页面 的效果；right表示由左向右滑动，达到 向左切换页面 的效果。
例如：swipe("up")，表示将页面向上滑动。

4. long_press(element: int)
这个函数用于长按智能手机屏幕上显示的UI元素。
"element" 同上。
例如：long_press(8), 表示长按标有数字8的UI元素。

5. assert_exist(element: int, content: str)
根据测试用例，需要在本页面需要校验『存在』的元素，只校验测试用例中提到的必要的校验点。也可以是校验某项功能或特殊场景等，例如：校验**功能正常，此时element置为-1。
"element" 明确要在此页面校验存在的元素id。如果需要验证**页面，可以用验证页面中稳定的关键字替代，通常是页面title、标签等。
"content" 是校验目的，如果element != -1，需要体现出具体的元素信息。
例如：assert_exist(4, "验证存在点赞按钮")，需要验证当前页面需要元素4存在。assert_exist(-1, "校验视频正常播放")，表示验证当前视频正常播放。

6. assert_not_exist(content: str)
根据测试用例，需要在下一个页面校验『不存在』。只校验测试用例中提到的必要的校验点。
"content"是校验目的。
例如：assert_not_exist("验证页面不存在弹窗")，需要校验下一个页面不应该存在弹窗。

7. finsh(reason: str)
表示已经完成全部测试; 或基于当前页面，无法给出具体的操作，或者多次操作都无法达到预期目标。
"reason": 原因

## 任务说明 ##
基于当前截图、历史操作和目标任务，选择合适的操作函数继续执行任务。测试用例为大致路径，具体要如何操作以当前页面截图为主，目标是达成最终原始用例的测试目标。

## 注意事项 ##
1. 一定需要结合历史操作，可能需要交互很多次才能达成最终的目标，本次对话是其中一次交互。
2. 如果原始测试用例中存在明确的操作步骤，按顺序参考执行; 如果原始用例中明确用""或『』等标注的关键字，生成步骤时需注意生成的正确性。
3. 忽略原始用例中与自动化执行无关的描述或汇总性内容，专注于测试目标。
4. 确保函数中输出的element_id的正确性，当页面中有多个可备选的element时，选取最为稳定(不经常变化，最好为图标)的element来保证自动化测试的稳定性。
5. 操作执行的过程中可能出现很多意料之外的弹窗、提示框、权限弹窗等，你需要先进行点除，然后进行后续的操作。
6. 自动化测试最后一步通常是校验步骤，如果是校验页面可以用页面中的某个固定元素代替。
7. 注意操作的页面的前景和后景，例如：弹窗，下拉框等是前景内容，大多数操作需要在前景框中操作。
8. 在一些上下滑动的场景，当页面没有你需要的目标时，可能需要滑动页面，查找元素。例如：对话页、设置页、列表页等。
9. 给出的操作一定是基于当前截图的可以进行的操作

## 输出要求 ##
你的输出固定的包括以下五个部分，格式如下：
### 思考 ###
基于 截图、历史操作、测试用例 分析当前所在的页面和基于当前页面需要完成什么操作来达到目的。
### 行动 ###
调用适当的操作函数及参数继续任务。行动只能从上述『操作函数』中的7个选择一个，不要包含其他内容。每次仅能生成单个动作。
### 行动描述 ###
简要描述本次行动中的内容。例如：在主对话页，点击了**（或者校验了***；或向*方向滑动了）。不要包含数字标签。
### 总结 ###
将你过去的行动和你本次的行动总结成一两句话。不要在总结中包含数字标签。
### 对应测试用例 ###
上述操作或验证是对应到[测试用例]哪个节点，只能输出一个节点。例如原始输出的测试用例为 A -> B -> C, 上述操作是针对完成 B 描述的，则输出 B。"""
    return prompt


def get_task_decompose_prompt(mrd):
    """
    需求任务拆解
    :param mrd:
    :return:
    """
    decomposer_prompt = f"""
&& 角色 &&
你是一个具有丰富App自动化测试经验的测试智能助手

&& 任务 &&
当前有一份人工编写的测试用例，你需要判断该测试用例是否可以转为通过手机设备执行的自动化测试用例，如果可以转化，需要将测试任务分解为交互和检查两个正交的子任务。
原始人工编写的测试用例是脑图格式，为了方便理解，我将它转了为markdown格式，测试用例为：{mrd}

&& 输出格式 &&
你的输出固定的包括以下三部分，格式如下：
### 能否转化 ###
判断是否可以转为通过手机设备执行的自动化测试用例，输出只能是 True 或者 False
### 执行任务 ###
引导设备执行的任务，如果存在多个执行任务，用中文中的"；"分隔
### 校验任务 ###
需要进行校验的任务，如果存在多个执行任务，用中文中的"；"分隔

&& 样例 &&
输入：
# 发送内容为"你好"的评论并点赞
## 检查是否正确显示
## 点赞按钮是否变为红色

输出：
### 能否转化 ###
True
### 执行任务 ###
发送内容为"你好"的评论并点赞
### 校验任务 ###
校验评论发送后是否正确显示；校验点赞后点赞按钮是否变为红色
"""
    return decomposer_prompt


def get_reflect_check(history: list):
    """
    反思过程
    :param history:
    :return:
    """
    prompt = f"""
我根据你给出的行动操作了手机，当前截图为操作后的手机截图。
&& 任务 &&
你现在需要根究操作后的页面截图 判断 你在上一步给出的操作是否正确，如果不正确给出修正操作提示。目的是正确的达成测试目的，少走弯路。

&& 需注意 &&
1. 实际操作仅会是单步操作，仅需关注单步操作是否符合预期，保证整体的测试执行路径正确。
2. 如果页面无变化，可能是元素无法点击或已经位于当前页面，请仔细检查操作思考和实际操作，给出正确操作建议，不要轻易给出重复操作或重试的建议。
"""
    output = """
&& 输出 &&
输出应该包含两个部分，result 和 reason，以json的格式输出。
{
    "result": bool  // 结果, True表示正确，False表示错误
    "reason": string  // 原因，做出此判断原因，如果不符合预期给出正确的具体操作提示
}
"""
    return prompt + output


def get_reflect_check_v2(history: list):
    """
    反思过程
    :param history:
    :return:
    """
    operation_des = ""
    item = history[-1]
    operation_des += "[操作前的思考：{}；\n实际操作：{}]\n".format(item['thought'], item['action_desc'])

    prompt = f"""
这两张图像是本次操作前后的两张手机截图。
第一张截图中如果有红框，则表示操作的点击位置或校验位置；
如果有箭头，则表示页面滑动操作中手指在设备的滑动轨迹，左箭头表示由右向左滑动（达到 向右切换页面 的效果），上箭头表示由下往上滑动，达到 向下翻页 的效果。

&& 当前操作 &&
{operation_des}
&& 响应要求 &&
1. 现在你需要根据历史执行记录和截图判断【实际操作】的结果是否符合预期。无需太过死板，注意语义相似性。
true: “操作动作”的结果符合我对“操作思考”的预期。
false: 不符合预期。
2. 给出做出此判断原因，如果不符合预期给出正确的具体操作提示。
**关于操作提示**：尝试结合当前页面、最终的测试目标，检查当前思考和操作的问题，给出修正后的操作提示。

&& 需注意 &&
1. 实际操作仅会是单步操作，仅需关注单步操作是否符合预期，保证整体的测试执行路径正确。
2. 如果页面无变化，可能是元素无法点击或已经位于当前页面，请仔细检查操作思考和实际操作，给出正确操作建议，不要轻易给出重复操作或重试的建议。
"""
    output = """
&& 输出 &&
输出应该包含两个部分，result 和 reason，以json的格式输出。
{
    "result": bool  // 结果
    "reason": string  // 原因
}
"""
    return prompt + output


def process_prompt(mrd, history):
    """
    过程总结prompt
    :param mrd:
    :param history:
    :return:
    """
    operation_history_str = "&& 历史操作 && \n 为了完成用户指令的要求，已经执行了一系列操作。这些操作信息如下：\n"
    for idx, item in enumerate(history):
        operation_history_str += "步骤 {}: [具体操作：{}；操作后的反思:{}]\n".format(idx + 1, item['action_desc'],
                                                                                   item['reflect_cont'])
    operation_history_str += "每一个步骤仅包含一步操作。\n"

    prompt = f"""&& 背景 &&
你是一个具有App自动化测试经验的AI手机操作助手。
你正在操作手机完成一份测试用例，测试用例为：{mrd}

&& 任务 &&
图片内容仅用于帮助你理解，关注【历史操作】中的文字内容，你的任务有两部分。
1：判断【历史操作】中的【具体步骤】是否完成了整个测试过程(达成了最终测试目标)。
2：基于【历史操作】对当前已完成内容的总体总结，总结的内容需要与测试用例相关。例如：已经完成***，但尚未完成**。

&& 注意 &&
【历史操作】中【具体步骤】的最后一步步骤需要是校验**或验证**，才能判断为完成了整个测试过程！！
例如：测试用例为验证功能X，
1. 历史步骤为：点击A -> 点击B -> 点击C，此条case尚未完成测试。
2. 历史步骤为：点击A -> 点击B -> 点击C -> 校验功能点X，此条case完成了整个测试过程，因为具体操作的最后一步是验证步骤。

{operation_history_str}
&& 图片信息 &&
图片是『最后一步操作』的手机截图。
第一张截图中如果有红框，则表示操作的点击位置或校验位置；
如果有箭头，则表示页面滑动操作中手指在设备的滑动轨迹，左箭头表示由右向左滑动（达到 向右切换页面 的效果），上箭头表示由下往上滑动，达到 向下翻页 的效果。

&& 提示 &&
你拥有基本的自动化测试常识，例如：
1. 判断测试是否完成：自动化测试最后一步需要是『校验』步骤（校验元素存在或不存在），如果是校验页面可以用页面中的某个固定元素代替。
2. 操作：在输入操作后需要跟发送等操作。等
"""
    output = """
&& 输出格式 &&
你的输出固定为以下两部分，不要输出其他内容!! 确保以合法的json格式输出：
{
    "result": bool // 一个布尔值，表示历史操作是否全部执行完成，执行完成的最后一步需为校验步骤
    "completed_contents": string  //基于 历史操作 对当前已完成内容的总体总结
}
"""
    return prompt + output


if __name__ == '__main__':
    ele_des = """{
  "0": {"type": "icon", "description": "返回控件"}
  "1": {"type": "word", "description": "我的"}
...
}"""
    his = [
        {
            "thought": "thought1",
            "action_dec": "action_dec1",
            "summary": "summary1"
        },
        {
            "thought": "thought2",
            "action_dec": "action_dec2",
            "summary": "summary2"
        },
    ]
    res = process_prompt("Instruction", his)
    print(res)
