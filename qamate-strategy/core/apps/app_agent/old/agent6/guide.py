#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@File    : guide.py   自然语言驱动设备执行
<AUTHOR> <PERSON><PERSON><PERSON><PERSON>(renya<PERSON><PERSON>@baidu.com)
@Time    : 2024/8/8 17:07
@Desc    : 
"""
import sys

sys.path.append("/Users/<USER>/Workspace/Project/lazyOne/qamate-strategy/core")

import os
import json
import uuid
import re
import logging.handlers
import difflib

from apps.app_agent.utils.pandaGPT import chat_gpt_with_image, add_user_info_to_chat_list, \
    add_assistant_info_to_chat_list
from apps.app_agent.old.agent.image_process import get_image_with_grid
from apps.app_agent.utils.draw import draw_arrow, draw_rect
from apps.app_agent.multi_agent.preroute_agent.case_search import CaseKnowledgeRetrieve
from apps.app_agent.old.agent6.chat import init_app_action_chat, get_action_prompt, get_reflect_check
from apps.app_agent.utils.exception import AgentErrCode, AgentException
from basics.util.image import Image
from apps.auto_case_transform.device import Device
from apps.app_agent.conf.device import DEVICE_ID
from basics.util import logger
from basics.util.config import CACHE_DIR
from apps.app_agent.old.agent.save import AICaseSaveHandler
from basics.util.config import PY_LOG_DIR

format = logging.Formatter(
    '[%(process)s][%(asctime)s][%(levelname)s] %(message)s')
# logger.setLevel(logging.INFO)
logger.setLevel(logging.DEBUG)

file = logging.handlers.TimedRotatingFileHandler(
    filename=os.path.join(PY_LOG_DIR, 'lazyGuide.log'), encoding='utf-8',
    when='D', backupCount=3
)
file.setFormatter(format)
logger.addHandler(file)


class ExecuteInfo(object):
    """
    """

    def __init__(self, raw_img_path: str, grid_img_path: str, batdom_record: dict, prompt: str, llm_ans: str,
                 action_res: dict, exec_img_path: str, history):
        """

        :param raw_img_path:
        :param grid_img_path:
        :param batdom_record:
        :param prompt:
        :param llm_ans:
        """
        self.raw_img_path = raw_img_path
        self.grid_img_path = grid_img_path
        self.batdom_record = batdom_record
        self.prompt = prompt
        self.llm_ans = llm_ans
        self.action_res = action_res
        self.exec_img_path = exec_img_path
        self.history = history


class AppTestAgent(object):
    """

    """

    def __init__(self, mrd, knowledge_version_id, os_type, case_save_version_id):
        """

        :param mrd: str
        :param knowledge_version_id:
        :param os_type:
        """
        self.mrd = mrd
        self.knowledge_version_id = knowledge_version_id
        self.os_type = os_type

        self.temp_dir = os.path.join(CACHE_DIR, str(uuid.uuid4()))

        self.dv = Device(DEVICE_ID)

        self.retrieve_res = None
        self.execute_record = []  # 执行记录
        self.batdom_list = []

        self.ai_case_save_handler = AICaseSaveHandler(case_save_version_id, os_type)

    def mk_dir(self):
        """

        :return:
        """
        if not os.path.exists(self.temp_dir):
            os.mkdir(self.temp_dir)
        logger.info("temp_dir: {}".format(self.temp_dir))

    def _current_image(self, image_path):
        """
        获取当前截图
        :param image_path:
        :return:
        """
        img_url = self.dv.screenshot()['screenshot']
        image = Image(url=img_url)
        image.save_image(image_path)
        return image

    def _current_image_record_and_grid(self, image_path, grid_path):
        """
        页面建模 + 画框
        :param image_path: 原图存储地址
        :param grid_path: 带有gird图存储地址
        :return:
        """
        img_url = self.dv.screenshot()['screenshot']
        image = Image(url=img_url)
        image.save_image(image_path)

        dom_info, dom, dom_desc = get_image_with_grid(image_path, grid_path)
        batdom_record_res = {
            "info": dom_info,
            "dom": dom,
            "dom_desc": dom_desc,
            "deviceInfo": {
                "screenSize": {
                    "rotation": self.dv.rotation_info,
                    "width": self.dv.size_info["width"],
                    "height": self.dv.size_info["height"],
                    "scale": self.dv.size_info["scale"]
                },
                "screenshot": img_url,
                "type": "ios" if self.os_type == 2 else "android"
            }
        }

        return batdom_record_res

    def _reflect_guide_result(self, instruction: str, chat_list: list, history: list, img_paths: list):
        """

        :param mrd:
        :param history:
        :param img_paths:
        :return:
        """
        reflect_prompt = get_reflect_check(instruction=instruction, history=history)
        chat_list = add_user_info_to_chat_list(chat_list=chat_list, prompt_text=reflect_prompt, image_paths=img_paths)
        reflect_res = chat_gpt_with_image(chat_list=chat_list, response_format='json_object')
        reflect_res = json.loads(reflect_res)
        return reflect_res

    def _amend_lm_out_mrd_item(self, mrd_item):
        """
        对模型输出的 对应测试用例 做一个修正
        :param mrd_item:  模型输出的
        :return:
        """
        if len(self.new_mrd_list) == 1:
            return self.new_mrd_list[0]

        max_sim = 0
        res = mrd_item

        for item in self.new_mrd_list:
            matcher = difflib.SequenceMatcher(None, item, mrd_item)
            similarity = matcher.ratio()
            if similarity > max_sim:
                max_sim = similarity
                res = item
        return res

    def case_retrieve_and_run(self):
        """
        前置准备：知识检索+执行 用于冷启
        :return:
        """
        case_retrieve_tool = CaseKnowledgeRetrieve(self.knowledge_version_id, self.os_type)
        retrieve_res = case_retrieve_tool.main(self.mrd_list)
        self.new_mrd_list = retrieve_res['new_mrd_list']
        self.retrieve_res = retrieve_res

        # 执行前置步骤
        action_infos = []
        logger.info("执行前置case步骤, case_name: {}".format(retrieve_res['knowledge_case']['nodeText']))
        for step_info in retrieve_res['knowledge_case']['step']:
            action_info = json.loads(step_info['actionInfo'])
            self.dv.run_step(action_info)
            action_infos.append(action_info)

        # 执行成功的话，将执行的步骤挂载到手自一体的节点中去
        # 初期挂载逻辑为：mrd_index 对应的节点中挂载对应的全部步骤，其它mrd为空节点
        self.ai_case_save_handler.save_pre_operation_nodes(
            retrieve_res["mrd_list"], retrieve_res["mrd_index"], action_infos
        )

    def _self_reflect(self, idx, chat_list, history):
        """
        自我反思
        :return:
        """
        # 自我反思
        new_img_path = os.path.join(self.temp_dir, "{}.jpg".format(idx + 1))
        self._current_image(new_img_path)
        img_paths = [self.execute_record[-1]['exec_img_path']]
        img_paths.append(new_img_path)
        reflect_res = self._reflect_guide_result(instruction=mrd, chat_list=chat_list, history=history,
                                                 img_paths=img_paths)

        if reflect_res['result']:
            logger.info("执行结果符合预期")
            error_flag = False
        else:
            logger.info("Reflect answer is {}, 不符合预期".format(reflect_res['result']))
            error_flag = True
        return error_flag

    def guide_by_mllm(self):
        """
        大模型引导执行
        : return
            res_flag: 1 正常生成；0 判断转为自动化；-1 转化失败
        """

        res_flag = -1

        new_mrd = self.mrd
        logger.info("需求更新为：{}".format(new_mrd))

        summary = ""

        history = []
        error_flag = False

        for idx in range(10):
            print("=" * 10, "start {}".format(idx), "=" * 10)
            raw_img_path = os.path.join(self.temp_dir, "{}.jpg".format(idx))
            grid_img_path = os.path.join(self.temp_dir, "{}_grid.jpg".format(idx))
            exec_img_path = os.path.join(self.temp_dir, "{}_exec.jpg".format(idx))
            cur_batdom_record = self._current_image_record_and_grid(raw_img_path, grid_img_path)
            chat_list = init_app_action_chat()

            prompt = get_action_prompt(new_mrd, summary, dom_desc=cur_batdom_record['dom_desc'], history=history,
                                       error_flag=error_flag)
            chat_list = add_user_info_to_chat_list(chat_list, prompt_text=prompt, image_paths=[grid_img_path],
                                                   img_quality="auto")
            llm_ans = chat_gpt_with_image(chat_list=chat_list, temperature=0.1)
            chat_list = add_assistant_info_to_chat_list(chat_list=chat_list, ans_text=llm_ans)

            thought = llm_ans.split("### 思考 ###")[-1].split("### 行动 ###")[0].replace(" ", "").strip()
            action = llm_ans.split("### 行动 ###")[-1].split("### 操作描述 ###")[0].replace(" ", "").strip()
            action_dec = llm_ans.split("### 操作描述 ###")[-1].split("### 总结 ###")[0].replace(" ", "").strip()
            summary = llm_ans.split("### 总结 ###")[-1].replace(" ", "").strip().split("->")[-1]

            history.append({
                "thought": thought,
                "action": action,
                "action_dec": action_dec,
                "summary": summary
            })

            # 操作解析
            action_res = self._mllm_action_parse(action)

            # 执行图片可视化
            if action_res['action'] not in ('finsh', 'unknown'):
                if action_res['action'] == "swipe":
                    draw_arrow(image_path=raw_img_path, direction=action_res['direction'], output_path=exec_img_path)
                else:
                    ele_idx = action_res['element_id']
                    bat_ele = cur_batdom_record['dom']['children'][ele_idx]
                    draw_rect(image_path=raw_img_path, rect=bat_ele['rect'], output_path=exec_img_path)
            else:
                exec_img_path = raw_img_path

            self.execute_record.append(
                ExecuteInfo(raw_img_path=raw_img_path, grid_img_path=grid_img_path,
                            batdom_record=cur_batdom_record['dom'],
                            prompt=prompt, llm_ans=llm_ans, action_res=action_res, exec_img_path=exec_img_path,
                            history=history).__dict__)

            if "finsh" in action or "unknown" in action:
                print("=" * 10, "DONE", "=" * 10)
                res_flag = 1
                break

            # 自我反思
            error_flag = self._self_reflect(idx=idx, chat_list=chat_list, history=history)

            # 转为qamate 执行用例步骤，并执行
            # self._action_res_2_qamate_action_info(
            #     mrd_item, cur_batdom_record, action_res, action_assert, action_dec
            # )

        return res_flag

    def _mllm_action_parse(self, act: str):
        """
        模型输出解析
        :param action:
        :return:
        """

        action_res = {
            "action": "",
            "element_id": -1,
            "input_text": "",
            "direction": ""
        }

        act_name = act.split("(")[0]
        action_res['action'] = act_name

        if act_name == "tap":
            params = re.findall(r"tap\((.*?)\)", act)[0]
            element_id = int(params.strip())
            action_res['element_id'] = element_id
        elif act_name == "input":
            params = re.findall(r"input\((.*?)\)", act)[0]
            element_id = int(params.split(",")[0].strip())
            input_text = params.split(",")[1].strip().replace('"', '')
            action_res['element_id'] = element_id
            action_res['input_text'] = input_text
        elif act_name == "swipe":
            params = re.findall(r'swipe\("([^"]+)"\)', act)[0]
            swip_direction = params.strip()
            action_res['direction'] = swip_direction
        elif act_name == "long_press":
            params = re.findall(r"long_press\((.*?)", act)[0]
            element_id = int(params.strip())
            action_res['element_id'] = element_id
        elif act_name == "assert_exist" or act_name == "assert_not_exist":
            element_id = -1
        elif "unknown" in act:
            logger.warning("{}".format(act))
        elif "finsh" in act:
            logger.warning("执行结束")
        else:
            raise AgentException(err_code=AgentErrCode.UNKNOWN_ACTION_TYPE)

        return action_res

    def _mllm_assert_parse(self, check):
        """

        :param check:
        :return:
        """
        assert_res = {
            "is_not": True,  # 校验是否存在
            "element_id": -1,  # 元素序号
            "content": ""  # 校验内容
        }

        if "assert_exist" in check:
            assert_res["is_not"] = True
            params = re.findall(r"assert_exist\((.*?)\)", check)[0]
            assert_res['element_id'] = int(params.split(",")[0].strip())
            assert_res['content'] = params.split(",")[1].strip().replace('"', '')
        elif "assert_not_exist" in check:
            assert_res["is_not"] = False
            params = re.findall(r"assert_not_exist\((.*?)\)", check)[0]
            assert_res['element_id'] = int(params.split(",")[0].strip())
            assert_res['content'] = params.split(",")[1].strip().replace('"', '')
        else:
            raise AgentException(err_code=AgentErrCode.UNKNOWN_CHECK_TYPE)

        return assert_res

    def _action_res_2_qamate_action_info(self, mrd_item, cur_batdom_record, action_res, action_assert, action_dec):
        """
        check 点解析
        :param check:
        :return:
        """
        # 转为 qamate 的数据格式
        action_info_list = self.ai_case_save_handler.save_auto_generate_nodes(
            mrd_item, cur_batdom_record, action_res, action_assert, action_dec
        )

        # 并执行
        for action_info in action_info_list:
            self.dv.run_step(action_info)

    def main(self):
        """
        :return:
        """
        # 前置操作
        self.mk_dir()

        # 执行
        res_flag = self.guide_by_mllm()
        return res_flag


if __name__ == '__main__':
    mrd = "打开麦当劳App帮我点一份麦辣鸡翅，在店里吃"
    # summary = "进入到了麦当劳的微信小程序"

    t = AppTestAgent(mrd, 502, 2, 918).main()
