#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@File    : prompt.py
<AUTHOR> <PERSON><PERSON><PERSON><PERSON>(renya<PERSON><PERSON>@baidu.com)
@Time    : 2024/8/9 15:48
@Desc    : 
"""

from typing import List


def init_app_action_chat():
    """

    :return:
    """
    chat_list = []
    system_prompt = "你是一个AI手机操作助手。你需要帮我操作手机来完成用户的指令。"
    chat_list.append(["system", [{"type": "text", "text": system_prompt}]])
    return chat_list


def get_action_prompt(instruction, summary, dom_desc, history: List[dict], error_flag=False):
    """

    :param instruction:
    :param summary:
    :param dom_desc:
    :param error_flag:
    :return:
    """
    # 页面元素
    if dom_desc:
        dom_desc_str = "为了帮助你更好地理解这张截图的内容，我们通过其他能力得到每个框选元素的补充信息，这些信息id与标签数字是对应的，type表示元素类型，desc是元素的描述\n"
        dom_desc_str += dom_desc
        dom_desc_str += "\n请注意，这些信息不一定准确。你需要结合截图来理解。"
    else:
        dom_desc_str = ""

    # 操作历史
    if len(history) == 0:
        operation_history_str = ""
    else:
        operation_history_str = "## 操作历史 ## \n 为了完成用户指令的要求，你已经执行了一系列操作。这些操作如下：\n"
        for idx, item in enumerate(history):
            operation_history_str += "步骤 {}: [思考：{}；操作动作：{}]\n".format(idx + 1, item['thought'],
                                                                               item['action_dec'])

    # 错误反馈
    if not error_flag:
        reflect_str = ""
    else:
        reflect_str = "## 上次操作反思 ##\n你的上一步操作并没有达到你的操作目的，你需要反思并在这次操作中进行修正"

    prompt = f"""
## 背景 ##
这是当前手机所在页面的截图。用户的指令是：{instruction}

## 截图信息 ##
本轮对话的图片是最新的手机截图。截图中的交互式UI元素都用数字标签标记，标签从0开始，每个交互元素用红色框框选出，数字标签都位于元素的左上角。
为了帮助你更好地理解这张截图的内容，我们提前将每个元素进行了语义描述，供你参考，内容如下:{dom_desc_str}
{operation_history_str}
{reflect_str}

## 操作函数 ##
1. tap(element: int)
这个函数用于点击智能手机屏幕上显示的UI元素。
"element" 是分配给智能手机屏幕上显示的UI元素的数字标签；如果想要点击的UI元素在页面中存在，但是没有分配标签，则element=-1；如果要点击的UI元素在页面中不存在，则element=-2。
例如：tap(5)，表示点击标有数字5的UI元素。

2. input(element: int, text_input: str)
这个函数用于在输入字段/框中插入文本输入。
"element" 同上。
"text_input" 是你想插入的字符串，必须用双引号括起来。
例如：input(10, "Hello, world!")，表示在10元素上输入"Hello, world"。通常对搜索框、输入框等元素才使用此函数。

3. swipe(direction: str)
这个函数用于滑动智能手机屏幕上显示的UI元素，通常是滚动视图或滑动条。默认为滑动半个屏幕的距离。
"direction" 是表示四个方向之一的字符串：up, down, left, right。"direction" 必须用双引号括起来。
例如：swipe("up")，表示将页面向上滑动。

4. long_press(element: int)
这个函数用于长按智能手机屏幕上显示的UI元素。
"element" 同上。
例如：long_press(8), 表示长按标有数字8的UI元素。

5. finsh()
如果你认为已经到达需求描述待测试，不需要进一步操作，你可以选择此操作来终止操作过程。

6. unknown(reason: str)
这个函数表示基于当前截图，无法给出具体的操作。
"reason": 解释原因

## 任务 ##
现在，根据我给出的信息，你需要思考并调用完成任务所需的函数。操作执行的过程中可能出现很多意料之外的弹窗、提示框等，你需要先进行点除，然后进行后续的操作。你过去执行此任务的操作总结为：{summary}

## 输出要求 ##
你的输出固定的包括以下四个部分，格式如下：
### 思考 ###
思考在之前操作中已经完成的要求以及在下一次操作中需要完成的要求。
### 行动 ###
调用具有正确参数的函数以继续任务，行动只能从上述『操作函数』中选择一个。直接给出函数及参数，不要包含其他内容。
### 操作描述 ###
请根据你的思考生成一个简短的自然语言描述，用于解释操作中的动作。对行动的内容，生成一个简短的自然语言描述
### 总结 ###
将你过去的行动和你最新的行动总结成一两句话。不要在总结中包含数字标签。

可能需要交互很多次才能达成最终的目标，但是你一次只能采取一个行动，所以请直接调用函数。element的数字使用最新截图的信息。
"""
    return prompt


def get_reflect_check(instruction, history: list):
    """
    反思过程
    :param history:
    :return:
    """
    operation_des = ""
    item = history[-1]
    operation_des += "[思考：{}；操作动作：{}]\n".format(item['thought'], item['action_dec'])

    thought = history[-1]['thought']
    action = history[-1]['action']

    prompt = f"""
&& 角色 &&
你是一个具有丰富App自动化测试经验的测试智能助手，目的是驱动手机完成用户的指令。

&& 背景 &&
我正在实时操作手机，这些图像是某次操作前后的两张手机截图。你需要检查下我最新的操作执行是否正确，来保证正确完成用户的指令。我会提供给你，为了完成用户指令，历史的操作、思考以及操作截图。

&& 当前操作 &&
用户的指令是：{instruction}。在完成指令要求的过程中，在手机上执行了一次操作。以下是此次操作的详细信息：
操作思考：{thought}
操作动作：{action}

&& 注意 &&
当页面中存在预期外的弹窗、提示框等需要先点除弹窗；

&& 响应要求 &&
现在你需要根据历史执行记录和截图判断【当前操作】的结果是否符合【操作思考】的预期？
true: “操作动作”的结果符合我对“操作思考”的预期。
false: 不符合预期。
"""
    output = """
&& 输出 &&
输出应该包含两个部分，result 和 reason，以json的格式输出。
{
    "result": bool  // 结果
    "reason": string  // 原因
}
"""
    return prompt + output


def get_reflect_check_v2(instruction, history: list):
    """
    反思过程, 用于单轮交互
    :param history:
    :return:
    """
    operation_des = ""
    item = history[-1]
    operation_des += "[思考：{}；操作动作：{}]\n".format(item['thought'], item['action_dec'])

    thought = history[-1]['thought']
    action = history[-1]['action']

    prompt = f"""
这些图像是本次操作前后的两张手机截图。截图中的每一步操作位置都使用红框标注，箭头表示页面滑动操作中手指在设备的滑动轨迹。
你需要检查下最新的操作执行是否正确，来保证正确完成用户的指令。我会提供给你，为了完成用户指令，历史的操作、思考以及操作截图。

&& 当前操作 &&
用户的指令是：{instruction}。在完成指令要求的过程中，在手机上执行了一次操作。以下是此次操作的详细信息：
操作思考：{thought}
操作动作：{action}

&& 注意 &&
当页面中存在预期外的弹窗、提示框等需要先点除弹窗；

&& 响应要求 &&
现在你需要根据历史执行记录和截图判断【当前操作】的结果是否符合【操作思考】的预期？
true: “操作动作”的结果符合我对“操作思考”的预期。
false: 不符合预期。
"""
    output = """
&& 输出 &&
输出应该包含两个部分，result 和 reason，以json的格式输出。
{
    "result": bool  // 结果
    "reason": string  // 原因
}
"""
    return prompt + output
