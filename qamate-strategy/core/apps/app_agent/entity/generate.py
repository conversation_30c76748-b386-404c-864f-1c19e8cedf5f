#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@File    : generate.py
<AUTHOR> <PERSON><PERSON><PERSON><PERSON>(renyan<PERSON>@baidu.com)
@Time    : 2024/11/15 15:13
@Desc    : 
"""


class GuideRes(object):
    """

    """
    def __init__(self, thought, action, action_desc, summary, mrd_item):
        """

        :param thought:
        :param action:
        :param action_desc:
        :param summary:
        :param mrd_item:
        """
        self.thought = thought
        self.action = action
        self.action_desc = action_desc
        self.summary = summary
        self.mrd_item = mrd_item

    def to_dict(self):
        """
        转为列表
        :return:
        """
        return self.__dict__


class ExecuteInfo(object):
    """
    执行记录
    """

    def __init__(self, raw_img_path: str, grid_img_path: str, batdom_record: dict,
                 action_res: dict, exec_img_path: str, guide_res_info: dict, reflect_res_info: dict,
                 process_res_info: dict, step_info: dict):
        """

        :param raw_img_path:
        :param grid_img_path:
        :param batdom_record:
        :param action_res:
        :param exec_img_path:
        :param guide_res_info:
        :param reflect_res_info:
        :param process_res_info:
        :param step_info:
        """
        self.raw_img_path = raw_img_path
        self.grid_img_path = grid_img_path
        self.batdom_record = batdom_record
        self.action_res = action_res
        self.exec_img_path = exec_img_path
        self.guide_res_info = guide_res_info
        self.reflect_res_info = reflect_res_info
        self.process_res_info = process_res_info
        self.step_info = step_info

    def to_dict(self):
        """
        转为列表
        :return:
        """
        return self.__dict__
