#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@Desc    :   
@File    :   auto_case_kb.py
@Time    :   2024-11-26
<AUTHOR>   xuz<PERSON><PERSON>@baidu.com
"""
# import sys
# sys.path.append("/Users/<USER>/Workspace/Project/lazyOne/qamate-strategy/core")

import json
import traceback

from apps.app_agent.utils.qamate_web_util import QamateWebUtil
from basics.util import logger
from apps.app_agent.knowledge_search.auto_case_kb_agent.ack_agent import AckAgent
from apps.app_agent.knowledge_search.knowledge_search_for_text_list import KnowledgeSearchForTextList

class AutoCaseKb(object):
    """
    自动化知识库
    """
    def __init__(self, os_type, kb_node_id, tmp_dir):
        """
        初始化
        """
        self.os_name = "ios" if os_type == 2 else "android"
        self.__QWU = QamateWebUtil(os_type=os_type)
        self.__agent = AckAgent(tmp_dir=tmp_dir)
        
        self.kb_node_id = kb_node_id
        self.knowledge_dict = {}
        self.knowledge_query_list = []

        self.search_knowledge_number = 5

        self.load_auto_case_kb()
        self.__ksftl = KnowledgeSearchForTextList(self.knowledge_query_list)

    def __load_case_node(self, node, parent_step_list, parent_desc_list):
        """
        递归加载节点
        """
        mp_desc = node["nodeName"].strip()
        node_id = node["caseNodeId"]
        step_list = parent_step_list[:]
        new_step_list = node["extra"]["stepInfo"][self.os_name]
        
        for step_info in new_step_list:
            step_list.append(step_info)

        new_parent_desc_list = parent_desc_list[:]
        new_parent_desc_list.append(mp_desc)

        self.knowledge_query_list.append(mp_desc)
        self.knowledge_dict[mp_desc] = {
            # "mp_desc": mp_desc,
            "step_list": step_list, 
            "node_id": node_id
            # "parent_desc_list": new_parent_desc_list
        }
        if "children" in node and len(node["children"]) > 0:
            for child in node["children"]:
                self.__load_case_node(child, step_list, new_parent_desc_list)

    def load_auto_case_kb(self):
        """
        加载自动化知识库
        """
        node_tree = self.__QWU.get_case_tree_query_with_auto(self.kb_node_id)
        node_list = node_tree["children"]
        for node in node_list:
            self.__load_case_node(node, [], [])

    def add_single_auto_case_knowledge(self, input_node_id):
        """
        添加自动化知识库
        """
        # 对newapp的用例格式进行一定的定制化处理。
        # 当前只处理单个node_id下的步骤
        
        step_list = []

        # 获取node_id下的对应的步骤信息
        step_raw_list = self.__QWU.get_node_step_list(input_node_id)

        last_step_info = {}

        for step in step_raw_list:
            # 如果这个步骤是失败可以跳过的，则不进行描述，直接挂载到前一个步骤中
            skip_flag = False
            if step["stepType"] == 201 and "findParams" in step["stepInfo"]["params"]:
                skip_flag = step["stepInfo"]["params"]["findParams"]["allowFail"]

            # 如果是等待类型，也不进行描述，直接挂载到前一个步骤中
            if step["stepType"] == 417:
                skip_flag = True
            
            if skip_flag is True and last_step_info != {}:
                last_step_info["other_step"].append(step)
                continue

            # 得到每个步骤的描述信息 -> sp_desc single point
            sp_desc = self.__agent.get_single_point_desc(step)
            add_step_info = {
                "sp_desc": sp_desc,
                "step_info": step,
                "other_step": []
            }
            step_list.append(add_step_info)
            last_step_info = add_step_info


        before_desc_list = []
        # 对多个步骤进行总结
        for i in range(len(step_list)):
            step = step_list[i]
            before_desc_list.append(step["sp_desc"])

            # 得到从根节点到当前步骤的描述信息 -> mp_desc multi point
            if i == 0:
                step["mp_desc"] = step["sp_desc"]
                continue

            mp_desc = self.__agent.get_auto_mp_desc(before_desc_list)
            step["mp_desc"] = mp_desc

        # 开始进行添加到知识库的操作
        # mp 存在 node 的 node_name 中
        # sp 存在 node 下 唯一一个 step 的 step_name 中
        last_node_id = self.kb_node_id
        for step in step_list:
            # 添加节点
            node_id = self.__QWU.add_node(last_node_id, step["mp_desc"])
            # 添加步骤
            # 先挂载正式节点
            self.__QWU.add_step_with_sp_desc(node_id, step["step_info"], step["sp_desc"])
            # 再挂载附加节点
            for other_step in step["other_step"]:
                self.__QWU.add_step_with_sp_desc(node_id, other_step, "")

            last_node_id = node_id
        return step_list

    def search_auto_case_knowledge(self, input_query_list):
        """
        检索自动化知识
        """
        # 加载知识库 -> 实时加载还是加载一次缓存？
        # self.load_auto_case_kb()
        # 去除query中的空白字符
        query_list = [query.strip() for query in input_query_list]
        query = "。".join(query_list)
        logger.info("input search query list: {}".format(query))
        
        retrieve_res = self.__ksftl.knowledge_search_for_text_list(query, self.search_knowledge_number, 0)
        res_flag = False
        max_query_index = -1
        max_step_list = []
        step_log_info = ""

        for i in range(len(retrieve_res)):
            knowledge_name = retrieve_res[i]['document'].page_content
            score = retrieve_res[i]['score']
            logger.info("search kn no: {}, name: {}, score: {}".format(i, knowledge_name, score))
            kn_info = self.knowledge_dict[knowledge_name]
            step_desc_list = []
            for step_info in kn_info["step_list"]:
                step_desc = step_info["stepDesc"]
                if step_desc == "":
                    step_desc = "空步骤"
                step_desc_list.append(step_desc)
            
            # index_dict = {
            #     "case_id": last_case_id,
            #     "step_id": last_step_id
            # }
            ck_flag = False
            try:
                ck_flag, index_dict = self.__agent.recheck_search_kn(
                    query_list, knowledge_name, step_desc_list, kn_info["step_list"]
                )
                logger.info("recheck_search_flag: {}, index_dict: {}".format(ck_flag, index_dict))
            except:
                logger.error("mrd 执行异常: {}".format(step_desc_list))
                logger.error("错误信息: {}".format(traceback.format_exc()))
            
            if ck_flag is True and index_dict["case_id"] > max_query_index:
                res_flag = True
                max_step_list = []
                max_query_index = index_dict["case_id"]
                for i in range(0, index_dict["step_id"] + 1):
                    max_step_list.append(kn_info["step_list"][i])
                kn_id = kn_info["node_id"]
                try:
                    step_log_info = json.dumps({
                        "knowledge_id": kn_id,
                        "search_detail": index_dict["search_info"]
                    }, ensure_ascii=False, indent=4)
                except:
                    step_log_info = "特殊字符导致异常"
            
        if res_flag is False or max_query_index == -1:
            # TODO: 进行兜底
            max_query_index = 0
            knowledge_name = "[兜底]打开APP，进入首页"
            max_step_list = self.knowledge_dict[knowledge_name]["step_list"]
            # 并且强行插入一个用例操作
            input_query_list.insert(0, knowledge_name)
            logger.info("no match, update max_index: {}, knowledge_name: {}".format(max_query_index, knowledge_name))
            step_log_info = "兜底策略"

        logger.info("step_log_info: {}".format(step_log_info))

        return max_query_index, max_step_list, step_log_info
    
    def split_input_query(self, input_query_list):
        """
        分割输入查询
        """
        self.__agent.split_input_query(input_query_list)
        return


if __name__ == '__main__':
    ack = AutoCaseKb(
        os_type=2, kb_node_id=32111974, tmp_dir="/Users/<USER>/Workspace/Project/lazyOne/qamate-strategy/tmp"
    )

    # 添加知识
    kn_node_ids = [
        # # 历史版本
        # 26009216, 26009240, 26009244, 26009257, 26009319, 26009386, 26009399, 26009421, 26009546
        # # 4.3:
        # 26129928, 26131949, 26133074, 26137338, 26138705, 26141528, 26142369, 26143113
        # 4.2:
        # 26116935, 26122110, 
        # 26127076, 
        # 4.1:
        # 32679081
        # -1:
        # 32679081, 
        # 32679233, 32679512, 32788275, 32792611, 32797384, 32811628,
        # -2: 
        # 32876490, 32875611, 32875618, 32876492, 33169096, 33174980, 33181707, 33192489, 33196738, 33197665, 32878009,
        # 33204941, 32881032, 32898608
        # 3.0:
        32580755, 32594537, 32588535
    ]
    for kn_node_id in kn_node_ids:
        print(f"正在添加{kn_node_id}")
        ack.add_single_auto_case_knowledge(kn_node_id)
        # break

    # ack.add_single_auto_case_knowledge("23358466")
    # ack.add_single_auto_case_knowledge("26133284")

    # # 加载知识库
    # ack.load_auto_case_kb()
    
    # # 检索知识
    # input_query =  [
    #     "进入首页及对话页面",
    #     "输入框输入'生成200字关于秋天的作文'，点击发送",
    #     # "输入框输入'生成300字关于夏天的作文'，点击发送",
    #     "长按主助手回复的对话气泡",
    #     "在弹出的功能面板上点击“分享”",
    #     "校验界面底部显示“分享到”的字样",
    #     "校验界面顶部显示“选择分享内容”的字样"
    # ]
    # index, step_list, _ = ack.search_auto_case_knowledge(input_query)

    # # 拆分输入
    # ack.split_input_query([
    #     "3.6/3.7版本需求", "【动画效果】输入框结构优化。孙佳敏", "智能体", "输入框", "只有一个功能时", "只有一个功能时",
    #     "只有语聊模式", "操作步骤: 1.点击发现-智能体进入智能体聊天页面 2.对话中点击页面下方键盘按钮 3.点击输入框右侧语聊按钮",
    #     "预期：1.切换至语聊页面 2.页面有按钮【分享】"
    # ])
