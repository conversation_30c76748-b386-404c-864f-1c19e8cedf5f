#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@File    : knowledge_search_for_text_list.py
<AUTHOR> <PERSON><PERSON><PERSON><PERSON>(renya<PERSON><PERSON>@baidu.com)
@Time    : 2024/8/6 15:28
@Desc    : 
"""

from typing import List
import traceback
import time
import random

from langchain_community.vectorstores import FAISS

from apps.app_agent.utils.embedding_util import EmbeddingUtil
from basics.util import logger


class KnowledgeSearchForTextList(object):
    """
    本地知识检索
    """
    def __init__(self, text_list: List[str]):
        """

        :param text_list:
        """
        self.text_list = text_list
        self.embeddings = EmbeddingUtil.wenxin_embeddings()
        self.docsearch = self._get_docsearch()

    def _get_docsearch(self):
        """

        :return:
        """
        iter = 10
        while iter > 0:
            iter -= 1
            try:
                docsearch = FAISS.from_texts(self.text_list, self.embeddings)
                return docsearch
            except:
                logger.error(traceback.format_exc())
                logger.error("qps limit, try again, left %d" % iter)
                time.sleep(random.randint(1, 3))
                continue

    def knowledge_search_for_text_list(self, query: str, top_k: int = 3,
                                       score_threshold: float = 0.0) -> List[dict]:
        """
        针对 text list 对象进行检索，
        例如：[
            "I like apples",
            "I like oranges",
            "Apples and oranges are fruits",
        ]

        Returns:
            返回一个包含查询信息的列表[{
                "document" (Document) ,
                "score"
            }]。
        """
        if not self.text_list:
            return []
        iter = 10
        while iter > 0:
            iter -= 1
            try:
                docs_and_scores = self.docsearch.similarity_search_with_relevance_scores(query, k=top_k)

                search_docs = []
                for item in docs_and_scores:
                    if item[1] > score_threshold:
                        search_docs.append({'document': item[0], 'score': item[1]})
                return search_docs
            except:
                logger.error(traceback.format_exc())
                logger.error("qps limit, try again, left %d" % iter)
                time.sleep(random.randint(1, 3))
                continue
        return []


if __name__ == '__main__':
    t_l = [
        "进入首页",
        "进入对话页",
        "进入发现页",
        "进入通知页",
        "进入个人中心页"
    ]
    mrd_list = ["首页及对话   （普通对话+语音电话对话）（97）", "对话页", "助手tab和历史tab可点击切换、可手势横划切换"]
    know_search = KnowledgeSearchForTextList(t_l)
    res1 = know_search.knowledge_search_for_text_list(query=mrd_list[0])
    res2 = know_search.knowledge_search_for_text_list(query=mrd_list[1])
    res3 = know_search.knowledge_search_for_text_list(query=mrd_list[2])
    print(res1)
