#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@Desc    :   
@File    :   scene_kb.py
@Time    :   2024-11-20
<AUTHOR>   xuzhe<PERSON>@baidu.com
"""
# import sys
# sys.path.append("/Users/<USER>/Workspace/Project/lazyOne/qamate-strategy/core")

import json
import cv2
import requests
import os
import datetime
from bs4 import BeautifulSoup

from apps.app_agent.knowledge_search.knowledge_search_for_text_list import KnowledgeSearchForTextList
from apps.app_agent.utils.qamate_web_util import QamateWebUtil


class SceneKB(object):
    """
    场景知识库
    """

    def __init__(self, os_type, kb_node_id, tmp_dir):
        """
        初始化
        """
        self.__QWU = QamateWebUtil(os_type=os_type)
        self.scene_name_list = []
        self.scene_info_cache = {}
        self.kb_node_id = kb_node_id
        self.os_name = "ios" if os_type == 2 else "android"
        self.tmp_dir = tmp_dir

        self.knowledge_search = None

        self.__load_kb(kb_node_id)

    def __load_kb(self, kb_node_id):
        """
        载入知识库
        """
        res = self.__QWU.get_case_tree_query_with_auto(kb_node_id)

        # 一级子节点为知识名称
        for node_info in res["children"]:
            self.scene_name_list.append(node_info["nodeName"])
            self.scene_info_cache[node_info["nodeName"]] = {
                "id": node_info["caseNodeId"],
                "cache_flag": False,
                "text_desc": "",
                "image_demo": []
            }

        self.knowledge_search = KnowledgeSearchForTextList(self.scene_name_list)

    def get_scene_detail(self, scene_name):
        """
        获取场景详细信息
        """
        # 获取场景文本描述
        desc_res = self.__QWU.get_casenode_desc(self.scene_info_cache[scene_name]["id"])
        html_content = desc_res["description"]
        soup = BeautifulSoup(html_content, 'html.parser')
        # 查找和提取你需要的部分，比如 div 内容
        # text_desc = soup.find('div', class_='mp-paragraph-wrapper').text
        # text_desc = soup.find('div').text
        text_desc = ''
        dup_set = set()
        for div in soup.find_all('div'):
            if div.text in dup_set:
                # 去除重复文本
                continue
            text_desc += div.text
            dup_set.add(div.text)
            text_desc += '\n'

        # 获取场景图片示例
        image_demo = []
        image_res = []
        total_node_res = self.__QWU.get_case_tree_query_with_auto(self.kb_node_id)
        for node_info in total_node_res["children"]:
            if node_info["caseNodeId"] == self.scene_info_cache[scene_name]["id"]:
                image_res = node_info["extra"]["stepInfo"][self.os_name]
                break

        goal_dir = "{}/scene".format(self.tmp_dir)
        # 如果目录不存在，则创建目录
        if not os.path.exists(goal_dir):
            os.makedirs(goal_dir)

        for image_info in image_res:
            # 进行图片下载
            screen_url = image_info["stepInfo"]["params"]["deviceInfo"]["screenshot"]
            screen_file = "{}/{}_{}.{}".format(
                goal_dir,
                screen_url.split('/')[-1].split('.')[0],
                int(datetime.datetime.now().timestamp() * 1000),
                screen_url.split('.')[-1]
            )

            with open(screen_file, 'wb') as f:
                f.write(requests.get(screen_url).content)

            # 获取画框区域
            rect_list = []
            for find_info in image_info["stepInfo"]["params"]["findInfo"]["findNode"]:
                rect_info = find_info["detailFeature"]["rect"]
                rect_list.append({
                    "x": rect_info["x"],
                    "y": rect_info["y"],
                    "width": rect_info["w"],
                    "height": rect_info["h"]
                })
            # 将多个区域合并为一个，既一个包含所有元素的区域
            final_rect = {
                "x": min([r["x"] for r in rect_list]),
                "y": min([r["y"] for r in rect_list]),
                "width": max([r["x"] + r["width"] for r in rect_list]) - min([r["x"] for r in rect_list]),
                "height": max([r["y"] + r["height"] for r in rect_list]) - min([r["y"] for r in rect_list])
            }
            s = image_info["stepInfo"]["params"]["deviceInfo"]["screenSize"]["scale"]
            # 在原始图片上绘制矩形框
            img = cv2.imread(screen_file)
            cv2.rectangle(
                img,
                (int(final_rect['x']) * s, int(final_rect['y']) * s),
                (
                    int(final_rect['x'] * s + final_rect['width'] * s),
                    int(final_rect['y'] * s + final_rect['height'] * s)
                ),
                (0, 0, 255),
                5
            )
            cv2.imwrite(screen_file, img)
            image_demo.append(screen_file)

        return {
            "text_desc": text_desc,
            "image_demo": image_demo
        }

    def get_scene_info(self, scene_name):
        """
        获取场景详细信息 - 带缓存
        """
        if self.scene_info_cache[scene_name]["cache_flag"] is False:
            scene_detail = self.get_scene_detail(scene_name)
            self.scene_info_cache[scene_name]["text_desc"] = scene_detail["text_desc"]
            self.scene_info_cache[scene_name]["image_demo"] = scene_detail["image_demo"]
            self.scene_info_cache[scene_name]["cache_flag"] = True

        return self.scene_info_cache[scene_name]

    def get_scene_by_step(self, query):
        """
        输入query，返回包含的场景信息
        """
        res = []

        ks = self.knowledge_search.knowledge_search_for_text_list(query)
        uniq_set = set()
        for k in ks:
            scene_name = k['document'].page_content
            if scene_name in uniq_set:
                continue
            uniq_set.add(scene_name)
            scene_info = self.get_scene_info(scene_name)
            res.append({
                "scene_words": scene_name,
                "text_desc": scene_info["text_desc"],
                "image_demo": scene_info["image_demo"],
                "score": k["score"]
            })
            for child in self.knowledge_search.knowledge_search_for_text_list(scene_info["text_desc"],
                                                                              score_threshold=0.15):

                child_name = child['document'].page_content
                if child_name in uniq_set:
                    continue
                uniq_set.add(child_name)
                scene_info = self.get_scene_info(child_name)
                res.append({
                    "scene_words": child_name,
                    "text_desc": scene_info["text_desc"],
                    "image_demo": scene_info["image_demo"],
                    "score": k["score"]
                })

        res.sort(key=lambda x: x["score"], reverse=True)
        if len(res) > 5:
            res = res[:5]
        return res


if __name__ == '__main__':
    s = SceneKB(os_type=1, kb_node_id=44670171, tmp_dir="~/tmp")
    result = s.get_scene_by_step("作文预览页")
    print(json.dumps(result, ensure_ascii=False))
