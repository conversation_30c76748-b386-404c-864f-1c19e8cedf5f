#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@Desc    :   
@File    :   ack_prompt.py
@Time    :   2024-11-27
<AUTHOR>   x<PERSON><PERSON><PERSON>@baidu.com
"""

def get_sp_prompt(action_name, raw_desc=""):
    """
    生成单点描述的prompt
    """
    if not raw_desc:
        raw_action_desc = raw_desc
    else:
        raw_action_desc = "无"
    

    prompt = \
f"""## 任务说明 ##
图片所示为一个APP的页面截图，红色方框圈出区域为进行操作的区域，操作类型为是{action_name}。
请你结合你对页面截图、操作区域对应的页面元素内容和操作类型的理解，如果原始的步骤描述存在的话，也参考原始的步骤描述，总结归纳出，这步操作是做了什么。

## 原始的步骤描述 ##
{raw_action_desc}

## 输出要求 ##
你的输出固定的包括以下几个部分，格式如下：
### 思考 ###
基于 页面截图、操作区域对应的页面元素内容、操作类型 分析在当前的页面做了什么操作。
### 总结 ###
用一个短句来描述操作的内容。"""
    return prompt


def get_mp_prompt(sp_desc_list):
    """
    生成多点描述的prompt
    """
    step_desc_str = ""
    for i in range(len(sp_desc_list)):
        sp_desc = sp_desc_list[i]
        step_desc_str += f"第{i + 1}个步骤: {sp_desc}\n"
    prompt = \
f"""## 任务说明 ##
『已执行的自动化测试步骤』中描述了一组自动化测试步骤，请你根据这些步骤，总结归纳出，这组步骤是实现了什么功能操作。

## 已执行的自动化测试步骤 ##
{step_desc_str}
## 输出要求 ##
你的输出固定的包括以下几个部分，格式如下：
### 思考 ###
基于 已执行的自动化测试步骤 分析这组步骤实现的是什么功能操作。
### 总结 ###
用一行语句来描述这组步骤实现的功能操作，注意要保留每一个前置、中间操作的过程描述和核心的功能点的描述。
"""

    return prompt


def get_check_and_return_index_prompt(knowledge_text, query_list):
    """
    生成检查并返回索引的prompt
    """
    query_list_str = "\n".join(query_list)
    prompt = \
f"""## 背景 ##
当前我们要做的事情的最终目标是想把手工用例转化成可以自动化执行的自动化测试步骤。
在『原始的手工测试用例』中，每行记录着一个手工测试用例的步骤或者节点。我们的建设思路是，先通过知识检索的方式，在知识库中尽可能的找到哪些前置的步骤或者节点可以直接使用已有的知识来进行自动化执行。然后将这些前置的步骤或者节点，直接使用知识进行替代。后续的步骤再通过生成的方式，生成新的步骤。

## 任务说明 ##
现在我们已经在『知识检索结果』中，给出一条和『原始的手工测试用例』中的手工用例相似的知识。希望你进行两个判断，一个判断是，是否存在前置的节点或者步骤，可以通过『知识检索结果』中的知识直接进行替代。第二个判断是，如果存在，请你给出这个前置的节点或者步骤在『原始的手工测试用例』中的原文。如果存在多个，请给出最后的那个。

## 原始的手工测试用例 ##
{query_list_str}

## 知识检索结果 ##
{knowledge_text}

## 输出要求 ##
你的输出固定的包括以下几个部分，格式如下：
### 思考 ###
基于 知识检索结果 判断和分析这条知识是否可以替代原始的手工测试用例中的某个步骤或者节点，请给出你的思考过程。
### 判断 ###
请用 Yes 或者 No 来回答，是否可以替代原始的手工测试用例中的某个步骤或者节点。请注意，只需要回答 Yes 或者 No，不需要给出具体的理由。
### index ###
如果可以替代，请你给出这个前置的节点或者步骤在『原始的手工测试用例』中的原文。如果存在多个，请给出最后的那个。如果不可以替代，请给出 No。
"""
    return prompt

def get_split_input_query_prompt(query_list):
    """
    生成检查并返回索引的prompt
    """
    query_list_str = "\n".join(query_list)
    prompt = \
f"""## 背景 ##
在『原始的手工测试用例』中，每行记录着一个手工测试用例的步骤或者节点。但是因为人工书写存在一些规范性的问题，导致有的节点没有实际的含义，有的节点可能包含了多个步骤或者校验。

## 任务说明 ##
请你根据『原始的手工测试用例』，判断哪些节点是可以拆分的，如果可以拆分，请尽量按照原文进行拆分成多个节点。

## 原始的手工测试用例 ##
{query_list_str}

## 输出要求 ##
你的输出固定的包括以下几个部分，格式如下：
### 思考 ###
基于 原始的手工测试用例 给出判断和分析哪些节点是可以拆分的，如果可以拆分，要如何拆分，请给出你的思考过程。
### 结果 ###
请你给出拆分后的结果，只需要输出拆分后，保留的节点或者步骤，每个节点或者步骤占一行
"""
    return prompt


def get_recheck_preaction_prompt(query_str, desc_list):
    """
    二次检查，防止过匹配
    """
    desc_list_str = "\n".join(desc_list)
    prompt = \
f"""## 背景 ##
在『原始的手工测试用例』中，记录着需要执行的操作。『知识检索结果』中，每行一条，包含了一组步骤的描述。有的步骤描述可能和『原始的手工测试用例』中的期望的操作匹配度非常高，且没有多余的动作。有的步骤描述可能和『原始的手工测试用例』中的期望的操作匹配度非常低，或者含有了一些有多余的动作。

## 任务说明 ##
请你根据『原始的手工测试用例』，寻找到 『知识检索结果』 中和『原始的手工测试用例』中的期望的操作匹配度最高，且没有任何多余动作的那行知识。

## 原始的手工测试用例 ##
{query_str}

## 知识检索结果 ##
{desc_list_str}

## 输出要求 ##
你的输出固定的包括以下几个部分，格式如下：
### 思考 ###
基于『原始的手工测试用例』期望执行的操作，寻找出『知识检索结果』中，哪行知识和『原始的手工测试用例』中的期望的操作匹配度最高，且没有任何多余动作。请注意，不能有任何多余动作。
### 结果 ###
请你给出那行的知识，只需要输出那行知识的原文，不要有其他的提示。"""
    return prompt


def get_recheck_search_kn_prompt(query_list, knowledge_name, step_desc_list):
    """
    二次确认复验
    """
    query_list_str = "\n".join(query_list)
    step_desc_list_str = "\n".join(step_desc_list)
    output_format = """[
    {
        "case_name": string, // 『手工测试用例』的操作名称，请使用输入的『手工测试用例』中的操作名称，不要有任何改变，且严格按照顺序输出
        "case_id": int, // 『手工测试用例』的操作对应的『已有的自动化步骤』中的步骤的编号，编号从0开始，且严格按照顺序输出
        "step_list": list[string], // case_name中『手工测试用例』的操作对应的『已有的自动化步骤』中的步骤，list中每个元素表示一个步骤的描述。步骤的描述请和『已有的自动化步骤』中的步骤描述完全一致，且严格按照顺序输出
        "step_id_list": list[int], // case_name中『手工测试用例』的操作对应的『已有的自动化步骤』中的步骤的编号，编号从0开始，且严格按照顺序输出
    },
    ... // 每个元素的格式和上面一致
]
"""
    prompt = \
f"""## 背景 ##
我们现在正在将输入的『手工测试用例』转化成自动化测试步骤。在转化的过程中，我们可以通过知识检索的方式，直接使用一些『已有的自动化步骤』，来实现前置的一些操作。但是在检索获取的已有知识中得到的步骤，并不完全符合输入的『手工测试用例』的目标，因此我们需要请你来进行一下甄别，哪些步骤可以使用。

## 输入说明 ##
在『手工测试用例』中，记录着期望进行的测试操作，每行表示一个测试操作，且操作具有顺序性，既先执行第一行，再执行第二行，依此类推。
在『已有的自动化步骤』中，每行记录着一个自动化步骤的描述，这些步骤也有顺序性，既先执行第一行，再执行第二行，依此类推。

## 任务说明 ##
请你根据『手工测试用例』和『已有的自动化步骤』，完成判断和甄别，哪些自动化步骤可以使用，以及对应到第几个手工测试用例中的操作。举个例子的话，比如说，『手工测试用例』中的操作分为A、B、C、D四个操作，而『已有的自动化步骤』中，有1、2、3、4、5、6、7、8等操作。其中，A可以由操作1完成，B可以由操作2、3完成，C可以由操作4完成一部分，但是操作5和C的测试目标不一致，即使操作D可以由步骤6、7、8完成，但是因为『手工测试用例』和『已有的自动化步骤』都具有顺序性，所以认为，可以实现的『手工测试用例』的操作是A、B，对应的『已有的自动化步骤』是第1、2、3步，从操作C开始，后续的操作无法由本次的『已有的自动化步骤』的后续步骤完成了。请注意，严格保证『手工测试用例』的操作可以完整的由对应的『已有的自动化步骤』中选择的对应的步骤全部实现每一个细节，否则认为这个操作无法有对应的步骤。
请注意，『手工测试用例』和『已有的自动化步骤』的选择都必须有保持连续且有序。举个例子的话，比如说，『手工测试用例』中的操作分为A、B两个操作，而『已有的自动化步骤』中，有1、2、3、4、5等操作。其中，操作A可以由步骤1完成，操作B可以由步骤2、4就可以完成，那么必须判断步骤3是否会影响操作B的完成。如果步骤3的执行，对操作B的实现没有影响，那么需要把步骤3加入进来，因为步骤必须连续且有序，即认为操作B由步骤2、3、4完成；如果步骤3的执行会导致操作B无法完成，或者和操作B的细节要求存在不一致，那么因为要保持连续且有序，所以无法跨过步骤3进行步骤4，所以认为操作B无法完成。那么认为只能完成操作A，以及对应的步骤1。
另外，如果『已有的自动化步骤』中存在某行标记为空步骤，那说明这个步骤是否执行对结果无影响，如果选择了这个步骤的前一个步骤，那么也请把这个空步骤选择进来，认为空步骤对应的『手工用例操作』中操作和它的前一个步骤对应的操作为同一个操作，且放在它的前一个步骤之后。
最后，再次强调，『手工测试用例』中的操作和『已有的自动化步骤』对应的步骤必须要保持严格的一致。每一种操作必须对应的步骤，出了操作、动作的类型要保持一致，操作或者动作的目标也要严格一致。例如点击、长按等操作的目标对象要严格一致，输入的文字内容等要严格一致，才能算作对应上。

## 手工测试用例 ##
{query_list_str}

## 已有的自动化步骤 ##
{step_desc_list_str}

## 输出要求 ##
你的输出固定的包括以下几个部分，格式如下：
### 思考 ###
基于『手工测试用例』和『已有的自动化步骤』，根据『任务说明』中的要求，选择出哪些『手工测试用例』中的操作可以由『已有的自动化步骤』中的步骤完成，以及对应的步骤，当后续步骤不匹配，提前结束匹配过程。给出你的思考过程和依据。

### 结果 ###
结果格式为一个json字符串，请保证输出的json字符串是可以被解析的，具体的字段要求如下：
{output_format}
"""
    return prompt
