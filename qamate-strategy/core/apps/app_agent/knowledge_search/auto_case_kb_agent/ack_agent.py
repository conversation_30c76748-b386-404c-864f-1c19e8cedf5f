#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@Desc    :   
@File    :   ack_agent.py
@Time    :   2024-11-27
<AUTHOR>   xuzhe<PERSON>@baidu.com
"""
# import sys
# sys.path.append("/Users/<USER>/Workspace/Project/lazyOne/qamate-strategy/core")

import os
import datetime
import requests
import cv2
import json

from basics.util import logger
from apps.app_agent.knowledge_search.auto_case_kb_agent.ack_prompt import *
from apps.app_agent.utils.big_model import BigModel
from apps.app_agent.utils.chat_message import ChatMessage


class AckAgent(object):
    """
    agent
    """
    def __init__(self, tmp_dir):
        """
        初始化
        """
        self.__ai_agent = BigModel()
        self.tmp_dir = tmp_dir
        pass

    def get_image_desc(self, step_info):
        """
        获取图片的描述信息
        """
        goal_dir = "{}/auto_case".format(self.tmp_dir)
        # 如果目录不存在，则创建目录
        if not os.path.exists(goal_dir):
            os.makedirs(goal_dir)
        
        if "deviceInfo" not in step_info["params"] and "params" in step_info["params"]:
            screen_url = step_info["params"]["params"]["img"]
            screen_file = "{}/{}_{}.{}".format(
                goal_dir,
                screen_url.split('/')[-1].split('.')[0],
                int(datetime.datetime.now().timestamp() * 1000),
                screen_url.split('.')[-1]
            )

            with open(screen_file, 'wb') as f:
                f.write(requests.get(screen_url).content)
            return screen_file
        
        # 进行图片下载
        screen_url = step_info["params"]["deviceInfo"]["screenshot"]
        screen_file = "{}/{}_{}.{}".format(
            goal_dir,
            screen_url.split('/')[-1].split('.')[0],
            int(datetime.datetime.now().timestamp() * 1000),
            screen_url.split('.')[-1]
        )

        with open(screen_file, 'wb') as f:
            f.write(requests.get(screen_url).content)
        
        # 获取画框区域
        rect_list = []
        for find_info in step_info["params"]["findInfo"]["findNode"]:
            rect_info = find_info["detailFeature"]["rect"]
            rect_list.append({
                "x": rect_info["x"],
                "y": rect_info["y"],
                "width": rect_info["w"],
                "height": rect_info["h"]
            })
        # # 将多个区域合并为一个，既一个包含所有元素的区域
        # final_rect = {
        #     "x": min([r["x"] for r in rect_list]),
        #     "y": min([r["y"] for r in rect_list]),
        #     "width": max([r["x"] + r["width"] for r in rect_list]) - min([r["x"] for r in rect_list]),
        #     "height": max([r["y"] + r["height"] for r in rect_list]) - min([r["y"] for r in rect_list])
        # }
        # rect_list.append(final_rect)

        s = step_info["params"]["deviceInfo"]["screenSize"]["scale"]
        # 在原始图片上绘制矩形框
        img = cv2.imread(screen_file)
        for rect in rect_list:
            cv2.rectangle(
                img,
                (int(rect['x']) * s, int(rect['y']) * s),
                (
                    int(rect['x'] * s + rect['width'] * s),
                    int(rect['y'] * s + rect['height'] * s)
                ), 
                (0, 0, 255), 
                5
            )
        cv2.imwrite(screen_file, img)
        logger.info("save image to {}".format(screen_file))
        return screen_file
    
    def get_action_desc(self, step_info):
        """
        获取操作的描述信息
        """
        if "actionInfo" not in step_info["params"] and "type" in step_info["params"]:
            return "人工校验步骤:{}".format(step_info["desc"])

        action_info = step_info["params"]["actionInfo"]
        action_type = action_info["type"]        
        # 增加batdom对于操作区域的理解
        # 当前只支持单控件 —— TODO
        # 先只支持文本类型 —— TODO
        content = "目标元素"
        action_node = step_info["params"]["findInfo"]["findNode"][0]
        if action_node["detailFeature"]["type"] == "Text":
            match_type = action_node["detailFeature"]["matchType"]
            content = "元素"
            if match_type == 0:
                content = "文本内容为『{}』的元素".format(action_node["detailFeature"]["ext"]["text"])
            elif match_type == 1:
                content = "文本内容包含『{}』的元素".format(action_node["detailFeature"]["ext"]["text"])
            elif match_type == 2:
                # 先不支持正则
                pass
        else:
            content = "目标元素"

        
        if action_type == "tap":
            if "duration" in action_info["params"] and action_info["params"]["duration"] > 500:
                action_desc = "长按{}".format(content)
            else:
                action_desc = "点击{}".format(content)
        elif action_type == "input":
            action_desc = "在{}输入文本:『{}』".format(content, action_info["params"]["text"])
        elif action_type == "nope":
            action_desc = "判断{}是否存在".format(content)
        else:
            raise Exception("不支持的操作类型: {}".format(action_type))
        
        return action_desc

    def get_auto_sp_desc(self, p_info):
        """
        生成自动化步骤的单点描述
        """
        # 准备前置内容
        step_info = p_info["stepInfo"]

        # 文字描述
        step_raw_desc = p_info["stepDesc"]

        # 图片描述
        img_desc = self.get_image_desc(step_info)

        # 操作描述
        action_desc = self.get_action_desc(step_info)

        # 拼接成完整的描述
        prompt = get_sp_prompt(action_desc, step_raw_desc)

        msg = ChatMessage()
        msg.add_user_message(prompt, image_paths=[img_desc])
        llm_ans = self.__ai_agent.chat(msg)
        sp_desc = llm_ans.split("### 总结 ###")[-1].strip()
        return sp_desc
    
    def get_auto_mp_desc(self, sp_desc_list):
        """
        生成自动化步骤的多点描述
        """
        # 拼接成完整的描述
        prompt = get_mp_prompt(sp_desc_list)

        msg = ChatMessage()
        msg.add_user_message(prompt)
        llm_ans = self.__ai_agent.chat(msg)
        mp_desc = llm_ans.split("### 总结 ###")[-1].strip()
        # mp_desc = llm_ans.split("### 思考 ###")[-1].split("###")[0].strip()
        return mp_desc

    
    def get_single_point_desc(self, p_info):
        """
        获取单个节点的描述信息
        """
        sp_desc = None

        # 节点类型枚举：https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/3LoFUu-M_a/LWyELLqriZ/FJ7zHeFZBW2Vhu
        
        step_type = p_info["stepType"]

        # 临时方案 - 写死唯一的测试片段
        if step_type == 412:
            sp_desc = "冷启动APP，进入首页（对话页）"
            return sp_desc

        # 对201的步骤，结合截图、操作、步骤信息，得到新的单点描述
        # 对非201的步骤，进行硬编码的单点描述，并且逐步补充
        if step_type == 201:
            # 特殊处理滑动操作
            if "type" in p_info["stepInfo"]["params"] and p_info["stepInfo"]["params"]["type"] == "swipe":
                direction = p_info["stepInfo"]["params"]["params"]["direction"]
                direction_str_dict = {
                    1: "从下往上",
                    2: "从上往下",
                    3: "从左往右",
                    4: "从右往左"
                }
                sp_desc = "{}滑动屏幕".format(direction_str_dict[direction])
            else:
                sp_desc = self.get_auto_sp_desc(p_info)
        elif step_type == 401:
            # 冷启动APP
            sp_desc = "从桌面冷启动APP：{}".format(p_info["stepInfo"]["params"]["params"]["appName"])
        elif step_type == 417:
            # 休眠等待
            sp_desc = "等待{}秒".format(p_info["stepInfo"]["params"]["params"]["seconds"])
        else:
            pass

        return sp_desc
    
    def check_and_return_index_knowledge(self, knowledge_text, query_list):
        """
        检查知识库是否存在，如果不存在则抛出异常
        """
        for i in range(3):
            # 拼接成完整的描述
            prompt = get_check_and_return_index_prompt(knowledge_text, query_list)
            msg = ChatMessage()
            msg.add_user_message(prompt)
            llm_ans = self.__ai_agent.chat(msg)
            flag = llm_ans.split("### 判断 ###")[-1].split("###")[0].strip()
            if flag not in ["Yes", "No"]:
                # 需要加日志
                continue

            if flag == "No":
                return False, -1
            
            query = llm_ans.split("### index ###")[-1].strip()
            try:
                index = query_list.index(query)
                return True, index
            except ValueError:
                # 需要加日志
                continue
        return False, -1
    
    def split_input_query(self, query_list):
        """
        重新拆分输入的query_list
        """
        prompt = get_split_input_query_prompt(query_list)
        msg = ChatMessage()
        msg.add_user_message(prompt)
        llm_ans = self.__ai_agent.chat(msg)
        print(llm_ans)

    def recheck_preaction(self, query_list, index, desc_list):
        """
        重新检查前置动作
        """
        tmp_query_list = query_list[:min(index + 1, len(query_list))]
        query_str = "->".join(tmp_query_list)
        prompt = get_recheck_preaction_prompt(query_str, desc_list)
        msg = ChatMessage()
        msg.add_user_message(prompt)
        for i in range(3):
            llm_ans = self.__ai_agent.chat(msg)
            goal_query = llm_ans.split("### 结果 ###")[-1].strip()
            if goal_query in desc_list:
                return True, goal_query
        
        return False, ""
    
    def recheck_search_kn_result_check(self, result, query_list, step_desc_list, step_list):
        """
        解析并且检查返回结果的合法性
        """
        start_index = result.find("[")
        end_index = result.rfind("]")
        if start_index <= 0 or end_index <= 0:
            return False, {}
        goal_res_json = {}
        try:
            goal_res_str = ''.join([char for char in result[start_index: end_index + 1] if not char.isspace()])
            goal_res_json = json.loads(goal_res_str)
        except:
            return False, {}

        check_flag = True
        last_case_id = -1
        last_step_id = -1
        for case_info in goal_res_json:
            # 关键词校验
            if "case_id" not in case_info or "step_id_list" not in case_info:
                check_flag = False
                break

            # case 连续性判断
            case_id = case_info["case_id"]
            if case_id == last_case_id + 1:
                last_case_id = case_id
            else:
                check_flag = False
                break

            # step连续性判断
            for step_id in case_info["step_id_list"]:
                if step_id == last_step_id + 1:
                    last_step_id = step_id
                else:
                    check_flag = False
                    break
            if check_flag is False:
                break
        
        if check_flag is False:
            return False, {}
        
        if last_case_id > len(query_list) - 1:
            return False, {}
        
        if last_step_id > len(step_desc_list) - 1:
            return False, {}
        
        # 增加检索结果日志信息
        search_info_list = []
        for case_info in goal_res_json:
            search_info = {}
            search_info["query"] = query_list[case_info["case_id"]]
            search_info["step_list"] = []
            for step_id in case_info["step_id_list"]:
                step_info = step_list[step_id]
                search_info["step_list"].append({
                        "step_id": step_info["stepId"],
                        "step_desc": step_info["stepDesc"]
                })
            search_info_list.append(search_info)
        
        index_dict = {
            "case_id": last_case_id,
            "step_id": last_step_id,
            "search_info": search_info_list
        }

        return check_flag, index_dict
    
    def recheck_search_kn(self, query_list, knowledge_name, step_desc_list, step_list):
        """
        重新检查搜索到的知识库的信息
        """
        prompt = get_recheck_search_kn_prompt(query_list, knowledge_name, step_desc_list)
        print(prompt)
        msg = ChatMessage()
        msg.add_user_message(prompt)
        # llm_ans = self.__ai_agent.chat(msg)
        # print(llm_ans)
        for i in range(3):
            llm_ans = self.__ai_agent.chat(msg)
            result = llm_ans.split("### 结果 ###")[-1].strip()
            ck_flag, res_dict = self.recheck_search_kn_result_check(result, query_list, step_desc_list, step_list)
            if ck_flag:
                return True, res_dict
        
        return False, {}
