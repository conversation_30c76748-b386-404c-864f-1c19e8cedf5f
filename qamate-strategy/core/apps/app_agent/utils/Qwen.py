#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@File    : Qwen.py
<AUTHOR> <PERSON><PERSON><PERSON><PERSON>(renya<PERSON><PERSON>@baidu.com)
@Time    : 2025/2/17 10:51
@Desc    : 
"""

import time
import json

import openai
from openai import OpenAI

from basics.util import logger
from apps.app_agent.utils.exception import Agent<PERSON>rrCode, AgentException
from apps.app_agent.conf.contants import QWEN_API

MAX_RETRIES = 3


class Qwen(object):
    """
    GPT
    """

    def __init__(self, api_key=QWEN_API):
        """

        :param api_key:
        :param organization_id:
        """
        self.api_key = api_key
        self.base_url = "https://dashscope.aliyuncs.com/compatible-mode/v1"
        self.client = OpenAI(api_key=self.api_key, base_url=self.base_url)

    def param_check(self, chat_message: list):
        """
        参数校验
        :param chat_message:
        :return:
        """

        if not isinstance(chat_message, list):
            AgentException(err_code=AgentErrCode.PARAM_ERROR)

        for idx, item in enumerate(chat_message):
            role = item.get("role", "")
            if role not in ("assistant", "user", "system"):
                raise AgentException(err_code=AgentErrCode.PARAM_ERROR,
                                     detail="role must be 'assistant', 'user' or 'system'")
            if idx % 2 == 1 and role != "user":
                raise AgentException(err_code=AgentErrCode.PARAM_ERROR,
                                     detail="the odd-index role must be 'user'")

    def chat(self, chat_message: list, model_name="qwen-vl-max-2025-01-25", **kwargs):
        """
        对话
        :param chat_message:
        :param model_name:
        :param kwargs:
        :return:
        """

        self.param_check(chat_message=chat_message)

        if isinstance(kwargs.get("response_format", ""), str) and kwargs.get("response_format", "") == "json_object":
            kwargs['response_format'] = {"type": "json_object"}

        retries = 0
        while retries < MAX_RETRIES:
            try:
                completion = self.client.chat.completions.create(
                    model=model_name,
                    messages=chat_message,
                    **kwargs
                )
                result = completion.choices[0].message.content
                logger.info("{}:---------------\n{}".format(model_name, result))
                logger.info(
                    "total_tokens:{}, prompt_tokens:{}, completion_tokens:{}".format(completion.usage.total_tokens,
                                                                                     completion.usage.prompt_tokens,
                                                                                     completion.usage.completion_tokens)
                )
                return result

            except openai._exceptions.OpenAIError as e:
                logger.error(f"Qwen API error: {e}")
                retries += 1
                if retries < MAX_RETRIES:
                    sleep_time = 2
                    logger.info(f"Retrying in {sleep_time} seconds...")
                    time.sleep(sleep_time)
                else:
                    logger.error("Max retries reached. Raising exception.")
                    raise


if __name__ == '__main__':
    from apps.app_agent.utils.chat_message import ChatMessage

    msg = ChatMessage()
    # 添加用户消息
    msg.add_user_message("介绍下这幅图片",
                         image_paths=["/Users/<USER>/Downloads/图片/03.png"])
    # msg.add_user_message("介绍下你自己")
    res = Qwen().chat(msg, model_name="qwen2.5-vl-72b-instruct")
