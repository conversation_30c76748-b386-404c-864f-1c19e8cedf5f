#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@File    : chat_message.py
<AUTHOR> <PERSON><PERSON><PERSON><PERSON>(renya<PERSON><PERSON>@baidu.com)
@Time    : 2024/11/14 14:59
@Desc    : 
"""

import os
from typing import List, Optional

from apps.app_agent.utils.exception import Agent<PERSON>rrCode, AgentException
from basics.util.image import image_path_to_base64


def get_image_format(file_path):
    """
    根据图片的文件头返回图片格式
    """
    with open(file_path, 'rb') as f:
        file_header = f.read(10)  # 读取文件头的前 10 个字节
    if file_header.startswith(b'\x89PNG'):
        return 'png'
    elif file_header.startswith(b'\xff\xd8'):
        return 'jpeg'
    elif file_header.startswith(b'BM'):
        return 'bmp'
    elif file_header.startswith(b'GIF87a') or file_header.startswith(b'GIF89a'):
        return 'gif'
    else:
        return '未知格式'


class ChatMessage(list):
    """
    管理和存储与 ChatGPT 的交互信息
    """

    def __init__(self, system_message: Optional[str] = None):
        """
        初始化消息列表，如果提供了 system_message 则加入
        :param system_message:
        """
        super(ChatMessage, self).__init__()
        # self._messages: List[Dict[str, str]] = []
        if not system_message:
            system_message = "你是一位人工智能AI助手"

        self.append({
            "role": "system",
            "content": system_message
        })

    def add_message(self, role: str, prompt_text: str, image_paths: List[str] = [], img_quality: str = "auto",
                    video_url: str = None, fps: int = 2):
        """
        添加信息
        :param role:
        :param prompt_text:
        :param image_paths:
        :param img_quality:
        :return:
        """
        if role not in ("assistant", "user", "system"):
            raise AgentException(err_code=AgentErrCode.PARAM_ERROR,
                                 detail="role must be 'assistant', 'user' or 'system'")

        content = [{
            "type": "text",
            "text": prompt_text
        }]

        for img_path in image_paths:

            if img_path.startswith("http"):
                url = img_path
            else:
                base64_image = image_path_to_base64(img_path)
                ext = get_image_format(img_path)
                url = f"data:image/{ext};base64,{base64_image}"
            img_cont = {
                "type": "image_url",
                "image_url": {
                    "url": url,
                    "detail": img_quality
                }
            }
            content.append(img_cont)

        if video_url is not None:
            content.append({
                "type": "video_url",
                # 第一张图片链接及细节设置为 high
                "video_url": {
                    # 您可以替换图片链接为您的实际图片链接
                    "url": video_url,
                    "fps": fps,  # 每秒截取2帧画面，用于视频理解
                }
            })

        self.append({
            "role": role,
            "content": content
        })

    def add_user_message(self, prompt_text: str, image_paths: List[str] = [], img_quality: str = "auto",
                         video_url: str = None, fps: int = 2):
        """
        添加用户信息
        :param prompt_text:
        :param image_paths:
        :param img_quality:
        :return:
        """
        self.add_message(role="user", prompt_text=prompt_text, image_paths=image_paths, img_quality=img_quality,
                         video_url=video_url, fps=fps)

    def add_assistant_message(self, prompt_text: str):
        """

        :param prompt_text:
        :param image_paths:
        :param img_quality:
        :return:
        """
        self.add_message(role="assistant", prompt_text=prompt_text)


if __name__ == '__main__':
    chat = ChatMessage(system_message="你是一名人工智能助手")
    # 添加用户消息
    chat.add_user_message("介绍下你自己")
    # 添加助手回复
    a = chat.add_assistant_message("我是一个由OpenAI开发的语言模型，旨在帮助人们回答问题。")
    print(a)
