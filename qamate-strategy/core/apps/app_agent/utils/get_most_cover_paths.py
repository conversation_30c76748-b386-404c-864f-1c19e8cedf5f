"""
挑选一个用例树中的最优覆盖用例，用最少的用例覆盖最多的节点
"""
import random

from apps.app_agent.utils.qamate_web_util import QamateWebUtil


def traversal_tree_leaves(tree):
    """
    遍历用例树的叶子节点
    """
    if 'children' not in tree or len(tree['children']) == 0:
        yield tree
    else:
        for child in tree['children']:
            yield from traversal_tree_leaves(child)

def get_most_cover(case_tree, rate=0.1):
    """
    使用贪心算法，获取最优覆盖
    """

    case_list = tree_to_case_list(case_tree)
    target_case_limit = int(len(case_list) * rate) + 1
    # target_case_limit = min(20, len(case_list))
    print(f'总case数量 :{len(case_list)}, 目标case数量 :{target_case_limit}')
    all_node_set = set()
    for case in case_list:
        all_node_set = all_node_set | {node["caseNodeId"] for node in case}
    leaf_node_set = {leaf['caseNodeId'] for leaf in traversal_tree_leaves(case_tree)}
    cover_node_set = set()
    target_cases = []
    case_list_copy = case_list[:]
    while len(target_cases) < target_case_limit:
        max_add_num = -1
        select_case = None
        select_case_node_set = None
        random.shuffle(case_list_copy)
        for case in case_list_copy:
            case_node_set = {node["caseNodeId"] for node in case}
            add_num = len(case_node_set - cover_node_set)
            if add_num > max_add_num:
                max_add_num = add_num
                select_case = case
                select_case_node_set = case_node_set
        target_cases.append(select_case)
        case_list_copy.remove(select_case)
        cover_node_set = cover_node_set | select_case_node_set

    print(f'总节点数量{len(all_node_set)}， 覆盖节点数量：{len(cover_node_set)}， 覆盖率'
          f'{round((len(cover_node_set)/len(all_node_set)),3)*100}%')
    print(f'非叶子节点数量{len(all_node_set - leaf_node_set)}， 覆盖非节点数量：{len(cover_node_set-leaf_node_set)}， 非叶子节点覆盖率'
          f'{round((len(cover_node_set-leaf_node_set) / len(all_node_set - leaf_node_set)), 3) * 100}%')
    return target_cases



def tree_to_case_list(case_tree):
    """
    用例树转用例列表
    """
    case_list = []

    def traversal_tree(tree, path=[]):
        new_path = path + [tree]
        if len(tree.get("children", [])) == 0:
            case_list.append(new_path)
        for child in tree.get("children", []):
            traversal_tree(child, new_path)

    traversal_tree(case_tree)
    return case_list


def get_case_tree(tree_node_id, os_type, product_id, goal_tags=[], exclude_tags=[]):
    """
    根据标签和端获得过滤后的用例树
    """
    def traversal_tree(tree):
        yield tree
        for child in tree.get("children", []):
            yield from traversal_tree(child)

    qwu = QamateWebUtil(os_type)
    case_tree = qwu.get_case_tree_query(tree_node_id)
    tag_list = qwu.get_product_tags(product_id)
    tag_set = {tag['id'] for tag in tag_list if tag['tagName'] in goal_tags}
    ex_tag_set = {tag['id'] for tag in tag_list if tag['tagName'] in exclude_tags}

    # 将case_tree转换为case_list
    case_list = tree_to_case_list(case_tree)

    # 筛选出包含目标标签的用例
    tag_case_list = []
    for case in case_list:
        tag_nodes = [node for node in case if len(set(node["extra"]['tagInfo']['moduleTagList']) & tag_set) > 0]
        ex_tag_nodes = [node for node in case if len(set(node["extra"]['tagInfo']['moduleTagList']) & ex_tag_set) > 0]
        if len(tag_nodes) > 0 and len(ex_tag_nodes) == 0:
            tag_case_list.append(case)
    print("tag_case_list: ", len(tag_case_list))

    # 对tag_case_list用例上的所有节点进行标记，删除没标记的用例
    mark_id_set = set()
    for case in tag_case_list:
        for node in case:
            mark_id_set.add(node["caseNodeId"])

    print("mark_id_set", len(mark_id_set))

    def clear_tree(tree, exclude_set=set()):
        for child in tree.get("children", [])[:]:
            if child["caseNodeId"] in exclude_set:
                clear_tree(child, exclude_set)
            else:
                tree["children"].remove(child)
    clear_tree(case_tree, exclude_set=mark_id_set)
    return case_tree

def mark_target_cases(target_cases, os_type, product_id, mark_tag="优先生成的用例"):
    """
    给目标用例打上标记
    """
    qwu = QamateWebUtil(os_type)
    tag_list = qwu.get_product_tags(product_id)
    tag_dict = {tag['tagName']: tag['id'] for tag in tag_list}
    tag_id = tag_dict[mark_tag]

    for case in target_cases:
        # 给每个case的叶节点打上标记
        node = case[-1]
        tag_info = node['extra']['tagInfo']
        if tag_id not in tag_info['moduleTagList']:
            tag_info['moduleTagList'].append(tag_id)
        qwu.update_node_tag(node["caseNodeId"], tag_info)

def delete_all_auto_nodes(tree_node_id, os_type):
    """
    删除所有自动化节点
    """

    qwu = QamateWebUtil(os_type)
    case_tree = qwu.get_case_tree_query(tree_node_id)

    del_nodes = []
    for leaf in traversal_tree_leaves(case_tree):
        node_name = leaf['nodeName']
        if node_name == '自动化测试步骤':
            print(leaf['caseNodeId'], leaf['nodeName'])
            del_nodes.append(leaf['caseNodeId'])

    qwu.del_nodes(del_nodes)






if __name__ == '__main__':
    case_tree = get_case_tree(32676681, os_type=2, product_id=21, goal_tags=['待生成'], exclude_tags=['不支持UI生成'])
    target_cases = get_most_cover(case_tree)
    print(len(target_cases))
    mark_target_cases(target_cases, os_type=2, product_id=21, mark_tag="优先生成的用例")


if __name__ == '__main__1':
    delete_all_auto_nodes(30723245, os_type=2)
    # qwu = QamateWebUtil(2)
    # qwu.del_nodes([30728763])