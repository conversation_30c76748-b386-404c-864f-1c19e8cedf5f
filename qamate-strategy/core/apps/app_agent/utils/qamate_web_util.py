#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@Desc    :   
@File    :   qamate_web_util.py
@Time    :   2024-10-28
<AUTHOR>   <EMAIL>
"""
# import sys
# sys.path.append("/Users/<USER>/Workspace/Project/lazyOne/qamate-strategy/core")


import requests
import json
import traceback


from basics.util import logger


class QamateWebUtil(object):
    """
    和QAMate Web端交互的工具类
    """
    def __init__(self, os_type=2, module_id=-1):
        """
        初始化
        """
        self.__module_id = 0
        self.__token = "super-9a796933bf024ebfa0420dce6f864a51"

        self.timeout = 3
        self.network_retry_num = 3
        self.os_type = os_type
        self.module_id = module_id

    def qamate_web_post(self, path, input_data):
        """
        post请求qamate web
        """
        logger.info("post path: {}, input_data: {}".format(path, input_data))
        for no in range(self.network_retry_num):
            try:
                res = requests.post(
                    "https://qamate.baidu-int.com{}".format(path),
                    data=json.dumps(input_data),
                    headers={
                        "Content-Type": "application/json",
                        "QAMate-ModuleId": "{}".format(self.__module_id),
                        "QAMate-Token": self.__token
                    },
                    timeout=3
                )
                # logger.info("post no.{} res: {}".format(no, res.text))
                result = json.loads(res.text)
                if result["code"] != 0:
                    raise Exception("post error, reason: {}".format(result))
                return result
            except Exception:
                logger.error("错误信息: {}".format(traceback.format_exc()))
        return None

    def del_nodes(self, case_node_ids):
        """
        删除节点
        """
        path = "/core/case/node/update"
        input_data = {
            "caseNodeIdList": case_node_ids,
            "isDel": True
        }
        result = self.qamate_web_post(path, input_data)
        logger.info("del_nodes result: {}".format(result))

    def get_case_tree_query(self, case_root_id):
        """
        获取完成的case树
        """
        path = "/core/case/tree/query"
        input_data = {
            "caseRootId": case_root_id,
            "withSign": False,
            "withAuto": False
        }
        result = self.qamate_web_post(path, input_data)
        if result is None:
            return None
        
        return result["data"]

    def get_case_tree_query_with_auto(self, case_root_id):
        """
        获取完成的case树
        """
        path = "/core/case/tree/query"
        input_data = {
            "caseRootId": case_root_id,
            "withSign": False,
            "withAuto": True
        }
        result = self.qamate_web_post(path, input_data)
        if result is None:
            return None
        
        return result["data"]
    
    def get_casenode_desc(self, case_node_id):
        """
        获取完成的case树
        """
        path = "/core/case/node/desc/query"
        input_data = {
            "caseNodeId": case_node_id
        }
        result = self.qamate_web_post(path, input_data)
        if result is None:
            return None
        
        return result["data"]
    
    def add_step(self, case_node_id, step_info):
        """
        获取完成的case树
        """
        # return

        # 先查询目标节点下的step, 获取last_step_id
        path = '/core/case/node/step/list'
        input_data = {
            'caseNodeId': case_node_id,
            'osType': self.os_type
        }
        step_info_result = self.qamate_web_post(path, input_data)
        last_step_id = 0
        if len(step_info_result['data']['stepList']) > 0:
            last_step_id = step_info_result['data']['stepList'][-1]['stepId']

        # 然后添加step
        path = "/core/case/node/step/create"
        input_data = {
            "caseNodeId": case_node_id,
            "osType": self.os_type,
            "preSibId": last_step_id,
            "stepType": step_info["stepType"],
            "stepInfo": step_info["stepInfo"],
            "stepDesc": step_info["stepDesc"], 
            "creationType": 1,
            "creationFrom": 102
        }
        step_add_result = self.qamate_web_post(path, input_data)
        return step_add_result
    
    def add_step_with_sp_desc(self, case_node_id, step_info, sp_desc):
        """
        获取完成的case树
        """
        path = "/core/case/node/step/create"
        input_data = {
            "caseNodeId": case_node_id,
            "osType": self.os_type,
            "stepType": step_info["stepType"],
            "stepInfo": step_info["stepInfo"],
            "stepDesc": sp_desc, 
            "creationType": 1,
            "creationFrom": 102
        }
        step_add_result = self.qamate_web_post(path, input_data)
        return step_add_result

    def del_steps(self, step_ids):
        """
        删除步骤
        """

        # 先查询目标节点下的step, 获取last_step_id
        path = '/core/case/node/step/updateDel'
        input_data = {
            'stepIdList': step_ids,
            'isDel': True
        }
        del_result = self.qamate_web_post(path, input_data)
        assert del_result['code'] == 0

    def clear_manual_steps(self, case_node_id):
        """
        清空手动步骤
        """
        steps = self.get_node_step_list(case_node_id)
        manual_steps = [step for step in steps if step['stepType'] in (101, 102)]
        # if len(steps) > len(manual_steps):
        #     raise Exception("节点不是纯手工节点，不允许清空")
        if len(manual_steps) == 0:
            logger.info("节点没有手工步骤，不需要清空")
            return
        step_ids = [step['stepId'] for step in manual_steps]
        self.del_steps(step_ids)

    
    def get_product_tags(self, product_id):
        """
        获取完成的case树
        """

        # 先查询目标节点下的step, 获取last_step_id
        path = '/base/tag/listTag'
        input_data = {
            "productId": product_id,
            "pageSize": 100
        }
        tag_result = self.qamate_web_post(path, input_data)
        tag_list = tag_result["data"]["tagList"]

        return tag_list

    def update_node_tag(self, case_node_id, tag_info):
        """
        给节点打标签
        """
        path = '/core/case/node/update'
        input_data = {
            "caseNodeIdList": [case_node_id],
            "tagInfo": tag_info
        }
        update_result = self.qamate_web_post(path, input_data)
        return update_result

    def update_node_desc(self, case_node_id: int, desc: str):
        """
        更新节点测试设计信息
        """
        path = '/core/case/node/desc/update'
        input_data = {
            "caseNodeId": case_node_id,
            "description": desc
        }
        update_result = self.qamate_web_post(path, input_data)
        return update_result
    
    def add_auto_node_tmp(self, case_node_id):
        """
        获取完成的case树
        """

        # 先查询目标节点下的step, 获取last_step_id
        path = '/core/case/node/create'
        input_data = {
            "parentCaseNodeId": case_node_id,
            "nodeType": 2,
            "nodeName": "自动化测试步骤",
        }
        add_node_result = self.qamate_web_post(path, input_data)
        last_node_id = add_node_result["data"]["caseNodeIdList"][0]

        return last_node_id
    
    def add_node(self, node_id, node_name):
        """
        添加节点
        """
        path = '/core/case/node/create'
        input_data = {
            "parentCaseNodeId": node_id,
            "nodeType": self.os_type,
            "nodeName": node_name,
        }
        add_node_result = self.qamate_web_post(path, input_data)
        last_node_id = add_node_result["data"]["caseNodeIdList"][0]

        return last_node_id

    def get_app_package_list(self):
        """
        得到 包名，用于启动App
        :return:
        """
        if self.module_id == -1:
            raise Exception("未传入moduleId")
        path = '/core/module/app/list'
        input_data = {
            'moduleId': self.module_id,
            'osType': self.os_type
        }
        result = self.qamate_web_post(path, input_data)
        app_list = result['data']['appList']
        return app_list

    def get_template_info(self):
        """
        得到模板信息
        :return:
        """
        if self.module_id == -1:
            raise Exception("未传入moduleId")

        path = '/core/module/snippet/list'
        input_data = {
            'moduleId': self.module_id,
            'osType': self.os_type
        }
        result = self.qamate_web_post(path, input_data)
        temp_list = result['data']
        return temp_list

    def get_node_step_list(self, case_node_id):
        """
        得到Node step list
        :param case_node_id:
        :return:
        """
        path = '/core/case/node/step/list'
        input_data = {
            'caseNodeId': case_node_id,
            'osType': self.os_type
        }
        result = self.qamate_web_post(path, input_data)
        step_case = result['data']['stepList']
        return step_case
    
    def add_node_desc(self, case_node_id, desc):
        """
        添加节点描述
        """
        path = "/core/case/node/desc/update"
        input_data = {
            'caseNodeId': case_node_id,
            'description': desc
        }
        result = self.qamate_web_post(path, input_data)
        return result



if __name__ == "__main__":
    qwu = QamateWebUtil(module_id=21, os_type=2)
    res = qwu.get_node_step_list(23215278)
    print(res)
    for step in res:
        print(step)
    # print(qwu.add_step(9690620))