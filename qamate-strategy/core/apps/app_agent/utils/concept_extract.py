"""
概念抽取，从业务的用例中抽取出专有名词
"""
import json

from apps.app_agent.utils.qamate_web_util import QamateWebUtil
from apps.app_agent.utils.wenxin_util import WenXinUtils


def tree_to_case_list(case_tree):
    """
    用例树转用例列表
    """
    case_list = []

    def traversal_tree(tree, path=[]):
        new_path = path + [tree]
        if len(tree.get("children", [])) == 0:
            case_list.append(new_path)
        for child in tree.get("children", []):
            traversal_tree(child, new_path)

    traversal_tree(case_tree)
    return case_list


def get_cases(tree_node_id, os_type):
    """
    根据标签和端获得过滤后的用例树
    """

    def traversal_tree(tree):
        yield tree
        for child in tree.get("children", []):
            yield from traversal_tree(child)

    qwu = QamateWebUtil(os_type)
    case_tree = qwu.get_case_tree_query(tree_node_id)
    case_list = tree_to_case_list(case_tree)
    return case_list


def get_prompt(case_str):
    """
    获取提示语
    """
    return f"""你是一个APP测试助手，下面我将提供一段或多段文本，这些文本和某个APP的功能、场景相关，其中可能会有一些APP的专有名词，请帮我抽取出这些专有名词。

&& 提供的文本描述 &&
{case_str}
&& 输出格式 &&
你需要按照json格式输出以下内容，不要输出多余的信息：
[
    string, // 抽取出来的专有名词
    ...
]
"""


def extract_list(s):
    """
    从字符串中提取json列表
    """
    s_cleaned = s.strip('```json\n')
    return json.loads(s_cleaned)


def spl_by_len(texts, len_limit=100):
    """
    根据文本长度分组，每组长度不超过限制
    """
    groups = []
    group = []
    cur_text = ''
    for text in texts:
        cur_text += text
        group.append(text)
        if len(cur_text) > len_limit:
            groups.append(group)
            group = []
            cur_text = ''
    return groups


def analyse_case(tree_node_id=32157939, os_type=2, len_limit=100):
    """
    分析一整份用例树，提取出其中的业务概念，并按出现频率从大到小排序
    """
    wenxin = WenXinUtils()
    case_list = get_cases(tree_node_id, os_type)
    all_text_map = {}
    for case in case_list:
        for node in case:
            t = node['nodeName'].strip()
            if len(t) == 0:
                continue
            all_text_map[t] = all_text_map.get(t, 0) + 1

    s = sorted(all_text_map.items(), key=lambda x: -x[1])

    texts = [k for k, v in s]
    groups = spl_by_len(texts, len_limit=len_limit)
    cpt_map = {}
    for group in groups:
        # print(group)
        s = '\n'.join(group)
        prompt = get_prompt(s)
        res = wenxin.get_llm_result(prompt)
        r = extract_list(res)
        for cpt in r:
            for k, v in all_text_map.items():
                if cpt in k:
                    cpt_map[cpt] = cpt_map.get(cpt, 0) + v

    sorted_cpts = sorted(cpt_map.items(), key=lambda x: -x[1])
    # for k, v in sorted_cpts:
    #     print(k, v)
    return sorted_cpts


if __name__ == '__main__':
    analyse_case()

if __name__ == '__main__1':
    case_list = get_cases(32157939, os_type=2)
    wenxin = WenXinUtils()
    concepts = set()
    all_texts = set()
    for case in case_list[:]:
        names = [node['nodeName'].strip() for node in case if node['nodeName'].strip() != '']
        all_texts |= set(names)
        # print("\n" * 3)
        # case_str = '->'.join(names)
        # prompt = get_prompt_2(case_str)
        # res = wenxin.get_llm_result(prompt)
        # r = extract_list(res)
        # print(r)
        # concepts |= set(r)
    print(all_texts)

    blocks = []
    cur_block = []
    cur_text = ''
    for text in all_texts:
        cur_text = cur_text + text
        cur_block.append(text)
        if len(cur_text) > 100:
            blocks.append(cur_block)
            cur_block = []
            cur_text = ''
    print(len(blocks))

    for block in blocks:
        case_str = ', '.join(block)
        prompt = get_prompt(case_str)
        print(len(prompt))
        res = wenxin.get_llm_result(prompt)
        r = extract_list(res)
        print(r)
