#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@File    : Yi<PERSON>an.py
<AUTHOR> <PERSON><PERSON><PERSON><PERSON>(renyan<PERSON>@baidu.com)
@Time    : 2024/11/15 11:29
@Desc    : 
"""

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@File    : wenxin_util.py
<AUTHOR> <PERSON><PERSON><PERSON><PERSON>(<EMAIL>)
@Time    : 2024/7/30 18:50
@Desc    : https://cloud.baidu.com/doc/WENXINWORKSHOP/s/clntwmv7t
"""


import json
import traceback
import time

import requests

from basics.util import logger
from apps.app_agent.conf.contants import WENXIN_AK, WENXIN_SK
from apps.app_agent.utils.exception import AgentErrCode, AgentException


class Yi<PERSON><PERSON>(object):
    """
    文心工具
    """

    def __init__(self, model_name: str = "ernie-4"):
        """
        初始化
        :param 模型名，当前仅支持ERNIE-Bot:
        """
        self.model_name = model_name.lower()
        self.url = self._get_url()
        self.token = self._get_access_token()

    def _get_url(self):
        """

        :return:
        """
        model_url_map = {
            "ernie-4": "https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/completions_pro",
            "ernie-4-turbo": "https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/ernie-4.0-turbo-8k",
            "ernie-3.5": "https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/completions",
            "ernie-3.5-128k": "https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/ernie-3.5-128k",
            "ernie-speed": "https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/ernie_speed",
            "ernie-speed-128k": "https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/ernie-speed-128k"
        }
        if self.model_name not in model_url_map:
            logger.error("model_name:{} not in {}, change to ernie-4".format(self.model_name,
                                                                             list(model_url_map.keys())))
            return model_url_map['ernie-4']
        return model_url_map[self.model_name]

    def _get_access_token(cls):
        """
        获取百度AI开放平台的access token
        """

        url = "https://aip.baidubce.com/oauth/2.0/token?grant_type=client_" \
              "credentials&client_id={}&client_secret={}".format(WENXIN_AK, WENXIN_SK)

        payload = ""
        headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        }

        response = requests.request("POST", url, headers=headers, data=payload)

        if response.status_code == 200:
            access_token = json.loads(response.text).get("access_token")
        else:
            raise AgentException(err_code=AgentErrCode.WENXIN_TOKEN_ERROR,
                                 detail="获取百度AI开放平台的access token失败:{}".format(response.text))
        return access_token

    def param_check(self, chat_message: list):
        """
        参数校验
        :param chat_message:
        :return:
        """

        if not isinstance(chat_message, list):
            AgentException(err_code=AgentErrCode.PARAM_ERROR)

        for idx, item in enumerate(chat_message):
            role = item.get("role", "")
            if role not in ("assistant", "user", "system"):
                raise AgentException(err_code=AgentErrCode.PARAM_ERROR,
                                     detail="role must be 'assistant', 'user' or 'system'")
            if idx % 2 == 1 and role != "user":
                raise AgentException(err_code=AgentErrCode.PARAM_ERROR,
                                     detail="the odd-index role must be 'user'")

    def message_translate(self, chat_message: list):
        """

        :param chat_message:
        :return:
        """
        message = []
        for idx, item in enumerate(chat_message):
            if item['role'] == "system":
                continue
            message.append({
                "role": item['role'],
                "content": item['content'][0]['text'] if not isinstance(item['content'], str) else item['content']
            })
        return message

    def chat(self, chat_message, **kwargs):
        """

        :param chat_message:
        :param kwargs:
        :return:
        """
        self.param_check(chat_message=chat_message)
        message = self.message_translate(chat_message=chat_message)

        url = self.url + "?access_token=" + self.token
        payload = json.dumps({
            "messages": message,
            "temperature": kwargs.get("temperature", 0.8),
            "top_p": kwargs.get("top_p", 0.8),
            "penalty_score": kwargs.get("penalty_score", 1.0),
            "user_id": "mail:renyanwei",
            "max_output_tokens": kwargs.get("max_output_tokens", 1024),
            "response_format": kwargs.get("response_format", "text")
        })

        headers = {
            'Content-Type': 'application/json'
        }

        i = 0
        answer = ""
        while i < 5:
            try:
                response = requests.request("POST", url, headers=headers, data=payload)
                answer = json.loads(response.text).get("result")
                logger.info("{}:===================================== \n{}".format(self.model_name, answer))
                if answer == "None":
                    raise Exception("模型输出异常")
                return answer
            except Exception as e:
                logger.error(traceback.format_exc())
                logger.error(response.text)
                logger.error("获取wenxin结果失败, 重试中...")
                time.sleep(1)
                i += 1
                continue
        raise AgentException(err_code=AgentErrCode.WENXIN_LLM_ERROR, detail=response.text)


if __name__ == '__main__':
    from apps.app_agent.utils.chat_message import ChatMessage
    msg = ChatMessage()
    # 添加用户消息
    msg.add_user_message("介绍下你自己")
    res = YiYan().chat(msg)


