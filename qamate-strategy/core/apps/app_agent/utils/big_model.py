#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@File    : big_model.py
<AUTHOR> <PERSON><PERSON><PERSON><PERSON>(renya<PERSON><PERSON>@baidu.com)
@Time    : 2025/3/10 14:56
@Desc    : 
"""
import json
import time

import requests

from apps.app_agent.conf.contants import OPENAI_ORGANIZATION_ID, GENERATE_API_KEY
from apps.app_agent.utils.ChatGPT import ChatGPT
from apps.app_agent.utils.LocalModel import LocalModel
from apps.app_agent.utils.QianFan import Qi<PERSON><PERSON>an
from apps.app_agent.utils.Qwen import Qwen, QWEN_API
from apps.app_agent.utils.exception import AgentErrCode, AgentException
from basics.util import logger


class QAMateLLM(object):
    """
    QAMate封装的大模型调用接口，便于进行数据统计
    """
    MAX_RETRIES = 3
    def __init__(self, model_name="doubao-1.5-vision-pro-250328", extra: dict={}, ctx: dict=None):
        """
        :param model_name:
        """
        self.model_name = model_name
        self.model = None
        # 路由模型
        self.route_model()
        self.extra = extra
        self.ctx = ctx

    def route_model(self):
        """
        根据模型名路由到平台
        """
        if self.model_name.startswith('qw') or self.model_name.startswith('qv'):
            logger.info("百炼千问模型：{}".format(self.model_name))
            self.platform = "qwen"
        elif self.model_name.startswith("gpt"):
            logger.info("CloseAI GPT模型：{}".format(self.model_name))
            self.platform = "gpt"
        elif self.model_name.startswith("ernie"):
            logger.info("千帆文心模型：{}".format(self.model_name))
            self.platform = "qianfan"
        elif self.model_name.startswith("doubao"):
            logger.info("火山豆包模型：{}".format(self.model_name))
            self.platform = "doubao"
        elif self.model_name.startswith("/"):
            logger.info("本地部署模型：{}".format(self.model_name))
            self.platform = "local"
        else:
            raise AgentException(err_code=AgentErrCode.UNKNOWN_MODEL)

    def wrap_cost_params(self, data):
        """
        封装大模型成本中心相关的参数
        """
        if self.ctx is None:
            return
        if 'module_id' in self.ctx and self.ctx['module_id'] is not None:
            data['moduleId'] = self.ctx['module_id']
        if 'task_id' in self.ctx and self.ctx['task_id'] is not None:
            data['taskId'] = self.ctx['task_id']
        if 'task_type' in self.ctx and self.ctx['task_type'] is not None:
            data['taskType'] = self.ctx['task_type']

    def call(self, chat_message):
        """
        调用qamate
        """
        url = 'https://qamate.baidu-int.com/core/ui/model/create'
        headers = {
            'Qamate-Token': 'super-9a796933bf024ebfa0420dce6f864a51',
            'Content-Type': 'application/json',
            'QAMate-ModuleId': "0"
        }

        data = {
            'platform': self.platform,
            'extra': self.extra,
            'modelParams': {
                'model': self.model_name,
                'messages': chat_message
            }
        }

        self.wrap_cost_params(data)
        # print(json.dumps(data, ensure_ascii=False, indent=4))
        # with open('tmp.json', 'w') as f:
        #     json.dump(data, f, ensure_ascii=False, indent=4)

        resp = requests.post(url=url, headers=headers, json=data).json()
        logger.info("llm result {}".format(resp))
        result_str = resp['data']['modelResult']
        result = json.loads(result_str)
        return result['choices'][0]['message']['content']

    def chat(self, chat_message: list, **kwargs):
        """
        调用大模型，返回结果
        :param chat_message:
        :param kwargs:
        :return: 大模型的回答
        """
        if isinstance(kwargs.get("response_format", ""), str) and kwargs.get("response_format", "") == "json_object":
            kwargs['response_format'] = {"type": "json_object"}

        retries = 0
        while retries < self.MAX_RETRIES:
            try:
                result = self.call(chat_message)
                return result
            except Exception as e:
                logger.error(f"QAMate API error: {e}")
                retries += 1
                if retries < self.MAX_RETRIES:
                    sleep_time = 2
                    logger.info(f"Retrying in {sleep_time} seconds...")
                    time.sleep(sleep_time)
                else:
                    logger.error("Max retries reached. Raising exception.")
                    raise



class BigModel(object):
    """
    大模型路由
    """
    def __init__(self, model_name="doubao-1.5-vision-pro-250328"):
        """

        :param model_name:
        """
        self.model_name = model_name
        self.model = None
        # 路由模型
        self.route_model()

    def route_model(self):
        """
        模型路由
        :return:
        """
        if self.model_name.startswith('qw') or self.model_name.startswith('qv'):
            logger.info("百炼千问模型：{}".format(self.model_name))
            self.model = Qwen(api_key=QWEN_API)
        elif self.model_name.startswith("gpt"):
            logger.info("CloseAI GPT模型：{}".format(self.model_name))
            self.model = ChatGPT(api_key=GENERATE_API_KEY, organization_id=OPENAI_ORGANIZATION_ID)
        elif self.model_name.startswith("ernie"):
            logger.info("千帆文心模型：{}".format(self.model_name))
            self.model = QianFan(model_name=self.model_name)
        elif self.model_name.startswith("/"):
            logger.info("本地部署模型：{}".format(self.model_name))
            self.model = LocalModel()
        else:
            raise AgentException(err_code=AgentErrCode.UNKNOWN_MODEL)

    def chat(self, chat_message: list, **kwargs):
        """
        :param chat_message:
        :param kwargs:
        :return:
        """

        return self.model.chat(chat_message, **kwargs, model_name=self.model_name)


if __name__ == '__main__':
    from apps.app_agent.utils.chat_message import ChatMessage

    msg = ChatMessage()
    # 添加用户消息
    msg.add_user_message("介绍下这幅图片",
                         image_paths=["/Users/<USER>/Downloads/9A474739644928E2B6A76A4A1.jpg"])
    # msg.add_user_message("介绍下你自己")
    # res = BigModel(model_name="/ssd1/ep/Qwen2.5-VL-72B-Instruct").chat(chat_message=msg)
    # res = BigModel(model_name="qwen-vl-max-2025-01-25").chat(chat_message=msg)
    # res = BigModel(model_name="gpt-4o-2024-11-20").chat(chat_message=msg)
    # res = BigModel(model_name="ernie-4.5-8k-preview").chat(chat_message=msg)
    res = QAMateLLM(model_name="doubao-1.5-vision-pro-250328").chat(chat_message=msg)
    print(res)
