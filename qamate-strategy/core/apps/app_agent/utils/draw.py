# !/usr/bin/env python3
# -*- coding: UTF-8 -*-
################################################################################
#
# Copyright (c) 2024 Baidu.com, Inc. All Rights Reserved
#
################################################################################
"""
画图工具

Authors: <AUTHORS>
Date:    2024/01/24
"""

from typing import List

import cv2
import numpy as np
from PIL import Image, ImageDraw


def draw_rect(image_path, rect: dict, color=(0, 0, 255), line_width=4, output_path=None):
    """

    :param image_path:
    :param rect:
    :param color:
    :param line_width:
    :param output_path:
    :return:
    """
    imgcv = cv2.imread(image_path)
    new_image = imgcv.copy()
    new_image = cv2.rectangle(new_image, (rect['x'], rect['y']),
                              (rect['x'] + rect['w'], rect['y'] + rect['h']), color, line_width)
    if output_path:
        cv2.imwrite(output_path, new_image)
    return new_image


def draw_rects(image_path, rects: List[dict], color=(0, 0, 255), line_width=4, output_path=None):
    """

    :param image_path:
    :param rect:
    :param color:
    :param line_width:
    :param output_path:
    :return:
    """
    imgcv = cv2.imread(image_path)
    new_image = imgcv.copy()
    for rect in rects:
        new_image = cv2.rectangle(new_image, (rect['x'], rect['y']),
                                  (rect['x'] + rect['w'], rect['y'] + rect['h']), color, line_width)
    if output_path:
        cv2.imwrite(output_path, new_image)
    return new_image


def draw_arrow(image_path, direction, output_path=None, reduce_size=1):
    """
    画箭头
    :param image_path:
    :param direction:
    :param output_path:
    :return:
    """
    # 打开图像文件
    img = Image.open(image_path)
    draw = ImageDraw.Draw(img)

    # 计算图像尺寸
    width, height = img.size

    # 设置箭头的基本参数
    arrow_length = int(500 * reduce_size)
    arrow_head_length = int(80 * reduce_size)
    line_width = int(10 * reduce_size)

    # 确定箭头的起点和终点
    if direction == 'up':
        start_point = (width // 2, height // 2 + arrow_length // 2)
        end_point = (width // 2, height // 2 - arrow_length // 2)
    elif direction == 'down':
        start_point = (width // 2, height // 2 - arrow_length // 2)
        end_point = (width // 2, height // 2 + arrow_length // 2)
    elif direction == 'left':
        start_point = (width // 2 + arrow_length // 2, height // 2)
        end_point = (width // 2 - arrow_length // 2, height // 2)
    elif direction == 'right':
        start_point = (width // 2 - arrow_length // 2, height // 2)
        end_point = (width // 2 + arrow_length // 2, height // 2)
    else:
        raise ValueError("Invalid direction. Use 'up', 'down', 'left', or 'right'.")

    # 画直线部分
    draw.line([start_point, end_point], fill='red', width=line_width)

    # 画箭头的头部
    if direction in ['up', 'down']:
        draw.line([end_point, (end_point[0] - arrow_head_length,
                               end_point[1] + (arrow_head_length if direction == 'up' else -arrow_head_length))],
                  fill='red', width=line_width)
        draw.line([end_point, (end_point[0] + arrow_head_length,
                               end_point[1] + (arrow_head_length if direction == 'up' else -arrow_head_length))],
                  fill='red', width=line_width)
    elif direction in ['left', 'right']:
        draw.line([end_point, (end_point[0] + (arrow_head_length if direction == 'left' else -arrow_head_length),
                               end_point[1] - arrow_head_length)],
                  fill='red', width=line_width)
        draw.line([end_point, (end_point[0] + (arrow_head_length if direction == 'left' else -arrow_head_length),
                               end_point[1] + arrow_head_length)],
                  fill='red', width=line_width)

    # 保存图像
    if output_path:
        img.save(output_path)


def draw_rect_with_index(imgcv: np.ndarray, result: list,
                         color=(0, 0, 255), line_width=4, font_size=1.5, output_path=None):
    """
    输出检测结果
    :param line_width:
    :param color:
    :param imgcv: 图片数据
    :param output_path:  输出检测结果图片路径
    :param result: 检测结果
    :return:
    """
    new_imgcv = imgcv.copy()
    idx = 0
    for item in result:
        left = item["x"]
        top = item["y"]
        right = item["x"] + item["w"]
        bottom = item["y"] + item["h"]

        new_imgcv = cv2.rectangle(new_imgcv, (left, top), (right, bottom), color, line_width)
        label = str(idx)

        # Display the label at the top of the bounding box
        label_size, base_line = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, font_size, 2)
        top = max(top, label_size[1])
        new_imgcv = cv2.rectangle(new_imgcv, (left, top - round(1.2 * label_size[1])),
                                  (left + round(1.2 * label_size[0]), top),
                                  (255, 0, 0), cv2.FILLED)
        new_imgcv = cv2.putText(new_imgcv, label, (left + 1, top - 1), cv2.FONT_HERSHEY_SIMPLEX, font_size,
                                (255, 255, 255), 2)

        idx += 1
    if output_path:
        cv2.imwrite(output_path, new_imgcv)
    return new_imgcv


if __name__ == '__main__':
    draw_arrow("/Users/<USER>/.lazyOne/localServer/cache/bcde80d4-2b6a-4345-8fad-ed732c01ec73/0_exec.jpg",
               "left", "swip.jpg", reduce_size=0.5)
