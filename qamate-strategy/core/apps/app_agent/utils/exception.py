#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@File    : exception_n.py
<AUTHOR> <PERSON><PERSON><PERSON><PERSON>(renya<PERSON><PERSON>@baidu.com)
@Time    : 2024/7/8 15:14
@Desc    :
"""

from enum import Enum, unique


@unique
class AgentErrCode(Enum):
    """
    自定义错误码
    """
    # 成功
    SUCCESS = {
        0: "success"
    }

    # 执行错误
    UNKNOWN_ACTION_TYPE = {201: "unknown action type"}
    UNKNOWN_CHECK_TYPE = {202: "unknown check type"}

    # 4~ 参数异常
    PARAM_ERROR = {400: 'param error'}
    NO_USEFUL_PRODUCT_KNOWLEDGE = {401: "no userful product knowledge"}
    UNKNOWN_MODEL = {402: "unknown model"}

    # 5~ 外部依赖异常
    LLM_UNEXPECTED_OUPUT = {501: "llm unexpected output"}
    GPT_ERROR = {510: 'GPT error'}

    WENXIN_ERROR = {520: 'Wenxin error'}
    WENXIN_LLM_ERROR = {521: 'Wenxin LLM error'}
    WENXIN_TOKEN_ERROR = {522: 'Wenxin token error'}
    WENXIN_EMBEDDING_ERROR = {523: 'Wenxin embedding error'}

    Agent_ERROR = {530: 'EP Agent error'}

    def get_code(self):
        """
        根据枚举名称取状态码code
        :return: 状态码code
        """
        return list(self.value.keys())[0]

    def get_msg(self):
        """
        根据枚举名称取状态说明msg
        :return: 状态说明msg
        """
        return list(self.value.values())[0]


class AgentException(Exception):
    """
    自定义异常
    """

    def __init__(self, err_code: AgentErrCode, detail: str = "", param=None):
        """
        初始化异常类
        :param err_code: 错误码
        :param detail: 错误细节描述
        """
        self.err_code = err_code
        self.detail = detail
        self.param = param

    def __str__(self):
        return ("AgentException:\n"
                "code->{}\n"
                "msg->{}\n"
                "detail:->{}").format(self.err_code.get_code(),
                                      self.err_code.get_msg(),
                                      self.detail)


if __name__ == '__main__':
    try:
        raise AgentException(err_code=AgentErrCode.BASIC_ERROR, detail='测试')
    except Exception as e:
        print(e)
