#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@File    : ZhipuChat.py
<AUTHOR> <PERSON><PERSON><PERSON><PERSON>(renya<PERSON><PERSON>@baidu.com)
@Time    : 2025/2/18 10:53
@Desc    : 
"""

import time
import json

from zhipuai import ZhipuAI

from apps.app_agent.conf.contants import ZHIPU_API
from basics.util import logger
from apps.app_agent.utils.exception import AgentErrCode, AgentException

MAX_RETRIES = 3


class GLMChat(object):
    """
    智谱GLM
    """

    def __init__(self, api_key=ZHIPU_API):
        """

        :param api_key:
        :param organization_id:
        """
        self.api_key = api_key
        self.client = ZhipuAI(api_key=self.api_key)

    def param_check(self, chat_message: list):
        """
        参数校验
        :param chat_message:
        :return:
        """

        if not isinstance(chat_message, list):
            AgentException(err_code=AgentErrCode.PARAM_ERROR)

        for idx, item in enumerate(chat_message):
            role = item.get("role", "")
            if role not in ("assistant", "user", "system"):
                raise AgentException(err_code=AgentErrCode.PARAM_ERROR,
                                     detail="role must be 'assistant', 'user' or 'system'")
            if idx % 2 == 1 and role != "user":
                raise AgentException(err_code=AgentErrCode.PARAM_ERROR,
                                     detail="the odd-index role must be 'user'")

    def analyze_message(self, chat_message: list):
        """
        解析message 将OpenAi样式转为ZhipuAI样式
        :param chat_message:
        :return:
        """
        for item in chat_message:
            if item.get("role", "") == "user":
                for cont_item in item.get("content", []):
                    if cont_item.get("type", "") == "image_url":
                        cont_item['image_url']['url'] = cont_item['image_url']['url'].replace("data:image/jpeg;base64,",
                                                                                              "")
        return chat_message

    def chat(self, chat_message: list, model_name="glm-4v-plus-0111", **kwargs):
        """
        对话
        :param chat_message:
        :param model_name:
        :param kwargs:
        :return:
        """

        self.param_check(chat_message=chat_message)
        chat_message = self.analyze_message(chat_message=chat_message)

        if isinstance(kwargs.get("response_format", ""), str) and kwargs.get("response_format", "") == "json_object":
            kwargs['response_format'] = {"type": "json_object"}

        retries = 0
        while retries < MAX_RETRIES:
            try:
                completion = self.client.chat.completions.create(
                    model=model_name,
                    messages=chat_message,
                    **kwargs
                )
                result = completion.choices[0].message.content
                logger.info("{}:---------------\n{}".format(model_name, result))
                logger.info(
                    "total_tokens:{}, prompt_tokens:{}, completion_tokens:{}".format(completion.usage.total_tokens,
                                                                                     completion.usage.prompt_tokens,
                                                                                     completion.usage.completion_tokens)
                )
                return result

            except Exception as e:
                logger.error(f"ZhipuAI API error: {e}")
                retries += 1
                if retries < MAX_RETRIES:
                    sleep_time = 2
                    logger.info(f"Retrying in {sleep_time} seconds...")
                    time.sleep(sleep_time)
                else:
                    logger.error("Max retries reached. Raising exception.")
                    raise


if __name__ == '__main__':
    from apps.app_agent.utils.chat_message import ChatMessage

    msg = ChatMessage()
    # 添加用户消息
    msg.add_user_message("介绍下这幅图片",
                         image_paths=["/Users/<USER>/Downloads/图片/03.png"])
    # msg.add_user_message("介绍下你自己")
    res = GLMChat().chat(msg)
