#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@File    : Qwen.py
<AUTHOR> <PERSON><PERSON><PERSON><PERSON>(renya<PERSON><PERSON>@baidu.com)
@Time    : 2025/3/10 10:51
@Desc    : 
"""

import time
import json

import openai
from openai import OpenAI

from basics.util import logger
from apps.app_agent.utils.exception import AgentErrCode, AgentException

MAX_RETRIES = 3


class LocalModel(object):
    """
    GPT
    """

    def __init__(self, model_url="http://10.214.169.79:8002/v1"):
        """

        :param api_key:
        :param organization_id:
        """
        self.api_key = "EMPTY"
        self.base_url = model_url
        self.client = OpenAI(api_key=self.api_key, base_url=self.base_url)

    def param_check(self, chat_message: list):
        """
        参数校验
        :param chat_message:
        :return:
        """

        if not isinstance(chat_message, list):
            AgentException(err_code=AgentErrCode.PARAM_ERROR)

        for idx, item in enumerate(chat_message):
            role = item.get("role", "")
            if role not in ("assistant", "user", "system"):
                raise AgentException(err_code=AgentErrCode.PARAM_ERROR,
                                     detail="role must be 'assistant', 'user' or 'system'")
            if idx % 2 == 1 and role != "user":
                raise AgentException(err_code=AgentErrCode.PARAM_ERROR,
                                     detail="the odd-index role must be 'user'")

    def chat(self, chat_message: list, model_name="/ssd1/ep/Qwen2.5-VL-72B-Instruct", **kwargs):
        """
        对话
        :param chat_message:
        :param model_name:
        :param kwargs:
        :return:
        """

        self.param_check(chat_message=chat_message)

        if isinstance(kwargs.get("response_format", ""), str) and kwargs.get("response_format", "") == "json_object":
            kwargs['response_format'] = {'type': 'json_object'}

        retries = 0
        while retries < MAX_RETRIES:
            try:
                completion = self.client.chat.completions.create(
                    model=model_name,
                    messages=chat_message,
                    **kwargs
                )
                result = completion.choices[0].message.content
                logger.info("{}:---------------\n{}".format(model_name, result))
                logger.info(
                    "total_tokens:{}, prompt_tokens:{}, completion_tokens:{}".format(completion.usage.total_tokens,
                                                                                     completion.usage.prompt_tokens,
                                                                                     completion.usage.completion_tokens)
                )
                return result

            except openai._exceptions.OpenAIError as e:
                logger.error(f"Local API error: {e}")
                retries += 1
                if retries < MAX_RETRIES:
                    sleep_time = 2
                    logger.info(f"Retrying in {sleep_time} seconds...")
                    time.sleep(sleep_time)
                else:
                    logger.error("Max retries reached. Raising exception.")
                    raise


if __name__ == '__main__':
    from apps.app_agent.utils.chat_message import ChatMessage

    msg = ChatMessage()
    # 添加用户消息
    msg.add_user_message("介绍下这幅图片",
                         image_paths=["https://bj.bcebos.com/newmvp/lazy-one/6884017-26571023-1735808665760-"
                                      "85c32731-c502-4540-98d1-5218129c9b2c.jpg"])
    # msg.add_user_message("介绍下你自己, 以json的格式输出")
    res = LocalModel().chat(msg, model_name="/work/UI-TARS-72B-SFT", response_format="json_object")

    # msg.add_user_message(prompt,
    #                      image_paths=["/Users/<USER>/Downloads/图片/07b8751c-b023"
    #                                   "-4f50-a9b0-1282d7920be8/0_grid.jpg"])
    # res = LocalModel(model_url="http://10.214.169.78:8002/v1").chat(msg, model_name="/work/UI-TARS-72B-DPO")


