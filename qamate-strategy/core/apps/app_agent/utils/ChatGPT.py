#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@File    : ChatGPT.py
<AUTHOR> <PERSON><PERSON><PERSON><PERSON>(<EMAIL>)
@Time    : 2024/11/14 15:39
@Desc    : 
"""

import time
import json

import openai
from openai import OpenAI

from basics.util import logger
from apps.app_agent.utils.exception import AgentErrCode, AgentException
from apps.app_agent.conf.contants import DEFAULT_API_KEY, OPENAI_ORGANIZATION_ID

MAX_RETRIES = 3


class ChatGPT(object):
    """
    GPT
    """

    def __init__(self, api_key=DEFAULT_API_KEY, organization_id=OPENAI_ORGANIZATION_ID):
        """

        :param api_key:
        :param organization_id:
        """
        self.api_key = api_key
        self.organization_id = organization_id
        self.client = OpenAI(organization=self.organization_id, api_key=self.api_key)

    def param_check(self, chat_message: list):
        """
        参数校验
        :param chat_message:
        :return:
        """

        if not isinstance(chat_message, list):
            AgentException(err_code=AgentErrCode.PARAM_ERROR)

        for idx, item in enumerate(chat_message):
            role = item.get("role", "")
            if role not in ("assistant", "user", "system"):
                raise AgentException(err_code=AgentErrCode.PARAM_ERROR,
                                     detail="role must be 'assistant', 'user' or 'system'")
            if idx % 2 == 1 and role != "user":
                raise AgentException(err_code=AgentErrCode.PARAM_ERROR,
                                     detail="the odd-index role must be 'user'")

    def chat(self, chat_message: list, model_name="gpt-4o", **kwargs):
        """
        对话
        :param chat_message:
        :param model_name:
        :param kwargs:
        :return:
        """

        self.param_check(chat_message=chat_message)

        if isinstance(kwargs.get("response_format", ""), str) and kwargs.get("response_format", "") == "json_object":
            kwargs['response_format'] = {"type": "json_object"}

        retries = 0
        while retries < MAX_RETRIES:
            try:
                completion = self.client.chat.completions.create(
                    model=model_name,
                    messages=chat_message,
                    **kwargs
                )
                result = completion.choices[0].message.content
                logger.info("{}:---------------\n{}".format(model_name, result))
                logger.info(
                    "total_tokens:{}, prompt_tokens:{}, completion_tokens:{}".format(completion.usage.total_tokens,
                                                                                     completion.usage.prompt_tokens,
                                                                                     completion.usage.completion_tokens)
                )
                return result

            except openai._exceptions.OpenAIError as e:
                logger.error(f"OpenAI API error: {e}")
                retries += 1
                if retries < MAX_RETRIES:
                    sleep_time = 2
                    logger.info(f"Retrying in {sleep_time} seconds...")
                    time.sleep(sleep_time)
                else:
                    logger.error("Max retries reached. Raising exception.")
                    raise


if __name__ == '__main__':
    from apps.app_agent.utils.chat_message import ChatMessage

    msg = ChatMessage()
    # 添加用户消息
    msg.add_user_message("介绍下这幅图片",
                         image_paths=[
                             "/Users/<USER>/.lazyOne/localServer/cache/31547b26-c0b7-4e9b-855b-7945d8ff5f76/0.jpg"])
    res = ChatGPT().chat(msg)
