#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@File    : YiYan.py
<AUTHOR> <PERSON><PERSON><PERSON><PERSON>(<EMAIL>)
@Time    : 2024/11/15 11:29
@Desc    : https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/pKzJfZczuc/5eKOIcLZMe/Uk4hQjG2jPvSfO
"""

import json
import traceback
import time
import re

import requests

from basics.util import logger
from apps.app_agent.conf.contants import QIANFAN_AGENT_BU, QIANFAN_AGENT_TOKEN
from apps.app_agent.utils.exception import AgentErrCode, AgentException


class QianFan(object):
    """
    千帆EP代理工具
    """

    def __init__(self, model_name: str = "ernie-4.5-8k-preview", target="qianfan"):
        """
        初始化
        :param 模型名，当前仅支持ERNIE-Bot:
        """
        self.model_name = model_name
        self.target = target

        # 联系WangSheng申请
        self.url = "http://qerag.baidu-int.com/rag/openapi/v1/chat/{}".format(QIANFAN_AGENT_BU)
        self.token = QIANFAN_AGENT_TOKEN

        self.route_model_name()

    def route_model_name(self):
        """
        路由模型名字
        :return:
        """
        if self.model_name == "ernie-4":
            self.model_name = "ernie-4.0-8k"
        elif self.model_name == "ernie-3.5":
            self.model_name = "ernie-3.5-8k"
        elif self.model_name == "ernie-4.0-turbo":
            self.model_name = "ernie-4.0-turbo-8k"

    def param_check(self, chat_message: list):
        """
        参数校验
        :param chat_message:
        :return:
        """

        if not isinstance(chat_message, list):
            AgentException(err_code=AgentErrCode.PARAM_ERROR)

        for idx, item in enumerate(chat_message):
            role = item.get("role", "")
            if role not in ("assistant", "user", "system"):
                raise AgentException(err_code=AgentErrCode.PARAM_ERROR,
                                     detail="role must be 'assistant', 'user' or 'system'")
            if idx % 2 == 1 and role != "user":
                raise AgentException(err_code=AgentErrCode.PARAM_ERROR,
                                     detail="the odd-index role must be 'user'")

    def json_parse(self, result):
        """
        ernie4.5经常输出带有```json的json结果，此函数对结果进行兼容
        将结果解析为合法的json数据
        :return:
        """
        response = re.sub(r"^```json|```$", "", result.strip(), flags=re.MULTILINE)
        return response

    def chat(self, chat_message, **kwargs):
        """

        :param chat_message:
        :param kwargs:
        :return:
        """
        self.param_check(chat_message=chat_message)

        if isinstance(kwargs.get("response_format", ""), str) and kwargs.get("response_format", "") == "json_object":
            kwargs['response_format'] = {"type": "json_object"}

        payload = json.dumps({
            "target": self.target,
            "model": self.model_name,
            "messages": chat_message,
            "temperature": kwargs.get("temperature", 0.95),
            "topP": kwargs.get("top_p", 0.7),
            "penaltyScore": kwargs.get("penalty_score", 1.0),
            "maxCompletionTokens": kwargs.get("max_completion_tokens", 2048),
            "responseFormat": kwargs.get("response_format", {"type": "text"})
        })

        headers = {
            'Content-Type': 'application/json',
            'Authorization': self.token
        }

        i = 0
        answer = ""
        while i < 5:
            try:
                response = requests.request("POST", self.url, headers=headers, data=payload)
                if json.loads(response.text).get("code") != 200:
                    raise AgentException(err_code=AgentErrCode.Agent_ERROR)

                completion = json.loads(response.text).get("data")
                result = completion['choices'][0]['message']['content']

                # json结果兼容
                if kwargs.get("response_format", {}) == {"type": "json_object"}:
                    result = self.json_parse(result)

                logger.info("{}:---------------\n{}".format(self.model_name, result))
                logger.info(
                    "total_tokens:{}, prompt_tokens:{}, completion_tokens:{}".format(
                        completion['usage']['total_tokens'], completion['usage']['prompt_tokens'],
                        completion['usage']['completion_tokens'])
                )
                return result
            except Exception as e:
                logger.error(traceback.format_exc())
                logger.error(response.text)
                logger.error("获取QianFan结果失败, 重试中...")
                time.sleep(1)
                i += 1
                continue
        raise AgentException(err_code=AgentErrCode.WENXIN_LLM_ERROR, detail=response.text)


if __name__ == '__main__':
    from apps.app_agent.utils.chat_message import ChatMessage

    msg = ChatMessage()
    # 添加用户消息
    # msg.add_user_message("介绍下你自己, 以json结果输出")
    msg.add_user_message("介绍下这幅图片, 以json格式输出",
                         image_paths=["/Users/<USER>/Downloads/图片/07b8751c-b023-4f50-a9b0-1282d7920be8/0.jpg"],
                         img_quality="low"
                         )
    res = QianFan(model_name="ernie-4.5-8k-preview").chat(msg, temperature=0.1, response_format={"type": "json_object"})
