#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@File    : openaiGPT.py
<AUTHOR> <PERSON><PERSON><PERSON><PERSON>(<EMAIL>)
@Time    : 2024/8/30 18:57
@Desc    : 
"""

import time
import json

import openai
from openai import OpenAI

from basics.util import logger
from apps.app_agent.utils.exception import AgentErrCode, AgentException
from apps.app_agent.conf.contants import DEFAULT_API_KEY, OPENAI_ORGANIZATION_ID

MAX_RETRIES = 3


def chat_with_gpt(chat_list, api_key=DEFAULT_API_KEY, model_name="gpt-4o", **kwargs):
    """
    用于单轮或多轮交互
    :param chat_list: list[[role, content]]
    :param model_name:
    :return:
    """

    message = []

    if isinstance(chat_list, list):
        for idx, item in enumerate(chat_list):
            role = item[0]
            content = item[1]
            if role not in ("assistant", "user", "system"):
                raise AgentException(err_code=AgentErrCode.PARAM_ERROR,
                                     detail="role must be 'assistant', 'user' or 'system'")
            if idx % 2 == 1 and role != "user":
                raise AgentException(err_code=AgentErrCode.PARAM_ERROR,
                                     detail="the odd-index role must be 'user'")
            message.append({
                "role": role,
                "content": content
            })
    elif isinstance(chat_list, str):
        message = [{"role": "user", "content": chat_list}]
    else:
        raise AgentException(err_code=AgentErrCode.PARAM_ERROR)

    client = OpenAI(
        organization=OPENAI_ORGANIZATION_ID,
        api_key=api_key
    )

    if isinstance(kwargs.get("response_format", ""), str) and kwargs.get("response_format", "") == "json_object":
        kwargs['response_format'] = {"type": "json_object"}

    retries = 0
    while retries < MAX_RETRIES:
        try:
            completion = client.chat.completions.create(
                model=model_name,
                messages=message,
                **kwargs
            )
            result = completion.choices[0].message.content
            logger.info("{}:---------------\n{}".format(model_name, result))
            return result

        except openai._exceptions.OpenAIError as e:
            logger.error(f"OpenAI API error: {e}")
            retries += 1
            if retries < MAX_RETRIES:
                sleep_time = 2
                logger.info(f"Retrying in {sleep_time} seconds...")
                time.sleep(sleep_time)
            else:
                logger.error("Max retries reached. Raising exception.")
                raise


if __name__ == '__main__':
    chat_list = [["user", "介绍下你自己, 告诉我你的姓名、知识储备期限、所属公司"]]
    from apps.app_agent.conf.contants import GENERATE_API_KEY, REPAIR_API_KEY
    res = chat_with_gpt(chat_list, api_key=GENERATE_API_KEY)
    # res1 = chat_with_gpt(chat_list, api_key=REPAIR_API_KEY)
