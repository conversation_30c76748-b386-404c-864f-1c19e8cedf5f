#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@Desc    :   
@File    :   qamate_case_handle.py
@Time    :   2024-10-28
<AUTHOR>   xuz<PERSON><PERSON>@baidu.com
"""
import copy
import sys
from bs4 import BeautifulSoup

from apps.app_agent.multi_agent.wait_time_predict_agent.agent import WaitTimePredictAgent

sys.path.append("/Users/<USER>/Workspace/Project/lazyOne/qamate-strategy/core")

import json

from basics.util import logger
from apps.app_agent.utils.qamate_web_util import QamateWebUtil


class QamateCaseHandle(object):
    """
    操作/保存/管理/修改 QAMate 的 case
    """

    # def __init__(self, root_id, os_type=2, guide_type=None):
    def __init__(self, os_type=2, guide_type=None):
        """
        初始化
        """
        self.os_type = os_type
        self.os_name = "ios" if os_type == 2 else "android"
        self.__QWU = QamateWebUtil(self.os_type)

        self.mrd_list = []
        self.mrd_node_info = {}

        self.guide_type = guide_type

        self.label_dict = {}

    def __trans_casenode_to_list(self, case_node, p_contents, m_contents_list, p_infos, m_infos_list, goal_labels):
        """
        将 case_node 转换为 manual_case_list
        """
        new_parents_content_list = p_contents[:]
        new_parents_content_list.append(case_node['nodeName'])
        if case_node['nodeName'] == "自动化测试步骤":
            return

        new_parents_info_list = p_infos[:]
        # 全部打平
        new_parents_info_list.append({
            "desc": case_node['nodeName'],
            "id": case_node['caseNodeId'],
            "caseNodeId": case_node['caseNodeId'],
            "type": "node",
            "sub_type": "node",
            "label": case_node['extra']['tagInfo']['moduleTagList']
        })

        # 添加对应端类型的步骤
        if len(case_node["extra"]["stepInfo"].get(self.os_name, [])) > 0:
            for step_info in case_node["extra"]["stepInfo"].get(self.os_name, []):
                step_desc = step_info['stepDesc']

                if step_info['stepType'] == 101:
                    step_desc = "操作：{}".format(step_desc)
                elif step_info['stepType'] == 102:
                    step_desc = "校验：{}".format(step_desc)

                new_parents_content_list.append(step_desc)
                new_parents_info_list.append({
                    "desc": step_desc,
                    "id": step_info['stepId'],
                    "caseNodeId": case_node['caseNodeId'],
                    "type": "step",
                    "sub_type": step_info['stepType'],
                    "label": None,
                })

                # # 如果已有步骤中已经有了自动化步骤，就直接跳过
                # if step_info['stepType'] not in [101, 102]:
                #     return

        if "children" in case_node and len(case_node["children"]) > 0:
            for child_node in case_node["children"]:
                self.__trans_casenode_to_list(
                    child_node, new_parents_content_list, m_contents_list,
                    new_parents_info_list, m_infos_list, goal_labels
                )
        else:
            label_flag = True
            if len(goal_labels) > 0:
                label_flag = False
                last_labels = []
                for node_info in new_parents_info_list:
                    if node_info["type"] == "node" and len(node_info["label"]) > 0:
                        update_flag = False
                        for tag_id in node_info["label"]:
                            if tag_id in self.label_dict.values():
                                update_flag = True
                                break
                        if update_flag is True:
                            last_labels = node_info["label"]

                for tag_id in last_labels:
                    if tag_id in goal_labels:
                        label_flag = True
                        break
            if label_flag is False:
                if len(last_labels) > 0:
                    print(last_labels)
                return

            m_contents_list.append(new_parents_content_list)
            m_infos_list.append(new_parents_info_list)

    def get_label_ids(self, product_id):
        """
        获取标签信息
        """
        # goal_labels = [
        #     "可进行自动化用例生成",
        #     "可进行自动化用例生成且规范化",
        #     "待采纳的生成用例",
        #     "直接采纳的生成用例",
        #     "微调后可采纳的生成用例",
        #     "不采纳的生成用例",
        #     "优先生成的用例",
        #     "可用于知识生成",
        #     "待生成"
        # ]
        tag_list = self.__QWU.get_product_tags(product_id)
        for tag in tag_list:
            # if tag["tagName"] in goal_labels:
            self.label_dict[tag["tagName"]] = tag["id"]
        return

    def get_manual_case_by_case_root_id(self, case_root_id, product_id=None, goal_labels=[]):
        """
        通过caseRootId获取手动case列表
        """
        # result = []
        manual_case_dict = self.__QWU.get_case_tree_query(case_root_id)
        if manual_case_dict is None:
            return [], []
        manual_case_list = []
        manual_case_info_list = []
        tag_ids = []
        if product_id is not None and len(goal_labels) > 0:
            self.get_label_ids(product_id)
            for tag_name in goal_labels:
                tag_ids.append(self.label_dict[tag_name])

        self.__trans_casenode_to_list(
            manual_case_dict, [], manual_case_list,
            [], manual_case_info_list, tag_ids
        )
        return manual_case_list, manual_case_info_list

    def get_last_node_info(self, mrd_info_list):
        """
        获取最后一个节点的信息
        """
        # 获取真实的节点列表
        last_node_info = None
        for node_info in mrd_info_list:
            if node_info["type"] == "node":
                last_node_info = node_info
        return last_node_info

    def save_pre_operation_nodes(self, mrd_info_list, step_infos, bind_leaf=True, after_index=-1, step_log_info=""):
        """
        保存前置步骤
        初期挂载逻辑为：mrd_index 对应的节点中挂载对应的全部步骤，其它mrd为空节点
        """
        if bind_leaf is True:
            # 获取真实的节点列表
            last_node_info = self.get_last_node_info(mrd_info_list)
            last_node_id = last_node_info["id"]

            # 新增逻辑，判断最后一个节点是否是专门的自动化步骤节点。
            # 如果是，直接用
            # 如果不是，则新增一个节点，并挂载对应的步骤
            if last_node_info["desc"] != "自动化测试步骤":
                last_node_id = self.__QWU.add_auto_node_tmp(last_node_id)
                mrd_info_list.append({
                    "desc": "自动化测试步骤",
                    "id": last_node_id,
                    "type": "node",
                    "sub_type": "node",
                })

            goal_node_id = last_node_id

            # 添加step_log_info
            self.__QWU.add_node_desc(goal_node_id, step_log_info)
        else:
            goal_node_id = mrd_info_list[after_index]['caseNodeId']
            self.__QWU.clear_manual_steps(goal_node_id)

        for step_info in step_infos:
            step_info_c = copy.deepcopy(step_info)
            step_info_c["stepDesc"] = "(检)" + step_info_c["stepDesc"]
            self.__QWU.add_step(goal_node_id, step_info_c)
            pass

    def get_qamate_knowledge_case_info(self, case_root_id):
        """
        获取Qamate Case信息
        """
        case_raw_info = self.__QWU.get_case_tree_query_with_auto(case_root_id)
        if case_raw_info is None or "children" not in case_raw_info:
            return []

        # 根节点是『知识』，所以返回children
        return case_raw_info["children"]

    def scale_down_rect(self, rect, scale):
        """
        根据scale缩放rect
        """
        rect["x"] = int(rect["x"] / scale)
        rect["y"] = int(rect["y"] / scale)
        rect["w"] = int(rect["w"] / scale)
        rect["h"] = int(rect["h"] / scale)

    def format_step_dom_by_scale(self, dom, device_info):
        """
        根据scale缩放dom
        """
        scale = device_info["screenSize"]["scale"]
        # 根节点
        self.scale_down_rect(dom["rect"], scale)

        # 第一层
        for node in dom["children"]:
            self.scale_down_rect(node["rect"], scale)
            # 第二层
            if "children" in node:
                for child_node in node["children"]:
                    self.scale_down_rect(child_node["rect"], scale)

    def format_action_assert(self, action_assert):
        """
        格式化对齐一下action_assert和action_res
        """
        if action_assert["action"] == "assert_exist":
            action_assert["action"] = "nope"
        elif action_assert["action"] == "assert_not_exist":
            action_assert["action"] = "absence"

    def search_goal_node_by_elementid(self, element_id, batdom):
        """
        通过element_id查找节点
        """
        path_summary = [0]
        goal_node = None

        # 临时逻辑，特殊处理TextArea
        # if self.guide_type is None:
        # goal_node = dom["children"][element_id]
        goal_node = batdom["id_ele_map"][element_id]
        path_summary.append(goal_node["debug"]["id"])
        if goal_node["type"] == "TextArea":
            goal_node = goal_node["children"][0]
            path_summary.append(goal_node["debug"]["id"])
        # else:
        #     for node in dom["children"]:
        #         if node["debug"]["id"] == element_id:
        #             goal_node = node
        #             path_summary.append(element_id)
        #             break

        logger.info("goal_node: {}".format(goal_node))
        # 根据goal_node 规范结果
        find_info = {
            "screenCount": 1,
            "findType": 0,
            "findNode": [{
                "detailFeature": {
                    "ext": goal_node["ext"],
                    "rect": goal_node["rect"],
                    "matchType": 1,
                    "type": goal_node["type"]
                },
                "featureFlag": True,
                "actionNode": True,
                "id": goal_node["debug"]["id"]
            }],
            "modelType": 2,
            "chosenTag": ["visual", "single"],
            "findPath": [],
            "mergeOCR": False
        }
        return find_info, path_summary

    def nope_special_handle(self, action_info, batdom, find_info):
        """
        验证操作特殊处理
        """
        find_node = find_info["findNode"][0]["detailFeature"]
        if find_node["type"] in ["TextArea", "Text"] and len(find_node["ext"]["text"]) > 7:
            action_info["type"] = 3
            action_info["params"] = {
                "type": "review",
                "params": {
                    "img": batdom["deviceInfo"]["screenshot"],
                    "rect": {
                        "x": 0,
                        "y": 0,
                        "width": 0,
                        "height": 0
                    },
                    "width": batdom["deviceInfo"]["screenSize"]["width"],
                    "height": batdom["deviceInfo"]["screenSize"]["height"]
                }
            }
            action_type = action_info["params"]["type"]
            return action_info, action_type
        else:
            return False, False

    def map_params_action_info(self, action_res):
        """
        将action_res转换为步骤中的action_info
        """
        action_info = {}
        if action_res["action"] == "tap":
            action_info = {
                "type": "tap",
                "params": {}
            }
        elif action_res["action"] == "input":
            action_info = {
                "type": "input",
                "params": {
                    "text": action_res["input_text"]
                }
            }
        elif action_res["action"] == "long_press":
            action_info = {
                "type": "tap",
                "params": {
                    "duration": 2000
                }
            }
        elif action_res["action"] == "long_press":
            action_info = {
                "type": "tap",
                "params": {
                    "duration": 2000
                }
            }
        elif action_res["action"] == "nope":
            action_info = {
                "type": "nope",
                "params": {}
            }
        elif action_res["action"] == "absence":
            action_info = {
                "type": "absence",
                "params": {}
            }
        else:
            raise Exception("action_res type error")
        return action_info

    def get_action_info_from_action_res(self, batdom, action_res, action_dec):
        """
        从action_res中提取出action_info
        """
        step_interval = WaitTimePredictAgent(action_desc=action_dec).main().get('waitTime', 2)
        action_info = {
            "caseVersion": "v1.0.0",
            "desc": action_dec,
            "type": -1,
            "params": {},
            "common": {
                "commonAlertClear": True,
                "stepInterval": step_interval
            }
        }

        if action_res["action"] == "swipe":
            # swipe 特殊处理
            direction = -1
            if action_res["direction"] == "up":
                direction = 1
            elif action_res["direction"] == "down":
                direction = 2
            elif action_res["direction"] == "right":
                direction = 3
            elif action_res["direction"] == "left":
                direction = 4
            action_info["type"] = 1
            action_info["params"] = {
                "type": "swipe",
                "params": {
                    "direction": direction,
                    "times": 1,
                    "interval": 1,
                    "duration": 500
                }
            }
            action_type = action_info["params"]["type"]
            return action_info, action_type
        elif action_res["action"] == "absence":
            # 不存在 特殊处理
            action_info["type"] = 3
            action_info["params"] = {
                "type": "review",
                "params": {
                    "img": batdom["deviceInfo"]["screenshot"],
                    "rect": {
                        "x": 0,
                        "y": 0,
                        "width": 0,
                        "height": 0
                    },
                    "width": batdom["deviceInfo"]["screenSize"]["width"],
                    "height": batdom["deviceInfo"]["screenSize"]["height"]
                }
            }
            action_type = action_info["params"]["type"]
            return action_info, action_type
        elif action_res["element_id"] == -1 and action_res["action"] == "nope":
            # 存在性校验，但是不存在具体元素
            action_info["type"] = 3
            action_info["params"] = {
                "type": "review",
                "params": {
                    "img": batdom["deviceInfo"]["screenshot"],
                    "rect": {
                        "x": 0,
                        "y": 0,
                        "width": 0,
                        "height": 0
                    },
                    "width": batdom["deviceInfo"]["screenSize"]["width"],
                    "height": batdom["deviceInfo"]["screenSize"]["height"]
                }
            }
            action_type = action_info["params"]["type"]
            return action_info, action_type
        elif action_res["action"] == "wait":
            action_info["type"] = 1
            action_info["params"] = {
                "type": "wait",
                "params": {
                    "seconds": action_res['time']
                }
            }
            action_type = action_info["params"]["type"]
            return action_info, action_type
        # elif action_res["action"] == "nope" and len(action_res['content']) > 10:
        #     # 存在，但是校验文字数超过闲置，也特殊处理，加人工步骤
        #     action_info["type"] = 3
        #     action_info["params"] = {
        #         "type": "review",
        #         "params": {
        #             "img": batdom["deviceInfo"]["screenshot"],
        #             "rect": {
        #                 "x": 0,
        #                 "y": 0,
        #                 "width": 0,
        #                 "height": 0   
        #             },
        #             "width": batdom["deviceInfo"]["screenSize"]["width"],
        #             "height": batdom["deviceInfo"]["screenSize"]["height"]
        #         }
        #     }
        #     action_type = action_info["params"]["type"]
        #     return action_info, action_type
        else:
            # tap,long_press,input可以统一处理
            find_params = {
                "allowFail": False,
                "times": 0,
                "before": {
                    "wait": 0
                },
                "interval": 1000,
                "until": {
                    "times": 1,
                    "enable": False,
                    "interval": 2000
                }
            }

            find_info, path_summary = self.search_goal_node_by_elementid(
                action_res["element_id"], batdom
            )

            # 额外判断，如果是nope类型，且目标元素是文本类型，则进行特殊处理
            if action_res["action"] == "nope":
                # 验证操作 Text 类型特殊处理
                sh_action_info, sh_action_type = self.nope_special_handle(action_info, batdom, find_info)
                if sh_action_info is not False:
                    return sh_action_info, sh_action_type

            params = {
                "dom": batdom["dom"],
                "domInfo": batdom["info"],
                "findType": 0,
                "findInfo": find_info,
                "actionInfo": self.map_params_action_info(action_res),
                "findParams": find_params,
                "pathSummary": path_summary,
                "deviceInfo": batdom["deviceInfo"]
            }
            action_info["type"] = 9
            action_info["params"] = params
            action_type = action_info["params"]["actionInfo"]["type"]

            # 如果点击文字是长文本，仅取前半部分
            if 'text' in action_info['params']['findInfo']['findNode'][0]['detailFeature']['ext']:
                action_info['params']['findInfo']['findNode'][0]['detailFeature']['ext']['text'] = \
                action_info['params']['findInfo']['findNode'][0]['detailFeature']['ext']['text'][:7]

        return action_info, action_type

    def save_auto_generate_nodes(self, mrd_item, mrd_info_list, record_batdom, action_res, action_dec,
                                 bind_leaf=True, save_node_index=-1):
        """
        转化且保存生成步骤
        有两种情况，要判断是否存在断言步骤
        如果断言步骤存在，需要判断是断言存在还是不存在
        如果是存在性校验:先判断存在，再执行步骤
        如果是不存在性校验：先执行步骤，再判断不存在
        """
        # 添加节点 - 全部添加到叶结点：last_node_id
        last_node_info = self.get_last_node_info(mrd_info_list)
        last_node_id = last_node_info["id"]

        if bind_leaf is True:
            # 获取要在哪个节点插入步骤
            goal_node_id = last_node_id
        else:
            goal_node_id = mrd_info_list[save_node_index]['caseNodeId']
            logger.info("清空节点所有手工步骤")
            self.__QWU.clear_manual_steps(goal_node_id)

        # 根据scale缩放dom
        self.format_step_dom_by_scale(record_batdom["dom"], record_batdom["deviceInfo"])

        action_info = None
        action_type = None

        # 兼容验证操作
        self.format_action_assert(action_res)

        # 添加 & 执行步骤
        action_info_list = []
        step_desc = "{}[{}]".format(action_dec, mrd_item[:20])
        # 结合batdom和action_res生成action_info
        action_info, action_type = self.get_action_info_from_action_res(
            record_batdom, action_res, step_desc
        )

        step_info = {
            "stepType": 201,
            "stepInfo": action_info,
            "stepDesc": step_desc
        }

        if action_res["action"] == "absence":
            step_info["stepType"] = 102

        _ = self.__QWU.add_step(goal_node_id, step_info)

        # 人工步骤不执行
        if action_type not in ["review"]:
            action_info_list.append(step_info)

        return action_info_list

    def transform_action_res_to_step_info(self, mrd_item, record_batdom, action_res, action_dec):
        """
        转化且保存生成步骤
        有两种情况，要判断是否存在断言步骤
        如果断言步骤存在，需要判断是断言存在还是不存在
        如果是存在性校验:先判断存在，再执行步骤
        如果是不存在性校验：先执行步骤，再判断不存在
        """
        # 根据scale缩放dom
        self.format_step_dom_by_scale(record_batdom["dom"], record_batdom["deviceInfo"])

        action_info = None
        action_type = None

        # 兼容验证操作
        self.format_action_assert(action_res)

        # 添加 & 执行步骤
        step_desc = "{}[{}]".format(action_dec, mrd_item[:20])
        # 结合batdom和action_res生成action_info
        action_info, action_type = self.get_action_info_from_action_res(
            record_batdom, action_res, step_desc
        )

        step_info = {
            "stepType": 201,
            "stepInfo": action_info,
            "stepDesc": step_desc
        }

        action_flag = True
        if action_res["action"] == "absence":
            step_info["stepType"] = 102
            action_flag = False

        return action_flag, step_info

    def single_add_step(self, step_info, mrd_info_list, bind_leaf=True, save_node_index=-1):
        """
        单独添加步骤
        """
        # 添加节点 - 全部添加到叶结点：last_node_id
        last_node_info = self.get_last_node_info(mrd_info_list)
        last_node_id = last_node_info["id"]

        if bind_leaf is True:
            # 获取要在哪个节点插入步骤
            goal_node_id = last_node_id
        else:
            goal_node_id = mrd_info_list[save_node_index]['caseNodeId']
            logger.info("清空节点所有手工步骤")
            self.__QWU.clear_manual_steps(goal_node_id)

        _ = self.__QWU.add_step(goal_node_id, step_info)
        return

    def get_steps_by_node_id(self, node_id):
        """
        通过节点ID获取步骤信息
        :param node_id: 节点ID
        :return: 步骤信息列表
        """
        steps = self.__QWU.get_node_step_list(node_id)
        return steps

    def add_node_tag_info(self, node_id: int, tag_info: dict):
        """
        为节点添加tag
        :param node_id:
        :param tag_info:
        :return:
        """
        res = self.__QWU.update_node_tag(case_node_id=node_id, tag_info=tag_info)
        return res

    def add_node_desc(self, node_id: int, desc: str):
        """
        为节点添加测试设计信息
        """
        res = self.__QWU.update_node_desc(case_node_id=node_id, desc=desc)
        return res

    def get_and_parse_node_desc_text(self, node_id):
        """

        :param node_id:
        :return:
        """
        desc_res = self.__QWU.get_casenode_desc(node_id)
        html_content = desc_res["description"]
        if not html_content:
            return ""
        soup = BeautifulSoup(html_content, 'html.parser')
        # 查找和提取你需要的部分，比如 div 内容
        # text_desc = soup.find('div', class_='mp-paragraph-wrapper').text
        # text_desc = soup.find('div').text
        text_desc = ''
        dup_set = set()
        for div in soup.find_all('div'):
            if div.text in dup_set:
                # 去除重复文本
                continue
            text_desc += div.text
            dup_set.add(div.text)
            text_desc += '\n'
        return text_desc


if __name__ == '__main__':
    qch = QamateCaseHandle()
    k = qch.get_qamate_knowledge_case_info(38765352)
    print(k)
