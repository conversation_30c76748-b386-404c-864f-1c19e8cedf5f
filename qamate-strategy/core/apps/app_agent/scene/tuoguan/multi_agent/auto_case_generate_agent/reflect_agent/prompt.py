#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@File    : prompt.py
<AUTHOR> <PERSON><PERSON><PERSON><PERSON>(<EMAIL>)
@Time    : 2024/11/15 14:49
@Desc    : 
"""


def system_prompt():
    """

    :return:
    """
    sys_prompt = """你是一个具有App自动化测试经验的AI手机操作助手，你需要帮我操作手机并且完成页面的功能点验证。
你拥有基本的自动化测试常识，例如：在输入操作后需要跟确认、完成、发送、搜索等操作；设备执行过程出现意料之外的弹窗、提示框、权限弹窗等需要先点除；菜单或设置页面可能需要滑动查找某些功能等。"""
    return sys_prompt


def reflect_prompt_v1(mrd, action_desc, action_thought):
    """
    反思agent
    :param action_desc:
    :param action_thought:
    :return:
    """
    prompt = f"""
&& 背景 &&
你正在按照测试用例逐步操作手机进行自动化测试。为了确保整体测试执行正确，每一步操作后都需要对执行结果进行校验，确保测试目标不偏离。当前处于操作后的校验环节。
测试用例为：{mrd}。
- 说明：测试用例路径描述的是大致的执行步骤，可能存在拆分的多步操作。例如：实际操作路径为A -> B -> B' -> C，但用例中只列出了A、B、C。所有用例中描述的关键步骤都必须最终得到执行。

&& 图片信息 &&
本次校验环节提供了两张手机截图：
- 第一张图片：操作前的截图，若存在红框表示操作的点击、输入或校验位置；箭头则表示页面滑动方向（左箭头：由右向左滑动；上箭头：由下往上滑动）。
- 第二张图片：操作后的截图，用于判断当前操作是否达到预期效果。

&& 操作信息 &&
操作前的思考：{action_thought}
实际操作描述：{action_desc}

&& 任务 &&
请根据操作后的页面截图判断刚才执行的单步操作是否正确：
- 如果操作正确，请在反馈中给出下一步操作的建议；
- 如果操作不正确，请说明原因并给出正确的操作提示。
目标是确保测试目标正确达成，及时纠正错误，防止测试流程偏离预期。

&& 注意 &&
1. 每次只关注单步操作的正确性，不要跨步考虑后续未执行的操作。
2. 测试用例中所有关键步骤都必须依序执行，不能跳步。例如：当用例为“1. 打开App -> 2. 输入hello -> 3. 校验展示”，若当前操作仅为输入hello，则下一步依然需要执行输入操作，而不能直接校验展示。
3. 当操作页面某些元素后页面未发生变化，不一定都是错误操作，需要结果整个用例来判断。
    * 例如1：用例为：1.点击头像下面的粉丝数 -> 2.验证页面无变化。操作操作为：点击头像下面的粉丝数。
    当执行1操作后，页面尽管未发生变化，但是也是符合预期的，也是正确步骤。
    * 例如2：用例为：1.打开App -> 2.点击『主页』tab -> **
    当执行1操作后，默认的页面就是主页tab，当执行第二步"点击『主页』tab"后页面未发生变化，也是正常的，这种需要结合图片信息判断。
"""
    output = """
&& 输出 &&
请以JSON格式输出，必须包含两个字段：
- "result": 布尔值，true表示操作正确，false表示操作错误。
- "feedback": 字符串内容；若操作正确，请给出下一步的操作建议；若操作错误，请说明原因并给出正确操作提示。

输出示例格式：
{
    "result": true,
    "feedback": "下一步建议：在表单中点击提交按钮。"
}
"""
    return prompt + output


def reflect_prompt_v2(mrd, action_desc, action_thought):
    """
    data:2025.2.12
    反思agent
    :param action_desc:
    :param action_thought:
    :return:
    """
    prompt = f"""
&& 背景 &&
你正在按照 **测试用例** 逐步操作手机进行自动化测试。为了确保整体测试执行正确，每一步操作后都需要对执行结果进行 **反思校验**，确保测试目标不偏离。当前处于 **操作后的反思校验环节**。

&& 测试用例 &&
- 用例执行路径：【{mrd}】
### 重要说明 ###
1. 测试用例路径是大致执行顺序，部分步骤可能需要拆分成多个具体操作。
2. 用例中的步骤是必须执行的，不能遗漏。每一步的具体执行方式需要结合当前页面截图判断。
3. 一个步骤可能需要多次交互才能完成，例如：
   - 用例步骤："搜索'兰花怎么养'"
   - 实际操作拆分为：1.在搜索框输入"兰花怎么养"。2.点击搜索按钮。

&& 图片信息 &&
本次校验环节提供了两张手机截图：
- 第一张图片：操作前的截图，若存在红框表示操作的点击、输入或校验位置；若存在箭头则表示页面滑动方向（左箭头：由右向左滑动；上箭头：由下往上滑动）。
- 第二张图片：操作后的截图，用于判断当前操作是否达到预期效果。

&& 操作信息 &&
操作前的思考：{action_thought}
实际执行的操作：{action_desc}

&& 任务 &&
请根据 **操作后的页面截图**，判断刚才执行的 **单步操作** 是否正确：
- 如果操作正确：
  - 在反馈中给出 **下一步的操作建议**，确保测试流程顺利推进。  
- 如果操作不正确：
  - **明确指出问题**（如：目标元素未变化、输入未提交、未按测试步骤执行等）。  
  - **提供正确的操作指导**，确保测试流程不偏离预期。
**目标**：确保测试目标 **正确达成**，如有偏差 **及时纠正**，防止流程错误累计。

&& 判断标准 &&
1. **关注单步操作的正确性**，不跨步考虑未执行的操作。  
2. **测试用例中的所有关键步骤必须依序执行**，不能跳步。  
  - 例如：测试路径为 **"1. 打开App → 2. 输入hello → 3. 校验展示"**  
  - 若当前操作 **仅执行了“输入 hello”**，则 **下一步仍然需要执行输入操作**，不能跳过直接校验展示。  
3. **页面未发生变化 ≠ 操作错误**，需结合测试目标判断。  
  - **示例1（预期无变化）**：
    - 用例："点击粉丝数 → 页面无变化"
    - 操作：点击粉丝数后页面未变化  
    - **这是正确行为**，因为用例明确说明页面应保持不变。  
  - **示例2（预期无变化但仍正确）**：
    - 用例："打开 App → 点击 '主页' tab"
    - 操作：默认页面就是主页，点击后无变化  
    - **操作仍正确**，因为虽然页面无变化，但测试路径需要点击该元素。
"""
    output = """
&& 输出 &&
输出应该包含两个部分，result 和 reason，以json的格式输出。
{
    "result": bool  // 结果, true表示正确，false表示错误
    "feedback": string  // 如果正确，给出下一步的操作建议，如果不正确给出原因和正确的操作提示
}
"""
    return prompt + output


def reflect_prompt_v3(action_desc):
    """
    data:2025.2.12，改为连续对话，辅助反思Agent更充分的了解上下文信息
    反思agent
    :param action_desc:
    :param action_thought:
    :return:
    """
    prompt = f"""
&& 任务 &&
请根据 **操作后的页面截图**，判断刚才执行的 **单步操作** 是否正确：
- 如果操作正确：
  - 在反馈中给出下一步的操作建议，确保测试流程顺利推进。  
- 如果操作不正确：
  - 明确指出问题（如：目标元素未变化、输入未提交、未按测试步骤执行等）。  
  - 提供正确的操作指导，确保测试流程不偏离预期。
**目标**：确保测试目标 **正确达成**，如有偏差 **及时纠正**，防止流程错误累计。

&& 判断标准 &&
1. **关注单步操作的正确性**，不跨步考虑未执行的操作。  
2. **测试用例中的所有关键步骤必须依序执行**，不能跳步。  
  - 例如：测试路径为 **"1. 打开App → 2. 输入hello → 3. 校验展示"**  
  - 若当前操作 **仅执行了“输入 hello”**，则 **下一步仍然需要执行输入操作**，不能跳过直接校验展示。  
3. **页面未发生变化 ≠ 操作错误**，需结合测试目标判断。  
  - **示例1（预期无变化）**：
    - 用例："点击粉丝数 → 页面无变化"
    - 操作：点击粉丝数后页面未变化  
    - **这是正确行为**，因为用例明确说明页面应保持不变。  
  - **示例2（预期无变化但仍正确）**：
    - 用例："打开 App → 点击 '主页' tab"
    - 操作：默认页面就是主页，点击后无变化  
    - **操作仍正确**，因为虽然页面无变化，但测试路径需要点击该元素。

&& 图片信息 &&
图片是上一步操作：{action_desc} 操作后的截图。
"""
    output = """
&& 输出 &&
请以JSON格式输出，必须包含两个字段：
- "result": 布尔值，true表示操作正确，false表示操作错误。
- "feedback": 字符串内容；若操作正确，请给出下一步的操作建议；若操作错误，请说明原因并给出正确操作提示。

输出示例格式：
{
    "result": true,
    "feedback": "下一步建议：在表单中点击提交按钮。"
}
"""
    return prompt + output