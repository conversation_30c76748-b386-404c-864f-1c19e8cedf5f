#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@File    : auto_case_generate.py
<AUTHOR> <PERSON><PERSON><PERSON><PERSON>(<EMAIL>)
@Time    : 2025/1/22 14:57
@Desc    : 
"""

import traceback
import re
import os
import uuid
import logging
import time
import shutil

import cv2

from basics.image.ui.image_process import image_to_base64
from basics.image.ui.webapi import get_popup_from_openapi
from apps.app_agent.knowledge_search.scene_kb import SceneKB
from apps.app_agent.utils.qamate_case_handle import QamateCaseHandle
from apps.app_agent.multi_agent.automated_judgment_agent.v2_1.agent import AutomatedJudgmentAgent
from apps.app_agent.multi_agent.optimize_manual_case_agent.v2_1.agent import OptimizeManualAgent
from apps.app_agent.scene.tuoguan.multi_agent.auto_case_generate_agent.guide_agent.guide_agent import GuideAgent
from apps.app_agent.scene.tuoguan.multi_agent.auto_case_generate_agent.reflect_agent.agent import GuideReflectAgent
from apps.app_agent.scene.tuoguan.multi_agent.auto_case_generate_agent.summary_agent.agent import ProcessSummaryAgent
from apps.app_agent.multi_agent.page_understand_agent.bat_page_understand_agent import BatPageUnderstandAgent
from apps.app_agent.multi_agent.auto_case_translate_agent.v1.agent import BatDomCaseTranslateAgent
from apps.app_agent.multi_agent.auto_case_generate_agent.utils.utils import mllm_guide_action_parse
from apps.app_agent.utils.draw import draw_arrow, draw_rect
from apps.app_agent.utils.exception import AgentErrCode, AgentException
from apps.auto_case_transform.device import Device
from basics.util import logger
from apps.app_agent.entity.generate import ExecuteInfo
from apps.app_agent.conf.device import DEVICE_ID
from basics.util.config import CACHE_DIR
from basics.util.config import PY_LOG_DIR
from apps.app_agent.conf.app_note import APP_NOTE
from basics.util.image import Image

format = logging.Formatter(
    '[%(process)s][%(thread)d][%(threadName)s][%(asctime)s][%(levelname)s] %(message)s')
logger.setLevel(logging.INFO)
# logger.setLevel(logging.DEBUG)

TEMP_DIR = os.path.join(PY_LOG_DIR, 'TuoGuanLazyGuide.log')

file = logging.handlers.TimedRotatingFileHandler(
    filename=TEMP_DIR, encoding='utf-8',
    when='D', backupCount=3
)
file.setFormatter(format)
logger.addHandler(file)


class TuoGuanAutoCaseGenerate(object):
    """

    """

    def __init__(self, mrd_list: list, mrd_info: list, knowledge_case_node_id: int, keyword_knowledge_node_id: int,
                 os_type: int, product_module_id: int, app_name: str, bind_leaf=True, device_id=DEVICE_ID):
        """

        :param mrd_list: 例如：["首页及对话", "对话页", "助手tab和历史tab可点击切换、可手势横划切换"]
        :param mrd_info:
        :param knowledge_case_node_id:
        :param os_type:
        :param product_module_id:
        :param app_name:
        """

        self.mrd_list = mrd_list
        self.mrd_info_list = mrd_info

        self.knowledge_version_id = knowledge_case_node_id
        self.keyword_knowledge_node_id = keyword_knowledge_node_id
        self.os_type = os_type
        self.module_id = product_module_id

        if os_type == 2:
            self.os_name = "ios"
        else:
            self.os_name = "android"

        self.model_name = "gpt-4o"
        self.reduce_ratio = 1  # 屏幕的缩小比例
        self.check_action_element = True  # 是否对生成的element_id进行校验

        self.temp_dir = os.path.join(CACHE_DIR, str(uuid.uuid4()))
        self.app_info = self._get_app_info(app_name)
        # self.dv = None
        self.dv = Device(device_id)

        self.new_mrd_list = None
        self.retrieve_res = None
        self.execute_record = []  # 执行记录
        self.execute_correct_record = []  # 正确步骤的执行记录
        self.new_mrd_retrieve = None

        self.save_node_index = -1
        self.bind_leaf = bind_leaf

        self.qamate_case_hander = QamateCaseHandle(os_type)

    def before(self):
        """
        前置准备工作
        :return:
        """
        # 创建文件夹
        self._mk_dir()

        # 参数配置
        self._get_reduce_ratio()

    def _mk_dir(self):
        """

        :return:
        """
        if not os.path.exists(self.temp_dir):
            os.mkdir(self.temp_dir)
        logger.info("temp_dir: {}".format(self.temp_dir))

    def _get_reduce_ratio(self):
        """
        降低图片大小，来达到减少token消耗的目的
        :return:
        """
        width = self.dv.size_info["width"] * self.dv.size_info['scale']
        height = self.dv.size_info["height"] * self.dv.size_info['scale']

        if self.reduce_ratio != 1:
            self.reduce_ratio = min(round(500 / width, 3), round(1000 / height, 3))

    def _resize_image(self, img_path):
        """
        缩放图片，节省token
        :param img_path:
        :return:
        """
        imgcv = cv2.imread(img_path)
        resized_img = cv2.resize(imgcv, (0, 0), fx=self.reduce_ratio, fy=self.reduce_ratio)
        cv2.imwrite(img_path, resized_img)

    def _check_popup_action(self, image_path):
        """
        检查是否有弹窗
        :param image_path:
        :param grid_path:
        :return:
        """
        click_num = 0
        while click_num < 5:
            # 根据截图判断是否有弹窗
            img64 = image_to_base64(image_path)
            res = get_popup_from_openapi(img64, [0, self.module_id])

            try:
                popup_flag = res["data"]["hit"]
            except:
                popup_flag = False

            if popup_flag is False:
                return

            click_x = res["data"]["rect"]["x"] + int(res["data"]["rect"]["w"] * 0.5)
            click_y = res["data"]["rect"]["y"] + int(res["data"]["rect"]["h"] * 0.5)

            click_x /= self.dv.size_info["scale"]
            click_y /= self.dv.size_info["scale"]

            self.dv.tap(click_x, click_y)
            time.sleep(5)
            click_num += 1

            # 重新进行截图
            img_url = self.dv.screenshot()['screenshot']
            image = Image(url=img_url)
            image.save_image(image_path)

    def _current_image_record_and_grid(self, image_path, grid_path):
        """
        页面建模 + 画框
        :param image_path: 原图存储地址
        :param grid_path: 带有gird图存储地址
        :return:
        """
        img_url = self.dv.screenshot()['screenshot']
        image = Image(url=img_url)
        image.save_image(image_path)

        # 判断是否有弹窗
        self._check_popup_action(image_path=image_path)

        page_unds_agent = BatPageUnderstandAgent(image_path=image_path, grid_img_save_path=grid_path)
        dom_info, dom, dom_desc, id_ele_map = page_unds_agent.main()

        batdom_record_res = {
            "info": dom_info,
            "dom": dom,
            "dom_desc": dom_desc,
            "id_ele_map": id_ele_map,
            "deviceInfo": {
                "screenSize": {
                    "rotation": self.dv.rotation_info,
                    "width": self.dv.size_info["width"],
                    "height": self.dv.size_info["height"],
                    "scale": self.dv.size_info["scale"]
                },
                "screenshot": img_url,
                "type": "ios" if self.os_type == 2 else "android"
            }
        }

        self._resize_image(image_path)
        self._resize_image(grid_path)

        return batdom_record_res

    def _get_app_info(self, app_name):
        """

        :param app_name:
        :return:
        """
        if app_name in APP_NOTE:
            return {
                "name": app_name,
                "desc": APP_NOTE[app_name]
            }
        else:
            logger.warning("APP_NOTE 没有{}信息".format(app_name))
            return {}

    def _draw_exec_image(self, action_res, raw_img_path, exec_img_path, cur_batdom_record):
        """

        :param image_path:
        :return:
        """

        if action_res['action'] in ('finsh', 'unknown', 'wait'):
            exec_img_path = raw_img_path
            return exec_img_path

        if action_res['action'] == "swipe":
            draw_arrow(image_path=raw_img_path, direction=action_res['direction'],
                       output_path=exec_img_path, reduce_size=self.reduce_ratio)
        elif action_res['action'] == "assert_not_exist" or (
                action_res['action'] == "assert_exist" and action_res['element_id'] == -1):
            exec_img_path = raw_img_path
        else:
            ele_idx = action_res['element_id']
            bat_ele = cur_batdom_record['id_ele_map'][ele_idx]
            exe_ele_rect = {
                "x": int(bat_ele['rect']['x'] * self.reduce_ratio),
                "y": int(bat_ele['rect']['y'] * self.reduce_ratio),
                "w": int(bat_ele['rect']['w'] * self.reduce_ratio),
                "h": int(bat_ele['rect']['h'] * self.reduce_ratio)
            }
            draw_rect(image_path=raw_img_path, rect=exe_ele_rect, output_path=exec_img_path)
        return exec_img_path

    def _action_2_step_info_and_run(self, grid_img_path, mrd_item, record_batdom, action_res, action_dec,
                                    p_knowledge=[]):
        """
        转为 QAMate step_info
        :param mrd_item:
        :param record_batdom:
        :param action_res:
        :param action_dec:
        :return:
        """
        step_info, action_flag = BatDomCaseTranslateAgent(grid_img_path=grid_img_path,
                                                          batdom_record=record_batdom,
                                                          mrd_item=mrd_item,
                                                          step_desc=action_dec,
                                                          action_res=action_res,
                                                          scene_knowledge=p_knowledge).main()

        # action_flag, step_info = self.qamate_case_hander.transform_action_res_to_step_info(
        #     mrd_item=mrd_item, record_batdom=record_batdom, action_res=action_res, action_dec=action_dec
        # )
        res = {
            "step_info": step_info,
            "run_flag": action_flag
        }
        run_res = self.dv.run_step_compatibility(step_info["stepInfo"],
                                                 os_type=self.os_type,
                                                 module_id=self.module_id)

        logger.info("执行结果: {}".format(run_res))
        time.sleep(5)
        return res

    def _current_image(self, image_path):
        """
        获取当前截图
        :param image_path:
        :return:
        """
        img_url = self.dv.screenshot()['screenshot']
        image = Image(url=img_url)
        image.imgcv = cv2.resize(image.imgcv, (0, 0), fx=self.reduce_ratio, fy=self.reduce_ratio)
        image.save_image(image_path)
        return image

    def _save_single_step_info(self, step_info, mrd_item, mrd_info_list):
        """
        保存 step_info
        :param step_info:
        :param mrd_info_list:
        :param bind_leaf:
        :param save_node_index:
        :return:
        """
        save_candidates = [i for i, item in enumerate(mrd_info_list) if item['desc'] == mrd_item and i >=
                           self.save_node_index]
        if self.bind_leaf is False:
            if len(save_candidates) == 0:
                if self.save_node_index < 0:
                    raise AgentException(err_code=AgentErrCode.PARAM_ERROR, detail="输入的用例不存在")
            else:
                self.save_node_index = save_candidates[0]

        self.qamate_case_hander.single_add_step(step_info=step_info, mrd_info_list=mrd_info_list,
                                                bind_leaf=self.bind_leaf, save_node_index=self.save_node_index)

    def extract_url_from_mrd(self, mrd_list: list):
        """
        从mrd中提取url数据
        :param mrd_list:
        :return:
        """
        urls = ""
        idx = 0
        for item_text in mrd_list:
            idx += 1
            url_pattern = r'https?://[^\s]+'
            urls = re.findall(url_pattern, item_text)
            if urls:
                break

        if not urls:
            raise AgentException(err_code=AgentErrCode.PARAM_ERROR, detail="case中未检测到URL信息")
        return urls[0], idx - 1

    def get_pre_route_case(self, replace_url: str, idx=0, route_case_id=38765352):
        """
        固定的前置路径
        :param replace_url:
        :param idx:
        :param route_case_id:
        :return:
        """

        route_steps = []

        flag = 0

        # 获取已经写好的模板
        route_case_info = self.qamate_case_hander.get_qamate_knowledge_case_info(route_case_id)
        os_name = "android" if self.os_type == 1 else "ios"
        for act_info in route_case_info[0]['extra']['stepInfo'][os_name]:
            if act_info['stepDesc'] == '输入组件升级受影响的链接':
                flag = 1
                act_info['stepInfo']['params']['actionInfo']['params']['text'] = replace_url
            route_steps.append(act_info)

        if flag == 0:
            raise AgentException(err_code=AgentErrCode,
                                 detail="未检测到路由用例中存在「{}」的自动化步骤".format('输入组件升级受影响的链接'))

        self.new_mrd_list = self.mrd_list[idx + 1:]
        self.retrieve_res = {
            "steps": route_steps,
            "mrd_index": idx,
            "mrd_list": self.mrd_list,
            "new_mrd_list": self.new_mrd_list,
            "knowledge_case": {
                "nodeName": route_case_info[0]['nodeName']
            }
        }

    def run_correct_step(self):
        """
        执行之前正确的步骤
        :return:
        """
        # 首先执行检索或前置父节点信息
        logger.info("重新执行检索节点信息")
        for step_info in self.retrieve_res['steps']:
            action_info = step_info['stepInfo']
            run_res = self.dv.run_step_compatibility(action_info, os_type=self.os_type, module_id=self.module_id)
            logger.info("执行结果: {}".format(run_res))
            time.sleep(2)

        count = 0
        if len(self.execute_correct_record) == 0:
            return count

        # 再执行前面生成的正确步骤
        for exec_info in self.execute_correct_record:
            logger.info("重新执行第{}步节点信息".format(count))
            step_info = exec_info['step_info']['step_info']
            # if exec_info['step_info']['run_flag']:
            run_res = self.dv.run_step_compatibility(step_info["stepInfo"],
                                                     os_type=self.os_type,
                                                     module_id=self.module_id)
            logger.info("执行结果: {}".format(run_res))
            time.sleep(5)
            count += 1

        return count

    def get_product_knowledge(self, mrd: str):
        """
        获取业务知识
        :param mrd:
        :return:
        """
        if self.keyword_knowledge_node_id == 0:
            return ""
        ks = SceneKB(os_type=self.os_type, kb_node_id=self.keyword_knowledge_node_id,
                     tmp_dir=self.temp_dir)
        p_knowledge = ks.get_scene_by_step(mrd)
        return p_knowledge

    def case_retrieve_and_run(self):
        """

        :return:
        """

        url, idx = self.extract_url_from_mrd(self.mrd_list)

        self.get_pre_route_case(replace_url=url, idx=idx)

        step_infos = []
        logger.info("执行前置模板用例")
        for step_info in self.retrieve_res['steps']:
            action_info = step_info['stepInfo']
            run_res = self.dv.run_step_compatibility(action_info, os_type=self.os_type, module_id=self.module_id)
            logger.info("执行结果: {}".format(run_res))
            time.sleep(2)
            step_infos.append(step_info)

        # save
        self.qamate_case_hander.save_pre_operation_nodes(mrd_info_list=self.mrd_info_list,
                                                         step_infos=step_infos,
                                                         bind_leaf=self.bind_leaf,
                                                         after_index=self.retrieve_res['mrd_index'])
        logger.info("存入前置模板用例")

        return step_infos

    def can_auto_case_generate(self):
        """
        能否自动化
        :return:
        """
        mrd = "->".join(self.mrd_list)
        auto_judge_agent = AutomatedJudgmentAgent(mrd=mrd, app_info=self.app_info)
        judge_res = auto_judge_agent.main()
        can_transform = judge_res.get("can_transform")
        return can_transform

    def guide_by_mllm(self):
        """
        大模型引导执行
        : return
            res_flag: 1 正常生成；0 判断转为自动化；-1 转化失败
        """

        res_flag = -1

        new_mrd = "->".join(self.new_mrd_list)
        logger.info("需求第一次更新，更新为：{}".format(new_mrd))
        summary = "已经完成 {} 操作".format(self.retrieve_res['knowledge_case']['nodeName'])
        error_info = ""

        # 转化验证
        opt_agent = OptimizeManualAgent(mrd=new_mrd, app_info=self.app_info, summary=summary)
        optm_res = opt_agent.main()
        latest_mrd_list = optm_res['optimized_test_case']
        mrd_desc = optm_res['case_description']
        latest_mrd = "->".join(latest_mrd_list)
        logger.info("需求第二次更新，更新为：{}".format(latest_mrd))

        # 获取业务知识
        p_knowledge = self.get_product_knowledge(mrd_desc)
        # p_knowledge = ""

        action_correct_flag = True
        idx, count = 0, 0
        max_replay_count = 2
        while idx < 10:
            # 有失败重新进行回放
            if not action_correct_flag and count < max_replay_count:
                idx = self.run_correct_step()
                action_correct_flag = True
                count += 1

            logger.info("=" * 10 + "start {}".format(idx) + "=" * 10)
            raw_img_path = os.path.join(self.temp_dir, "{}.jpg".format(idx))
            grid_img_path = os.path.join(self.temp_dir, "{}_grid.jpg".format(idx))
            exec_img_path = os.path.join(self.temp_dir, "{}_exec.jpg".format(idx))
            cur_batdom_record = self._current_image_record_and_grid(raw_img_path, grid_img_path)

            guide_agent = GuideAgent(mrd_list=self.new_mrd_list,
                                     opt_mrd_list=latest_mrd_list,
                                     mrd_desc=mrd_desc,
                                     batdom_record=cur_batdom_record,
                                     execute_record=self.execute_correct_record,
                                     app_info=self.app_info,
                                     pre_step=summary,
                                     raw_img_path=raw_img_path,
                                     grid_img_path=grid_img_path,
                                     product_knowledge=p_knowledge,
                                     completed_contents=self.retrieve_res['knowledge_case']['nodeName'],
                                     error_info=error_info)
            guide_agent_res = guide_agent.main()
            chat_list = guide_agent_res['message']
            guide_res = guide_agent_res['agent_res']

            # 操作解析
            action_res = mllm_guide_action_parse(guide_res['action'])
            if action_res['action'] == 'tap' and action_res['element_id'] == -1:
                raise Exception("期望点击的元素未建模出来，元素描述为：{}".format(guide_res['action_desc']))

            if action_res['error']:
                logger.info("舍弃本次结果，重新执行本次 Action Agent")
                continue

            guide_res_info = {
                "thought": guide_res['thought'],
                "action": guide_res['action'],
                "action_desc": guide_res['action_desc'],
                "mrd_item": guide_res['mrd_item'],
                "summary": guide_res.get("summary", "")
            }

            # 执行图片可视化
            exec_img_path = self._draw_exec_image(action_res=action_res,
                                                  raw_img_path=raw_img_path,
                                                  exec_img_path=exec_img_path,
                                                  cur_batdom_record=cur_batdom_record)

            if "finsh" in guide_res['action'] or "unknown" in guide_res['action']:
                logger.info("=====测试完成=====：{}".format("->".join(self.new_mrd_list)))
                res_flag = 1
                break

            # 转为qamate 执行用例步骤，并执行
            step_run_info = self._action_2_step_info_and_run(grid_img_path=grid_img_path,
                                                             mrd_item=guide_res['mrd_item'],
                                                             record_batdom=cur_batdom_record,
                                                             action_res=action_res,
                                                             action_dec=guide_res['action_desc'],
                                                             p_knowledge=p_knowledge)

            # 自我反思
            after_img_path = os.path.join(self.temp_dir, "{}.jpg".format(idx + 1))
            self._current_image(after_img_path)
            if "assert" in guide_res['action']:
                reflect_res_info = {
                    'reflect_flag': True,
                    'reflect_cont': "完成本条校验"
                }
            else:
                reflect_agent = GuideReflectAgent(chat_message=chat_list,
                                                  mrd=latest_mrd,
                                                  action_desc=guide_res_info['action_desc'],
                                                  action_thought=guide_res_info['thought'],
                                                  exec_image_path=exec_img_path,
                                                  after_image_path=after_img_path)
                reflect_agent_res = reflect_agent.main()
                reflect_res_info = {
                    'reflect_flag': reflect_agent_res['agent_res']['result'],
                    'reflect_cont': reflect_agent_res['agent_res']['feedback']
                }

            # 执行记录
            execute_info = ExecuteInfo(raw_img_path=raw_img_path, grid_img_path=grid_img_path,
                                       batdom_record=cur_batdom_record['dom'],
                                       action_res=action_res, exec_img_path=exec_img_path,
                                       guide_res_info=guide_res_info,
                                       reflect_res_info=reflect_res_info,
                                       process_res_info={},
                                       step_info=step_run_info).to_dict()
            self.execute_record.append(execute_info)

            action_correct_flag = reflect_res_info['reflect_flag']
            # 错误执行保留执行图片信息
            if not reflect_res_info['reflect_flag']:
                if count < max_replay_count:  # 是否进行了回退
                    shutil.copy(exec_img_path, exec_img_path.replace("_exec.jpg", "_exec_error.jpg"))
                    exec_img_path = exec_img_path.replace("_exec.jpg", "_exec_error.jpg")
                    error_info = "错误的操作：{}，错误提示为{}".format(guide_res_info['action_desc'],
                                                                     reflect_res_info['reflect_cont'])
                    continue
                else:
                    error_info = ""
                    self.execute_correct_record.append(execute_info)
            else:
                error_info = ""
                self.execute_correct_record.append(execute_info)

            # 是否完成 和 内容总结
            process_agent = ProcessSummaryAgent(mrd=latest_mrd, execute_record=self.execute_correct_record,
                                                exec_image_path=exec_img_path,
                                                app_info=self.app_info)
            process_agent_res = process_agent.main()
            process_res = process_agent_res['agent_res']
            execute_info['process_res_info'] = process_res
            idx += 1

            if process_res['result']:
                logger.info("=====测试完成=====：{}".format("->".join(self.new_mrd_list)))
                res_flag = 1
                break

        return res_flag

    def save_step_info(self):
        """
        保存节点信息
        :return:
        """
        if len(self.execute_correct_record) == 0:
            return
        for item in self.execute_correct_record:
            # if item['reflect_res_info']['reflect_flag']:
            step_info = item['step_info']['step_info']
            self._save_single_step_info(step_info=step_info, mrd_item=item['guide_res_info']['mrd_item'],
                                        mrd_info_list=self.mrd_info_list)

    def main(self):
        """

        :return:
        """
        # 前置操作
        self.before()

        # 自动化判断
        can_transform = self.can_auto_case_generate()
        if not can_transform:
            return -1

        # 执行前置步骤
        self.case_retrieve_and_run()

        # 执行
        try:
            res_flag = self.guide_by_mllm()
        except Exception as e:
            logger.info("生成用例步骤失败：{}".format(e))
            traceback.print_exc()
            res_flag = -1

        # 存储
        self.save_step_info()

        return res_flag


if __name__ == '__main__':
    os_type = 2
    product_id = 74
    qamate_case_hander = QamateCaseHandle(os_type)

    mrd_list, mrd_info_list = qamate_case_hander.get_manual_case_by_case_root_id(
        43303447, product_id=product_id, goal_labels=[]
    )

    print("共{}条case待生成".format(len(mrd_list)))
    # mrd_list = random.sample(mrd_list, k=10)

    for no in range(len(mrd_list)):
        # if no > 5:
        #     continue
        try:
            mrd = mrd_list[no][1:]
            mrd_info = mrd_info_list[no][1:]
            logger.info("----------------------{}: Agent 执行: {}----------------------".format(no, mrd))
            t = TuoGuanAutoCaseGenerate(
                mrd_list=mrd,
                mrd_info=mrd_info,
                knowledge_case_node_id=32111974,
                os_type=os_type,
                app_name="百度",
                product_module_id=product_id,
                keyword_knowledge_node_id=41868499,
                bind_leaf=True
            ).main()
        except:
            logger.error("mrd 执行异常: {}".format(mrd))
            logger.error("错误信息: {}".format(traceback.format_exc()))
        # break
