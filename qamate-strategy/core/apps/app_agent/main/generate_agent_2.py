#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@File    : generate_agent.py
<AUTHOR> <PERSON><PERSON><PERSON><PERSON>(<EMAIL>)
@Time    : 2024/11/15 17:33
@Desc    :
"""
import shutil
import sys
sys.path.append("/Users/<USER>/Workspace/Project/lazyOne/qamate-strategy/core")
import logging.handlers
import os
import time
import traceback
import uuid

import cv2

from apps.app_agent.conf.app_note import APP_NOTE
from apps.app_agent.conf.device import DEVICE_ID
from apps.app_agent.entity.generate import ExecuteInfo
from apps.app_agent.knowledge_search.scene_kb import SceneKB
from apps.app_agent.multi_agent.auto_case_generate_agent.guide_agent.guide_agent_v3 import GuideAgent
from apps.app_agent.multi_agent.auto_case_generate_agent.reflect_agent.agent import GuideReflectAgent
from apps.app_agent.multi_agent.auto_case_generate_agent.summary_agent.agent_2 import ProcessSummaryAgent
from apps.app_agent.multi_agent.auto_case_generate_agent.utils.utils import (mllm_guide_action_parse)
from apps.app_agent.multi_agent.automated_judgment_agent.agent import AutomatedJudgmentAgent
from apps.app_agent.multi_agent.high_quality_judgement_agent.agent import HighQualityCaseAgent
from apps.app_agent.multi_agent.base.agent import BaseAgent
from apps.app_agent.multi_agent.optimize_manual_case_agent.agent import OptimizeManualAgent
from apps.app_agent.multi_agent.page_understand_agent.bat_page_understand_agent import BatPageUnderstandAgent
from apps.app_agent.multi_agent.auto_case_translate_agent.agent import BatDomCaseTranslateAgent
from apps.app_agent.multi_agent.preroute_agent.agent import PrerouteAgent
from apps.app_agent.utils.draw import draw_arrow, draw_rect
from apps.app_agent.utils.exception import AgentErrCode, AgentException
from apps.app_agent.utils.qamate_case_handle import QamateCaseHandle
from apps.auto_case_transform.device import Device
from basics.image.ui.image_process import image_to_base64
from basics.image.ui.webapi import get_popup_from_openapi
from basics.util import logger
from basics.util.config import CACHE_DIR
from basics.util.config import PY_LOG_DIR
from basics.util.image import Image

format = logging.Formatter(
    '[%(process)s][%(asctime)s][%(levelname)s] %(message)s')
logger.setLevel(logging.INFO)
# logger.setLevel(logging.DEBUG)

TEMP_DIR = os.path.join(PY_LOG_DIR, 'lazyGuide.log')

file = logging.handlers.TimedRotatingFileHandler(
    filename=TEMP_DIR, encoding='utf-8',
    when='D', backupCount=3
)
file.setFormatter(format)
logger.addHandler(file)


class Manual2AutoCaseAgent(BaseAgent):
    """
    手工用例生成自动化用例agent
    """

    def __init__(self, mrd_list: list, mrd_info: list, knowledge_case_node_id: int, keyword_knowledge_node_id: int,
                 os_type: int, product_module_id: int, app_name: str, bind_leaf=True):
        """

        :param mrd_list: 例如：["首页及对话", "对话页", "助手tab和历史tab可点击切换、可手势横划切换"]
        :param mrd_info:
        :param knowledge_case_node_id:
        :param os_type:
        :param product_module_id:
        :param app_name:
        """
        super(Manual2AutoCaseAgent, self).__init__(name="manual_to_auto_case_agent")
        self.mrd_list = mrd_list
        self.mrd_info_list = mrd_info

        self.knowledge_version_id = knowledge_case_node_id
        self.keyword_knowledge_node_id = keyword_knowledge_node_id
        self.os_type = os_type
        self.module_id = product_module_id

        if os_type == 2:
            self.os_name = "ios"
        else:
            self.os_name = "android"

        self.model_name = "gpt-4o"
        self.reduce_ratio = 1  # 屏幕的缩小比例
        self.check_action_element = True  # 是否对生成的element_id进行校验

        self.temp_dir = os.path.join(CACHE_DIR, str(uuid.uuid4()))
        self.app_info = self._get_app_info(app_name)
        # self.dv = None
        self.dv = Device(DEVICE_ID)

        self.new_mrd_list = None
        self.retrieve_res = None
        self.execute_record = []  # 执行记录
        self.new_mrd_retrieve = None

        self.save_node_index = -1
        self.bind_leaf = bind_leaf

        self.qamate_case_hander = None
        if self.qamate_case_hander is None:
            self.qamate_case_hander = QamateCaseHandle(os_type)

    def _get_app_info(self, app_name):
        """

        :param app_name:
        :return:
        """
        if app_name in APP_NOTE:
            return {
                "name": app_name,
                "desc": APP_NOTE[app_name]
            }
        else:
            logger.warning("APP_NOTE 没有{}信息".format(app_name))
            return {}

    def mk_dir(self):
        """

        :return:
        """
        if not os.path.exists(self.temp_dir):
            os.mkdir(self.temp_dir)
        logger.info("temp_dir: {}".format(self.temp_dir))

    def _current_image(self, image_path):
        """
        获取当前截图
        :param image_path:
        :return:
        """
        img_url = self.dv.screenshot()['screenshot']
        image = Image(url=img_url)
        image.imgcv = cv2.resize(image.imgcv, (0, 0), fx=self.reduce_ratio, fy=self.reduce_ratio)
        image.save_image(image_path)
        return image

    def _resize_image(self, img_path):
        """
        缩放图片，节省token
        :param img_path:
        :return:
        """
        imgcv = cv2.imread(img_path)
        if self.reduce_ratio != 1:
            self.reduce_ratio = round(500 / imgcv.shape[1], 3)
        resized_img = cv2.resize(imgcv, (0, 0), fx=self.reduce_ratio, fy=self.reduce_ratio)
        cv2.imwrite(img_path, resized_img)

    def _check_popup_action(self, image_path):
        """
        检查是否有弹窗
        :param image_path:
        :param grid_path:
        :return:
        """
        click_num = 0
        while click_num < 5:
            # 根据截图判断是否有弹窗
            img64 = image_to_base64(image_path)
            res = get_popup_from_openapi(img64, [0, self.module_id])

            try:
                popup_flag = res["data"]["hit"]
            except:
                popup_flag = False

            if popup_flag is False:
                return

            click_x = res["data"]["rect"]["x"] + int(res["data"]["rect"]["w"] * 0.5)
            click_y = res["data"]["rect"]["y"] + int(res["data"]["rect"]["h"] * 0.5)

            click_x /= self.dv.size_info["scale"]
            click_y /= self.dv.size_info["scale"]

            self.dv.tap(click_x, click_y)
            time.sleep(5)
            click_num += 1

            # 重新进行截图
            img_url = self.dv.screenshot()['screenshot']
            image = Image(url=img_url)
            image.save_image(image_path)

    def _current_image_record_and_grid(self, image_path, grid_path):
        """
        页面建模 + 画框
        :param image_path: 原图存储地址
        :param grid_path: 带有gird图存储地址
        :return:
        """
        img_url = self.dv.screenshot()['screenshot']
        image = Image(url=img_url)
        image.save_image(image_path)

        # 判断是否有弹窗
        self._check_popup_action(image_path=image_path)

        page_unds_agent = BatPageUnderstandAgent(image_path=image_path, grid_img_save_path=grid_path)
        dom_info, dom, dom_desc, id_ele_map = page_unds_agent.main()

        batdom_record_res = {
            "info": dom_info,
            "dom": dom,
            "dom_desc": dom_desc,
            "id_ele_map": id_ele_map,
            "deviceInfo": {
                "screenSize": {
                    "rotation": self.dv.rotation_info,
                    "width": self.dv.size_info["width"],
                    "height": self.dv.size_info["height"],
                    "scale": self.dv.size_info["scale"]
                },
                "screenshot": img_url,
                "type": "ios" if self.os_type == 2 else "android"
            }
        }

        self._resize_image(image_path)
        self._resize_image(grid_path)

        return batdom_record_res

    def _draw_exec_image(self, action_res, raw_img_path, exec_img_path, cur_batdom_record):
        """

        :param image_path:
        :return:
        """

        if action_res['action'] in ('finsh', 'unknown'):
            exec_img_path = raw_img_path
            return exec_img_path

        if action_res['action'] == "swipe":
            draw_arrow(image_path=raw_img_path, direction=action_res['direction'],
                       output_path=exec_img_path, reduce_size=self.reduce_ratio)
        elif action_res['action'] == "assert_not_exist" or (
                action_res['action'] == "assert_exist" and action_res['element_id'] == -1):
            exec_img_path = raw_img_path
        else:
            ele_idx = action_res['element_id']
            bat_ele = cur_batdom_record['id_ele_map'][ele_idx]
            exe_ele_rect = {
                "x": int(bat_ele['rect']['x'] * self.reduce_ratio),
                "y": int(bat_ele['rect']['y'] * self.reduce_ratio),
                "w": int(bat_ele['rect']['w'] * self.reduce_ratio),
                "h": int(bat_ele['rect']['h'] * self.reduce_ratio)
            }
            draw_rect(image_path=raw_img_path, rect=exe_ele_rect, output_path=exec_img_path)
        return exec_img_path

    def can_auto_case_generate(self):
        """
        能否自动化
        :return:
        """
        mrd = "->".join(self.mrd_list)
        auto_judge_agent = AutomatedJudgmentAgent(mrd=mrd, app_info=self.app_info)
        judge_res = auto_judge_agent.main()
        can_transform = judge_res.get("can_transform")
        return can_transform

    def have_detail_exec_step(self):
        """
        判断是否有明确的执行步骤
        :return:
        """
        mrd = "->".join(self.mrd_list)
        judge_agent = HighQualityCaseAgent(mrd=mrd)
        res = judge_agent.main()
        have_detail_step = res.get("result")

        # 如果没有规范化，打上标签，返回
        if not have_detail_step:
            logger.warning("此用例：{}，不包含详细的用例步骤，不进行生成".format(mrd))
            tag_info = {
                "caseTagList": [],
                "moduleTagList": [921]  # 没有详细步骤
            }
            self.qamate_case_hander.add_node_tag_info(node_id=self.mrd_info_list[-1]['caseNodeId'], tag_info=tag_info)
            return False

        return True

    def get_product_knowledge(self, mrd: str):
        """
        获取业务知识
        :param mrd:
        :return:
        """
        if self.keyword_knowledge_node_id == 0:
            return ""

        ks = SceneKB(os_type=self.os_type, kb_node_id=self.keyword_knowledge_node_id,
                     tmp_dir=self.temp_dir)
        p_knowledge = ks.get_scene_by_step(mrd)
        return p_knowledge

    def case_retrieve_and_run(self):
        """
        前置准备：知识检索+执行 用于冷启
        :return:
        """

        preroute_agent = PrerouteAgent(mrd_list=self.mrd_list, knowledge_case_node_id=self.knowledge_version_id,
                                       os_type=self.os_type)
        retrieve_res = preroute_agent.main()
        self.new_mrd_list = retrieve_res['new_mrd_list']
        self.retrieve_res = retrieve_res

        # 执行前置步骤
        step_infos = []
        logger.info("执行前置case步骤, case_name: {}".format(retrieve_res['knowledge_case']['nodeName']))

        # TODO:暂时先不处理beforeAll和afterAll类型的步骤

        for step_info in retrieve_res['steps']:
            action_info = step_info['stepInfo']
            run_res = self.dv.run_step_compatibility(action_info, os_type=self.os_type, module_id=self.module_id)
            logger.info("执行结果: {}".format(run_res))
            time.sleep(2)
            step_infos.append(step_info)

        # 检索到的知识也挂载到父节点去
        # 执行成功的话，将执行的步骤挂载到手自一体的节点中去
        # 初期挂载逻辑为：mrd_index 对应的节点中挂载对应的全部步骤，其它mrd为空节点
        self.qamate_case_hander.save_pre_operation_nodes(
            # retrieve_res["mrd_list"], retrieve_res["mrd_index"],
            self.mrd_info_list, step_infos, bind_leaf=self.bind_leaf, after_index=retrieve_res["mrd_index"]
        )

    def run_correct_step(self):
        """
        执行之前正确的步骤
        :return:
        """
        # 首先执行检索或前置父节点信息
        logger.info("重新执行检索节点信息")
        for step_info in self.retrieve_res['steps']:
            action_info = step_info['stepInfo']
            run_res = self.dv.run_step_compatibility(action_info, os_type=self.os_type, module_id=self.module_id)
            logger.info("执行结果: {}".format(run_res))
            time.sleep(2)

        count = 0
        if len(self.execute_record) == 0:
            return count

        # 再执行前面生成的正确步骤
        for exec_info in self.execute_record:
            logger.info("重新执行第{}步节点信息".format(count))
            step_info = exec_info['step_info']['step_info']
            # if exec_info['step_info']['run_flag']:
            run_res = self.dv.run_step_compatibility(step_info["stepInfo"],
                                                     os_type=self.os_type,
                                                     module_id=self.module_id)
            logger.info("执行结果: {}".format(run_res))
            time.sleep(8)
            count += 1

        return count

    def generate_step_by_step(self, mrd_step_list: list, step_max_count=3):
        """
        分步骤进行生成
        :param mrd_step_list:
        :param step_max_count: 每一步的最大生成次数
        :return:
        """
        mrd_step = "->".join(mrd_step_list)
        logger.info("-" * 20 + "mrd_step:{}".format(mrd_step))
        step_flag = 0

        # 获取业务知识
        p_knowledge = self.get_product_knowledge(mrd_step)

        # 检索信息
        pre_step_info = "已经完成 {} 操作".format(self.retrieve_res['knowledge_case']['nodeName'])

        count = 0
        while len(self.execute_record) < 10 and count < step_max_count:
            idx = len(self.execute_record)
            count += 1
            logger.info("=" * 10 + "start {}".format(idx) + "=" * 10)
            raw_img_path = os.path.join(self.temp_dir, "{}.jpg".format(idx))
            grid_img_path = os.path.join(self.temp_dir, "{}_grid.jpg".format(idx))
            exec_img_path = os.path.join(self.temp_dir, "{}_exec.jpg".format(idx))
            cur_batdom_record = self._current_image_record_and_grid(raw_img_path, grid_img_path)

            # 得到动作
            guide_agent = GuideAgent(mrd_list=self.new_mrd_list,
                                     opt_mrd_list=mrd_step_list,
                                     batdom_record=cur_batdom_record,
                                     execute_record=self.execute_record,
                                     app_info=self.app_info,
                                     pre_step=pre_step_info,
                                     raw_img_path=raw_img_path,
                                     grid_img_path=grid_img_path,
                                     product_knowledge=p_knowledge,
                                     completed_contents=self.retrieve_res['knowledge_case']['nodeName'],
                                     error_info='')
            guide_agent_res = guide_agent.main()
            chat_list = guide_agent_res['message']
            guide_res = guide_agent_res['agent_res']

            # 操作解析
            action_res = mllm_guide_action_parse(guide_res['action'])
            if action_res['action'] == 'tap' and action_res['element_id'] == -1:
                raise Exception("期望点击的元素未建模出来，元素描述为：{}".format(guide_res['action_desc']))

            if action_res['error']:
                logger.info("舍弃本次结果，重新执行本次 Action Agent")
                continue

            guide_res_info = {
                "thought": guide_res['thought'],
                "action": guide_res['action'],
                "action_desc": guide_res['action_desc'],
                "mrd_item": guide_res['mrd_item'],
                "summary": guide_res.get("summary", "")
            }

            # 执行图片可视化
            exec_img_path = self._draw_exec_image(action_res=action_res,
                                                  raw_img_path=raw_img_path,
                                                  exec_img_path=exec_img_path,
                                                  cur_batdom_record=cur_batdom_record)

            if "finsh" in guide_res['action'] or "unknown" in guide_res['action']:
                logger.info("=====测试完成=====：{}".format("->".join(self.new_mrd_list)))
                step_flag = 1
                break

            # 转为qamate 执行用例步骤，并执行
            step_run_info = self._action_2_step_info_and_run(grid_img_path=grid_img_path,
                                                             mrd_item=guide_res['mrd_item'],
                                                             record_batdom=cur_batdom_record,
                                                             action_res=action_res,
                                                             action_dec=guide_res['action_desc'])

            # 自我反思
            if "assert" in guide_res['action']:
                reflect_res_info = {
                    'reflect_flag': True,
                    'reflect_cont': "完成本条校验"
                }
            else:
                after_img_path = os.path.join(self.temp_dir, "{}.jpg".format(idx + 1))
                self._current_image(after_img_path)
                reflect_agent = GuideReflectAgent(chat_message=chat_list,
                                                  mrd=mrd_step,
                                                  action_desc=guide_res_info['action_desc'],
                                                  action_thought=guide_res_info['thought'],
                                                  exec_image_path=exec_img_path,
                                                  after_image_path=after_img_path)
                reflect_agent_res = reflect_agent.main()
                reflect_res_info = {
                    'reflect_flag': reflect_agent_res['agent_res']['result'],
                    'reflect_cont': reflect_agent_res['agent_res']['feedback']
                }

            # 执行记录
            execute_info = ExecuteInfo(raw_img_path=raw_img_path, grid_img_path=grid_img_path,
                                       batdom_record=cur_batdom_record['dom'],
                                       action_res=action_res, exec_img_path=exec_img_path,
                                       guide_res_info=guide_res_info,
                                       reflect_res_info=reflect_res_info,
                                       process_res_info={},
                                       step_info=step_run_info).to_dict()
            self.execute_record.append(execute_info)

            if not reflect_res_info['reflect_flag']:
                process_res = {
                    "result": False,
                    "completed_contents": ""
                }
                execute_info['process_res_info'] = process_res
                continue

            # 是否完成 和 内容总结
            process_agent = ProcessSummaryAgent(mrd=mrd_step, execute_record=self.execute_record,
                                                exec_image_path=exec_img_path,
                                                app_info=self.app_info)
            process_agent_res = process_agent.main()
            process_res = process_agent_res['agent_res']
            execute_info['process_res_info'] = process_res

            if process_res['result']:
                logger.info("----------done----------：{}".format(mrd_step))
                step_flag = 1
                break

        return step_flag

    def guide_by_mllm(self):
        """
        大模型引导执行
        : return
            res_flag: 1 正常生成；0 判断转为自动化；-1 转化失败
        """

        res_flag = -1

        new_mrd = "->".join(self.new_mrd_list)
        logger.info("需求第一次更新，更新为：{}".format(new_mrd))
        summary = "已经完成 {} 操作".format(self.retrieve_res['knowledge_case']['nodeName'])
        error_info = ""

        # 转化验证
        opt_agent = OptimizeManualAgent(mrd=new_mrd, app_info=self.app_info, summary=summary)
        optm_res = opt_agent.main()
        latest_mrd_list = optm_res['optimized_test_case']
        mrd_desc = optm_res['case_description']
        latest_mrd = "->".join(latest_mrd_list)
        logger.info("需求第二次更新，更新为：{}".format(latest_mrd))

        for step_id in range(len(latest_mrd_list)):
            step_mrd_list = latest_mrd_list[:step_id + 1]
            step_flag = self.generate_step_by_step(step_mrd_list)
            if step_flag == 0:
                res_flag = -1
                logger.warning("！！！在{}生成失败".format(step_mrd_list))
                break
            else:
                res_flag = 1

        if res_flag == 1:
            logger.info("========正常生成完成========")

        return res_flag

    def _action_2_step_info_and_run(self, grid_img_path, mrd_item, record_batdom, action_res, action_dec):
        """
        转为 QAMate step_info
        :param mrd_item:
        :param record_batdom:
        :param action_res:
        :param action_dec:
        :return:
        """
        step_info, action_flag = BatDomCaseTranslateAgent(grid_img_path=grid_img_path,
                                                          batdom_record=record_batdom,
                                                          mrd_item=mrd_item,
                                                          step_desc=action_dec,
                                                          action_res=action_res).main()

        # action_flag, step_info = self.qamate_case_hander.transform_action_res_to_step_info(
        #     mrd_item=mrd_item, record_batdom=record_batdom, action_res=action_res, action_dec=action_dec
        # )
        res = {
            "step_info": step_info,
            "run_flag": action_flag
        }
        run_res = self.dv.run_step_compatibility(step_info["stepInfo"],
                                                 os_type=self.os_type,
                                                 module_id=self.module_id)

        logger.info("执行结果: {}".format(run_res))
        time.sleep(5)
        return res

    def _save_single_step_info(self, step_info, mrd_item, mrd_info_list):
        """
        保存 step_info
        :param step_info:
        :param mrd_info_list:
        :param bind_leaf:
        :param save_node_index:
        :return:
        """
        save_candidates = [i for i, item in enumerate(mrd_info_list) if item['desc'] == mrd_item and i >=
                           self.save_node_index]
        if self.bind_leaf is False:
            if len(save_candidates) == 0:
                if self.save_node_index < 0:
                    raise AgentException(err_code=AgentErrCode.PARAM_ERROR, detail="输入的用例不存在")
            else:
                self.save_node_index = save_candidates[0]

        self.qamate_case_hander.single_add_step(step_info=step_info, mrd_info_list=mrd_info_list,
                                                bind_leaf=self.bind_leaf, save_node_index=self.save_node_index)

    def save_step_info(self):
        """
        保存节点信息
        :return:
        """
        if len(self.execute_record) == 0:
            return
        for item in self.execute_record:
            # if item['reflect_res_info']['reflect_flag']:
            step_info = item['step_info']['step_info']
            self._save_single_step_info(step_info=step_info, mrd_item=item['guide_res_info']['mrd_item'],
                                        mrd_info_list=self.mrd_info_list)

    def run_preceding_nodes(self):
        """
        查询当前用例的所有前置节点是否有自动化步骤，如果有，则直接执行
        1. 提取第一次出现手工步骤前的最后一个自动化步骤
        """

        auto_step_indices = []
        node_ids = []
        for i, mrd_i in enumerate(self.mrd_info_list):
            if mrd_i['type'] == 'step':
                if mrd_i['sub_type'] in (101, 102):
                    # 如果是手工步骤，直接跳出循环
                    break
                else:
                    # 如果是自动化步骤，则把下标加入列表
                    auto_step_indices.append(i)
                    if mrd_i['caseNodeId'] not in node_ids:
                        node_ids.append(mrd_i['caseNodeId'])

        if len(node_ids) == 0:
            logger.info("当前用例没有前置自动化步骤")
            self.new_mrd_list = self.mrd_list
            self.retrieve_res = {
                "steps": [],
                "mrd_index": -1,
                "mrd_list": self.mrd_list,
                "new_mrd_list": self.new_mrd_list,
                "knowledge_case": {
                    "nodeName": ""
                }
            }
            # 后续会查询self.retrieve_res['knowledge_case']['nodeName']作为已经完成的操作输入到大模型，因此需要模拟这个数据
            return False

        # 提取第一次出现手工步骤前的最后一个自动化步骤
        last_auto_step_index = auto_step_indices[-1]

        # 判断last_auto_step_index对应步骤，是否是其所属caseNode的最后一个步骤，如果不是，则存在半自动化步骤，抛异常
        last_auto_cae_node_id = self.mrd_info_list[last_auto_step_index]['caseNodeId']
        case_step_indices = [i for i, x in enumerate(self.mrd_info_list) if x['caseNodeId'] ==
                             last_auto_cae_node_id]
        if last_auto_step_index != case_step_indices[-1]:
            raise AgentException(err_code=AgentErrCode.PARAM_ERROR, detail="输入的用例包括半自动化节点")

        # 提取所有前置自动化步骤
        preceding_auto_steps = [self.mrd_info_list[i] for i in auto_step_indices]

        steps = []
        # 根据node_indices查询所有步骤信息，并执行前置自动化步骤
        for node_id in node_ids:
            steps = self.qamate_case_hander.get_steps_by_node_id(node_id)
            for step in steps:
                action_info = step['stepInfo']
                run_res = self.dv.run_step_compatibility(action_info, os_type=self.os_type, module_id=self.module_id)
                logger.info("执行结果: {}".format(run_res))
                time.sleep(2)

        # 调整上下文，让Agent意识到前置步骤已经执行了
        self.new_mrd_list = self.mrd_list[last_auto_step_index + 1:]
        # 后续会查询self.retrieve_res['knowledge_case']['nodeName']作为已经完成的操作输入到大模型，因此需要模拟这个数据

        steps_desc = [mrd_i["desc"] for mrd_i in self.mrd_info_list[:last_auto_step_index + 1]
                      if (mrd_i["type"] == "step")]
        self.retrieve_res = {
            "steps": steps,
            "mrd_index": last_auto_step_index,
            "mrd_list": self.mrd_list,
            "new_mrd_list": self.new_mrd_list,
            "knowledge_case": {
                "nodeName": "->".join(steps_desc)
            }
        }
        return True

    def main(self):
        """
        :return:
        """
        # 前置操作
        self.mk_dir()

        # 校验：如果叶子节点有自动化步骤，则不执行生成
        last_mrd = self.mrd_info_list[-1]
        if last_mrd["type"] == "step" and last_mrd["sub_type"] not in (101, 102):
            raise AgentException(err_code=AgentErrCode.PARAM_ERROR, detail="叶子节点有自动化步骤，不执行生成")

        # 自动化判断
        can_transform = self.can_auto_case_generate()
        if not can_transform:
            return -1

        # 是否含有详细的操作步骤
        have_detail_steps = self.have_detail_exec_step()
        if not have_detail_steps:
            return -1

        # 自动化步骤挂载父节点的改造
        # 1. 如果父节点有自动化步骤，跳过引导模块，直接执行父节点的自动化步骤。
        # 2. QAMate3.0把中间步骤展平成了和节点同级别。因此生成过程中很难知道挂载到哪个节点(直接在info里塞一个caseNodeId，
        # 不论这个info的type是node还是step，挂载就按照caseNodeId来)
        # 3. 执行前置步骤后，模拟知识检索更新上下文：new_mrd_list，retrieve_res，用于后面大模型引导执行
        # 4. 如果执行知识检索，检索到的知识也挂载到对应节点
        # 5. 用例优化agent吐出优化后和优化前节点的对应关系，用于挂载到父节点
        # 6. 知识检索保存、大模型引导保存，如果节点只有手工步骤，支持直接清空
        if not self.run_preceding_nodes():
            # 判断父节点是否有自动化步骤，如果有，则直接执行
            # 如果没有，检索启动场景
            self.case_retrieve_and_run()
        elif self.bind_leaf is True:
            raise AgentException(err_code=AgentErrCode.PARAM_ERROR, detail="父节点有前置自动化步骤，不能绑定到叶节点")

        try:
            res_flag = self.guide_by_mllm()
        except Exception as e:
            logger.info("生成用例步骤失败：{}".format(e))
            traceback.print_exc()
            res_flag = -1

        # 存储
        self.save_step_info()

        return res_flag


if __name__ == '__main__':
    os_type = 2
    product_id = 21
    qamate_case_hander = QamateCaseHandle(os_type)

    # root_nodes = [27700321, 27795906, 27796585]
    # mrd_list, mrd_info_list = [], []
    #
    # for root_node in root_nodes:
    #     mrd_list_, mrd_info_list_ = qamate_case_hander.get_manual_case_by_case_root_id(
    #         root_node, product_id=product_id, goal_labels=[]
    #     )
    #     mrd_list.extend(mrd_list_)
    #     mrd_info_list.extend(mrd_info_list_)

    mrd_list, mrd_info_list = qamate_case_hander.get_manual_case_by_case_root_id(
        18670951, product_id=product_id, goal_labels=["可进行自动化用例生成"]
    )

    print("共{}条case待生成".format(len(mrd_list)))
    # mrd_list = random.sample(mrd_list, k=10)

    for no in range(len(mrd_list)):
        # if no != 4:
        #     continue
        try:
            mrd = mrd_list[no][1:]
            mrd_info = mrd_info_list[no][1:]
            logger.info("----------------------{}: Agent2 执行: {}----------------------".format(no, mrd))
            t = Manual2AutoCaseAgent(
                mrd_list=mrd,
                mrd_info=mrd_info,
                knowledge_case_node_id=25196425,
                os_type=os_type,
                app_name="NewApp",
                product_module_id=product_id,
                keyword_knowledge_node_id=22447589,
                bind_leaf=True
            ).main()
        except:
            logger.error("mrd 执行异常: {}".format(mrd))
            logger.error("错误信息: {}".format(traceback.format_exc()))
