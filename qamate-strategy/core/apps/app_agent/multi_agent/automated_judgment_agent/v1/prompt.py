#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@File    : prompt.py
<AUTHOR> <PERSON><PERSON><PERSON><PERSON>(renya<PERSON><PERSON>@baidu.com)
@Time    : 2024/11/14 14:51
@Desc    : 
"""
from typing import List


def judgment_prompt(mrd: str, app_info: dict, pd_unsupported_dependencies: List[str] = None) -> str:
    """

    :return:
    """
    if pd_unsupported_dependencies is None:
        pd_unsupported_dependencies = ['特定数据',
                                       '动态数据',
                                       '相册图片',
                                       '相机权限',
                                       '预装其他APP',
                                       '上传手机文件',
                                       '依赖历史记录',
                                       '手机顶部推送通知相关',
                                       '动态效果',
                                       '评论互动',
                                       '服务异常',
                                       '重试**',
                                       '首次**操作',
                                       '手按住不放',
                                       '轻划手势',
                                       '转到青少年模式',
                                       '音频播放',
                                       '视频播放',
                                       '切换App']

    normal_unsupported_dependencies = [
        '审核操作', '拍照操作', '发送评论', '删除操作', '语音输入', '动态生成的图片', '手按住不放', '开启/关闭云控'
    ]

    unsupported_dependencies = normal_unsupported_dependencies + pd_unsupported_dependencies

    if app_info:
        app_info_str = "&& 补充信息 &&\n当前正在测试的App为{}，简介：{}".format(app_info['name'], app_info['desc'])
    else:
        app_info_str = ""

    prompt = f"""&& 角色 &&
你是一个具有丰富App自动化测试经验的测试智能助手

{app_info_str}

&& 任务 &&
当前有一份人工编写的手工测试用例，你需要判断该测试用例是否可以转为通过手机设备执行的自动化测试用例，用例中可能存在与自动化执行无关的汇总性记录或表达，忽略这部分，关注测试路径和目标。
原始的测试用例是脑图格式，为了方便你理解，我做了格式转化，按层次结构使用'->'链接，测试用例为：【{mrd}】

&& 判断标准 &&
这里给出能否转化为自动化测试用例的部分标准参考：
1. 前置条件：前置条件过多或过于苛刻的不适合转化，例如：无网络、弱网条件下、实验场景（需要命中**实验）、未登录、历史看过的情况下、历史收藏过的情况下、历史点赞过的情况下、如果***等。
2. 复杂依赖：测试用例依赖过于复杂，不适合转化，例如：特定数据、动态数据、数据mock、依赖历史记录等。
3. 复杂交互：如果完成测试的操作过于复杂不适合转化，例如"累计多次**"。
4. 特殊场景：涉及{'、'.join(unsupported_dependencies)}场景不适合转化。
5. 短暂出现信息：我们的当前的自动化执行速度较慢，无法捕捉到短时间停留的信息，例如：页面toast。因此也不适合转为自动化。
6. 动态生成的目标：根据前置操作动态生成的可变的图片，不适合转为自动化。
7. 等待时间过长：例如在用例中明确设置了等待时间，例如：等待了30秒或5min后等，不适合自动化。
8. 特殊设备：折叠屏、pad平板电脑设备不适合转化。

请注意：上述所列举的不支持场景只有在执行过程中涉及到才不转为自动化用例，如果验证步骤中涉及，可以正常转化。
例如：用例[1、点击菜单，2、点击更多，3、预期：展开二级页面入口：拍照、照片、文档解析、打电话] 这里虽然涉及到打电话、拍照等场景，但是是在校验步骤中，因此可以正常转化。

&& 举例 &&
你需要从以下例子中学习分析的方法，并尽可能在分析任务中用到：
1. 用例[操作步骤：1、在主对话页面输入框中输入ask“请帮我画一张瓷器的图片”，点击发送按钮，生成小狗的图片 2、长按图片]不适合转化，因为操作目标小狗图片是动态生成的，无法定位到，不适合转化。
2. 用例[1、点击拍照搜题，2、打开相册，3、选择一张图片...]不适合自动化转化，因为依赖相机拍到的内容依赖外部现实的环境，无法保证拍到什么内容，结果不可控。
3. 用例[...选择相册第一张图片...]不适合自动化转化，因为不同的设备相册中的图片内容顺序不同，还有可能是空相册，无法保证选择到什么图片。
等"""
    output = """
&& 输出要求 &&
你的输出固定的包括以下部分，无需输出其他内容，以json格式输出：
{
    "can_transform": bool, // 一个布尔值，表示是否可以转化
    "reason": string, // 给能否转化判断的具体原因
    "label": string  // 如果不能转化，给出不能自动化的具体原因标签，例如：'toast场景'；如果可以转化，则固定为'可自动化'
}
"""
    return prompt + output


def judgment_prompt_v1(mrd: str, app_info: dict) -> str:
    """
    2025.3.4 优化
    :return:
    """

    if app_info:
        app_info_str = "&& 补充信息 &&\n当前正在测试的App为{}，简介：{}".format(app_info['name'], app_info['desc'])
    else:
        app_info_str = ""

    prompt = f"""&& 角色 &&
你是一个具有丰富App自动化测试经验的测试智能助手

{app_info_str}

&& 任务 &&
当前有一份人工编写的手工测试用例，你需要判断该测试用例是否可以转为通过手机设备执行的自动化测试用例，用例中可能存在与自动化执行无关的汇总性记录或表达，忽略这部分，关注测试路径和目标。
测试用例为：【{mrd}】

&& 判断标准 &&
这里给出无法转化为自动化测试用例的部分标准参考（**包括但不限于**）：
1. **依赖特殊物理交互**：涉及摇一摇/翻转/重力感应等物理行为、需要特殊传感器（陀螺仪/气压计等）等。
2. **依赖设备本机资源**：需要操作本机相册/通讯录/文件系统、依赖特定硬件（NFC/蓝牙等）、需要预置特定格式/大小的文件等。
3. **依赖特殊设备**：折叠屏、pad平板电脑设备不适合转化。
4. **依赖特定环境条件**：要求特殊网络状态（弱网/断网）、实验场景：需要特定实验参数配置等。
5. **依赖复杂触发条件**：如首次安装、首次进入某页面、长时间间隔后操作、服务异常场景、当**>(或<)N次时等，不包括『冷启动App』。
6. **涉及自动化不支持的操作**：轻划手势、切换App、语音输入等。
7. **其他影响自动化的因素**：如高频UI变动、无法精准定位元素、弹窗不可控等。

**请注意**：上述所列举的不支持场景只有在执行过程中涉及到才不转为自动化用例，如果验证步骤中涉及，可以正常转化。
例如：用例[1、点击菜单，2、点击更多，3、预期：展开二级页面入口：拍照、照片、文档解析、打电话] 这里虽然涉及到打电话、拍照等场景，但是是在校验步骤中，因此可以正常转化。

&& 样例 &&
- 用例[1、点击拍照搜题，2、拍摄一张数学题目...]不适合自动化转化，因为依赖相机拍到的内容依赖外部现实的环境，无法保证拍到什么内容，结果不可控。
等"""
    output = """
&& 输出要求 &&
你的输出固定的包括以下部分，无需输出其他内容，以json格式输出：
{
    "can_transform": bool, // 一个布尔值，表示是否可以转化
    "reason": string, // 给能否转化判断的具体原因
    "label": string  // 如果不能转化，给出不能自动化的具体原因标签，例如：'toast场景'；如果可以转化，则固定为'可自动化'
}
"""
    return prompt + output
