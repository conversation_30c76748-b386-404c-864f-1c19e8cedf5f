#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@File    : guide_agent.py
<AUTHOR> <PERSON><PERSON><PERSON><PERSON>(renya<PERSON><PERSON>@baidu.com)
@Time    : 2024/11/14 14:46
@Desc    : 
"""

import json
import threading

from apps.app_agent.multi_agent.base.agent import BaseChat
from apps.app_agent.utils.chat_message import ChatMessage
from apps.app_agent.utils.YiYan import <PERSON><PERSON><PERSON>
from apps.app_agent.multi_agent.automated_judgment_agent.v2_1.prompt import judgment_prompt_v1
from basics.util import logger
from apps.app_agent.utils.exception import AgentErrCode, AgentException

MAX_COUNT = 3


class AutomatedJudgmentAgent(BaseChat):
    """
    自动化判断Agent
    """

    def __init__(self, mrd: str, app_info: dict={}):
        """

        :param mrd:
        """
        super().__init__(name="automated_judgment_agent")
        self.mrd = mrd
        self.app_info = app_info

    def prompt_message(self):
        """

        :return:
        """
        prompt = judgment_prompt_v1(mrd=self.mrd, app_info=self.app_info)
        # logger.info("judge prompt: {}".format(prompt))
        message = ChatMessage()
        message.add_user_message(prompt_text=prompt)
        return message

    def format_answer(self, llm_ans):
        """
        解析回答
        :param llm_ans:
        :return:
        """
        f_ans = json.loads(llm_ans)
        return f_ans

    def judge(self):
        """

        :return:
        """
        # prompt
        chat_message = self.prompt_message()

        # answer
        num = 0
        while num < MAX_COUNT:
            num += 1
            try:
                llm_ans = YiYan().chat(chat_message=chat_message, response_format="json_object")
                res = self.format_answer(llm_ans)
                return res
            except Exception as e:
                logger.error(e)
                logger.error("output error, try again, left %d" % num)
        raise AgentException(err_code=AgentErrCode.LLM_UNEXPECTED_OUPUT)

    def main(self):
        """
        多线程，多数投票的方法
        :return:
        """

        results = []

        def works():
            """
            线程函数
            :return:
            """
            result = self.judge()
            results.append(result)

        threads = []

        # 创建线程
        for _ in range(3):
            thread = threading.Thread(target=works)
            threads.append(thread)
            thread.start()

        # 等待完成
        for thread in threads:
            thread.join()

        # 投票统计结果
        can_transform_votes = [result["can_transform"] for result in results]

        final_can_transform = max(set(can_transform_votes), key=can_transform_votes.count)

        for res in results:
            if res['can_transform'] == final_can_transform:
                final_reason = res["reason"]
                final_label = res["label"]
                break

        final_res = {
            "can_transform": final_can_transform,
            "reason": final_reason,
            "label": final_label
        }
        logger.info("判断可自动化最终结果为：{}".format(final_res))
        return final_res


if __name__ == '__main__':
    from apps.app_agent.utils.qamate_case_handle import QamateCaseHandle
    os_type = 2
    product_id = 21
    qamate_case_hander = QamateCaseHandle(os_type)

    mrd_list, mrd_info_list = qamate_case_hander.get_manual_case_by_case_root_id(
        43746550, product_id=product_id, goal_labels=['可进行自动化用例生成且规范化']
    )
    # mrd_list_p = []
    # for item in mrd_list:
    #     if "智能体社区评论互动" in item:
    #         mrd_list_p.append(item)

    mrd = "->".join(mrd_list[1])
    print(mrd)
    res = AutomatedJudgmentAgent(mrd=mrd).main()

