#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@File    : prompt.py
<AUTHOR> <PERSON><PERSON><PERSON><PERSON>(<EMAIL>)
@Time    : 2024/11/14 14:51
@Desc    : 
"""
from typing import List


def judgment_prompt_v1(mrd: str, app_info: dict) -> str:
    """
    2025.3.4 优化
    :return:
    """

    if app_info:
        app_info_str = "&& 补充信息 &&\n当前正在测试的App为{}，简介：{}".format(app_info['name'], app_info['desc'])
    else:
        app_info_str = ""

    prompt = f"""&& 角色 &&
你是一个具有丰富App自动化测试经验的测试智能助手

{app_info_str}

&& 任务 &&
当前有一份人工编写的手工测试用例，你需要判断该测试用例是否可以转为通过手机设备执行的自动化测试用例，用例中可能存在与自动化执行无关的汇总性记录或表达，忽略这部分，关注测试路径和目标。
测试用例为：【{mrd}】

&& 判断标准 &&
这里给出无法转化为自动化测试用例的部分标准参考（**包括但不限于**）：
1. **依赖特殊物理交互**：涉及摇一摇/翻转/重力感应等物理行为、需要特殊传感器（陀螺仪/气压计等）等。
2. **依赖设备本机资源**：需要操作本机相册/通讯录/文件系统、依赖特定硬件（NFC/蓝牙等）、需要预置特定格式/大小的文件等。
3. **依赖特殊设备**：折叠屏、pad平板电脑设备不适合转化。
4. **依赖特定环境条件**：要求特殊网络状态（弱网/断网）、实验场景：需要特定实验参数配置等。
5. **依赖复杂触发条件**：如首次安装、首次进入某页面、长时间间隔后操作、服务异常场景、当**>(或<)N次时等，不包括『冷启动App』。
6. **涉及自动化不支持的操作**：轻划手势、切换App、语音输入等。
7. **其他影响自动化的因素**：如高频UI变动、无法精准定位元素、弹窗不可控等。

**请注意**：上述所列举的不支持场景只有在执行过程中涉及到才不转为自动化用例，如果验证步骤中涉及，可以正常转化。
例如：用例[1、点击菜单，2、点击更多，3、预期：展开二级页面入口：拍照、照片、文档解析、打电话] 这里虽然涉及到打电话、拍照等场景，但是是在校验步骤中，因此可以正常转化。

&& 样例 &&
- 用例[1、点击拍照搜题，2、拍摄一张数学题目...]不适合自动化转化，因为依赖相机拍到的内容依赖外部现实的环境，无法保证拍到什么内容，结果不可控。
等"""
    output = """
&& 输出要求 &&
请直接输出一个能够被 json.loads 解析的 JSON 格式字符串，不要包含任何代码块标记：
{
    "can_transform": bool, // 一个布尔值，表示是否可以转化
    "reason": string, // 给能否转化判断的具体原因
    "label": string  // 如果不能转化，给出不能自动化的具体原因标签，例如：'toast场景'；如果可以转化，则固定为'可自动化'
}
"""
    return prompt + output
