#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@File    : recheck_retrieve_agent.py
<AUTHOR> <PERSON><PERSON><PERSON><PERSON>(renya<PERSON><PERSON>@baidu.com)
@Time    : 2024/11/18 15:25
@Desc    : 
"""

import json

from apps.app_agent.multi_agent.base.agent import Base<PERSON>hat
from apps.app_agent.utils.chat_message import ChatMessage
from apps.app_agent.utils.YiYan import <PERSON><PERSON><PERSON>
from apps.app_agent.multi_agent.preroute_agent.chat import recheck_retrieve_prompt
from basics.util import logger
from apps.app_agent.utils.exception import AgentErrCode, AgentException

MAX_COUNT = 3


class RecheckRetrieveAgent(BaseChat):
    """
    检索结果Agent
    """

    def __init__(self, retrieve_result):
        """

        :param mrd:
        """
        super().__init__(name="recheck_retrieve_res_agent")
        self.retrieve_result = retrieve_result

    def prompt_message(self):
        """

        :return:
        """

        mrd = "->".join(self.retrieve_result['mrd_list'])
        auto_case = self.retrieve_result['knowledge_case']['nodeName']
        new_case_list = [auto_case]
        new_case_list.extend(self.retrieve_result['new_mrd_list'])
        new_case = "->".join(new_case_list)

        prompt = recheck_retrieve_prompt(mrd=mrd, new_case=new_case)
        message = ChatMessage()
        message.add_user_message(prompt_text=prompt)
        return message

    def format_answer(self, llm_ans):
        """
        解析回答
        :param llm_ans:
        :return:
        """
        f_ans = json.loads(llm_ans)
        return f_ans

    def main(self):
        """

        :return:
        """
        # prompt
        chat_message = self.prompt_message()

        # answer
        num = 0
        while num < MAX_COUNT:
            num += 1
            try:
                llm_ans = YiYan().chat(chat_message=chat_message, temperature=0.01, response_format="json_object")
                res = self.format_answer(llm_ans)
                return res
            except Exception as e:
                logger.error(e)
                logger.error("output error, try again, left %d" % num)
        raise AgentException(err_code=AgentErrCode.LLM_UNEXPECTED_OUPUT)


if __name__ == '__main__':
    pass