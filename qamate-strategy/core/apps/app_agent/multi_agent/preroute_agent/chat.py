#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@File    : chat.py
<AUTHOR> <PERSON><PERSON><PERSON><PERSON>(<EMAIL>)
@Time    : 2024/11/15 17:20
@Desc    : 
"""


def recheck_retrieve_prompt(mrd, new_case):
    """
    检索检查
    :return:
    """
    prompt = f"""
&& 角色 &&
你是一名有丰富App自动化测试经验的AI助手。

&& 任务 &&
我正在根据一条手工测试用例编写自动化测试用例，我对原始用例进行了部分修改，你需要判断『修改后的用例』是否正确。

&& 手工用例 &&
## 原始手工用例：【{mrd}】
需注意，忽略原始用例中与自动化执行无关的描述或汇总性内容，专注于测试目标。

## 修改后的用例：【{new_case}】

&& 判断标准 &&
判断是否正确的标准是：修改后的用例，用例路径完整，没有改变原始测试用例的自动化路径，能够满足后续手工用例的正常生成自动化用例。"""
    output = """
&& 输出格式 &&
请直接输出一个能够被 json.loads 解析的 JSON 格式字符串，不要包含任何代码块标记：
{
    "result": bool // 一个布尔值，表示是否正确
    "reason": string // 原因
}"""
    return prompt + output