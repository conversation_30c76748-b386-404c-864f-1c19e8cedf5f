#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@File    : case_search.py
<AUTHOR> <PERSON><PERSON><PERSON><PERSON>(renya<PERSON><PERSON>@baidu.com)
@Time    : 2024/8/6 19:49
@Desc    : 
"""
from apps.app_agent.knowledge_search.auto_case_kb import AutoCaseKb
from apps.app_agent.multi_agent.pre_optmize_manual_case_agent.agent import PreOptimizeManualAgent
from apps.app_agent.utils.qamate_case_handle import QamateCaseHandle
from apps.app_agent.knowledge_search.knowledge_search_for_text_list import KnowledgeSearchForTextList
from apps.app_agent.multi_agent.preroute_agent.recheck_retrieve_agent import RecheckRetrieveAgent
from basics.util import logger
from apps.app_agent.utils.exception import AgentErrCode, AgentException


class AutoCaseRetrieveAdapter(object):
    """
    自动化用例检索适配器
    """

    def __init__(self, os_type, kb_node_id, tmp_dir='~/app_agent/tmp'):
        """
        初始化自动化知识库
        """
        self.auto_case_kb = AutoCaseKb(os_type, kb_node_id, tmp_dir)
        self.os_type = os_type
        self.os_name = "android" if os_type == 1 else "ios"

    def format_result(self, index, step_list, mrd_list, step_log_info):
        """
        组装输出结果
        """
        virtual_node_name = '->'.join([s['stepDesc'] for s in step_list])
        result = {
            "steps": step_list,
            "mrd_index": index,
            "new_mrd_list": mrd_list[index + 1:],
            "mrd_list": mrd_list,
            "knowledge_case": {
                "nodeName": virtual_node_name,
                "extra": {
                    "stepInfo": {
                        self.os_name: step_list
                    }
                }
            },
            "step_log_info": step_log_info
        }
        return result

    def opt_manual_case(self, mrd_list):
        """
        优化手工用例，多步操作拆分多节点
        """
        mrd = '->'.join(mrd_list)
        opt_agent = PreOptimizeManualAgent(mrd)
        opt_res = opt_agent.main()
        return opt_res['optimized_test_case']

    def main(self, mrd_list):
        """
        自动化用例检索
        """
        opt_mrd_list = self.opt_manual_case(mrd_list)
        index, step_list, step_log_info = self.auto_case_kb.search_auto_case_knowledge(opt_mrd_list)
        return self.format_result(index, step_list, opt_mrd_list, step_log_info)


class CaseKnowledgeRetrieve(object):
    """
    知识检索
    """

    def __init__(self, version_id, os_type, recheck=False, score_threshold=0.1):
        """

        :param version_id:
        :param os_type:
        """
        self.version_id = version_id
        self.os_type = os_type
        self.score_threshold = score_threshold
        self.recheck = recheck

        self.knowledge_case = None
        self.knowledge_description = None
        self.qamate_case_hander = QamateCaseHandle()

        self.know_search_tool = self._get_know_search_tool()

    def _get_know_search_tool(self):
        """
        :return:
        """
        self.knowledge_case = self.qamate_case_hander.get_qamate_knowledge_case_info(self.version_id)
        logger.info("共找到{}个业务知识".format(len(self.knowledge_case)))
        if len(self.knowledge_case) == 0:
            logger.error("未查询到业务知识case")

        self.knowledge_description = [item['nodeName'] for item in self.knowledge_case]

        KnowSearch = KnowledgeSearchForTextList(self.knowledge_description)
        return KnowSearch

    def retrieve(self, mrd_list):
        """
        检索
        :param mrd_list:
        :return:
        """
        retrieve_result = []

        for query in mrd_list:
            retrieve_res = self.know_search_tool.knowledge_search_for_text_list(query=query, top_k=3,
                                                                                score_threshold=self.score_threshold)

            for res in retrieve_res:
                content = {
                    "mrd_item": query,
                    "knowledge_case_des": res["document"].page_content,
                    "score": res['score']
                }
                retrieve_result.append(content)

        if len(retrieve_result) == 0:
            logger.warning("未检索可用的业务知识")
            return retrieve_result
        retrieve_result = sorted(retrieve_result, key=lambda x: x['score'], reverse=True)
        logger.info("知识检索得分最高的 mrd_item:{}, 知识case描述为：{}, "
                    "score:{}".format(retrieve_result[0]['mrd_item'],
                                      retrieve_result[0]['knowledge_case_des'],
                                      retrieve_result[0]['score']))
        return retrieve_result

    def recheck_retrieve_res(self, retrieve_res: dict, mrd_list: list):
        """
        复验检索结果
        :param retrieve_res:
        :param mrd_list:
        :return:
        """
        result = self.format_result(retrieve_res, mrd_list)
        recheck_agent = RecheckRetrieveAgent(retrieve_result=result)
        recheck_res = recheck_agent.main()

        return recheck_res['result']

    def recheck_retrieve(self, retrieve_result: list, mrd_list: list):
        """

        :param retrieve_result:
        :param mrd_list:
        :return:
        """
        if not self.recheck and len(retrieve_result) > 0:
            return retrieve_result[0]

        for retrieve_res in retrieve_result:
            if self.recheck_retrieve_res(retrieve_res, mrd_list):
                # logger.info("经过复验，最终的检索case为：{}".format(retrieve_res))
                return retrieve_res

        return None

    def format_result(self, retrieve_res: dict, mrd_list: list):
        """
        结果格式化
        :param retrieve_res:
        :param mrd_list:
        :return:
        """
        knowledge_case_info = {}
        for item in self.knowledge_case:
            if item['nodeName'] == retrieve_res['knowledge_case_des']:
                knowledge_case_info = item
                break

        index = mrd_list.index(retrieve_res['mrd_item'])
        new_mrd_list = mrd_list[index + 1:]

        os_name = "android" if self.os_type == 1 else "ios"
        result = {
            "steps": [step for step in knowledge_case_info['extra']['stepInfo'][os_name]],
            "knowledge_case": knowledge_case_info,
            "mrd_index": index,
            "new_mrd_list": new_mrd_list,
            "mrd_list": mrd_list,
            "step_log_info": ""
        }
        return result

    def main(self, mrd_list):
        """
        添加兜底
        :param mrd_list:
        :return:
        """

        # 检索
        retrieve_result = self.retrieve(mrd_list)

        # 复验
        retrieve_res = self.recheck_retrieve(retrieve_result, mrd_list)

        # 添加兜底
        if not retrieve_res:
            logger.info("触发一次兜底，检测是否有进入App知识用例")
            spare_case = "冷启动App"
            mrd_list.insert(0, spare_case)
            retrieve_result = self.retrieve([spare_case])
        if len(retrieve_result) == 0:
            raise AgentException(err_code=AgentErrCode.NO_USEFUL_PRODUCT_KNOWLEDGE)
        else:
            retrieve_res = retrieve_result[0]

        # 结构化结果
        result = self.format_result(retrieve_res, mrd_list)
        return result


if __name__ == '__main__':
    version_id = 502
    os_type = 2
    # mrd_list = ["菜单界面", "用户信息（头像+昵称）", "验证点击昵称旁边的>是否可正常交互进入个人中心"]
    mrd_list = ["首页及对话   （普通对话+语音电话对话）（97）", "对话页", "助手tab和历史tab可点击切换、可手势横划切换"]
    # from apps.app_agent.agent.tool import get_mrd_list_by_caseid
    # case_mrd_list = get_mrd_list_by_caseid(75479)[6: 10]

    # for mrd_list in case_mrd_list:
    res = CaseKnowledgeRetrieve(version_id, os_type, recheck=True).main(mrd_list)
    print(res)
