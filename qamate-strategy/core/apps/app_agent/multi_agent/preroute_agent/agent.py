#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@File    : guide_agent.py
<AUTHOR> <PERSON><PERSON><PERSON><PERSON>(renya<PERSON><PERSON>@baidu.com)
@Time    : 2024/11/15 17:20
@Desc    : 
"""

from apps.app_agent.multi_agent.base.agent import BaseChat
from apps.app_agent.multi_agent.preroute_agent.case_search import CaseKnowledgeRetrieve, AutoCaseRetrieveAdapter


class PrerouteAgent(BaseChat):
    """
    前置路由Agent, 场景进入agent
    """

    def __init__(self, mrd_list: list, knowledge_case_node_id: int, os_type: int):
        """

        :param mrd_list:
        :param knowledge_case_node_id:
        :param os_type:
        """
        super().__init__(name="preroute_agent")
        self.mrd_list = mrd_list
        self.knowledge_case_node_id = knowledge_case_node_id
        self.os_type = os_type

    def prompt_message(self):
        """

        :return:
        """
        pass

    def format_answer(self, llm_ans):
        """
        解析回答
        :param llm_ans:
        :return:
        """
        pass

    def main(self):
        """

        :return:
        """
        # 旧的case检索能力
        # case_retrieve_tool = CaseKnowledgeRetrieve(self.knowledge_case_node_id, self.os_type,
        #                                            recheck=False, score_threshold=0.2)

        # 使用新的自动化知识检索
        case_retrieve_tool = AutoCaseRetrieveAdapter(kb_node_id=self.knowledge_case_node_id, os_type=self.os_type)
        retrieve_res = case_retrieve_tool.main(self.mrd_list)
        return retrieve_res


if __name__ == '__main__':
    pass
