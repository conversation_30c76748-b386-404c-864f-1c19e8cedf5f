#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@File    : guide_agent.py
<AUTHOR> <PERSON><PERSON><PERSON><PERSON>(renyan<PERSON>@baidu.com)
@Time    : 2024/11/14 18:26
@Desc    : 
"""

from abc import ABC, abstractmethod


class BaseAgent(ABC):
    """
    基
    """
    def __init__(self, name: str):
        self.name = name

    @abstractmethod
    def main(self):
        """
        主逻辑
        :return:
        """
        pass


class BaseChat(BaseAgent):
    """
    模型对话类型
    """
    def __init__(self, name: str):
        """

        :param name:
        """
        super().__init__(name=name)

    @abstractmethod
    def prompt_message(self):
        """
        构建对话 chat_message
        :return:
        """
        pass

    @abstractmethod
    def format_answer(self, llm_res):
        """
        llm结果解析
        :return:
        """
        pass

    @abstractmethod
    def main(self):
        """
        主流程
        :return:
        """
        pass
