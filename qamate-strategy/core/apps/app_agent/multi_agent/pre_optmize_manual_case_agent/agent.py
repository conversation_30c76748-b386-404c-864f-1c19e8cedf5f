#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@File    : guide_agent.py
<AUTHOR> <PERSON><PERSON><PERSON><PERSON>(renya<PERSON><PERSON>@baidu.com)
@Time    : 2024/11/15 14:16
@Desc    : 
"""

import json

from apps.app_agent.multi_agent.base.agent import BaseChat
from apps.app_agent.multi_agent.pre_optmize_manual_case_agent.prompt import pre_optimize_prompt
from apps.app_agent.utils.YiYan import <PERSON><PERSON><PERSON>
from apps.app_agent.utils.chat_message import ChatMessage
from apps.app_agent.utils.exception import AgentErrCode, AgentException
from basics.util import logger

MAX_COUNT = 3


class PreOptimizeManualAgent(BaseChat):
    """
    手工用例优化Agent
    """

    def __init__(self, mrd: str):
        """

        :param mrd:
        """
        super().__init__(name="optimize_manual_agent")
        self.mrd = mrd

    def prompt_message(self):
        """

        :return:
        """
        prompt = pre_optimize_prompt(mrd=self.mrd)
        message = ChatMessage()
        message.add_user_message(prompt_text=prompt)
        return message

    def format_answer(self, llm_ans):
        """
        解析回答
        :param llm_ans:
        :return:
        """
        f_ans = json.loads(llm_ans)
        return f_ans

    def main(self):
        """

        :return:
        """
        # prompt
        chat_message = self.prompt_message()

        # answer
        num = 0
        while num < MAX_COUNT:
            num += 1
            try:
                llm_ans = YiYan().chat(chat_message=chat_message, response_format="json_object")
                res = self.format_answer(llm_ans)
                return res
            except Exception as e:
                logger.error(e)
                logger.error("output error, try again, left %d" % num)
        raise AgentException(err_code=AgentErrCode.LLM_UNEXPECTED_OUPUT)


if __name__ == '__main__':
    pass