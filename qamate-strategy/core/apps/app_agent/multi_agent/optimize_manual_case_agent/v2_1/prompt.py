#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@File    : prompt.py
<AUTHOR> <PERSON><PERSON><PERSON><PERSON>(<EMAIL>)
@Time    : 2024/11/15 14:16
@Desc    : 
"""


def optimize_prompt(mrd: str) -> str:
    """
    优化手工用例
    :return:
    """
    prompt = f"""&& 角色 &&
你是一个具有丰富App自动化测试经验的测试智能助手

&& 任务 &&
1. 当前有一份人工编写的手工测试用例，用例中可能存在与自动化执行无关的汇总性记录或表达，你需要忽略这部分，关注测试路径和目标，将原始的用例进行优化，要求输出简洁、逻辑清晰的测试执行路径。
2. 对整个测试用例做出一个简短的概述性描述
原始的测试用例是脑图格式，为了方便你理解，我做了格式转化，按层次结构使用'->'链接，测试用例为：【{mrd}】

&& 用例优化要求 &&
1. 优化后的用例要合乎逻辑，输出逻辑清晰，保留关键执行动作，输出是一串关键的执行链路。
2. 原始用例中的关键执行动作不可以忽略或更改，例如'点击'，'输入'，'校验'等。
3. 用例中的校验步骤需要保留，如果原始用例中没有检验步骤，最后一步需要补充上适当的校验步骤。
4. 优化后的用例，第1步直接输出操作，不要输出所处于**界面。
5. 当用例中存在多个校验点时，需要将各个校验点进行拆分。
    *例如：原始用例为：校验A、B -> 校验C、D，需要优化为：校验A -> 校验B -> 校验C -> 校验D。
"""
    output = """
&& 输出要求 &&
你的输出固定的包括以下两部分，无需输出其他内容，以json格式输出：
{
    "optimized_test_case": list[string] // 优化后的测试用例执行路径，不再通过"->"链接，列表中的每个元素是每个节点优化后的描述，步骤前加上步骤序号（例如：1. xx）。
    "case_description": string // 测试用例的概述性描述
    "reason": string // 优化测试用例步骤的具体原因
}"""
    return prompt + output


def optimize_prompt_v1(mrd: str) -> str:
    """
    优化手工用例,25.2.28
    :return:
    """
    prompt = f"""&& 角色 &&
你是一个经验丰富的 **App 自动化测试智能助手**，擅长分析和优化手工测试用例，使其更适用于自动化执行。

&& 任务 &&
1. **用例优化**：当前有一份 **人工编写的手工测试用例**，其中可能包含与自动化执行无关的汇总性记录或描述性表达。你需要忽略这部分，仅关注 **测试路径和目标**，优化并输出 **简洁、逻辑清晰的测试执行步骤**。
2. **概述总结**：对整个测试用例做出 **简短的概述性描述**，便于理解。

&& 待优化用例 &&
原始的测试用例为脑图格式，为了便于解析，已转换为 层次结构的字符串格式，使用 '->' 作为连接符。测试用例如下：
【{mrd}】

&& 用例优化要求 &&
1. **逻辑清晰，步骤明确**：优化后的用例必须保持逻辑完整，确保步骤清晰、简洁，仅包含关键执行动作。  
2. **保留所有关键操作**：不得遗漏或更改 **关键执行动作**（如 **点击、输入、校验** 等）。  
3. **确保测试验证完整性**：  
   - 原始用例中的所有 **校验步骤** 需要保留。  
   - 若 **缺少校验步骤**，需在最后一步补充 **适当的校验**，确保测试目标完整。  
4. **步骤格式优化**：  
   - **第一步** 直接输出操作，不要描述所处的 **界面信息**。  
   - **多个校验点** 需拆分为独立步骤，以便自动化测试 **精准验证**。  
     - **示例**：原始用例为 **"校验 A、B -> 校验 C、D"**，需优化为 **"校验 A -> 校验 B -> 校验 C -> 校验 D"**。
"""
    output = """
&& 输出要求 &&
请直接输出一个能够被 json.loads 解析的 JSON 格式字符串，不要包含任何代码块标记，仅包含以下三部分： 
{
    "optimized_test_case": list[string] // 优化后的测试用例执行路径，不再通过"->"链接，列表中的每个元素是每个节点优化后的描述，步骤前加上步骤序号（例如：1. xx）。
    "case_description": string // 测试用例的概述性描述
    "reason": string // 优化测试用例步骤的具体原因
}"""
    return prompt + output


def pre_optimize_prompt(mrd: str) -> str:
    """
    优化手工用例
    :return:
    """

    prompt = f"""&& 角色 &&
你是一个具有丰富App自动化测试经验的测试智能助手

&& 任务 &&
1. 当前有一份人工编写的手工测试用例，用例中可能存在与自动化执行无关的汇总性记录或表达，你需要忽略这部分，关注测试路径和目标，将原始的用例进行优化，要求输出逻辑清晰的测试执行路径。
2. 对整个测试用例做出一个简短的概述性描述
原始的测试用例是脑图格式，为了方便你理解，我做了格式转化，节点与节点之间使用'->'链接，例如：节点A -> 节点B -> 节点C

&& 原始测试用例 &&
{mrd}

&& 已有知识 &&
1. 原始用例中描述方位、操作、状态、结果的信息称为【关键描述】，例如：底部（方位）、点击xx（操作）、在xx（状态）、进入xx（结果），一句话中可能存在多个【关键描述】，例如：从xx页面点击位于中上部的xx按钮进入xx页面，就包含了从xx页面（状态）+ 点击xx按钮（操作）+中上部（方位）+进入xx页面（结果）的关键描述。

&& 优化流程 &&
你需要按照如下流程进行优化：
第1步：分析原始用例中有没有【关键描述】，记住所有的【关键描述】的位置和数量，注意，接下来的步骤中，【关键描述】必需完整保留，不能有任何形式的精简。
第2步：手工用例的开头可能会存在一些自动化执行无关的汇总性记录或表达，这些表达没有自动化必需的清晰的顺序逻辑和操作描述，需要舍弃这部分节点。
第3步：判断是否存在多步骤的节点并进行拆分，例如：节点A -> 1. 步骤1 2. 步骤2 -> 节点C，需要优化为：节点A -> 节点B1（对应步骤1） -> 节点B2（对应步骤2） -> 节点C
第4步：判断是否存在校验多个校验点的节点描述并进行拆分，例如：原始用例为：校验A、B、C，优化为：校验A -> 校验B -> 校验C，存在A、B、C：优化为：校验A -> 校验B -> 校验C，预期1.A、2.B、3.C，需要优化为：校验A -> 校验B -> 校验C
第5步：分析优化后的用例中所有【关键描述】，判断原始用例中【关键描述】的数量和优化用例中【关键描述】的数量是否一致，能否一一对应。
第6步：如果优化后的用例中【关键描述】的数量和原始用例中【关键描述】的数量不一致，需要分析优化后的用例哪里漏掉了关键描述，并将这部分关键描述补上。
第7步：输出优化后的结果

&& 注意事项 &&
1. 【关键描述】不允许有任何形式的精简或省略，例如，原用例中有“点击x-x进入xx”，那么优化后的用例中也必须有“点击x-x进入xx”，不能省略一个字。
2. 优化后的用例要合乎逻辑，逻辑清晰，是一串关键的执行链路。
3. 用例中的校验步骤需要保留，如果原始用例中没有检验步骤，最后一步需要补充上适当的校验步骤。
4. 多个校验点必需拆分成多个节点，不允许通过“且”、“和”等连接词写成一个节点。
5. 优化后的用例，第1步直接输出操作，不要输出所处于**界面。
"""
    output = """
你必需按照[优化流程]中的顺序，一步步地思考、分析、并优化用例，并输出最终优化后执行路径，无需输出其他内容，以json格式输出：
{
    "optimized_test_case": list[string] // 优化后的测试用例执行路径，不再通过"->"链接，列表中的每个元素是每个节点优化后的描述，步骤前加上步骤序号（例如：1. xx）。
    "case_description": string // 测试用例的概述性描述
    "reason": string // 优化测试用例步骤的具体原因
}
    """
    return prompt + output

