#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@File    : guide_agent.py
<AUTHOR> <PERSON><PERSON><PERSON><PERSON>(renya<PERSON><PERSON>@baidu.com)
@Time    : 2024/11/15 14:16
@Desc    : 
"""

import json

from apps.app_agent.multi_agent.base.agent import BaseChat
from apps.app_agent.utils.chat_message import ChatMessage
from apps.app_agent.utils.big_model import BigModel
from apps.app_agent.utils.YiYan import <PERSON><PERSON><PERSON>
from apps.app_agent.multi_agent.optimize_manual_case_agent.v2_1.prompt import optimize_prompt_v1
from basics.util import logger
from apps.app_agent.utils.exception import AgentErrCode, AgentException

MAX_COUNT = 3


class OptimizeManualAgent(BaseChat):
    """
    手工用例优化Agent
    """

    def __init__(self, mrd: str, app_info: dict = {}, **kwargs):
        """

        :param mrd:
        """
        super().__init__(name="optimize_manual_agent")
        self.mrd = mrd
        self.app_info = app_info
        self.summary = kwargs.get('summary', '')

    def prompt_message(self):
        """

        :return:
        """
        prompt = optimize_prompt_v1(mrd=self.mrd)
        logger.info("optimize_prompt:\n {}".format(prompt))
        message = ChatMessage()
        message.add_user_message(prompt_text=prompt)
        return message

    def format_answer(self, llm_ans):
        """
        解析回答
        :param llm_ans:
        :return:
        """
        f_ans = json.loads(llm_ans)
        return f_ans

    def main(self):
        """

        :return:
        """
        # prompt
        chat_message = self.prompt_message()

        # answer
        num = 0
        while num < MAX_COUNT:
            num += 1
            try:
                # llm_ans = BigModel(model_name="ernie-4").chat(chat_message=chat_message,
                #                                               response_format="json_object",
                #                                               temperature=0.1)
                llm_ans = YiYan(model_name="ernie-4").chat(chat_message=chat_message,
                                                           response_format="json_object",
                                                           temperature=0.1)

                res = self.format_answer(llm_ans)
                return res
            except Exception as e:
                logger.error(e)
                logger.error("output error, try again, left %d" % num)
        raise AgentException(err_code=AgentErrCode.LLM_UNEXPECTED_OUPUT)


if __name__ == '__main__':
    mrd_list = [
        "进入我的页面->点击创作，弹出你可以发布XX的页面→查看页面显示>预期：页面有视频发布入口",
        "发送query：菠萝怎么切->点击第一个视频，进入播放器->点击文小言入口调起半屏对话页->点击视频区域->校验视频和音频是否暂停，是否展示播放按钮",
        "点击对话-顶部对话->点击PPT大纲生成智能体->点击输入框，拉起键盘->输入你在干嘛->点击顶部返回上一页面按钮->再次点击PPT大纲生成智能体->校验：离开当前对话页面，不中断对话生成"
    ]

    mrd_s = mrd_list[2]
    oa = OptimizeManualAgent(mrd=mrd_s)
    a = oa.main()
