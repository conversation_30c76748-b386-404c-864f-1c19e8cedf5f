#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@Desc    :   
@File    :   ack_prompt.py
@Time    :   2024-11-27
<AUTHOR>   xuz<PERSON><EMAIL>
"""

def get_summarize_prompt(query):
    """
    生成单点描述的prompt
    """
    query_str = "\n".join(query)
    
    prompt = \
f"""## 背景 ##
你现在是一个有丰富测试经验的移动端测试经验的测试工程师，现在会给你一些『测试步骤』，希望你根据这些『测试步骤』和『测试用例规范』，参考『示例』，写出一个完整的测试用例。

## 测试步骤 ##
{query_str}

## 测试用例规范 ##
测试用例包含两类描述：节点描述和步骤描述。步骤描述就是输入的测试步骤，节点描述是将若干个测试步骤进行总结，用总结、干练的短语或者语句，描述出这些测试步骤索要达成的测试目的。一个测试用例包含多个节点描述和步骤描述。步骤描述从属于节点描述。节点描述的内容根据它所属的步骤描述总结得到。请特别注意，如果测试步骤的描述中存在专业名词、功能，请在生成节点总结的时候，重点关注这些专业名词、功能。

## 示例 ##
如下是一个优秀的测试用例：

### 示例输入的测试步骤 ###
点击首页的听书tab
点击一本书籍
正确展示简介内容
简介文本多行的话，最多只展示三行
『更多』按钮存在
点击更多按钮，进入更多页面
展开显示完整的简介内容

### 示例的输出测试用例 ###
节点1: 听书详情页
节点1的步骤1: 冷启APP
节点1的步骤2: 点击首页的听书tab
节点1的步骤3: 点击一本书籍
节点2: 简介功能校验
节点2的步骤1: 正确展示简介内容
节点2的步骤2: 简介文本多行的话，最多只展示三行
节点3: 点击更多功能
节点3的步骤1: 『更多』按钮存在
节点3的步骤2: 点击更多按钮，进入更多页面
节点3的步骤3: 展开显示完整的简介内容

## 输出要求 ##
你的输出固定的包括以下几个部分，格式如下：
### 思考 ###
基于『示例』所提供的输出格式和总结方式，结合『测试用例规范』，理解『测试步骤』的内容，思考如何写出一个完整的测试用例。
### 测试用例 ###
输出完整的测试用例"""
    return prompt
