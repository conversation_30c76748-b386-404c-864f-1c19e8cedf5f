#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@Desc    :   
@File    :   ack_agent.py
@Time    :   2024-11-27
<AUTHOR>   x<PERSON><PERSON><PERSON>@baidu.com
"""
import sys
sys.path.append("/Users/<USER>/Workspace/Project/lazyOne/qamate-strategy/core")

import os
import datetime
import requests
import cv2

from apps.app_agent.multi_agent.standard_case_agent.sc_prompt import *
from apps.app_agent.utils.ChatGPT import ChatGPT
from apps.app_agent.conf.contants import GENERATE_API_KEY
from apps.app_agent.utils.chat_message import ChatMessage


class SCAgent(object):
    """
    agent
    """
    def __init__(self):
        """
        初始化
        """
        self.__ai_agent = ChatGPT(api_key=GENERATE_API_KEY)

    def main(self, query):
        """
        主流程 
        """
        prompt = get_summarize_prompt(query)
        msg = ChatMessage()
        msg.add_user_message(prompt)
        llm_ans = self.__ai_agent.chat(msg)
        print(llm_ans)
    

if __name__ == '__main__':
    sc_agent = SCAgent()
    query = [
        "1. 进入社区玩法页面",
        "2. 选择智能体社区评论互动",
        "3. 点击输入框调起键盘",
        "4. 确认输入框左侧展示@",
        "5. 输入评论内容不点击@",
        "6. 确认输入框不展示@xx智能体",
        "7. 发送评论",
        "8. 校验评论为普通评论"
    ]
    sc_agent.main(query)
