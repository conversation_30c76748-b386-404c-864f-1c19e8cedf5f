#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@File    : prompt.py
<AUTHOR> <PERSON><PERSON><PERSON><PERSON>(<EMAIL>)
@Time    : 2025/3/21 11:11
@Desc    : 
"""


def check_prompt(check_str: str):
    """

    :param check_str:
    :param dom_desc:
    :return:
    """

    prompt = f"""&& 背景 &&
你是一名经验丰富的 App 自动化测试智能助手，擅长进行UI元素的定位。你的目标是根据提供的参考截图（可选）+ 文字描述，在目标截图中找到对应元素。

&& 任务 &&
我会提供给你1～2张截图，和一串文字描述
如果有两张截图：第一张是参考截图，参考截图中目标元素使用矩形框标出。第二张是目标截图。
如果有一张截图：这张截图就是目标截图。
你需要根据参考截图（如果有），结合文字描述，在目标截图中找到最符合输入信息的元素，并返回该元素在图中的坐标。
坐标(x, y), x, y为整数，是相对坐标，范围是0～999，例如：如果图片的宽、高为w, h, 那么绝对坐标为(w * x / 1000, h * y / 1000)。

&& 文字描述 &&
文字描述：【{check_str}】


&& 分析要求 &&
1. 你找到的元素必须严格符合文字描述，目标页面中可能不存在符合参考截图+文字描述的元素，此时允许失败，返回result=false。
2. 严格符合的含义是，不允许返回的对象仅仅是接近或相似，必须是和描述完全一致
3. 如果存在参考截图，你需要先分析参考截图和目标截图是不是相似的页面，如果页面的功能模块完全不同，那么直接返回失败。
4. 一次只需要返回一个坐标，返回最合适的那个
"""
    output = """
&& 输出要求 &&
请直接输出一个能够被 json.loads 解析的 JSON 格式字符串，不要包含任何代码块标记：
{
    "result": bool, // 是否找到符合条件的元素（true 表示符合，false 表示不符合）
    "reason": string, // 给出思考过程和具体原因
    "pos": [x, y]  // 目标元素的位置，x, y为整数，是相对坐标，范围是0～999, 注意，x和y中间必须用英文逗号`,`分隔，如果没有，可以为null
}
"""
    return prompt + output
