#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@File    : agent.py
<AUTHOR> <PERSON><PERSON><PERSON><PERSON>(renya<PERSON><PERSON>@baidu.com)
@Time    : 2025/3/21 11:10
@Desc    : 
"""
# !/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import math
import os
import shutil
import threading
import time
import uuid

import cv2
import requests

from apps.app_agent.multi_agent.ai_replay_agent.v1_0.prompt import check_prompt
from apps.app_agent.multi_agent.base.agent import BaseChat
from apps.app_agent.utils.big_model import QAMateLLM
from apps.app_agent.utils.chat_message import ChatMessage
from apps.app_agent.utils.exception import AgentErrCode, AgentException
from basics.util import logger
from basics.util.bos_utils import BosUtil, LLM_BOS_CONF, CONTENT_JSON
from basics.util.config import CACHE_DIR

MAX_COUNT = 1
MAX_RETRY = 3


class AiLocateAgent(BaseChat):
    """
    大模型校验Agent
    """

    def __init__(self, desc: str, record_img_path: str, replay_img_path: str, rect: dict = None,
                 mode_name="doubao-1.5-thinking-vision-pro-250428", ctx=None):
        """
        """
        super().__init__(name="ai_replay_agent")
        self.desc = desc
        self.record_img_path = record_img_path
        self.replay_img_path = replay_img_path
        self.model_name = mode_name

        self.temp_dir = os.path.join(CACHE_DIR, str(uuid.uuid4()))
        # 图片解析
        self.task_id = str(uuid.uuid4().hex)
        self.task_tag = 'ai_locate'
        self.rect = rect
        self.bos_client = BosUtil(**LLM_BOS_CONF)
        self.final_res = None
        self.proc_record_img_path = None
        self.log = None
        self.ctx = ctx

    def _mk_dir(self):
        """
        :return:
        """
        if not os.path.exists(self.temp_dir):
            os.mkdir(self.temp_dir)
        logger.info("temp_dir: {}".format(self.temp_dir))

    def _clear_dir(self):
        """
        清理临时目录
        """
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)

    def upload_llm_log(self):
        """
        上传大模型交互日志
        """

        def upload_all_llm_log():
            """
            单独的线程内上传大模型交互日志
            """
            try:
                msg, llm_ans = self.log
                # print(msg, llm_ans)
                msg.append({
                    "role": "assistant",
                    "content": [
                        {
                            "type": "text",
                            "text": llm_ans
                        }
                    ],
                })
                chat_str = json.dumps(msg, ensure_ascii=False)
                char_url = self.bos_client.upload_string(chat_str, key=f"llm/log/{self.task_id}/chat.json",
                                                         content_type=CONTENT_JSON)
                suffix = os.path.splitext(self.record_img_path)[-1]
                record_url = self.bos_client.upload_image(self.record_img_path,
                                                          key=f"llm/log/{self.task_id}/record{suffix}")
                replay_url = self.bos_client.upload_image(self.replay_img_path,
                                                          key=f"llm/log/{self.task_id}/replay{suffix}")
                extra = {
                    'origin_img': record_url,
                    'grid_img': replay_url,
                    'llm_ans': json.loads(llm_ans),
                    'check_str': self.desc
                }
                if self.final_res is not None:
                    extra['final_res'] = self.final_res
                extra_str = json.dumps(extra, ensure_ascii=False)
                extra_url = self.bos_client.upload_string(extra_str, key=f"llm/log/{self.task_id}/extra.json",
                                                          content_type=CONTENT_JSON)
                log_list = []
                log_list.append({
                    'taskId': self.task_id,
                    'taskIndex': 0,
                    'taskTag': self.task_tag,
                    'model': self.model_name,
                    'chatUrl': char_url,
                    'extraUrl': extra_url
                })

                url = 'https://qamate.baidu-int.com/core/llm/log/create'
                body = {
                    'logList': log_list
                }
                # print(json.dumps(body, ensure_ascii=False, indent=2))
                headers = {
                    "Content-Type": "application/json",
                    "QAMate-ModuleId": "0",
                    "QAMate-Token": "super-9a796933bf024ebfa0420dce6f864a51"
                }
                r = requests.post(url, json=body, headers=headers)
                # print(self.task_id)
                logger.info(f'task: {self.task_id}, upload result: {r.json()}')
            except Exception as e:
                logger.error(e)
            finally:
                self._clear_dir()

        threading.Thread(target=upload_all_llm_log).start()

    def prompt_message(self):
        """
        :return:
        """
        prompt = check_prompt(check_str=self.desc)
        # logger.info("Check Prompt: {}".format(prompt))
        message = ChatMessage()
        if self.rect is not None:
            message.add_user_message(prompt_text=prompt, image_paths=[self.proc_record_img_path, self.replay_img_path])
        else:
            message.add_user_message(prompt_text=prompt, image_paths=[self.replay_img_path])
        logger.info("img_paths: {}".format(self.proc_record_img_path))
        return message

    def format_answer(self, llm_ans):
        """
        解析回答
        :param llm_ans:
        :return:
        """
        f_ans = json.loads(llm_ans)
        return f_ans

    def locate(self):
        """

        :return:
        """
        # prompt
        chat_message = self.prompt_message()

        # answer
        num = 0
        while num < MAX_RETRY:
            num += 1
            try:
                llm_ans = (QAMateLLM(model_name=self.model_name, extra={
                    'usage': 'ai_replay'
                }, ctx=self.ctx).chat(chat_message=chat_message, response_format="json_object"))
                # llm_ans = BigModel(model_name=self.model_name).chat(chat_message=chat_message,
                #                                                     response_format="json_object")
                res = self.format_answer(llm_ans)
                chat_message.add_assistant_message(llm_ans)
                self.log = (chat_message, llm_ans)
                return res
            except Exception as e:
                logger.error(e)
                logger.error("output error, try again, left %d" % num)
                chat_message.add_user_message("你生成的json格式有错误，不符合要求，报错为{}, 修正错误，然后重新生成".format(e))

        raise AgentException(err_code=AgentErrCode.LLM_UNEXPECTED_OUPUT)

    def vote_for_res(self):
        """
        三次尝试 + 聚类投票，解决大模型输出坐标不稳定的问题
        """
        results = []
        def locate_for_res():
            """
            把结果临时村到一个数组
            """
            try:
                res = self.locate()
            except Exception as e:
                res = {
                    'result': False,
                    'reason': str(e),
                    'pos': None
                }
            results.append(res)

        def in_group(r, group):
            """
            """
            if r['result'] is False:
                return group[0]['result'] is False
            elif group[0]['result'] is False:
                return False
            else:
                pos = r['pos']
                gpos = group[0]['pos']
                dis = math.sqrt((pos[0] - gpos[0]) ** 2 + (pos[1] - gpos[1]) ** 2)
                logger.info("distance is {}".format(dis))
                return dis < 80

        threads = []

        for i in range(3):
            t = threading.Thread(target=locate_for_res)
            t.start()
            threads.append(t)
        for t in threads:
            t.join()

        groups = []
        for r in results:
            is_in_group = False
            for g in groups:
                if in_group(r, g):
                    g.append(r)
                    is_in_group = True
                    break
            if is_in_group is False:
                groups.append([r])

        logger.info("groups {}".format(groups))

        max_len_g = None
        max_len = 0
        for g in groups:
            if len(g) > max_len:
                max_len = len(g)
                max_len_g = g
        return max_len_g[0]



    def proc_img(self):
        """
        对输入图片画上矩形框（如果有）
        """
        try:
            if self.rect is not None:
                img_name = os.path.basename(self.record_img_path)
                spl = os.path.splitext(img_name)
                new_name = spl[0] + '_loc_proc' + spl[-1]
                self.proc_record_img_path = os.path.join(self.temp_dir, new_name)

                imgcv = cv2.imread(self.record_img_path)
                x, y, w, h = self.rect['x'], self.rect['y'], self.rect['w'], self.rect['h']
                x1, y1, x2, y2 = x, y, x + w, y + h
                imgcv = cv2.rectangle(imgcv, pt1=(x1, y1), pt2=(x2, y2), color=(0, 0, 255), thickness=8)
                cv2.imwrite(self.proc_record_img_path, imgcv)
        except Exception as e:
            logger.error("绘制目标矩形失败, {}".format(e))

    def _get_abs_pos(self, pos):
        x, y = pos
        img = cv2.imread(self.record_img_path)
        h, w = img.shape[:2]
        ax, ay = int(w * x / 1000), int(h * y / 1000)
        return {
            "x": ax,
            "y": ay
        }

    def main(self):
        """
        多线程，多数投票的方法
        :return:
        """
        self._mk_dir()
        self.proc_img()

        t1 = int(time.time() * 1000)
        # res = self.locate()
        res = self.vote_for_res()
        t2 = int(time.time() * 1000)
        final_reason = res["reason"]
        if res['result'] is True:
            final_pos = self._get_abs_pos(res["pos"])
        else:
            final_pos = None

        final_res = {
            "result": res['result'],
            "reason": final_reason,
            "pos": final_pos,
            "cost": t2 - t1,
        }
        logger.info("判断可生成最终结果为：{}".format(final_res))
        self.final_res = final_res
        self.upload_llm_log()
        return final_res


if __name__ == '__main__':
    import sys

    # sys.path.append('/')
    import basics.config as config

    config.load_config('/Users/<USER>/batdom_vers/10.1.0/core/profile.json')
    s = "ppt内部的矩形框"
    img = "/Users/<USER>/Downloads/IMG_3F8702685F35-1.jpeg"
    r = AiLocateAgent(desc=s, record_img_path=img, replay_img_path=img).main()
    print(r)
    imgcv = cv2.imread(img)
    x, y = r['pos']['x'], r['pos']['y']
    imgcv[y - 2: y + 2, :, 0] = 0
    imgcv[y - 2: y + 2, :, 1] = 0
    imgcv[y - 2: y + 2, :, 2] = 255
    imgcv[:, x - 2: x + 2, 0] = 0
    imgcv[:, x - 2: x + 2, 1] = 0
    imgcv[:, x - 2: x + 2, 2] = 255
    cv2.imshow("test", imgcv)
    cv2.waitKey(0)
    # time.sleep(20)
#
# if __name__ == '__main__':
#     bos_client = BosUtil(**LLM_BOS_CONF)
#     url = bos_client.upload_file(file_path="/Users/<USER>/lab/评测集/evl-3.zip", key="test/evl-3.zip")
#     print(url)
