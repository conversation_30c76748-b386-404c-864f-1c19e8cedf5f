#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@File    : guide_agent.py
<AUTHOR> <PERSON><PERSON><PERSON><PERSON>(renya<PERSON><PERSON>@baidu.com)
@Time    : 2024/11/14 14:46
@Desc    :
"""

import json
import os.path

from apps.app_agent.multi_agent.auto_case_translate_agent.v1.prompt import translate_prompt, system_prompt, \
    button_switch_prompt
from apps.app_agent.multi_agent.base.agent import BaseChat
from apps.app_agent.multi_agent.page_understand_agent.image_process import get_image_with_grid
from apps.app_agent.multi_agent.wait_time_predict_agent.agent import WaitTimePredictAgent
from apps.app_agent.utils.big_model import BigModel
from apps.app_agent.utils.chat_message import ChatMessage
from basics.util import logger

MAX_COUNT = 3


class AutoCaseOptimizeAgent(BaseChat):
    """
    自动化用例优化agent
    """

    def __init__(self, grid_img_path, id_ele_map, dom_desc, target_ele_id, step_desc, scene_knowledge=[]):
        """
        :param grid_img_path: 打标后的截图
        :param id_ele_map: id -> 组件信息 的映射
        :param dom_desc: dom 结构描述
        :param target_ele_id: 目标元素id
        :param step_desc: 步骤描述
        """
        super().__init__(name="auto_case_optimize_agent")
        self.ele_list = []
        self.grid_img_path = grid_img_path
        self.id_ele_map = id_ele_map
        self.dom_desc = dom_desc
        self.target_ele_id = target_ele_id
        self.step_desc = step_desc
        self.scene_knowledge = scene_knowledge

    def prompt_message(self):
        """
        :return:
        """
        # 构建prompt
        tran_prompt = translate_prompt(self.dom_desc, self.target_ele_id, self.step_desc, self.scene_knowledge)
        logger.info("guide_action_prompt: {}".format(tran_prompt))

        tran_system = system_prompt()
        chat_message = ChatMessage(system_message=tran_system)
        chat_message.add_user_message(prompt_text=tran_prompt, image_paths=[self.grid_img_path], img_quality="auto")
        return chat_message

    def format_answer(self, llm_ans):
        """
        解析回答
        :param llm_ans:
        :return:
        """
        f_ans = json.loads(llm_ans)
        return f_ans

    def llm_optimize(self):
        """
        基于大模型的优化
        """
        # 构建prompt
        chat_message = self.prompt_message()

        # 生成
        # llm_ans = ChatGPT(api_key=GENERATE_API_KEY).chat(chat_message=chat_message, temperature=0.2,
        #                                                  response_format='json_object')
        llm_ans = BigModel().chat(chat_message=chat_message, temperature=0.2, response_format='json_object')

        # 结果解析
        f_ans = self.format_answer(llm_ans=llm_ans)

        chat_message.add_assistant_message(llm_ans)
        # res = {
        #     "agent_res": f_ans,
        #     "message": chat_message
        # }
        return f_ans['eleList']

    def button_switch_optimize(self):
        """
        针对button_switch_on/off的专项优化
        选择【左相邻】的【文本】作为辅助控件
        【相邻】 = 【同一行】 && 【在左边】 && 【没有更近的控件】
        """
        button_switch = self.id_ele_map[self.target_ele_id]

        res_ele_list = []
        ele = self.id_ele_map[self.target_ele_id]
        ele_text = ele.get('ext', {}).get('text', '')
        res_ele_list.append({
            'eleId': self.target_ele_id,
            'featureFlag': True,
            'matchType': 2,
            'matchText': ele_text,
            'reason': '保留目标控件'
        })

        bs_x1, bs_y1 = button_switch['rect']['x'], button_switch['rect']['y']
        bs_x2, bs_y2 = bs_x1 + button_switch['rect']['w'], bs_y1 + button_switch['rect']['h']
        bs_x_center = (bs_x1 + bs_x2) / 2
        bs_y_center = (bs_y1 + bs_y2) / 2

        candidates = []
        # 获取与button_switch同一行且在button_switch左边的文本
        for ele_id, ele in self.id_ele_map.items():
            if ele['type'] != 'Text':
                continue
            ele_x1, ele_y1 = ele['rect']['x'], ele['rect']['y']
            ele_x2, ele_y2 = ele_x1 + ele['rect']['w'], ele_y1 + ele['rect']['h']
            ele_x_center = (ele_x1 + ele_x2) / 2
            ele_y_center = (ele_y1 + ele_y2) / 2
            y_center_delta = abs(bs_y_center - ele_y_center)
            if y_center_delta < button_switch['rect']['h'] * 0.5 and ele_x_center < bs_x_center:
                candidates.append(ele_id)
        if len(candidates) == 0:
            # 如果没有找到辅助控件，则直接返回单控件
            return res_ele_list

        # 按x坐标排序，选择最右边的文本作为辅助控件
        candidates.sort(key=lambda x: self.id_ele_map[x]['rect']['x'])
        support_ele_id = candidates[-1]
        support_text_ele = self.id_ele_map[support_ele_id]
        res_ele_list.append({
            'eleId': support_ele_id,
            'featureFlag': True,
            'matchType': 2,
            'matchText': support_text_ele['ext']['text'],
            'reason': '开关按钮辅助文本'
        })
        return res_ele_list

    def route(self):
        """
        路由到合适的优化器进行优化
        """
        ele = self.id_ele_map[self.target_ele_id]
        ele_type = ele['type']
        ele_name = ele.get('ext', {}).get('name', '')
        if ele_type == 'Icon' and 'button_switch' in ele_name:
            # 选择的控件是button_switch_on/off, 走特殊逻辑
            return 'button_switch'
        elif ele_type == 'Text' or ele_name in ('button', 'arrow_right', 'picture'):
            # 是否需要大模型优化，需要大模型优化的选择控件类型包括：
            # 1.文本
            # 2. button组件
            # 3. arrow_right图标
            # 4. picture组件
            return 'llm'
        else:
            return 'not_optimize'

    def not_optimize(self):
        """
        不作任何优化，直接返回单控件
        """
        ele = self.id_ele_map[self.target_ele_id]
        ele_text = ele.get('ext', {}).get('text', '')
        return [{
            'eleId': self.target_ele_id,
            'featureFlag': True,
            'matchType': 2,
            'matchText': ele_text,
            'reason': '不需要优化'
        }]

    def main(self):
        """

        :return:
        """
        opt = self.route()
        if opt == 'llm':
            ele_list = self.llm_optimize()
        elif opt == 'button_switch':
            ele_list = self.button_switch_optimize()
        else:
            ele_list = self.not_optimize()
        return ele_list


class BatDomCaseTranslateAgent:
    """
    batdom自动化步骤智能翻译agent
    """

    def __init__(self, grid_img_path, batdom_record, step_desc, mrd_item, action_res, scene_knowledge=[]):
        self.step_desc = step_desc
        self.grid_img_path = grid_img_path
        self.dom_desc = batdom_record['dom_desc']
        self.id_ele_map = batdom_record['id_ele_map']
        self.dom = batdom_record['dom']
        self.dom_info = batdom_record['info']
        self.device_info = batdom_record['deviceInfo']
        self.action_res = action_res
        self.target_ele_id = self.action_res['element_id']
        self.mrd_item = mrd_item
        self.ele_list = None
        self.action_info = None
        self.skip_on_fail = False
        self.scene_knowledge = scene_knowledge

        # if self.target_ele_id < 0:
        #     raise AgentException(AgentErrCode.PARAM_ERROR, 'target_ele_id is invalid')

    def _get_swipe_step(self):
        # swipe 特殊处理
        direction = -1
        if self.action_res["direction"] == "up":
            direction = 1
        elif self.action_res["direction"] == "down":
            direction = 2
        elif self.action_res["direction"] == "right":
            direction = 3
        elif self.action_res["direction"] == "left":
            direction = 4
        self.action_info["type"] = 1
        self.action_info["params"] = {
            "type": "swipe",
            "params": {
                "direction": direction,
                "times": 1,
                "interval": 1,
                "duration": 500
            }
        }
        action_type = self.action_info["params"]["type"]
        return self.action_info, action_type

    def _get_absence_step(self):
        # 不存在 特殊处理
        self.action_info["type"] = 3
        self.action_info["params"] = {
            "type": "review",
            "params": {
                "img": self.device_info["screenshot"],
                "rect": {
                    "x": 0,
                    "y": 0,
                    "width": 0,
                    "height": 0
                },
                "width": self.device_info["screenSize"]["width"],
                "height": self.device_info["screenSize"]["height"]
            }
        }
        action_type = self.action_info["params"]["type"]
        return self.action_info, action_type

    def _get_empty_node_nope(self):
        # 存在性校验，但是不存在具体元素
        self.action_info["type"] = 3
        self.action_info["params"] = {
            "type": "review",
            "params": {
                "img": self.device_info["screenshot"],
                "rect": {
                    "x": 0,
                    "y": 0,
                    "width": 0,
                    "height": 0
                },
                "width": self.device_info["screenSize"]["width"],
                "height": self.device_info["screenSize"]["height"]
            }
        }
        action_type = self.action_info["params"]["type"]
        return self.action_info, action_type

    def _get_wait_step(self):
        self.action_info["type"] = 1
        self.action_info["params"] = {
            "type": "wait",
            "params": {
                "seconds": self.action_res['time']
            }
        }
        action_type = self.action_info["params"]["type"]
        return self.action_info, action_type

    def search_node_in_tree(self, element_id):
        """
        通过element_id查找节点
        """
        path_summary = [0]
        goal_node = self.id_ele_map[element_id]
        path_summary.append(goal_node["debug"]["id"])
        if goal_node["type"] == "TextArea":
            goal_node = goal_node["children"][0]
            path_summary.append(goal_node["debug"]["id"])

        logger.info("goal_node: {}".format(goal_node))
        return goal_node, path_summary

    def button_switch_process(self, find_node):
        """
        button_switch特殊处理
        1. 使用大模型判断操作描述是打开还是关闭
        2. 如果是打开，则把ext.name设置为button_switch_off
        3. 如果是关闭，则把ext.name设置为button_switch_on
        4. 在全局设置里，把失败可跳过开关打开
        """
        prompt = button_switch_prompt(self.step_desc)
        message = ChatMessage()
        message.add_user_message(prompt_text=prompt)

        llm_ans_text = ''
        llm_ans = None
        for i in range(3):
            try:
                # llm_ans_text = YiYan().chat(chat_message=message, response_format="json_object")
                llm_ans_test = BigModel(model_name="ernie-4").chat(chat_message=message,
                                                                   response_format="json_object")
                llm_ans = json.loads(llm_ans_text)
                if 'isOpen' in llm_ans and type(llm_ans['isOpen']) is bool:
                    break
            except Exception as e:
                logger.error(f"llm_ans: {llm_ans_text}")
                llm_ans = {
                    "isOpen": True,
                    "isVerify": True,
                    "reason": "发生错误，兜底"
                }
        if llm_ans['isOpen']:
            new_name = "button_switch_off"
        else:
            new_name = "button_switch_on"

        if llm_ans['isVerify']:
            # 如果是校验操作，则不可跳过
            self.skip_on_fail = False
        else:
            self.skip_on_fail = True
        find_node["detailFeature"]["ext"]["name"] = new_name

    def get_find_info(self):
        """
        生成findInfo
        """
        if len(self.ele_list) > 1:
            tag = ["visual", "multiple"]
        else:
            tag = ["visual", "single"]

        find_info = {
            "screenCount": 1,
            "findType": 0,
            "findNode": [],
            "modelType": 2,
            "chosenTag": tag,
            "findPath": [],
            "mergeOCR": False
        }

        path_summary = []
        for ele_param in self.ele_list:
            node, path_summary = self.search_node_in_tree(
                ele_param["eleId"]
            )
            find_node = {
                "detailFeature": {
                    "ext": node["ext"],
                    "rect": node["rect"],
                    "matchType": ele_param["matchType"],
                    "type": node["type"]
                },
                "featureFlag": ele_param["featureFlag"],
                "actionNode": ele_param["eleId"] == self.target_ele_id,
                "id": node["debug"]["id"]
            }

            if node["type"] == 'Text' and ele_param["matchType"] in (1, 2):
                param_text = ele_param["matchText"]
                node_text = node["ext"]["text"]
                if param_text not in node_text:
                    # raise AgentException(AgentErrCode.PARAM_ERROR, "模型给出的文本有错误")
                    logger.warning(f"{param_text}不在{node_text}中，模型给出的文本不符合要求，直接使用{node_text}")
                    find_node["detailFeature"]["ext"]["text"] = node_text
                    if len(node_text) > 7:
                        find_node["detailFeature"]["ext"]["text"] = node_text[:7]
                elif len(param_text) > 7:
                    # 如果文本长度大于7，则只保留前7个字符
                    find_node["detailFeature"]["ext"]["text"] = param_text[:7]
                else:
                    find_node["detailFeature"]["ext"]["text"] = param_text

            if find_node['detailFeature']['ext'].get('name', '').startswith('button_switch'):
                # button_switch特殊处理
                self.button_switch_process(find_node)
            find_info["findNode"].append(find_node)
        return find_info, path_summary

    def map_params_action_info(self):
        """
        将action_res转换为步骤中的action_info
        """
        action_info = {}
        if self.action_res["action"] == "tap":
            action_info = {
                "type": "tap",
                "params": {}
            }
        elif self.action_res["action"] == "input":
            action_info = {
                "type": "input",
                "params": {
                    "text": self.action_res["input_text"]
                }
            }
        elif self.action_res["action"] == "long_press":
            action_info = {
                "type": "tap",
                "params": {
                    "duration": 2000
                }
            }
        elif self.action_res["action"] == "long_press":
            action_info = {
                "type": "tap",
                "params": {
                    "duration": 2000
                }
            }
        elif self.action_res["action"] == "nope":
            action_info = {
                "type": "nope",
                "params": {}
            }
        elif self.action_res["action"] == "absence":
            action_info = {
                "type": "absence",
                "params": {}
            }
        else:
            raise Exception("action_res type error")
        return action_info

    def get_action_info_from_action_res(self):
        """
        从action_res中提取出action_info
        """
        action_desc = "{}[{}]".format(self.step_desc, self.mrd_item[:20])

        step_interval = WaitTimePredictAgent(action_desc=action_desc).main().get('waitTime', 2)
        self.action_info = {
            "caseVersion": "v1.0.0",
            "desc": self.step_desc,
            "type": -1,
            "params": {},
            "common": {
                "commonAlertClear": True,
                "stepInterval": step_interval
            }
        }

        action = self.action_res["action"]

        if action == "swipe":
            # swipe 特殊处理
            return self._get_swipe_step()
        elif action == "absence":
            return self._get_absence_step()
        elif self.action_res["element_id"] == -1 and action == "nope":
            return self._get_empty_node_nope()
        elif action == "wait":
            return self._get_wait_step()
        else:
            # tap,long_press,input可以统一处理
            find_params = {
                "allowFail": False,
                "times": 0,
                "before": {
                    "wait": 0
                },
                "interval": 1000,
                "until": {
                    "times": 1,
                    "enable": False,
                    "interval": 2000
                }
            }

            find_info, path_summary = self.get_find_info()
            # get_find_info可能会更新allowFail参数
            find_params['allowFail'] = self.skip_on_fail
            params = {
                "dom": self.dom,
                "domInfo": self.dom_info,
                "findType": 0,
                "findInfo": find_info,
                "actionInfo": self.map_params_action_info(),
                "findParams": find_params,
                "pathSummary": path_summary,
                "deviceInfo": self.device_info
            }
            self.action_info["type"] = 9
            self.action_info["params"] = params
            action_type = self.action_info["params"]["actionInfo"]["type"]

        return self.action_info, action_type

    def format_step(self):
        """
        生成batdom格式的步骤信息
        """

        def scale_down_rect(rect, scale):
            """
            根据scale缩放rect
            """
            rect["x"] = int(rect["x"] / scale)
            rect["y"] = int(rect["y"] / scale)
            rect["w"] = int(rect["w"] / scale)
            rect["h"] = int(rect["h"] / scale)

        def format_step_dom_by_scale(dom, device_info):
            """
            根据scale缩放dom
            """
            scale = device_info["screenSize"]["scale"]
            # 根节点
            scale_down_rect(dom["rect"], scale)

            # 第一层
            for node in dom["children"]:
                scale_down_rect(node["rect"], scale)
                # 第二层
                if "children" in node:
                    for child_node in node["children"]:
                        scale_down_rect(child_node["rect"], scale)

        def format_action_assert(action_assert):
            """
            格式化对齐一下action_assert和action_res
            """
            if action_assert["action"] == "assert_exist":
                action_assert["action"] = "nope"
            elif action_assert["action"] == "assert_not_exist":
                action_assert["action"] = "absence"

        # 根据scale缩放dom
        format_step_dom_by_scale(self.dom, self.device_info)

        # 兼容验证操作
        format_action_assert(self.action_res)

        # 结合batdom和action_res生成action_info
        action_info, action_type = self.get_action_info_from_action_res()

        step_info = {
            "stepType": 201,
            "stepInfo": action_info,
            "stepDesc": self.step_desc
        }

        action_flag = True
        if self.action_res["action"] == "absence":
            step_info["stepType"] = 102
            action_flag = False

        return action_flag, step_info

    def main(self):
        """
        main函数
        """
        if self.action_res["element_id"] == -1:
            # 不存在校验，不需要优化，直接格式化
            pass
        else:
            self.ele_list = AutoCaseOptimizeAgent(grid_img_path=self.grid_img_path,
                                                  id_ele_map=self.id_ele_map,
                                                  dom_desc=self.dom_desc,
                                                  target_ele_id=self.target_ele_id,
                                                  step_desc=self.step_desc,
                                                  scene_knowledge=self.scene_knowledge).main()
        action_flag, step_info = self.format_step()
        return step_info, action_flag


if __name__ == '__main__':
    # url = 'xxx'
    # image_path = download_img(url=url, path='./tmp')

    image_path = 'xxx'

    step_desc = '点击页面中部靠右位置的“记忆”开关按钮。[操作步骤：1、点击主对话页左上角菜单按]'
    img_name = image_path.split('/')[-1]
    # image_path = os.path.join('./tmp', img_name)
    grid_path = os.path.join('./tmp', 'grid_' + img_name)
    dom_info, dom, dom_desc, id_ele_map = get_image_with_grid(image_path=image_path, grid_path=grid_path)

    agent = AutoCaseOptimizeAgent(grid_img_path=grid_path,
                                  id_ele_map=id_ele_map,
                                  dom_desc=dom_desc,
                                  target_ele_id=44,
                                  step_desc=step_desc,
                                  scene_knowledge=[])
    res = agent.main()
    print(res)
