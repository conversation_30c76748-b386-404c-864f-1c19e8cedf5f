#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@File    : prompt.py
<AUTHOR> <PERSON><PERSON><PERSON><PERSON>(<EMAIL>)
@Time    : 2024/11/14 14:51
@Desc    : 
"""


def system_prompt():
    """
    :return:
    """
    sys_prompt = """你是一个具有丰富App自动化测试经验的测试智能助手，我现在手里有一个控件定位工具，专门用于编写和执行App自动化测试步骤。使用这个工具编写步骤时，需要选择合适的控件，并对每个控件设置合适的定位参数。你需要根据我提供的一些信息，帮我选择合适的控件和参数。
    """
    return sys_prompt


def translate_prompt(dom_desc, target_ele_id, step_desc, scene_knowledge=[]) -> str:
    """

    :return:
    """
    if len(scene_knowledge) > 0:
        knowledge_desc = \
            "\n".join([f"{i+1}. {t['scene_words']}:{t['text_desc']}" for i, t in enumerate(scene_knowledge)])
        knowledge = "&& 提供以下和场景相关的知识供你参考 &&\n" + knowledge_desc
    else:
        knowledge = ""

    prompt = f"""
&& 控件定位工具介绍 &&
你使用的控件定位工具，是用于定位页面中的控件的。工具会提供给你步骤编写的必要信息：【标记截图】和【控件信息】和【目标控件】。
获取这些信息的原理如下：
1. 首先，工具会对当前设备页面进行截图
2. 然后，工具会对截图进行分析，获得所有可操作控件列表。
3. 然后，工具会在截图中，把所有可操作控件用矩形框标记出来，并打上数字标签，从而得到【标记截图】
4. 然后，工具会把所有可操作控件的文字信息也提供给你，包括控件的数字标签、控件的类型和额外信息，从而得到【控件信息】
5. 此外，工具还会提供给你待点击的【目标控件】，目标控件就是你最终要定位到的控件，但是为了准确地定位到这个控件，你可能额外选择一些辅助控件
6. 最后，工具还会提供给你【操作意图】，这是一段文字描述描述，告诉你控件定位的的目的是什么。

&& 任务 &&
工具提供给你【标记截图】、【控件信息】、【目标控件】、【操作意图】
你的目标是参考工具给你的输入，结合《控件选择的过程》，选择最合适的每个控件定位ID和定位参数，从而保证控件定位步骤能够准确、稳定地执行，达成操作意图。

&& 输入 &&
【标记截图】：
标记截图即为输入的图片
【控件信息】：
{dom_desc}
【目标控件】：
目标控件的ID为：{target_ele_id}
【操作意图】：
{step_desc}

{knowledge}

&& 辅助控件的选择标准 &&
工具使用辅助控件定位目标控件的原理是，工具不仅会根据设置的参数匹配每个控件，还会匹配控件之间的相对位置，因此，只有当目标控件和辅助控件的相对位置接近时，目标控件才能被找到。基于这个原理，辅助控件的选择应该满足如下标准：
1. 你应该判断【目标控件】有没有必要通过辅助控件定位，如果不需要，就不要选择辅助控件，例如：
    1.a: 一些APP页面导航栏、菜单栏、标题栏一些固定的文本或图标，位置、内容都不会发生变化，不需要辅助控件就能稳定定位。
    1.b: 一些APP页面的设置界面的一些功能开关，往往需要辅助控件定位，因为设置列表是可滑动的，而且有很多功能开关，如果只选一个控件，工具不知道点哪个开关，所以需要选择配合的文本描述作为辅助控件。
    1.c: 一些APP页面有图文描述列表，例如一个商城页面有商品列表，如果你想点进任意一个商品，就需要辅助定位，因为如果你只选择一个图片，可能点到的不是商品的图片，可能是用户的头像图片。
    1.d: 只选择单字或一个词“我”、“关注”等，因为页面中可能有很多地方都出现这类简单文本，造成干扰，这种情况通常需要其他控件辅助定位。
2. 你应该选择合适数量的控件，一般【目标控件】+【辅助控件】数量在2～3个就足够了，最多不超过5个
3. 避免选择容易与目标控件或其他辅助控件发生相对偏移的控件作为辅助控件。例如，一个APP页面一般可以分为静态部分和动态部分，例如：一个电商页面，搜索位就是静态部分，每次刷新都会在相同的位置不变，商品列表就是动态部分，列表中所有的控件都会随着滑动位置发生变化，因此不能把搜索栏和商品列表中的标题选在一起。
4. 避免选择与目标控件距离太远的辅助控件，例如：目标控件在顶部，选择了底部的控件，容易因为设备分辨率变化造成相对位置发生显著变化，从而导致定位失败。
5. 位置会发生变化，但相对位置不发生变化的控件，可以选在一起，例如电商页面中，同一个商品的标题和封面，可以选在一起。因为无论怎么滑动，它们之间的相对位置是不变的。
6. 注意设置合适的参数，例如：
    5.a【操作意图】所描述的是：点击商城中任意一个商品，那么你就应该把控件的featureFlag设置为false，这样才能保证下次列表刷新后也能匹配到任何一个商品。
    5.b 【操作意图】所描述的是：打开设置页面xx功能，那么你就不能把featureFlag设置为false，因为操作意图描述了要打开具体的设置，而不是任意设置。
    5.c 如果控件涉及到用户名、日期等经常容易发生变化的文本，那么一般把featureFlag设置为false，避免因为这些文本变化而造成失败。
    5.d 如果选择的控件是文本，且位于图文描述列表这种动态区域，属于每个卡片的标题、文字描述这类动态的内容，那么把featureFlag设置为false。
    5.d 如果选择的控件是文本，那么matchType一般为1（包含），因为这样更稳定。
    5.e 如果选择文本是操作描述中提到的，featureFlag设置为true。
    5.f 如果选择的文本位置是可变的，但是内容是固定的，featureFlag设置为true，例如APP的设置页面。
    5.g 如果选择的控件是一个图标，且样式简单，那么内容一般不会变化，featureFlag设置为true。
    5.h 如果选择的控件是一个图片、头像、视频等，那么内容很可能会发生变化，featureFlag设置为false。
    5.i 如果选择的是一个和APP功能无关的内容型文本，那么内容一般会发生变化，featureFlag设置为false。

    
&& 不能稳定定位的判断依据 &&
1. 选择单个控件，且这个控件的内容是动态变化的，例如动态图片，动态文本，一般是不稳定的。
2. 选择单个控件，页面中存在很多和目标控件相同或相似控件，一般是不稳定的，因为会随着设备分辨率变化或页面滑动导致匹配错误，所以是不稳定的。


&& 控件参数的设置标准 &&
每个控件都有自己的参数，包括如下参数：
1. featureFlag(bool，是否叠加视觉特征)：当选择true时，不仅会匹配这个位置是否有同类控件，还会匹配控件的视觉特征是否相似，例如：如果选择的控件是一本书的封面，而且这个选项为true，那么这个自动化用例执行时，就会检查控件的封面是否和此次选择的目标控件封面是同一个，如果不是，就不能定位到这个控件。同样，如果你选择的是一个文本，且featureFlag=false，那么这个控件的文本就可以是任意的。所以如果你选择的控件是动态内容，每次刷新都有可能变化，推荐关闭这个选项。
2. matchType(int，文本匹配模式): 只有当你选择的控件是文本类型时，才有意义，有如下几种模式：0 - 等于匹配，1 - 包含匹配，2 - 正则匹配，这个参数要与matchText参数配合使用，例如：如果matchType=2，matchText="小狗"，那么这个控件的文本可以"黑小狗"、"白小狗"，只要包含小狗就能匹配成功，否则不能定位到这个控件。如果matchType=3，matchText=".*"，就能匹配到任何文本。
3. matchText(string，文本匹配内容): 只有当你选择的控件是文本类型时，才有意义，matchText的值就是文本匹配的内容，需要与matchType配合使用，而且当matchType=1或2时，文本智能从【控件信息】中的文本类型控件的内容中选择或截取，不能有自定义的内容，支持的字符集也只能是阿拉伯数字、英文字母，中文。


&& 控件选择的过程 &&
你需要先分析，再给出结论，分析的过程如下：
第一步：首先，选择控件，如果是第一次选择，直接选择【目标控件】作为第一个控件，否则，根据《辅助控件的选择标准》选择其他控件作为辅助控件，选择的控件不可重复
第二步：然后，根据《控件参数的设置标准》，给选择的控件设置合适的参数
第三步：然后，分析当前所选择的控件、配合对应的控件参数是否可以稳定定位到目标控件，注意参考《不能稳定定位的判断依据》，
第四步：可以稳定定位到目标控件，则直接返回结果，如果不能，则跳转到第一步，继续分析。
"""
    output = """
&& 输出要求 &&
请直接输出一个能够被 json.loads 解析的 JSON 格式字符串，不要包含任何代码块标记，你的输出固定为以下两部分：
{
    "needSupport": bool, // 一个布尔值，表示是否需要辅助控件
    "eleList": [ // 一个数组，包含所有选择的控件信息和配置参数
        {
            "eleId": int, // 控件的ID
            "featureFlag": bool, // 一个布尔值，表示是否需要叠加视觉特征
            "matchType": int, // 一个整数，表示文本匹配模式
            "matchText": string, // 一个字符串，表示文本匹配的内容
            "reason": string // 给出选择这个控件以及对应设置的具体原因
        }, ...
    ]
}
"""
    return prompt + output


def button_switch_prompt(step_desc) -> str:
    """
    判断操作类型是打开还是关闭的prompt
    """
    prompt = f"""
    && 任务 &&
    你正在根据我给你的文字描述操作APP，我提供给你一段对开关按钮的操作描述，这个文字描述可能是校验操作，也可能是点击操作。你需要先分析当前操作是校验还是点击，然后：
    * 如果是校验操作（例如：校验xx开关为打开状态），你需要：判断这个校验是校验开关为开启状态还是关闭状态。
    * 如果是点击操作（例如：开启xxx功能、点击xx开关），你需要：判断这个操作是打开开关还是关闭开关。
    && 操作的文字描述 &&
    {step_desc}
    
    """
    output = """
    && 输出要求 &&
    请直接输出一个能够被 json.loads 解析的 JSON 格式字符串，不要包含任何代码块标记：
    {
        "isOpen": bool, // 一个布尔值，当为校验操作时, true表示校验开关为关闭状态，false表示校验开关为打开状态，当为点击操作时，true表示打开开关，false表示关闭开关
        "isVerify": bool, // 一个布尔值，true表示校验操作，false表示点击操作
        "reason": string // 给出选分析原因
    }
    """
    return prompt + output


# if __name__ == '__main__':
#     prompt = button_switch_prompt('开关已经开启')
#     message = ChatMessage()
#     message.add_user_message(prompt_text=prompt)
#
#     llm_ans_text = ''
#     llm_ans = None
#     for i in range(3):
#         try:
#             llm_ans_text = YiYan().chat(chat_message=message, response_format="json_object")
#             llm_ans = json.loads(llm_ans_text)
#             if 'isOpen' in llm_ans and type(llm_ans['isOpen']) is bool:
#                 break
#         except Exception as e:
#             print(llm_ans_text)
#             llm_ans = {
#                 "isOpen": True,
#                 "reason": "发生错误，兜底"
#             }
#     if llm_ans['isOpen']:
#         new_name = "button_switch_off"
#     else:
#         new_name = "button_switch_on"