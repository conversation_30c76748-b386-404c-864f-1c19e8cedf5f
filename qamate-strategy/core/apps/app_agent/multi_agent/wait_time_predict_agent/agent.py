#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@File    : guide_agent.py
<AUTHOR> <PERSON><PERSON><PERSON><PERSON>(renya<PERSON><PERSON>@baidu.com)
@Time    : 2024/11/14 14:46
@Desc    : 
"""

import json

from apps.app_agent.multi_agent.base.agent import BaseChat
from apps.app_agent.multi_agent.wait_time_predict_agent.prompt import predict_prompt
from apps.app_agent.utils.YiYan import <PERSON><PERSON><PERSON>
from apps.app_agent.utils.chat_message import ChatMessage
from apps.app_agent.utils.exception import AgentErrCode, AgentException
from basics.util import logger

MAX_COUNT = 3


class WaitTimePredictAgent(BaseChat):
    """
    自动化判断Agent
    """

    def __init__(self, action_desc: str):
        """

        :param mrd:
        """
        super().__init__(name="wait_time_agent")
        self.action_desc = action_desc

    def prompt_message(self):
        """

        :return:
        """
        prompt = predict_prompt(self.action_desc)
        message = ChatMessage()
        message.add_user_message(prompt_text=prompt)
        return message

    def format_answer(self, llm_ans):
        """
        解析回答
        :param llm_ans:
        :return:
        """
        f_ans = json.loads(llm_ans)
        return f_ans

    def main(self):
        """

        :return:
        """
        # prompt
        chat_message = self.prompt_message()

        # answer
        num = 0
        while num < MAX_COUNT:
            num += 1
            try:
                llm_ans = YiYan().chat(chat_message=chat_message, response_format="json_object")
                res = self.format_answer(llm_ans)
                logger.info(f'操作[{self.action_desc}]的等待时长分析结果为: {res}')
                return res
            except Exception as e:
                logger.error(e)
                logger.error("output error, try again, left %d" % num)
        # raise AgentException(err_code=AgentErrCode.LLM_UNEXPECTED_OUPUT)
        return {'waitTime': 2, 'reason': '从大模型获取回答失败，返回默认等待时长2s'}


if __name__ == '__main__':
    res = WaitTimePredictAgent(action_desc='检查搜索控件').main()
    print(res)
