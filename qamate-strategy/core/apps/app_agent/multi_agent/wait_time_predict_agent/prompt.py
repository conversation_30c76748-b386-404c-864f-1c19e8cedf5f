#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@File    : prompt.py
<AUTHOR> <PERSON><PERSON><PERSON><PERSON>(<EMAIL>)
@Time    : 2024/11/14 14:51
@Desc    : 
"""
from typing import List


def predict_prompt(action_desc: str) -> str:
    """

    :return:
    """
    prompt = f"""&& 角色 &&
你是一个具有丰富App自动化测试经验的测试智能助手

&& 任务 &&
当前有一条对App操作的描述，对App进行操作后，都需要等待app响应结束，下一个场景页面加载完成，才能执行后续的步骤。你需要根据当前app的操作描述，判断提供给你的操作执行后，需要等待多少秒。提供给你的操作为：{action_desc}

&& 现有知识 &&
你可以利用以下知识分析操作的等待时长：
1. 如果是校验控件是否存在、不存在的操作，而不涉及到实际的交互动作（如点击、长按、滑动等），等待时长可以是0s。
2. 如果是点击输入框、搜索中的【发送】按钮后，等待时间应该是10s。
3. 其他控件操作，默认等待2s

"""
    output = """
&& 输出要求 &&
请直接输出一个能够被 json.loads 解析的 JSON 格式字符串，不要包含任何代码块标记：
{
    "waitTime": int, // 整数，代表等待的时长，单位是秒
    "reason": string // 判断等待时长的原因
}
"""
    return prompt + output
