#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@File    : prompt.py
<AUTHOR> <PERSON><PERSON><PERSON><PERSON>(<EMAIL>)
@Time    : 2024/11/15 14:16
@Desc    : 
"""


def judgement_prompt(mrd: str) -> str:
    """
    判断原始用例中是否有明确的用例执行步骤
    :return:
    """

    prompt = f"""&& 角色 &&
你是一个具有丰富App自动化测试经验的测试智能助手

&& 任务 &&
当前有一份人工编写的手工测试用例，你需要判断出这份用例中是否有明确的执行或操作路径。需要用例中明确存在"步骤"提示。

&& 测试用例 &&
原始的测试用例是脑图格式，为了方便你理解，我做了格式转化，按层次结构使用'->'链接，测试用例为：【{mrd}】
"""
    output = """
&& 输出要求 &&
你的输出固定的包括以下两部分，无需输出其他内容，以json格式输出：
{
    "result": bool // 是否存在，存在为true，不存在为false
    "reason": string // 原因
}"""
    return prompt + output


def judgement_prompt_v1(mrd: str) -> str:
    """
    2025.3.4 优化
    :return:
    """
    prompt = f"""&& 任务 &&
请判断以下手工测试用例是否可以由自动化测试Agent执行。

&& 输入用例 &&
当前有一份人工编写的手工测试用例，用例中可能存在与自动化执行无关的汇总性记录或表达，忽略这部分，关注测试路径和目标。'
测试用例为：【{mrd}】

&& 判断纬度 &&
请严格按照以下框架对测试用例进行可执行性全面评估：
1. 可执行性总体判断
- 是否符合Agent可执行的基本动作范围（点击、输入、滑动、长按、校验、等待、启动App）
- 是否存在无法映射到上述基础操作的复合操作

2. 时间敏感性检查
时间敏感性的操作无法生成：
- 是否包含时间敏感型操作（如：在X完成前执行Y）
- 是否需要捕获短时存在元素（如toast、弹窗<2秒）
- 是否要求在某个过程的特定阶段执行操作

3. 系统状态影响评估
影响系统状态的无法执行：
- 会对应用或账号状态造成持久性修改(如：退出登录、切换账号)
- 涉及高风险的系统操作（如删除、注销）

4. 依赖性复杂度分析
依赖复杂的无法执行：
- 是否需要特定账号状态（如VIP/新用户）
- 是否需要历史数据依赖或外部条件（收藏/浏览记录）
- 是否需要外部设备/服务（GPS/NFC/蓝牙）

5. 数据准备检查
需要提前准备好特殊数据的无法执行：
- 是否需要预先mock数据
- 是否依赖特定测试数据（精确ID/唯一内容）
- 是否需要跨模块数据联动
"""

    output = """
&& 输出要求 &&
请直接输出一个能够被 json.loads 解析的 JSON 格式字符串，不要包含任何代码块标记：
{
    "result": bool, // 一个布尔值，表示是否可以执行
    "reason": string, // 给出思考过程和具体原因
    "label": string  // 如果不能执行，给出不能执行的具体原因标签，例如：'存在需要捕获短时存在元素（toast场景）'；如果可以执行，则固定为'可执行'
}
"""
    return prompt + output
