#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@File    : guide_agent.py
<AUTHOR> <PERSON><PERSON><PERSON><PERSON>(renya<PERSON><PERSON>@baidu.com)
@Time    : 2024/11/15 14:16
@Desc    : 
"""

import json
import threading

from apps.app_agent.multi_agent.base.agent import BaseChat
from apps.app_agent.utils.chat_message import ChatMessage
from apps.app_agent.utils.YiYan import <PERSON><PERSON><PERSON>
from apps.app_agent.multi_agent.generated_judgement_agent.v2_1.prompt import judgement_prompt_v1
from basics.util import logger
from apps.app_agent.utils.exception import AgentErrCode, AgentException

MAX_COUNT = 3


class CaseGeneratedJudgementAgent(BaseChat):
    """
    判断 case能否进行自动化生成
    """

    def __init__(self, mrd: str):
        """

        :param mrd:
        """
        super().__init__(name="case_generate_judgement_agent")
        self.mrd = mrd

    def prompt_message(self):
        """

        :return:
        """
        prompt = judgement_prompt_v1(mrd=self.mrd)
        message = ChatMessage()
        message.add_user_message(prompt_text=prompt)
        return message

    def format_answer(self, llm_ans):
        """
        解析回答
        :param llm_ans:
        :return:
        """
        f_ans = json.loads(llm_ans)
        return f_ans

    def judge(self):
        """

        :return:
        """
        # prompt
        chat_message = self.prompt_message()

        # answer
        num = 0
        while num < MAX_COUNT:
            num += 1
            try:
                llm_ans = YiYan(model_name="ernie-4-turbo").chat(chat_message=chat_message,
                                                                 response_format="json_object")
                res = self.format_answer(llm_ans)
                return res
            except Exception as e:
                logger.error(e)
                logger.error("output error, try again, left %d" % num)
        raise AgentException(err_code=AgentErrCode.LLM_UNEXPECTED_OUPUT)

    def main(self):
        """
        多线程，多数投票的方法
        :return:
        """

        results = []

        def works():
            """
            线程函数
            :return:
            """
            result = self.judge()
            results.append(result)

        threads = []

        # 创建线程
        for _ in range(3):
            thread = threading.Thread(target=works)
            threads.append(thread)
            thread.start()

        # 等待完成
        for thread in threads:
            thread.join()

        # 投票统计结果
        can_transform_votes = [result["result"] for result in results]

        final_can_transform = max(set(can_transform_votes), key=can_transform_votes.count)

        for res in results:
            if res['result'] == final_can_transform:
                final_reason = res["reason"]
                final_label = res["label"]
                break

        final_res = {
            "result": final_can_transform,
            "reason": final_reason,
            "label": final_label
        }
        logger.info("判断可生成最终结果为：{}".format(final_res))
        return final_res


if __name__ == '__main__':
    mrd_s = """前置条件：登录成功、视频审核已通过
操作步骤：
1.在个人中心点击头像进入个人主页
2.点击tab下方视频的更多按钮
3.点击置顶
->预期：1、展示置顶、删除
2、某一视频点击置顶，toast提示置顶成功，视频左上方会显示白色字体的置顶
3、支持3个视频置顶"""
    oa = CaseGeneratedJudgementAgent(mrd=mrd_s)
    a = oa.main()
