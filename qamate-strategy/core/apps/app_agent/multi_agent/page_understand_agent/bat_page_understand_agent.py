#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@File    : bat_page_understand_agent.py
<AUTHOR> <PERSON><PERSON><PERSON><PERSON>(renya<PERSON><PERSON>@baidu.com)
@Time    : 2024/11/18 16:18
@Desc    : 
"""

from apps.app_agent.multi_agent.base.agent import BaseAgent
from apps.app_agent.multi_agent.page_understand_agent.image_process import get_image_with_grid


class BatPageUnderstandAgent(BaseAgent):
    """
    基于batdom的页面类理解能力
    """
    def __init__(self, image_path, grid_img_save_path):
        """
        初始化
        :param image_path:
        :param grid_img_save_path:
        """
        super(BatPageUnderstandAgent, self).__init__(name="page_understand_agent")
        self.image_path = image_path
        self.grid_img_save_path = grid_img_save_path

    def main(self):
        """

        :return:
        """
        return get_image_with_grid(image_path=self.image_path, grid_path=self.grid_img_save_path)
