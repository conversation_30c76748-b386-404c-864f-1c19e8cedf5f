#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@File    : image_process.py
<AUTHOR> <PERSON><PERSON><PERSON><PERSON>(<EMAIL>)
@Time    : 2024/11/18 16:19
@Desc    : 
"""

import cv2

from apps.app_agent.utils.draw import draw_rect_with_index
from apps.auto_case_transform.utils.utils import batdom_record_by_path_new
from basics.util import logger


# config.load_config(os.path.dirname(__file__))
# config.load_config('/profile.json')



def get_image_with_grid(image_path, grid_path):
    """

    :param image_path:
    :param grid_path:  # 存储地址
    :return:
    """

    dom_info, dom = batdom_record_by_path_new(image_path, bat_version="v10.4")
    id_ele_map = {}

    # ele_list = [item['rect'] for item in dom['children']]
    ele_list = []
    for item in dom['children']:
        if item['type'] == 'TextArea':
            for child in item['children']:
                ele_list.append(child)
        else:
            ele_list.append(item)

    ele_list = sort_ele_rects(ele_list, tolerance=50)
    rect_list = [ele['rect'] for ele in ele_list]

    imgcv = cv2.imread(image_path)
    grid_image = draw_rect_with_index(imgcv, rect_list, output_path=grid_path)

    ele_desc = "[\n"
    for idx, ele in enumerate(ele_list):
        t, d = dom_ele_2_desc(ele)
        position = add_ele_position_info(ele['rect'], img_w=dom['rect']['w'], img_h=dom['rect']['h'])
        position_desc = "截图的{}方".format(position)
        ele_desc += "id:{}, 类型:{}, 描述:{}, 位置:{}\n".format(idx, t, d, position_desc)
        id_ele_map[idx] = ele
    ele_desc += "]"

    return dom_info, dom, ele_desc, id_ele_map


def dom_ele_2_desc(dom_ele: dict):
    """

    :param dom_ele:
    :return:
    """
    if 'Text' in dom_ele['type']:
        desc = dom_ele['ext']['text'] if len(dom_ele['ext']['text']) < 15 else dom_ele['ext']['text'][:15] + "..."
        desc = "'{}'".format(desc)
        type = "文字"
    elif dom_ele['type'] == "Icon":
        desc = "『{}』".format(dom_ele['ext']['cname'].replace("_beta", "")) + " 控件"
        type = "控件"
    elif dom_ele['type'] == "Component":
        desc = "输入框" if "评论框" in dom_ele['ext']['cname'] else dom_ele['ext']['cname']
        if dom_ele['ext']['name'] == "icon":
            type = "控件"
        else:
            type = "组件"
    else:
        logger.warning("未知的操作类型：{}".format(dom_ele['type']))
        desc = ""
        type = ""
    return type, desc


def sort_rects(rects, tolerance=30):
    """
    rect排序 先按 `y` 坐标排序，再按 `x` 坐标排序
    :param rects:
    :param tolerance: 容忍差
    :return:
    """

    def sort_key(rect):
        return (rect['y'] // tolerance, rect['x'] // tolerance)

    # 按排序键进行排序
    sorted_rects = sorted(rects, key=sort_key)

    return sorted_rects


def sort_ele_rects(ele_rects, tolerance=30):
    """
    针对batdom ele的排序
    :param ele_rects:
    :param tolerance:
    :return:
    """
    def sort_key(ele_rect):
        return (ele_rect['rect']['y'] // tolerance, ele_rect['rect']['x'] // tolerance)

    # 按排序键进行排序
    sorted_rects = sorted(ele_rects, key=sort_key)

    return sorted_rects


def add_ele_position_info(ele_rect, img_w, img_h):
    """
    为batdom的建模元素添加位置信息
    :param ele_rect: { 'x', 'y', 'w', 'h' }
    :param img_w:
    :param img_h:
    :return:
    """
    # 将图片9等分
    grid_w, grid_h = img_w / 3, img_h / 3

    # rect中心点
    center_x = ele_rect['x'] + ele_rect['w'] / 2
    center_y = ele_rect['y'] + ele_rect['h'] / 2

    # 横向
    if center_x < grid_w:
        col = '左'
    elif center_x < 2 * grid_w:
        col = '中'
    else:
        col = '右'

    # 纵向
    if center_y < grid_h:
        row = '上'
    elif center_y < 2 * grid_h:
        row = '中'
    else:
        row = '下'

    position = f"{col}-{row}"
    return position


if __name__ == '__main__':
    img_path = "/Users/<USER>/.lazyOne/localServer/cache/0bc3521b-d5d7-4a55-bebd-ed40d2af6413/0.jpg"
    grid_path = "/Users/<USER>/.lazyOne/localServer/1_grid.jpg"
    a, b, c, d = get_image_with_grid(img_path, grid_path)
    print(c)

