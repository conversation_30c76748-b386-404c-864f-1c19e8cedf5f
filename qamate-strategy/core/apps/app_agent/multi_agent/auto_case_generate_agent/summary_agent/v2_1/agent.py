#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@File    : guide_agent.py
<AUTHOR> <PERSON><PERSON><PERSON><PERSON>(renyan<PERSON>@baidu.com)
@Time    : 2024/11/15 14:55
@Desc    : 
"""

import json

from apps.app_agent.multi_agent.base.agent import BaseChat
from apps.app_agent.utils.chat_message import ChatMessage
from apps.app_agent.utils.big_model import BigModel
from apps.app_agent.multi_agent.auto_case_generate_agent.summary_agent.v2_1.prompt import process_summary_prompt, \
    testing_complete_prompt
from basics.util import logger


class ProcessSummaryAgent(BaseChat):
    """
    进度总结Agent
    """

    def __init__(self, mrd: str, app_info: dict, execute_record: list, exec_image_path,
                 model_name="/ssd1/ep/Qwen2.5-VL-72B-Instruct"):
        """

        :param mrd:
        :param app_info:
        :param execute_record:
        :param exec_image_path:
        """
        super().__init__(name="process_summary_agent")
        self.mrd = mrd
        self.app_info = app_info
        self.history = []
        self.exec_image_path = exec_image_path

        self.model_name = model_name

        self._get_history(execute_record=execute_record)

    def _get_history(self, execute_record):
        """
        得到历史有效的执行记录
        :param execute_record:
        :return:
        """
        if len(execute_record) == 0:
            return

        for item in execute_record:
            # if item.get('reflect_res_info', {}).get('reflect_flag'):
            history_item = {**item['guide_res_info'], **item['reflect_res_info'], **item['process_res_info']}
            self.history.append(history_item)

    def prompt_message(self, complete_result):
        """

        :return:
        """
        prompt = process_summary_prompt(mrd=self.mrd, app_info=self.app_info, history=self.history,
                                        complete_hint=complete_result.get("reason", ""))
        logger.info("guide_summary_prompt: {}".format(prompt))
        img_paths = [self.exec_image_path]
        logger.info("对应的图片为:{}".format(img_paths))
        chat_message = ChatMessage()
        chat_message.add_user_message(prompt_text=prompt, image_paths=img_paths)
        return chat_message

    def format_answer(self, llm_ans):
        """
        解析回答
        :param llm_ans:
        :return:
        """
        f_ans = json.loads(llm_ans)
        res = {
            "completed_contents": f_ans["content"]
        }
        return res

    def process_summary(self, complete_result):
        """
        进度总结
        :return:
        """
        chat_message = self.prompt_message(complete_result)
        llm_ans = BigModel(model_name=self.model_name).chat(chat_message=chat_message, response_format='json_object')
        summary_res = self.format_answer(llm_ans)
        return summary_res

    def test_case_completed(self):
        """
        是否完成测试
        :return:
        """
        complete_prompt = testing_complete_prompt(mrd=self.mrd, app_info=self.app_info, history=self.history)
        logger.info("complete_prompt: {}".format(complete_prompt))
        chat_message = ChatMessage()
        chat_message.add_user_message(prompt_text=complete_prompt)

        llm_ans = BigModel(model_name=self.model_name).chat(chat_message=chat_message,
                                                            temperature=0.2,
                                                            response_format='json_object')
        complete_result = json.loads(llm_ans)
        return complete_result

    def main(self):
        """

        :return:
        """
        # 判断是否完成测试
        complete_result = self.test_case_completed()

        # 内容总结
        # summary_res = self.process_summary(complete_result)
        summary_res = {"completed_contents": ""}

        f_ans = {**summary_res, **complete_result}

        res = {
            "agent_res": f_ans,
        }
        return res


if __name__ == '__main__':
    pass
