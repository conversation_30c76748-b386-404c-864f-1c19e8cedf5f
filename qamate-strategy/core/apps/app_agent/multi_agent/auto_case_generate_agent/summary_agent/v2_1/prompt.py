#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@File    : prompt.py
<AUTHOR> <PERSON><PERSON><PERSON><PERSON>(<EMAIL>)
@Time    : 2024/11/15 14:55
@Desc    : 
"""


def process_summary_prompt(mrd: str, history: list, app_info: dict, complete_hint):
    """
    2025.2.12
    过程总结prompt
    :param mrd:
    :param history:
    :param app_info:
    :return:
    """
    # App简介
    if app_info:
        app_info_str = "当前正在测试的App为{}，{}".format(app_info['name'], app_info['desc'])
    else:
        app_info_str = ""

    operation_history_str = "为了完成用户指令的要求，已经执行了一系列操作。这些操作信息如下：\n"
    for idx, item in enumerate(history):
        # reflect_str = item["reflect_cont"] if not item["reflect_flag"] else "操作正确"
        reflect_str = item["reflect_cont"]
        operation_history_str += "步骤 {}: [具体操作：{}；操作后的反思:{}]\n".format(idx + 1, item['action_desc'],
                                                                                   reflect_str)

    if not complete_hint:
        complete_hint = "无"

    prompt = f"""&& 背景 &&
你是一个具有App自动化测试经验的AI手机操作助手。{app_info_str}
你正在执行一项自动化测试任务，目标是 **精准判断当前测试进度**。  
**测试用例路径** 为：【{mrd}】

&& 任务 &&
你的核心任务是：**基于【历史操作】分析当前执行到整个测试流程中的哪一步了**，确保测试路径按照预期推进。  
**注意：图片内容仅作为辅助信息，关键判断依据是【历史操作】中的具体操作记录！**

&& 关键判断规则 &&
1. **测试步骤可能由多个具体操作组成**  
   - 例如，测试步骤：“搜索‘兰花怎么养’” 实际需要：
     - (1) 在搜索框输入“兰花怎么养”
     - (2) 点击搜索按钮  
   - 如果只执行了 (1) 而未执行 (2)，则该步骤仍 **未完成**。

2. **测试路径为大致路径，实际执行可能更复杂**  
   - 用例路径示例：A → B → C  
   - 但实际执行可能为：A → B → B' → C（即 B 可能包含多个子步骤）。  
   - 你需要 **分析历史操作，判断当前流程是否符合预期**。

3. **测试用例中的所有关键操作必须在历史操作中体现**  
   - 例如：用例路径为 **“打开 App → 输入 hello → 校验显示 hello”**  
   - 如果 **历史操作未包含“输入 hello”**，但页面截图中已有该内容，仍然需要执行输入操作，不能直接跳到校验步骤。

4. **测试进度必须包含完整的关键环节**  
   - 输入操作后，通常需要 **发送/确认/提交** 等操作，不能只输入不提交。  
   - 最后一步通常是 **校验**（如 assert_exist、assert_not_exist），如果没有校验步骤，则测试 **尚未完成**。

&& 历史操作 &&   
{operation_history_str}  **重要提示**：
- **每个步骤仅包含一个具体操作**，你需要综合分析操作的连续性和完成度。
- 如果某个测试步骤尚未完成，你需要准确指出当前执行进度。

&& 初步思考结果 &&
{complete_hint}
**提示**：初步思考结果可能存在错误，仅供参考！

&& 图片信息 &&
本次校验环节提供了 **两张手机截图**：
- 第一张图片：操作前的截图，若存在红框表示操作的点击、输入或校验位置；若存在箭头则表示页面滑动方向（左箭头：由右向左滑动；上箭头：由下往上滑动）。
- 第二张图片：操作后的截图，用于判断当前操作是否达到预期效果。

&& 提示 &&
你拥有 **基本的自动化测试常识**，例如：
1. **测试完成的标志**  
   - 自动化测试的 **最后一步** 需要是 **‘校验’**（assert_exist 或 assert_not_exist）。  
   - 如果是 **校验页面**，可以使用某个 **固定元素** 进行验证（例如页面标题）。

2. **输入操作后通常需要提交或确认**  
   - 例如，在搜索框输入内容后，需要 **点击搜索按钮**，否则搜索未完成。  
"""
    output = """
&& 输出格式 &&
请直接输出一个能够被 json.loads 解析的 JSON 格式字符串，不要包含任何代码块标记：
{
    "think": string // 给出你的思考分析过程
    "completed_contents": string  // 基于 历史操作 对当前已完成内容的总体总结,
}
"""
    return prompt + output


def testing_complete_prompt(mrd: str, history: list, app_info: dict):
    """
    判断自动化话完成prompt
    :param mrd:
    :param history:
    :param app_info:
    :return:
    """
    # App简介
    if app_info:
        app_info_str = "当前正在测试的App为{}，{}".format(app_info['name'], app_info['desc'])
    else:
        app_info_str = ""

    operation_history_str = "&& 历史操作 && \n 为了完成测试，已经执行了一系列操作。这些操作信息如下：\n"
    for idx, item in enumerate(history):
        reflect_str = item["reflect_cont"]
        operation_history_str += "步骤 {}: [·具体操作：{}\n·操作后的思考：{}]\n".format(idx + 1, item['action_desc'],
                                                                                      reflect_str)
    operation_history_str += "每一个步骤仅包含一步操作。\n"

    prompt = f"""&& 背景 &&
你是一个具有App自动化测试经验的AI手机操作助手。{app_info_str}
你正在操作手机完成一份测试用例，测试用例为：{mrd}
{operation_history_str}

&& 任务 &&
判断【历史操作】中的【具体步骤】是否完成了整个测试过程(即达成了最终测试目标)。

&& 判断测试完成标准 &&
1. **最后一步中[具体操作]必须是校验（验证）步骤**，否则测试 **不算完成**。
   - 即使测试用例 **未明确要求校验**，也 **必须补充适当的校验** 来确认测试完成。
2. **测试用例中最后的校验点都必须执行**，否则测试 **不算完成**。
   - 例如，若用例要求验证 **功能 X 和 Y 的样式**，则 **X 和 Y 都需要被校验**。
3. **不必咬文嚼字**，重点关注测试点（对象）是否被测试到了。
   - 例如，若需要测试[文案A的完整性展示]，可以用[其中的关键性展示]替代。

例如：测试用例为：执行ABC，验证功能X和Y元素的样式。
1. 历史步骤为：点击A -> 点击B -> 点击C，此条case尚未完成测试。
2. 历史步骤为：点击A -> 点击B -> 点击C -> 校验功能点X，此条case尚未完成测试，因为仅校验了功能X没有检验Y的样式。
3. 历史步骤为：点击A -> 点击B -> 点击C -> 校验功能点X -> 校验Y样式，此条case完成测试，不仅校验了功能X，还检验了Y的样式。
"""
    output = """
&& 输出格式 &&
请直接输出一个能够被 json.loads 解析的 JSON 格式字符串，不要包含任何代码块标记，你的输出固定为以下两个部分：
{
    "result": bool // 一个布尔值，表示具体操作是否全部执行完成，执行完成的最后一步需为校验步骤
    "reason": string // 给出判断的原因。
}
"""
    return prompt + output
