#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@File    : prompt.py
<AUTHOR> <PERSON><PERSON><PERSON><PERSON>(renyan<PERSON>@baidu.com)
@Time    : 2024/11/15 14:55
@Desc    : 
"""


def system_prompt():
    """

    :return:
    """
    sys_prompt = """你是一个具有App自动化测试经验的AI手机操作助手，你需要帮我操作手机并且完成页面的功能点验证。
你拥有基本的自动化测试常识，例如：在输入操作后需要跟确认、完成、发送、搜索等操作；设备执行过程出现意料之外的弹窗、提示框、权限弹窗等需要先点除；菜单或设置页面可能需要滑动查找某些功能等。"""
    return sys_prompt


def process_summary_prompt(mrd: str, history: list, app_info: dict, complete_hint):
    """
    过程总结prompt
    :param mrd:
    :param history:
    :param app_info:
    :return:
    """
    # App简介
    if app_info:
        app_info_str = "当前正在测试的App为{}，{}".format(app_info['name'], app_info['desc'])
    else:
        app_info_str = ""

    operation_history_str = "&& 历史操作 && \n 为了完成用户指令的要求，已经执行了一系列操作。这些操作信息如下：\n"
    for idx, item in enumerate(history):
        # reflect_str = item["reflect_cont"] if not item["reflect_flag"] else "操作正确"
        reflect_str = item["reflect_cont"]
        operation_history_str += "步骤 {}: [·具体操作：{}\n·操作后的思考：{}]\n".format(idx + 1, item['action_desc'],
                                                                                    reflect_str)
    operation_history_str += "每一个步骤仅包含一步操作。\n"

    if not complete_hint:
        complete_hint = "无"

    prompt = f"""&& 背景 &&
你是一个具有App自动化测试经验的AI手机操作助手。{app_info_str}
你正在操作手机完成一份测试用例，测试用例为：{mrd}

&& 任务 &&
图片内容仅用于帮助你理解，关注【历史操作】中的文字内容，你的任务是：基于【历史操作】分析出当前执行到整个测试流程中的哪一步了。

&& 初步思考结果 &&
{complete_hint}

&& 注意 &&
1. 测试路径中的一个用例步骤，实际可能需要多步操作。例如：测试步骤为：搜索"兰花怎么养"，实际的操作会分为：1.在搜索框输入"兰花怎么养"，2.点击搜索按钮。如果只执行了在搜索框输入了内容，不能算作完成本用例步骤。
2. 测试用例路径为大致路径，可能不是完整步骤，但是用例中提到的执行步骤都需要执行，具体要如何操作以当前页面截图为主。例如：测试路径为：A -> B -> C，实际的执行路径为：A -> B -> B' -> C。
3. 用例中提到的执行步骤都需要在【具体操作】中体现。
这里列出一个易错点：
例如：用例为1.打开App -> 2. 输入hello -> 3. 校验展示整成。
当执行完1后，页面截图默认已经显示了输入hello的信息，但是这并不我们本次操作输入的，因此我们的测试需要按用例顺序，输入hello，不可以直接跳到执行步骤3的用例。

{operation_history_str}
&& 图片信息 &&
图片是『最后一步操作』的手机截图。
第一张截图中如果有红框，则表示操作的点击、输入或校验位置；
如果有箭头，则表示页面滑动操作中手指在设备的滑动轨迹，左箭头表示由右向左滑动（达到 向右切换页面 的效果），上箭头表示由下往上滑动，达到 向下翻页 的效果。
"""
    output = """
&& 输出格式 &&
你的输出固定确保以合法的json格式输出：
{
    "content": string  // 执行进度，明确给出具体步骤，例如：已经执行到***
}
"""
    return prompt + output


def testing_complete_prompt(mrd: str, history: list, app_info: dict):
    """
    判断自动化话完成prompt
    :param mrd:
    :param history:
    :param app_info:
    :return:
    """
    # App简介
    if app_info:
        app_info_str = "当前正在测试的App为{}，{}".format(app_info['name'], app_info['desc'])
    else:
        app_info_str = ""

    operation_history_str = "&& 历史操作 && \n 为了完成测试，已经执行了一系列操作。这些操作信息如下：\n"
    for idx, item in enumerate(history):
        operation_history_str += "步骤 {}: [具体操作：{}]\n".format(idx + 1, item['action_desc'])
    operation_history_str += "每一个步骤仅包含一步操作。\n"

    prompt = f"""&& 背景 &&
你是一个具有App自动化测试经验的AI手机操作助手。{app_info_str}
你正在操作手机完成一份测试用例，测试用例为：{mrd}

&& 任务 &&
判断【历史操作】中的【具体步骤】是否完成了整个测试过程。

{operation_history_str}
"""
    output = """
&& 输出格式 &&
你的输出固定为以下两个部分，不要输出其他内容!! 确保以合法的json格式输出：
{
    "result": bool // 一个布尔值，表示具体操作是否全部执行完成
    "reason": string // 给出判断的原因。
}
"""
    return prompt + output

