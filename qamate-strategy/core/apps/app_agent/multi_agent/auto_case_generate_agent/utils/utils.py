#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@File    : utils.py
<AUTHOR> <PERSON><PERSON><PERSON><PERSON>(renyan<PERSON>@baidu.com)
@Time    : 2024/11/22 14:14
@Desc    : 
"""

import re
from basics.util import logger


def mllm_guide_action_parse(act: str):
    """
    模型输出解析
    :param action:
    :return:
    """

    action_res = {
        "error": False,
        "action": "",
        "element_id": -1,
        "input_text": "",
        "direction": "",
        "content": "",
        "time": 0
    }

    act_name = act.split("(")[0]
    action_res['action'] = act_name

    if act_name == "tap":
        params = re.findall(r"tap\((.*?)\)", act)[0]
        element_id = int(params.strip())
        action_res['element_id'] = element_id
    elif act_name == "wait":
        params = re.findall(r"wait\((.*?)\)", act)[0]
        time = int(params.strip())
        action_res['time'] = time
    elif act_name == "input":
        params = re.findall(r"input\((.*?)\)", act)[0]
        element_id = int(params.split(",")[0].strip())
        input_text = params.split(",")[1].strip().replace('"', '')
        action_res['element_id'] = element_id
        action_res['input_text'] = input_text
    elif act_name == "swipe":
        params = re.findall(r'swipe\("([^"]+)"\)', act)[0]
        swip_direction = params.strip()
        action_res['direction'] = swip_direction
    elif act_name == "long_press":
        params = re.findall(r"long_press\((.*?)\)", act)[0]
        element_id = int(params.strip())
        action_res['element_id'] = element_id
    elif "finsh" in act:
        params = re.findall(r'finsh\("([^"]+)"\)', act)[0]
        content = params.strip()
        logger.warning("执行结束,{}".format(content))
    elif act_name == "assert_exist":
        params = re.findall(r"assert_exist\((.*?)\)", act)[0]
        element_id = int(params.split(",")[0].strip())
        content = params.split(",")[1].strip().replace('"', '')
        action_res['element_id'] = element_id
        action_res['content'] = content
    elif act_name == "assert_not_exist":
        params = re.findall(r'assert_not_exist\("([^"]+)"\)', act)[0]
        action_res['content'] = params.strip()
    else:
        # raise AgentException(err_code=AgentErrCode.UNKNOWN_ACTION_TYPE)
        logger.error("预期外的操作类型：{}".format(act_name))
        action_res['error'] = True

    return action_res


def mllm_guide_assert_parse(check):
    """
    校验步骤拆解
    :param check:
    :return:
    """
    assert_res = {
        "error": False,
        "is_not": True,  # 校验是否存在
        "element_id": -1,  # 元素序号
        "content": ""  # 校验描述
    }

    if "assert_exist" in check:
        assert_res["is_not"] = True
        params = re.findall(r"assert_exist\((.*?)\)", check)[0]
        assert_res['element_id'] = int(params.split(",")[0].strip())
        assert_res['content'] = params.split(",")[1].strip().replace('"', '')
    elif "assert_not_exist" in check:
        assert_res["is_not"] = False
        params = re.findall(r"assert_not_exist\((.*?)\)", check)[0]
        assert_res['element_id'] = int(params.split(",")[0].strip())
        assert_res['content'] = params.split(",")[1].strip().replace('"', '')
    else:
        # raise AgentException(err_code=AgentErrCode.UNKNOWN_CHECK_TYPE)
        logger.error("预期外的验证类型：{}".format(check))
        assert_res['error'] = True

    return assert_res
