#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@File    : prompt.py
<AUTHOR> <PERSON><PERSON><PERSON><PERSON>(renya<PERSON><PERSON>@baidu.com)
@Time    : 2024/11/15 14:48
@Desc    : 
"""
import json
from typing import List


def system_prompt():
    """

    :return:
    """
    sys_prompt = """你是一个具有App自动化测试经验的AI手机操作助手，你需要帮我操作手机并且完成页面的功能点验证。
你拥有基本的自动化测试常识，例如：在输入操作后需要跟确认、完成、发送、搜索等操作；设备执行过程出现意料之外的弹窗、提示框、权限弹窗等需要先点除；菜单或设置页面可能需要滑动查找某些功能等。"""
    return sys_prompt


def get_action_prompt(mrd_desc: str, mrd_path: str, app_info: dict, history: List[dict], dom_desc: str,
                      error_info: str,
                      product_knowledge: List[dict], completed_contents="", pre_step=""):
    """
    2025.2.5

    :param mrd_desc:
    :param mrd_path:
    :param app_info:
    :param history:
    :param dom_desc:
    :param product_knowledge:
    :param completed_contents:
    :param pre_step:
    :return:
    """

    # App简介
    if app_info:
        app_info_str = "当前正在测试的App为{}，{}".format(app_info['name'], app_info['desc'])
    else:
        app_info_str = ""

    # 页面元素
    if dom_desc:
        dom_desc_str = f"""为了帮助你更好地理解这张截图的内容，，我们提供了每个框选区域的补充信息。说明：
  - id:标签数字与截图中显示的数字对应。
  - 类型：文字、控件等。
  - 描述：元素的描述或文字内容。
  补充信息内容：{dom_desc}"""
        dom_desc_str += """
**注意**：
- 这些解析信息可能存在误差，一切以截图为准！
- 若 UI 元素未在截图中找到，可能需要滑动页面或采取其他交互方式。\n"""
    else:
        dom_desc_str = ""

    # 操作历史
    if len(history) == 0:
        operation_history_str = ""
        if pre_step:
            operation_history_str += "## 操作历史 ## \n前置已完成操作：[{}]\n前置步骤均已执行，后续步骤均在前置操作后顺序执行。\n".format(
                pre_step)
    else:
        operation_history_str = "## 操作历史 ## \n为了完成用户指令的要求，已经执行了一系列操作，实际操作内容即为之前输出的操作描述。这些操作信息如下：\n"
        if pre_step:
            operation_history_str += "前置已完成操作：【{}】\n前置步骤均已执行，后续步骤均在前置操作后顺序执行。\n".format(
                pre_step)
        for idx, item in enumerate(history):
            # reflect_str = item["reflect_cont"] if not item["reflect_flag"] else "操作正确"
            reflect_str = item["reflect_cont"]
            operation_history_str += "- 步骤{}: [·实际操作：{}] ·操作后的思考：{}\n".format(idx + 1,
                                                                                          item['action_desc'],
                                                                                          reflect_str)
        operation_history_str += ("**注意**：\n- *实际操作* 中的内容才是历史实际操作，*操作后的思考* 是对此步骤的反思，仅供参考。\n"
                                  "- 对于已成功执行的步骤，请不要重复操作。\n- 当前操作必须基于最新截图做出决策！")

        if error_info:
            error_info_str = f"你此前在当前页面进行了错误的操作:{error_info}，我已经撤回此次错误操作，你需要在本次执行中进行修正"
            operation_history_str += error_info_str

    # 进度
    if not completed_contents:
        completed_contents_str = ""
    else:
        completed_contents_str = "## 执行进度 ##\n{}\n**注意**：此处的执行进度可能存在错误，仅供参考！\n".format(
            completed_contents)

    if product_knowledge:
        product_hint = "## 提示 ##\n这里有一些业务术语或经验可能会对你理解执行case有所帮助，提示如下：\n"
        for item in product_knowledge:
            product_hint += "{}：{}\n".format(item['scene_words'], item['text_desc'])
    else:
        product_hint = ""

    prompt = f"""## 背景 ##
你需要协助我操作手机完成一项手机App测试任务。{app_info_str}
你需要根据我提供的信息和截图内容，推导出当前测试进度，并一步步地给出下一步的具体操作。请注意，每次只输出一步操作，且必须确保当前操作的目标UI元素在截图中存在，不能重复执行已经完成的操作。

## 测试用例 ##
- 测试描述：【{mrd_desc}】
- 用例执行路径：【{mrd_path}】
### 重要说明 ###
1. 测试用例路径是大致路径，可能存在缺失关键步骤的情况，也可能存在部分步骤需要拆分成多个具体操作的情况。
2. 用例中的步骤是必须执行的，不能遗漏。每一步的具体执行方式需要结合当前页面截图判断。
3. 一个步骤可能需要多次交互才能完成，例如：
   - 用例步骤："搜索'兰花怎么养'"
   - 实际操作拆分为：1.在搜索框输入"兰花怎么养"。2.点击搜索按钮。

## 截图信息 ##
图片是当前手机所在页面的截图。{dom_desc_str}
{operation_history_str}

{product_hint}
——————
## 任务说明 ##
- 请基于历史操作、当前截图信息和测试进度，进行深度思考，判断当前测试进程并给出下一步的操作。  
- 仅输出一步具体操作，并确保该操作能够顺利推动测试流程，且所使用的UI元素在截图中存在。
- 请避免一次执行多个操作（例如输入和点击不能同时输出）。

### 思考步骤 ###
1. 判断测试进度
   - 结合测试用例、历史操作，确认当前进度。
   - 确保所有前置步骤已正确执行。

2. 分析当前页面
   - 结合截图，判断当前页面是否符合预期？
   - 是否出现了额外弹窗、权限请求或加载状态？（如果有，先处理这些情况）

3. 选择最佳操作
   - 在所有可选操作中，选择最稳定、最符合测试目标的操作。
   - 确保该操作可以在当前截图中执行，而不是基于假设。

4. 再次检查
   - 检查当前进度判断是否正确。
   - 检查选择的操作是否正确。
   - 再次确认选择的元素id是否正确，如果期望操作或校验的UI元素未分配id，则element输出-1。
——————

## 操作函数（仅限使用以下函数之一） ##
1. tap(element: int)
   - 用途：点击屏幕上指定的UI元素。  
   - 示例：tap(5)

2. input(element: int, text_input: str)
   - 用途：在指定输入框中输入文本。  
   - 示例：input(10, "Hello, world!")

3. swipe(direction: str)
   - 用途：滑动屏幕。  
   - 参数："up", "down", "left", "right"
   - 解释：up表示由下往上滑动，达到 向下翻页 的效果；down表示由上往下滑动，达到 向上翻页 的效果（或查看历史信息）；left表示由右向左滑动，达到 向右切换页面 的效果；right表示由左向右滑动，达到 向左切换页面 的效果。
   - 示例：swipe("up")

4. long_press(element: int)
   - 用途：长按屏幕上指定的UI元素。  
   - 示例：long_press(8)

5. assert_exist(element: int, content: str)
   - 用途：校验当前页面存在某个UI元素 或 必要的校验点，也可以是校验某项功能或特殊场景等，例如：校验**功能正常，此时element置为-1。
   - 解释：content是校验目的
   - 示例：assert_exist(4, "验证存在点赞按钮"); assert_exist(-1, "tab顺序展示正确")

6. assert_not_exist(content: str)
   - 用途：校验下一个页面不存在某些元素。  
   - 解释：content是校验目的
   - 示例：assert_not_exist("验证页面不存在弹窗")

7. wait(time: int)
   - 用途：等待指定秒数。建议time为5的倍数。
   - 示例：wait(5)

8. finsh(reason: str)
   - 用途：结束测试或当无法继续操作时给出理由。  
   - 示例：finsh("无法找到目标元素")

## 注意事项 ##
1. 确保所有操作基于截图中存在的元素，不要假设页面内容。
2. 每次仅能输出一个操作函数，不要合并多个操作  
3. 用例中描述的所有关键步骤必须在测试中体现，不可省略。  
4. 当遇到意外弹窗或提示框时，优先处理后再进行下一步操作。  
5. 对于控件操作，尽量选用页面上稳定且代表性的元素。  
6. 如果界面未出现目标元素，可能需要滑动页面（swipe）查找。

## 输出要求 ##
输出必须包含以下四个部分，且格式固定：

### 思考 ###
详细说明你的思考推理过程。输出内容中不要包含元素id。
### 具体操作 ###
调用一个操作函数及相应参数（仅允许使用上述八个函数中的一个，且唯一），例如：tap(1)，直接输出函数，不要添加额外的符号。
### 操作描述 ###
简要描述本次操作内容，确保描述中只涉及本次单一动作，例如“在主界面点击搜索框”。
### 对应测试用例 ###
上述操作或验证是对应到[测试用例]哪个节点，只能输出一个节点(输出全称)。例如原始输出的测试用例为 A -> B -> C, 上述操作是针对完成 B 描述的，则输出 B。"""
    return prompt


def check_element_id_prompt(element_id: int, action_desc: str, action_thought, app_info: dict, dom_desc: str):
    """
    校验元素检测prompt
    :param element_id:
    :param action_desc:
    :return:
    """

    if app_info:
        app_info_str = "当前正在测试的App为{}，{}".format(app_info['name'], app_info['desc'])
    else:
        app_info_str = ""

    if dom_desc:
        dom_desc_str = ("为了帮助你更好地理解这张截图的内容，我们通过其他能力得到每个框选元素的补充信息供你参考，"
                        "标签是元素数字标签与图片中的数字标签相对应，类型表示元素类型，描述是元素的描述或文字内容。"
                        "注意：所有框出的元素都可能是可交互元素，可操作元素不局限于控件类型，文字标签和其他标签的元素也可能是交互操作按钮\n")
        dom_desc_str += dom_desc
        dom_desc_str += "\n这些信息不一定准确，仅供参考，你需要结合截图来理解!!\n"
    else:
        dom_desc_str = ""

    if dom_desc:
        dom_desc_str = f"""为了帮助你更好地理解这张截图的内容，，我们提供了每个框选区域的补充信息。说明：
    - id:标签数字与截图中显示的数字对应。
    - 类型：文字、控件等。
    - 描述：元素的描述或文字内容。
    补充信息内容：{dom_desc}"""
        dom_desc_str += """
**注意**：
- 这些解析信息可能存在误差，一切以截图为准！
- 所有框出的元素都可能是可交互元素，可操作元素不局限于控件类型。\n"""
    else:
        dom_desc_str = ""

    prompt = f"""&& 角色 &&
你是一个具有丰富App自动化测试经验的测试智能助手。{app_info_str}

&& 背景 &&
我正在操作手机完成自动化测试，当前为自动化测试过程中的一步操作。

&& 任务 &&
你需要根据[[操作思考]、[操作描述]和[截图]判断我操作的元素id是否正确，如果不正确，给出正确的元素id。

&& 图片信息 &&
图片信息为当前页面截图，截图中的交互式UI元素都用数字标签标记(从0开始)，每个交互元素用红色框框选出，数字标签都位于元素的左上角。
{dom_desc_str}

操作思考：【{action_thought}】
操作描述：【{action_desc}】
操作元素标签id：【{element_id}】
"""
    output = """
&& 输出格式 &&
请直接输出一个能够被 json.loads 解析的 JSON 格式字符串，不要包含任何代码块标记，你的输出固定为以下三部分：
{
    "result": bool // 一个布尔值，表示输入的操作元素id是否正确
    "correct_element_id": int // 给出正确的元素id，如果输入的操作元素id是正确的，返回输入的id即可；如果输入的id是错误的，给出修正后正确的元素id
    "reason": string // 给出原因、思考过程。
}
"""
    return prompt + output


if __name__ == '__main__':
    text = "月光下的美女与野兽"
    encoded_text = json.dumps(text)
    print(encoded_text)
