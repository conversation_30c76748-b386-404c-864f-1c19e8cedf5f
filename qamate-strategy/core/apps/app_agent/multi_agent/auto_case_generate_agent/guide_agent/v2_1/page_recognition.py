#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@File    : page_recognition.py
<AUTHOR> <PERSON><PERSON><PERSON><PERSON>(<EMAIL>)
@Time    : 2025/3/13 15:42
@Desc    : 
"""

from apps.app_agent.utils.chat_message import ChatMessage
from apps.app_agent.utils.big_model import BigModel
from apps.app_agent.multi_agent.page_understand_agent.bat_page_understand_agent import BatPageUnderstandAgent


def page_recognition(image_path, mrd_str):
    """

    :param img_path:
    :return:
    """
    grid_path = image_path.replace('.jpg', '_g.jpg').replace('.png', '_g.png')
    page_unds_agent = BatPageUnderstandAgent(image_path=image_path, grid_img_save_path=grid_path)
    dom_info, dom, dom_desc, id_ele_map = page_unds_agent.main()

    prompt = f"""
## 背景 ##
你需要协助我操作手机完成一项手机App测试任务。当前正在测试的App为百度，百度App是一款移动搜索和信息服务应用，它拥有强大的搜索引擎技术、丰富多样的内容和服务。
你需要根据我提供的信息和截图内容，推导出当前测试进度，并一步步地给出下一步的具体操作。请注意，每次只输出一步操作，且必须确保当前操作的目标UI元素在截图中存在，不能重复执行已经完成的操作。

## 测试用例 ##
- 用例执行路径：【{mrd_str}】
### 重要说明 ###
1. 测试用例路径是大致路径，可能存在缺失关键步骤的情况，也可能存在部分步骤需要拆分成多个具体操作的情况。
2. 用例中的步骤是必须执行的，不能遗漏。每一步的具体执行方式需要结合当前页面截图判断。
3. 一个步骤可能需要多次交互才能完成，例如：
   - 用例步骤："搜索'兰花怎么养'"
   - 实际操作拆分为：1.在搜索框输入"兰花怎么养"。2.点击搜索按钮。

## 截图信息 ##
图片是当前手机所在页面的截图。为了帮助你更好地理解这张截图的内容，，我们提供了每个框选区域的补充信息。说明：
  - id:标签数字与截图中显示的数字对应。
  - 类型：文字、控件等。
  - 描述：元素的描述或文字内容。
  补充信息内容：[{dom_desc}]
**注意**：
- 这些解析信息可能存在误差，一切以截图为准！
- 若 UI 元素未在截图中找到，可能需要滑动页面或采取其他交互方式。

## 操作历史 ## 
无

——————
## 任务说明 ##
- 请基于历史操作、当前截图信息和测试进度，进行深度思考，判断当前测试进程并给出下一步的操作。  
- 仅输出一步具体操作，并确保该操作能够顺利推动测试流程，且所使用的UI元素在截图中存在。
- 请避免一次执行多个操作（例如输入和点击不能同时输出）。

### 思考步骤 ###
1. 判断测试进度
   - 结合测试用例、历史操作，确认当前进度。
   - 确保所有前置步骤已正确执行。

2. 分析当前页面
   - 结合截图，判断当前页面是否符合预期？
   - 是否出现了额外弹窗、权限请求或加载状态？（如果有，先处理这些情况）

3. 选择最佳操作
   - 在所有可选操作中，选择最稳定、最符合测试目标的操作。
   - 确保该操作可以在当前截图中执行，而不是基于假设。

4. 再次检查
   - 检查当前进度判断是否正确。
   - 检查选择的操作是否正确。
   - 再次确认选择的元素id是否正确，如果期望操作或校验的UI元素未分配id，则element输出-1。
——————

## 操作函数（仅限使用以下函数之一） ##
1. tap(element: int)
   - 用途：点击屏幕上指定的UI元素。  
   - 示例：tap(5)

2. input(element: int, text_input: str)
   - 用途：在指定输入框中输入文本。  
   - 示例：input(10, "Hello, world!")

3. swipe(direction: str)
   - 用途：滑动屏幕。  
   - 参数："up", "down", "left", "right"
   - 解释：up表示由下往上滑动，达到 向下翻页 的效果；down表示由上往下滑动，达到 向上翻页 的效果（或查看历史信息）；left表示由右向左滑动，达到 向右切换页面 的效果；right表示由左向右滑动，达到 向左切换页面 的效果。
   - 示例：swipe("up")

4. long_press(element: int)
   - 用途：长按屏幕上指定的UI元素。  
   - 示例：long_press(8)

5. assert_exist(element: int, content: str)
   - 用途：校验当前页面存在指定的UI元素。
   - 解释：content是校验目的
   - 示例：assert_exist(4, "验证存在点赞按钮")

6. assert_not_exist(content: str)
   - 用途：校验下一个页面不存在某些元素。  
   - 解释：content是校验目的
   - 示例：assert_not_exist("验证页面不存在弹窗")

7. wait(time: int)
   - 用途：等待指定秒数。建议time为5的倍数。
   - 示例：wait(5)

8. finsh(reason: str)
   - 用途：结束测试或当无法继续操作时给出理由。  
   - 示例：finsh("无法找到目标元素")

## 注意事项 ##
1. 确保所有操作基于截图中存在的元素，不要假设页面内容。
2. 每次仅能输出一个操作函数，不要合并多个操作  
3. 用例中描述的所有关键步骤必须在测试中体现，不可省略。  
4. 当遇到意外弹窗或提示框时，优先处理后再进行下一步操作。  
5. 对于校验操作，尽量选用页面上稳定且代表性的元素。  
6. 如果界面未出现目标元素，可能需要滑动页面（swipe）查找。

## 输出要求 ##
输出必须包含以下三个部分，且格式固定：

### 思考 ###
详细说明你的推理过程和判断当前测试流程所在阶段。
### 具体操作 ###
调用一个操作函数及相应参数（仅允许使用上述八个函数中的一个，且唯一），例如：tap(1)，直接输出函数，不要添加额外的符号。
### 操作描述 ###
简要描述本次操作内容，确保描述中只涉及本次单一动作，例如“在主界面点击搜索框”。
"""
    print(prompt)
    chat_message = ChatMessage()
    chat_message.add_user_message(prompt_text=prompt, image_paths=[grid_path], img_quality="auto")

    res = BigModel(model_name="qwen2.5-vl-72b-instruct").chat(chat_message=chat_message)


if __name__ == '__main__':
    img_p = "/Users/<USER>/Downloads/图片/07b8751c-b023-4f50-a9b0-1282d7920be8/0.jpg"
    instruct = "点击菜单"
    page_recognition(image_path=img_p, mrd_str=instruct)