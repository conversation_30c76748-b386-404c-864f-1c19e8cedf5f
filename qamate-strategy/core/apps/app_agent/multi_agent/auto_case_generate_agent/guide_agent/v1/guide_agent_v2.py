#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@File    : guide_agent.py
<AUTHOR> <PERSON><PERSON><PERSON><PERSON>(renya<PERSON><PERSON>@baidu.com)
@Time    : 2024/11/15 14:48
@Desc    : 
"""
import json

from apps.app_agent.knowledge_search.knowledge_search_for_text_list import KnowledgeSearchForTextList
from apps.app_agent.conf.contants import GENERATE_API_KEY
from apps.app_agent.utils.chat_message import ChatMessage
from apps.app_agent.utils.ChatGPT import ChatGPT
from apps.app_agent.multi_agent.auto_case_generate_agent.guide_agent.v1.prompt import (get_action_desc_prompt,
                                                                                       get_action_function_prompt,
                                                                                       system_prompt)
from apps.app_agent.multi_agent.base.agent import BaseChat
from basics.util import logger
from apps.app_agent.multi_agent.auto_case_generate_agent.utils.utils import mllm_guide_action_parse


class GuideAgent(BaseChat):
    """

    """

    def __init__(self, mrd_list: list, opt_mrd_list: list, mrd_desc: str, app_info: dict, batdom_record: dict,
                 execute_record: list,
                 raw_img_path: str, grid_img_path, pre_step: str, product_knowledge: list, **kwargs):
        """
        初始化
        :param mrd_list:  测试用例
        :param app_info: App信息
        :param batdom_record: batdom结果
        :param execute_record: dict
        :param raw_img_path: 当前截图
        :param pre_step: 前置步骤
        """
        super(GuideAgent, self).__init__(name="generate_guide_agent")
        self.mrd_list = mrd_list
        self.opt_mrd_list = opt_mrd_list
        self.mrd_desc = mrd_desc
        self.app_info = app_info
        self.batdom_record = batdom_record
        self.raw_img_path = raw_img_path
        self.grid_img_path = grid_img_path
        self.completed_contents = ""
        self.pre_step = pre_step
        self.product_knowledge = product_knowledge
        self.history = []
        # self.optimize_mrd = kwargs.get("optimize_mrd", "")

        self._get_history(execute_record=execute_record)

    def _get_history(self, execute_record):
        """
        得到历史有效的执行记录
        :param execute_record:
        :return:
        """
        if len(execute_record) == 0:
            return

        for item in execute_record:
            # if item.get('reflect_res_info', {}).get('reflect_flag'):
            history_item = {**item['guide_res_info'], **item['reflect_res_info'], **item['process_res_info']}
            self.history.append(history_item)
            self.completed_contents = item['process_res_info']['completed_contents']

    def param_check(self):
        """

        :return:
        """
        pass

    def prompt_message(self) -> ChatMessage:
        """
        构建prompt
        :return:
        """
        mrd_path = "->".join(self.opt_mrd_list)
        # 构建prompt
        guide_prompt = get_action_desc_prompt(
            mrd_desc=self.mrd_desc,
            mrd_path=mrd_path,
            history=self.history,
            dom_desc=self.batdom_record['dom_desc'],
            completed_contents=self.completed_contents,
            app_info=self.app_info,
            pre_step=self.pre_step,
            product_knowledge=self.product_knowledge
        )
        logger.info("guide_action_prompt: {}".format(guide_prompt))
        img_paths = [self.grid_img_path]
        logger.info("图片为：{}".format(img_paths))

        guide_system = system_prompt()
        chat_message = ChatMessage(system_message=guide_system)
        chat_message.add_user_message(prompt_text=guide_prompt, image_paths=img_paths, img_quality="auto")
        return chat_message

    def format_answer(self, llm_ans) -> dict:
        """
        解析大模型的回答
        :param llm_ans:
        :return:
        """
        thought = llm_ans.split("### 思考 ###")[-1].split("### 操作描述 ###")[0].replace(" ", "").strip()
        action_desc = llm_ans.split("### 操作描述 ###")[-1].split("### 对应测试用例 ###")[0].replace(" ", "").strip()
        mrd_item = llm_ans.split("### 对应测试用例 ###")[-1].replace(" ", "").strip().split("->")[-1]

        # 调整 对应测试用例
        mrd_item = self._map_lm_out_mrd_item(mrd_item)

        res = {
            "thought": thought,
            "action_desc": action_desc,
            "mrd_item": mrd_item
        }
        return res

    def _map_lm_out_mrd_item(self, model_mrd_item) -> str:
        """
        输出映射
        :param model_mrd_item:
        :return:
        """

        if len(self.mrd_list) == 1:
            return self.mrd_list[0]

        new_mrd_retrieve = KnowledgeSearchForTextList(self.mrd_list)
        retrieve_res = new_mrd_retrieve.knowledge_search_for_text_list(query=model_mrd_item, top_k=1,
                                                                       score_threshold=-1.0)
        map_res = retrieve_res[0]['document'].page_content

        return map_res

    def _is_correct_element_id(self, action: str):
        """
        是否是预期内的操作元素
        :param action:
        :return:
        """

        action_res = mllm_guide_action_parse(action)

        if action_res['action'] in ("tap", "input", "long_press") and action_res['element_id'] == -1:
            return False
        if action_res['error']:
            return False
        return True

    def guide_action_desc(self):
        """
        生成动作描述
        :return:
        """
        # 构建prompt
        chat_message = self.prompt_message()

        # 生成
        llm_ans = ChatGPT(api_key=GENERATE_API_KEY).chat(chat_message=chat_message, temperature=0.2)

        # 结果解析
        act_desc_ans = self.format_answer(llm_ans=llm_ans)

        chat_message.add_assistant_message(prompt_text=llm_ans)

        return chat_message, act_desc_ans

    def guide_action_function(self, act_desc_ans):
        """
        生成具体的动作
        :param act_desc_ans:
        :return:
        """

        flag = False

        act_prompt = get_action_function_prompt(dom_desc=self.batdom_record['dom_desc'],
                                                action_desc=act_desc_ans['action_desc'],
                                                action_thought=act_desc_ans['thought'])
        logger.info("action_function_prompt: {}".format(act_prompt))
        img_paths = [self.grid_img_path]
        logger.info("图片为：{}".format(img_paths))

        chat_message = ChatMessage()
        chat_message.add_user_message(prompt_text=act_prompt, image_paths=img_paths, img_quality="auto")

        count = 0
        while count < 2:
            # 生成
            llm_ans = ChatGPT(api_key=GENERATE_API_KEY).chat(chat_message=chat_message, temperature=0.2,
                                                             response_format='json_object')

            action_result = json.loads(llm_ans)

            action = action_result['content']

            if self._is_correct_element_id(action):
                flag = True
                break
            else:
                count += 1
                logger.warning("预期外的操作:{}，重新生成具体的操作函数信息".format(action))

        return flag, action

    def main(self):
        """

        :return:
        """
        # 参数校验
        # self.param_check()

        count = 0
        while count < 3:
            # 生成动作描述
            chat_message, act_desc_ans = self.guide_action_desc()

            # 生成具体动作
            flag, action = self.guide_action_function(act_desc_ans=act_desc_ans)

            act_desc_ans['action'] = action
            if flag is True:
                break
            else:
                count += 1
                logger.warning("生成非预期的操作，重新生成")

        res = {
            "agent_res": act_desc_ans,
            "message": chat_message
        }

        return res


if __name__ == '__main__':
    pass

