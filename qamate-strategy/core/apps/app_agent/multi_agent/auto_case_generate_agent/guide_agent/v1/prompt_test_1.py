#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@File    : prompt.py
<AUTHOR> <PERSON><PERSON><PERSON><PERSON>(renya<PERSON><PERSON>@baidu.com)
@Time    : 2024/11/15 14:48
@Desc    : 
"""
import json
from typing import List


def system_prompt():
    """

    :return:
    """
    sys_prompt = """你是一个具有App自动化测试经验的AI手机操作助手，你需要帮我操作手机并且完成页面的功能点验证。
你拥有基本的自动化测试常识，例如：在输入操作后需要跟确认、完成、发送、搜索等操作；设备执行过程出现意料之外的弹窗、提示框、权限弹窗等需要先点除；菜单或设置页面可能需要滑动查找某些功能等。"""
    return sys_prompt


def get_action_prompt(mrd_desc: str, mrd_path: str, app_info: dict, history: List[dict], dom_desc: str,
                      error_info: str,
                      product_knowledge: List[dict], completed_contents="", pre_step=""):
    """

    :param mrd_desc:
    :param mrd_path:
    :param app_info:
    :param history:
    :param dom_desc:
    :param product_knowledge:
    :param completed_contents:
    :param pre_step:
    :return:
    """

    # App简介
    if app_info:
        app_info_str = "当前正在测试的App为{}，{}".format(app_info['name'], app_info['desc'])
    else:
        app_info_str = ""

    # 页面元素
    if dom_desc:
        dom_desc_str = "为了帮助你更好地理解当前设备截图的内容，我们通过其他能力得到每个框选元素的补充信息供你参考，标签是元素数字标签与图片中的数字标签相对应，类型表示元素类型，描述是元素的描述或文字内容\n"
        dom_desc_str += dom_desc
        dom_desc_str += "\n这些信息不一定准确，仅供参考，一切以截图为准!\n"
    else:
        dom_desc_str = ""

    # 截图信息
    if len(history) == 0:
        image_str = f"最后一张图片是当前手机所在页面的截图。{dom_desc_str}"
    else:
        image_str = (f"最后一张图片是当前手机所在页面的截图，既当前需要操作的界面。前面的图片是历史操作的截图，历史截图中如果有红框，"
                     f"则表示操作的点击、输入或校验位置，如果有箭头，则表示页面滑动操作中手指在设备的滑动轨迹。\n{dom_desc_str}")

    # 操作历史
    if len(history) == 0:
        operation_history_str = ""
        if pre_step:
            operation_history_str += "## 操作历史 ## \n前置已完成操作：{}\n前置步骤均已执行，后续步骤均在前置操作后顺序执行。\n".format(
                pre_step)
    else:
        operation_history_str = "## 操作历史 ## \n为了完成用户指令的要求，已经执行了一系列操作，实际操作内容即为之前输出的操作描述。这些操作信息如下：\n"
        if pre_step:
            operation_history_str += "前置已完成操作：{}\n前置步骤均已执行，后续步骤均在前置操作后顺序执行。\n".format(
                pre_step)
        for idx, item in enumerate(history):
            reflect_str = item["reflect_cont"] if not item["reflect_flag"] else "操作正确"
            operation_history_str += "步骤 {}: [·实际操作：{};\n·操作后的思考：{}]\n".format(idx + 1,
                                                                                           item['action_desc'],
                                                                                           reflect_str)
        operation_history_str += "请注意：对于已经正确完成的操作历史，本次请勿重复生成!\n"

        if error_info:
            error_info_str = f"你此前在当前页面进行了错误的操作:{error_info}，我已经撤回本次错误操作，你需要在本次执行中进行修正"
            operation_history_str += error_info_str

    # 进度
    if not completed_contents:
        completed_contents_str = ""
    else:
        completed_contents_str = "## 执行进度 ##\n{}\n".format(completed_contents)

    # # 错误反馈
    # if len(history) > 0 and not history[-1]['reflect_flag']:
    #     reflect_str = "最近一步操作反思：{}\n关于你的上一步操作反思仅供参考，如果操作错误需在这次操作中进行修正".format(
    #         history[-1]['reflect_flag'])
    #     completed_contents_str += reflect_str

    if product_knowledge:
        product_hint = "## 提示 ##\n这里有一些业务术语可能会对你理解执行case有所帮助，提示如下：\n"
        for item in product_knowledge:
            product_hint += "{}：{}\n".format(item['scene_words'], item['text_desc'])
    else:
        product_hint = ""

    prompt = f"""## 背景 ##
你需要协助完成一项手机App测试任务。{app_info_str}
你需要根据我提供的信息，一步步的帮助我操作手机，完成整个测试用例，本次操作是整体测试的其中一步。

## 测试用例 ##
- 测试用例描述：【{mrd_desc}】
- 测试用例大致执行路径：【{mrd_path}】

注意：
1. 测试用例路径为大致路径，可能不是完整步骤，但是用例中提到的执行步骤都需要执行，不能遗漏，具体要如何操作需要参考当前页面截图。
2. 测试路径中的一个步骤，实际可能需要多步操作。例如：测试步骤为：搜索"兰花怎么养"，实际的操作会分为：1.在搜索框输入"兰花怎么养"，2.点击搜索按钮。

{operation_history_str}

## 截图信息 ##
{image_str}

{completed_contents_str}
{product_hint}
## 任务说明 ##
你需要一步步思考，按要求给出下一步的具体操作信息，仅给出一步操作，不要给出基于当前操作截图无法完成的操作。

## 思考提示 ##
1. 首先，你需要根据历史操作、当前截图、执行进度判断出当前进行到了整个测试流程中的哪一步了。
2. 然后，基于你得到的信息给出下一步需要执行什么操作来正确推动测试继续进行。
3. 最后，按照输出要求，输出最后的结果。

&& 操作函数 &&
你可以使用以下操作函数继续执行任务：
1. tap(element: int)
这个函数用于点击智能手机屏幕上显示的UI元素。
例如：tap(5)，表示点击标有数字5的UI元素。

2. input(element: int, text_input: str)
这个函数用于文本输入。需确保input函数中的element对应的是输入框或搜索框元素。
例如：input(10, "Hello, world!")，表示在10元素的输入框中上输入"Hello, world"。

3. swipe(direction: str)
这个函数用于滑动智能手机屏幕，表示手指在屏幕上的滑动轨迹。默认为滑动半个屏幕的距离。
"direction" 是表示四个方向之一的字符串：up, down, left, right。"direction" 必须用双引号括起来。
up表示由下往上滑动，达到 向下翻页 的效果；down表示由上往下滑动，达到 向上翻页 的效果（或查看历史信息）；
left表示由右向左滑动，达到 向右切换页面 的效果；right表示由左向右滑动，达到 向左切换页面 的效果。
例如：swipe("up")，表示向下翻页。

4. long_press(element: int)
这个函数用于长按智能手机屏幕上显示的UI元素。
例如：long_press(8), 表示长按标有数字8的UI元素。

5. assert_exist(element: int, content: str)
根据测试用例，需要在本页面需要校验『存在』的元素，只校验测试用例中提到的必要的校验点。也可以是校验某项功能或特殊场景等，例如：校验**功能正常，此时element置为-1。
"element" 明确要在此页面校验存在的元素id。如果需要验证**页面，可以用验证页面中稳定的关键字替代，通常是页面title、标签等。
"content" 是校验目的，如果element != -1，需要体现出具体的元素信息。
例如：assert_exist(4, "验证存在点赞按钮")，需要验证当前页面需要元素4存在。assert_exist(-1, "校验视频正常播放")，表示验证当前视频正常播放。

6. assert_not_exist(content: str)
根据测试用例，需要在下一个页面校验『不存在』。只校验测试用例中提到的必要的校验点。
"content"是校验目的。
例如：assert_not_exist("验证页面不存在弹窗")，需要校验下一个页面不应该存在弹窗。

7. wait(time: int)
表示等待操作，常用于页面尚有操作未执行完成，需要等待一定的时间。
time是等待时长，单位为秒，建议是5或5的倍数。
例如：wait(5) 表示等待5s。

8. finsh(reason: str)
表示已经完成全部测试; 或基于当前页面，无法给出具体的操作，或者多次操作都无法达到预期目标。
"reason": 原因

## 注意事项 ##
1. 一定需要根据当前截图并结合历史操作，可能需要交互很多次才能达成最终的目标，本次对话是其中一次交互。
2. 不要给出基于当前截图无法完成的动作，给出的操作元素需要在截图中存在，这点尤为重要。
3. 如果原始用例中明确用""等标注的关键字，生成步骤时需注意生成的正确性。
4. 忽略原始用例中与自动化执行无关的描述或汇总性内容，专注于测试目标。
5. 当页面中有多个可备选的element时，选取最为稳定(不经常变化，最好为图标)的element来保证自动化测试的稳定性。
6. 操作执行的过程中可能出现很多意料之外的弹窗、提示框、权限弹窗等，你需要先进行点除，然后进行后续的操作。
7. 自动化测试最后一步通常是校验步骤，如果是校验页面可以用页面中的某个不经常变化的、具有代表性的固定元素代替。
8. 在一些上下滑动的场景，当页面没有你需要的目标时，可能需要滑动页面，查找元素。例如：对话页、设置页、列表页等。
9. 每次仅能输出一个具体操作，不要输出两个，例如：输入"hello"点击发送，就属于两个动作，是不被允许的。
10. 用例中提到的执行步骤都需要在【具体操作】中体现。
例如：用例为1.打开App -> 2. 输入hello -> 3. 校验展示整成。
当执行完1后，页面截图默认已经显示了输入hello的信息，但是这并不我们本次操作输入的，因此我们的测试需要按用例顺序，输入hello，不可以直接跳到执行步骤3的用例。

## 再次强调 ##
你需要一步步思考，每次仅给出一步具体的操作，需确保给出的操作元素需要在截图中存在，且不要重复生成上一步已经成功执行的操作。

## 输出要求 ##
你的输出固定的包括以下四个部分，格式如下：
### 思考 ###
给出你的思考过程。
### 具体操作 ###
调用适当的操作函数及参数继续任务。每次仅能生成单个动作。只能从上述『操作函数』中的8个选择一个，不要包含其他内容或格式，输出操作函数即可，例如：tap(1)。
### 操作描述 ###
简要描述本次需要的操作内容，仅能输出一个动作。例如：在主对话页，点击**（或者校验了***；或向*方向滑动了）。如果有具体的操作控件元素，输出操作元素在页面的大致位置。
### 对应测试用例 ###
上述操作或验证是对应到[测试用例]哪个节点，只能输出一个节点(输出全称)。例如原始输出的测试用例为 A -> B -> C, 上述操作是针对完成 B 描述的，则输出 B。"""
    return prompt


def check_element_id_prompt(element_id: int, action_desc: str, action_thought, app_info: dict, dom_desc: str):
    """
    校验元素检测prompt
    :param element_id:
    :param action_desc:
    :return:
    """

    if app_info:
        app_info_str = "当前正在测试的App为{}，{}".format(app_info['name'], app_info['desc'])
    else:
        app_info_str = ""

    if dom_desc:
        dom_desc_str = ("为了帮助你更好地理解这张截图的内容，我们通过其他能力得到每个框选元素的补充信息供你参考，"
                        "标签是元素数字标签与图片中的数字标签相对应，类型表示元素类型，描述是元素的描述或文字内容。所有框出的元素都可能是可交互元素，文字类型也可能是文字控件元素\n")
        dom_desc_str += dom_desc
        dom_desc_str += "\n这些信息不一定准确，仅供参考，你需要结合截图来理解!!\n"
    else:
        dom_desc_str = ""

    prompt = f"""&& 角色 &&
你是一个具有丰富App自动化测试经验的测试智能助手。{app_info_str}

&& 背景 &&
我正在操作手机完成自动化测试，当前为自动化测试过程中的一步操作。

&& 任务 &&
你需要根据[[操作思考]、[操作描述]和[截图]判断我操作的元素id是否正确，如果不正确，给出正确的元素id。

图片信息为当前页面截图，截图中的交互式UI元素都用数字标签标记(从0开始)，每个交互元素用红色框框选出，数字标签都位于元素的左上角。
{dom_desc_str}

操作思考：【{action_thought}】
操作描述：【{action_desc}】
操作元素标签id：【{element_id}】
"""
    output = """
&& 输出格式 &&
你的输出固定为以下两部分，不要输出其他内容!! 确保以合法的json格式输出：
{
    "result": bool // 一个布尔值，表示输入的操作元素id是否正确
    "correct_element_id": int // 给出正确的元素id，如果输入的操作元素id是正确的，返回输入的id即可；如果输入的id是错误的，给出修正后正确的元素id
    "reason": string // 给出原因、思考过程。
}
"""
    return prompt + output


def get_action_desc_prompt(mrd_desc: str, mrd_path: str, app_info: dict, history: List[dict], dom_desc: str,
                           product_knowledge: List[dict], completed_contents="", pre_step=""):
    """

    :param mrd_desc:
    :param mrd_path:
    :param app_info:
    :param history:
    :param dom_desc:
    :param product_knowledge:
    :param completed_contents:
    :param pre_step:
    :return:
    """

    # App简介
    if app_info:
        app_info_str = "当前正在测试的App为{}，{}".format(app_info['name'], app_info['desc'])
    else:
        app_info_str = ""

    # 页面元素
    if dom_desc:
        dom_desc_str = "为了帮助你更好地理解这张截图的内容，我们通过其他能力得到每个框选元素的补充信息供你参考，标签是元素数字标签与图片中的数字标签相对应，类型表示元素类型，描述是元素的描述或文字内容\n"
        dom_desc_str += dom_desc
        dom_desc_str += "\n这些信息不一定准确，仅供参考，一切以截图为准!\n"
    else:
        dom_desc_str = ""

    # 操作历史
    if len(history) == 0:
        operation_history_str = ""
        if pre_step:
            operation_history_str += "## 操作历史 ## \n前置已完成操作：{}\n前置步骤均已执行，后续步骤均在前置操作后顺序执行。\n".format(
                pre_step)
    else:
        operation_history_str = "## 操作历史 ## \n为了完成用户指令的要求，已经执行了一系列操作，实际操作内容即为之前输出的操作描述。这些操作信息如下：\n"
        if pre_step:
            operation_history_str += "前置已完成操作：{}\n前置步骤均已执行，后续步骤均在前置操作后顺序执行。\n".format(
                pre_step)
        for idx, item in enumerate(history):
            reflect_str = item["reflect_cont"] if not item["reflect_flag"] else "操作正确"
            operation_history_str += "步骤 {}: [·实际操作：{};\n·操作后的思考：{}]\n".format(idx + 1,
                                                                                           item['action_desc'],
                                                                                           reflect_str)
        operation_history_str += "请注意：对于已经正确完成的操作历史，本次请勿重复生成!\n"

    # 进度
    if not completed_contents:
        completed_contents_str = ""
    else:
        completed_contents_str = "## 执行进度及反思 ##\n根据历史操作，进度思考：{}\n".format(completed_contents)

    # # 错误反馈
    # if len(history) > 0 and not history[-1]['reflect_flag']:
    #     reflect_str = "最近一步操作反思：{}\n关于你的上一步操作反思仅供参考，如果操作错误需在这次操作中进行修正".format(
    #         history[-1]['reflect_flag'])
    #     completed_contents_str += reflect_str

    if product_knowledge:
        product_hint = "## 提示 ##\n这里有一些业务术语可能会对你理解执行case有所帮助，提示如下：\n"
        for item in product_knowledge:
            product_hint += "{}：{}\n".format(item['scene_words'], item['text_desc'])
    else:
        product_hint = ""

    prompt = f"""## 背景 ##
你需要协助完成一项手机App测试任务。{app_info_str}
你需要根据我提供的信息，一步步的帮助我操作手机，完成整个测试用例，本次操作是整体测试的其中一步。

## 测试用例 ##
测试用例描述：【{mrd_desc}】
测试用例大致执行路径：【{mrd_path}】
测试用例路径为大致路径，可能不是完整步骤，但是用例中提到的执行步骤都需要执行，不能遗漏，具体要如何操作需要参考当前页面截图。
例如：测试路径为：A -> B -> C，实际的执行路径为：A -> B -> B' -> C。
测试路径中的一个步骤，实际可能需要多步操作。例如：测试步骤为：搜索"兰花怎么养"，实际的操作会分为：1.在搜索框输入"兰花怎么养"，2.点击搜索按钮。

## 截图信息 ##
图片是当前手机所在页面的截图。{dom_desc_str}
{operation_history_str}
{completed_contents_str}
{product_hint}
## 任务说明 ##
你需要一步步思考，按要求给出下一步的具体操作信息，仅给出一步操作，不要给出基于当前操作截图无法完成的操作。

## 思考提示 ##
1. 首先，你需要根据历史操作、执行进度及反思判断出当前进行到了整个测试流程中的哪一步了。
2. 然后，基于你得到的信息给出下一步需要执行什么操作来正确推动测试继续进行。
3. 最后，按照输出要求，输出最后的结果。

你可以进行的操作类型有：点击、输入、滑动、长按、校验、等待。对于具体的操作控件要给出在页面中的大致位置。
点击：表示你要点击的元素，例如：点击页面左下角的点赞按钮。
输入：表示你要在哪里输入什么内容，例如：在页面下方的输入框中输入"你好"。
滑动：表示滑动页面的手势，例如：由下往上滑动页面(达到 页面下滑 的效果),由右向左滑动(达到 页面右滑 的效果)。
长按：表示你要长按的元素，例如：长按页面左下角的图片。
校验：表示你要校验什么，例如：验证菜单中存在点赞按钮 或 验证当前视频正常播放 或 验证页面中不存在点赞按钮。
等待：表示等待操作，常用于页面尚有操作未执行完成，需要等待一定的时间，建议单次等待时长为5秒或5的倍数。

## 注意事项 ##
1. 一定需要根据当前截图并结合历史操作，可能需要交互很多次才能达成最终的目标，本次对话是其中一次交互。
2. 不要给出基于当前截图无法完成的动作，给出的操作元素需要在截图中存在，这点尤为重要。
3. 如果原始用例中明确用""等标注的关键字，生成步骤时需注意生成的正确性。
4. 忽略原始用例中与自动化执行无关的描述或汇总性内容，专注于测试目标。
5. 当页面中有多个可备选的element时，选取最为稳定(不经常变化，最好为图标)的element来保证自动化测试的稳定性。
6. 操作执行的过程中可能出现很多意料之外的弹窗、提示框、权限弹窗等，你需要先进行点除，然后进行后续的操作。
7. 自动化测试最后一步通常是校验步骤，如果是校验页面可以用页面中的某个固定元素代替。
8. 在一些上下滑动的场景，当页面没有你需要的目标时，可能需要滑动页面，查找元素。例如：对话页、设置页、列表页等。
9. 每次仅能输出一个具体操作，不要输出两个，例如：输入"hello"点击发送，就属于两个动作，是不被允许的。
10. 用例中提到的执行步骤都需要在【具体操作】中体现。
这里列出一个易错点：
例如：用例为1.打开App -> 2. 输入hello -> 3. 校验展示整成。
当执行完1后，页面截图默认已经显示了输入hello的信息，但是这并不我们本次操作输入的，因此我们的测试需要按用例顺序，输入hello，不可以直接跳到执行步骤3的用例。

## 再次强调 ##
你需要一步步思考，每次仅给出一步具体的操作，需确保给出的操作元素需要在截图中存在，且不要重复生成上一步已经成功执行的操作。

## 输出要求 ##
你的输出固定的包括以下四个部分，格式如下：
### 思考 ###
给出你的思考过程。
### 操作描述 ###
简要描述本次需要的操作内容，仅能输出一个动作。例如：在主对话页，点击**（或者校验了***；或向*方向滑动了）。如果有具体的操作控件元素，需确保给出的操作元素需要在截图中存在，且需要输出操作元素在页面的大致位置。
### 对应测试用例 ###
上述操作或验证是对应到[测试用例]哪个节点，只能输出一个节点(输出全称)。例如原始输出的测试用例为 A -> B -> C, 上述操作是针对完成 B 描述的，则输出 B。"""
    return prompt


def get_action_function_prompt(dom_desc: str, action_desc: str, action_thought: str):
    """

    :param dom_desc:
    :param action_desc:
    :param action_thought:
    :return:
    """
    # 页面元素
    if dom_desc:
        dom_desc_str = "为了帮助你更好地理解这张截图的内容，我们通过其他能力得到每个框选元素的补充信息供你参考，标签是元素数字标签与图片中的数字标签相对应，类型表示元素类型，描述是元素的描述或文字内容\n"
        dom_desc_str += dom_desc
        dom_desc_str += "\n这些信息不一定准确，仅供参考，一切以截图为准!\n"
    else:
        dom_desc_str = ""

    prompt = f"""
&& 你的角色 &&
你是一个具有App自动化测试经验的AI手机操作助手

&& 你的任务 &&
你需要根据【操作描述】给出具体的操作函数和参数内容。【操作思考】的内容仅辅助你理解，不一定准确。如果想要点击的UI元素在页面中不存在或没有分配标签，则id输出 -1。
图片是当前手机所在页面的截图，截图中的交互式UI元素都用数字标签标记(从0开始)，每个交互元素用红色框框选出，数字标签都位于元素的左上角。
{dom_desc_str}

&& 操作函数 &&
你可以使用以下操作函数继续执行任务：
1. tap(element: int)
这个函数用于点击智能手机屏幕上显示的UI元素。
例如：tap(5)，表示点击标有数字5的UI元素。

2. input(element: int, text_input: str)
这个函数用于文本输入。需确保input函数中的element对应的是输入框或搜索框元素。
例如：input(10, "Hello, world!")，表示在10元素的输入框中上输入"Hello, world"。

3. swipe(direction: str)
这个函数用于滑动智能手机屏幕，表示手指在屏幕上的滑动轨迹。默认为滑动半个屏幕的距离。
"direction" 是表示四个方向之一的字符串：up, down, left, right。"direction" 必须用双引号括起来。
up表示由下往上滑动，达到 向下翻页 的效果；down表示由上往下滑动，达到 向上翻页 的效果（或查看历史信息）；
left表示由右向左滑动，达到 向右切换页面 的效果；right表示由左向右滑动，达到 向左切换页面 的效果。
例如：swipe("up")，表示向下翻页。

4. long_press(element: int)
这个函数用于长按智能手机屏幕上显示的UI元素。
例如：long_press(8), 表示长按标有数字8的UI元素。

5. assert_exist(element: int, content: str)
根据测试用例，需要在本页面需要校验『存在』的元素，只校验测试用例中提到的必要的校验点。也可以是校验某项功能或特殊场景等，例如：校验**功能正常，此时element置为-1。
"element" 明确要在此页面校验存在的元素id。如果需要验证**页面，可以用验证页面中稳定的关键字替代，通常是页面title、标签等。
"content" 是校验目的，如果element != -1，需要体现出具体的元素信息。
例如：assert_exist(4, "验证存在点赞按钮")，需要验证当前页面需要元素4存在。assert_exist(-1, "校验视频正常播放")，表示验证当前视频正常播放。

6. assert_not_exist(content: str)
根据测试用例，需要在下一个页面校验『不存在』。只校验测试用例中提到的必要的校验点。
"content"是校验目的。
例如：assert_not_exist("验证页面不存在弹窗")，需要校验下一个页面不应该存在弹窗。

7. wait(time: int)
表示等待操作，常用于页面尚有操作未执行完成，需要等待一定的时间。
time是等待时长，单位为秒，建议是5或5的倍数。
例如：wait(5) 表示等待5s。

&& 操作信息 &&
操作思考：【{action_thought}】
操作描述：【{action_desc}】

&& 注意 &&
1. 当页面存在多个目标元素时，选择最符合操作描述的元素（根据位置等提示）。
2. 生成的操作函数需要与操作描述的首个操作相符。例如：操作描述为"验证页面中点赞数，点击无变化"，应该生成的函数为assert_exist()而不是tap()。
"""
    output = """
&& 输出 &&
结果以json的格式输出，每次仅输出一个动作：
{
    "content": str  // 给出具体的操作函数和参数内容，每次仅能生成单个操作函数。
}
"""
    return prompt + output


if __name__ == '__main__':
    text = "月光下的美女与野兽"
    encoded_text = json.dumps(text)
    print(encoded_text)
