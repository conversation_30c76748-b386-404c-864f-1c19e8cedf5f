#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@File    : guide_check_element_agent.py
<AUTHOR> <PERSON><PERSON><PERSON><PERSON>(renya<PERSON><PERSON>@baidu.com)
@Time    : 2024/11/19 17:17
@Desc    : 
"""

# !/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@File    : guide_agent.py
<AUTHOR> <PERSON><PERSON><PERSON><PERSON>(renya<PERSON><PERSON>@baidu.com)
@Time    : 2024/11/15 14:48
@Desc    : 
"""
import json

from apps.app_agent.conf.contants import GENERATE_API_KEY
from apps.app_agent.utils.chat_message import ChatMessage
from apps.app_agent.utils.ChatGPT import ChatGPT
from apps.app_agent.multi_agent.auto_case_generate_agent.guide_agent.v1.prompt import check_element_id_prompt
from apps.app_agent.multi_agent.base.agent import BaseChat
from basics.util import logger


class GuideCheckElementAgent(BaseChat):
    """

    """

    def __init__(self, app_info: dict, element_id, action_desc, grid_img_path):
        """
        init
        :param app_info:
        :param element_id:
        :param action_desc:
        """
        super().__init__(name="guide_element_check_agent")
        self.app_info = app_info
        self.element_id = element_id
        self.action_desc = action_desc
        self.grid_img_path = grid_img_path
        # self.optimize_mrd = kwargs.get("optimize_mrd", "")

    def param_check(self):
        """

        :return:
        """
        pass

    def prompt_message(self) -> ChatMessage:
        """
        构建prompt
        :return:
        """
        check_prompt = check_element_id_prompt(element_id=self.element_id, action_desc=self.action_desc,
                                               app_info=self.app_info, )
        logger.info("check_action_element_prompt: {}".format(check_prompt))
        check_message = ChatMessage()
        check_message.add_user_message(prompt_text=check_prompt, image_paths=[self.grid_img_path], img_quality="auto")
        return check_message

    def format_answer(self, llm_ans) -> dict:
        """
        解析大模型的回答
        :param llm_ans:
        :return:
        """
        res = json.loads(llm_ans)
        return res

    def main(self):
        """

        :return:
        """
        # 参数校验
        # self.param_check()

        # 构建prompt
        check_message = self.prompt_message()

        # 生成
        llm_ans = ChatGPT(api_key=GENERATE_API_KEY).chat(chat_message=check_message, temperature=0.2)

        # 结果解析
        res = self.format_answer(llm_ans=llm_ans)

        res = {
            "agent_res": res,
            "message": check_message
        }

        return res
