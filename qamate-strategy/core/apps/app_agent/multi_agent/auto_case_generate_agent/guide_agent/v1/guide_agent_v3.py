#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@File    : guide_agent.py
<AUTHOR> <PERSON><PERSON><PERSON><PERSON>(renya<PERSON><PERSON>@baidu.com)
@Time    : 2024/11/15 14:48
@Desc    :
"""
import json

from apps.app_agent.knowledge_search.knowledge_search_for_text_list import KnowledgeSearchForTextList
from apps.app_agent.conf.contants import GENERATE_API_KEY
from apps.app_agent.entity.generate import GuideRes
from apps.app_agent.utils.chat_message import ChatMessage
from apps.app_agent.utils.ChatGPT import ChatGPT
from apps.app_agent.multi_agent.auto_case_generate_agent.guide_agent.v1.prompt_3 import (get_action_prompt,
                                                                                         system_prompt,
                                                                                         check_element_id_prompt)
from apps.app_agent.multi_agent.base.agent import BaseChat
from basics.util import logger
from apps.app_agent.multi_agent.auto_case_generate_agent.utils.utils import mllm_guide_action_parse


class GuideAgent(BaseChat):
    """

    """

    def __init__(self, mrd_list: list, opt_mrd_list: list, app_info: dict, batdom_record: dict,
                 execute_record: list, completed_contents: str, error_info: str,
                 raw_img_path: str, grid_img_path, pre_step: str, product_knowledge: list):
        """
        初始化
        :param mrd_list:  测试用例
        :param app_info: App信息
        :param batdom_record: batdom结果
        :param execute_record: dict
        :param raw_img_path: 当前截图
        :param pre_step: 前置步骤
        """
        super(GuideAgent, self).__init__(name="generate_guide_agent")
        self.mrd_list = mrd_list
        self.opt_mrd_list = opt_mrd_list
        self.app_info = app_info
        self.batdom_record = batdom_record
        self.raw_img_path = raw_img_path
        self.grid_img_path = grid_img_path
        self.completed_contents = completed_contents
        self.pre_step = pre_step
        self.product_knowledge = product_knowledge
        self.error_info = error_info
        self.error_action = ""
        self.history = []
        self.check_element = True
        # self.optimize_mrd = kwargs.get("optimize_mrd", "")

        self._get_history(execute_record=execute_record)
        self._get_error_action(execute_record=execute_record)

    def _get_history(self, execute_record):
        """
        得到历史有效的执行记录
        :param execute_record:
        :return:
        """
        if len(execute_record) == 0:
            return

        for item in execute_record:
            # if item.get('reflect_res_info', {}).get('reflect_flag'):
            history_item = {**item['guide_res_info'], **item['reflect_res_info'], **item['process_res_info']}
            history_item['exec_img_path'] = item['exec_img_path']
            self.history.append(history_item)
            self.completed_contents = item['process_res_info']['completed_contents']

    def _get_error_action(self, execute_record):
        """

        :return:
        """
        if not self.error_info:
            return

        self.error_action = "操作描述：{}".format(execute_record[-1]['guide_res_info']['action_desc'])

    def param_check(self):
        """

        :return:
        """
        pass

    def prompt_message(self) -> ChatMessage:
        """
        构建prompt
        :return:
        """
        mrd_path = "->".join(self.opt_mrd_list)

        # 构建prompt
        guide_prompt = get_action_prompt(
            mrd_path=mrd_path,
            history=self.history,
            dom_desc=self.batdom_record['dom_desc'],
            completed_contents=self.completed_contents,
            app_info=self.app_info,
            product_knowledge=self.product_knowledge,
            pre_step=self.pre_step
        )
        logger.info("guide_action_prompt: {}".format(guide_prompt))

        if len(self.history) > 0:
            pre_step_exec_img = self.history[-1]['exec_img_path']
            img_paths = [pre_step_exec_img, self.grid_img_path]
        else:
            img_paths = [self.grid_img_path]
        logger.info("对应的图片为：{}".format(img_paths))

        guide_system = system_prompt()
        chat_message = ChatMessage(system_message=guide_system)
        chat_message.add_user_message(prompt_text=guide_prompt, image_paths=img_paths, img_quality="auto")
        return chat_message

    def format_answer(self, llm_ans) -> dict:
        """
        解析大模型的回答
        :param llm_ans:
        :return:
        """
        thought = llm_ans.split("### 思考 ###")[-1].split("### 具体操作 ###")[0].replace(" ", "").strip()
        action = llm_ans.split("### 具体操作 ###")[-1].split("### 操作描述 ###")[0].replace(" ", "").strip()
        action_desc = llm_ans.split("### 操作描述 ###")[-1].split("### 对应测试用例 ###")[0].replace(" ", "").strip()
        mrd_item = llm_ans.split("### 对应测试用例 ###")[-1].replace(" ", "").strip().split("->")[-1]

        # 调整 对应测试用例
        mrd_item = self._map_lm_out_mrd_item(mrd_item)

        res = GuideRes(thought=thought, action=action, action_desc=action_desc, mrd_item=mrd_item, summary="")
        return res.to_dict()

    def check_element_id(self, action_desc: str, action_thought: str, element_id: int):
        """
        校验元素id是否正确
        :param action_desc:
        :param element_id:
        :return:
        """

        check_prompt = check_element_id_prompt(element_id=element_id, action_desc=action_desc,
                                               action_thought=action_thought,
                                               app_info=self.app_info, dom_desc=self.batdom_record['dom_desc'])
        logger.info("check_action_element_prompt:\n {}".format(check_prompt))
        check_message = ChatMessage()
        check_message.add_user_message(prompt_text=check_prompt, image_paths=[self.grid_img_path], img_quality="auto")

        llm_check_res = ChatGPT(api_key=GENERATE_API_KEY).chat(chat_message=check_message, temperature=0.2,
                                                               response_format='json_object')
        check_res = json.loads(llm_check_res)
        correct_ele_id = check_res["correct_element_id"]

        if not isinstance(correct_ele_id, int) or correct_ele_id < 0:
            correct_ele_id = element_id

        return correct_ele_id

    def check(self, f_ans):
        """

        :return:
        """
        retry_count = 2

        action_res = mllm_guide_action_parse(f_ans['action'])

        if action_res['element_id'] == -1:
            return

        for i in range(retry_count):
            correct_ele_id = self.check_element_id(action_desc=f_ans['action_desc'],
                                                   action_thought=f_ans['thought'],
                                                   element_id=action_res['element_id'])
            if action_res['element_id'] == correct_ele_id:
                logger.info("操作元素id{}，符合预期".format(action_res['element_id']))
                break
            else:
                if i == retry_count - 1:
                    f_ans['action'] = f_ans['action'].replace(str(action_res['element_id']), str(correct_ele_id))
                    logger.info("操作元素id由 {} 修正为 {}".format(action_res['element_id'], correct_ele_id))
                else:
                    logger.info("检测到操作元素错误，原始id为{}，检测id为{}，为保证正确再次进行错误检测".format(
                        action_res['element_id'], correct_ele_id))
                    continue

    def _map_lm_out_mrd_item(self, model_mrd_item) -> str:
        """
        输出映射
        :param model_mrd_item:
        :return:
        """

        if len(self.mrd_list) == 1:
            return self.mrd_list[0]

        new_mrd_retrieve = KnowledgeSearchForTextList(self.mrd_list)
        retrieve_res = new_mrd_retrieve.knowledge_search_for_text_list(query=model_mrd_item, top_k=1,
                                                                       score_threshold=-1.0)
        map_res = retrieve_res[0]['document'].page_content

        return map_res

    def _is_correct_element_id(self, action: str):
        """
        是否是预期内的操作元素
        :param action:
        :return:
        """

        action_res = mllm_guide_action_parse(action)

        if action_res['action'] in ("tap", "input", "long_press") and action_res['element_id'] == -1:
            return False
        if action_res['error']:
            return False
        return True

    def main(self):
        """
        生成动作描述
        :return:
        """
        # 参数校验
        # self.param_check()

        # 构建prompt
        chat_message = self.prompt_message()

        # 生成
        llm_ans = ChatGPT(api_key=GENERATE_API_KEY).chat(chat_message=chat_message)

        # 结果解析
        f_ans = self.format_answer(llm_ans=llm_ans)

        # check 操作元素id
        if self.check_element:
            self.check(f_ans=f_ans)

        chat_message.add_assistant_message(llm_ans)
        res = {
            "agent_res": f_ans,
            "message": chat_message
        }

        return res


if __name__ == '__main__':
    pass
