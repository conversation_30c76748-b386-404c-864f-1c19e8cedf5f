#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@File    : prompt.py
<AUTHOR> <PERSON><PERSON><PERSON><PERSON>(<EMAIL>)
@Time    : 2024/11/15 14:49
@Desc    : 
"""


def system_prompt():
    """

    :return:
    """
    sys_prompt = """你是一个具有App自动化测试经验的AI手机操作助手，你需要帮我操作手机并且完成页面的功能点验证。
你拥有基本的自动化测试常识，例如：在输入操作后需要跟确认、完成、发送、搜索等操作；设备执行过程出现意料之外的弹窗、提示框、权限弹窗等需要先点除；菜单或设置页面可能需要滑动查找某些功能等。"""
    return sys_prompt


def reflect_prompt(action_desc):
    """
    反思过程

    :return:
    """
    prompt = f"""
我根据你给出的操作描述:【{action_desc}】操作了手机。
这两张图像是本次操作前后的两张手机截图。
第一张图片中如果有红框，则表示操作的点击位置、输入或校验位置；
如果有箭头，则表示页面滑动操作中手指在设备的滑动轨迹，左箭头表示由右向左滑动（达到 向右切换页面 的效果），上箭头表示由下往上滑动，达到 向下翻页 的效果。
第二张图片为操作后的手机截图。

&& 任务 &&
你现在需要根究操作后的页面截图 判断 你给出的具体操作是否正确，如果不正确给出修正操作提示。目的是正确的达成测试目的，少走弯路，及时纠正。

&& 需注意 &&
1. 实际操作仅会是单步操作，仅需关注单步操作是否符合预期，保证整体的测试执行路径正确。
2. 上一步中的操作思考可能是存在错误，无需过度关注，关注本次的操作执行是否正确，是否是整个测试流程中的正确一步。
"""
    output = """
&& 输出 &&
输出应该包含两个部分，result 和 reason，以json的格式输出。
{
    "result": bool  // 结果, true表示正确，false表示错误
    "reason": string  // 原因，做出此判断原因，如果不符合预期给出正确的具体操作提示
}
"""
    return prompt + output


def reflect_prompt_v2(mrd, action_desc, action_thought):
    """
    反思agent
    :param action_desc:
    :param action_thought:
    :return:
    """
    prompt = f"""
&& 背景 &&
我正在一步步的操作手机进行自动化测试，为了保证整体测试执行的正确性，我需要在每一步操作后都进行正确性判断，目的是正确的达成测试目的，少走弯路，及时纠正。当前就是执行操作后的一次校验环节。
进行的测试用例为：{mrd}。
需注意：测试用例路径为大致路径，可能不是完整步骤，但是用例中提到的执行步骤都需要执行，具体要如何操作是当前页面截图为主做出的决定。
例如：测试路径为：A -> B -> C，实际的执行路径为：A -> B -> B' -> C，但是用例中提到A、B、C都是需要执行到的。

&& 图片信息 &&
本次校验环节提供了两张手机截图：
- 第一张图片：操作前的截图，若存在红框表示操作的点击、输入或校验位置；若存在箭头则表示页面滑动方向（左箭头：由右向左滑动；上箭头：由下往上滑动）。
- 第二张图片：操作后的截图，用于判断当前操作是否达到预期效果。

&& 操作信息 &&
操作前的思考：{action_thought}
实际操作：{action_desc}

&& 任务 &&
你现在需要根究操作后的页面截图 判断 进行的实际操作是否正确，如果正确，给出下一步的操作建议，如果不正确给出原因和正确的操作提示。目的是正确的达成测试目的，少走弯路，及时纠正。

&& 注意 &&
1. 每次只关注单步操作的正确性，不要跨步考虑后续未执行的操作。
2. 用例中提到的执行步骤都需要执行，你在给出下一步操作提示时需要注意如下易错点。
    * 例如：用例为：1.打开App -> 2. 输入hello -> 3. 校验展示整成。
    当执行完1后，页面截图默认已经显示了输入hello的信息，但是这并不我们本次操作输入的，因此我们的测试需要按用例顺序，输入hello，不可以直接跳到执行步骤3的用例。
3. 当操作页面某些元素后页面未发生变化，不一定都是错误操作，需要结果整个用例来判断。
    * 例如1：用例为：1.点击头像下面的粉丝数 -> 2.验证页面无变化。操作操作为：点击头像下面的粉丝数。
    当执行1操作后，页面尽管未发生变化，但是也是符合预期的，也是正确步骤。
    * 例如2：用例为：1.打开App -> 2.点击『主页』tab -> **
    当执行1操作后，默认的页面就是主页tab，当执行第二步"点击『主页』tab"后页面未发生变化，也是正常的，这种需要结合图片信息判断。
"""
    output = """
&& 输出 &&
输出应该包含两个部分，result 和 reason，以json的格式输出。
{
    "result": bool  // 结果, true表示正确，false表示错误
    "feedback": string  // 如果正确，给出下一步的操作建议，如果不正确给出原因和正确的操作提示
}
"""
    return prompt + output


def reflect_prompt_v1(mrd, action_desc, action_thought):
    """
    反思agent
    :param action_desc:
    :param action_thought:
    :return:
    """
    prompt = f"""
&& 背景 &&
你正在按照测试用例逐步操作手机进行自动化测试。为了确保整体测试执行正确，每一步操作后都需要对执行结果进行校验，确保测试目标不偏离。当前处于操作后的校验环节。
测试用例为：{mrd}。
- 说明：测试用例路径描述的是大致的执行步骤，可能存在拆分的多步操作。例如：实际操作路径为A -> B -> B' -> C，但用例中只列出了A、B、C。所有用例中描述的关键步骤都必须最终得到执行。

&& 图片信息 &&
本次校验环节提供了两张手机截图：
- 第一张图片：操作前的截图，若存在红框表示操作的点击、输入或校验位置；箭头则表示页面滑动方向（左箭头：由右向左滑动；上箭头：由下往上滑动）。
- 第二张图片：操作后的截图，用于判断当前操作是否达到预期效果。

&& 操作信息 &&
操作前的思考：{action_thought}
实际操作描述：{action_desc}

&& 任务 &&
请根据操作后的页面截图判断刚才执行的单步操作是否正确：
- 如果操作正确，请在反馈中给出下一步操作的建议；
- 如果操作不正确，请说明原因并给出正确的操作提示。
目标是确保测试目标正确达成，及时纠正错误，防止测试流程偏离预期。

&& 注意 &&
1. 每次只关注单步操作的正确性，不要跨步考虑后续未执行的操作。
2. 测试用例中所有关键步骤都必须依序执行，不能跳步。例如：当用例为“1. 打开App -> 2. 输入hello -> 3. 校验展示”，若当前操作仅为输入hello，则下一步依然需要执行输入操作，而不能直接校验展示。
3. 当操作页面某些元素后页面未发生变化，不一定都是错误操作，需要结果整个用例来判断。
    * 例如1：用例为：1.点击头像下面的粉丝数 -> 2.验证页面无变化。操作操作为：点击头像下面的粉丝数。
    当执行1操作后，页面尽管未发生变化，但是也是符合预期的，也是正确步骤。
    * 例如2：用例为：1.打开App -> 2.点击『主页』tab -> **
    当执行1操作后，默认的页面就是主页tab，当执行第二步"点击『主页』tab"后页面未发生变化，也是正常的，这种需要结合图片信息判断。
"""
    output = """
&& 输出 &&
请以JSON格式输出，必须包含两个字段：
- "result": 布尔值，true表示操作正确，false表示操作错误。
- "feedback": 字符串内容；若操作正确，请给出下一步的操作建议；若操作错误，请说明原因并给出正确操作提示。

输出示例格式：
{
    "result": true,
    "feedback": "下一步建议：在表单中点击提交按钮。"
}
"""
    return prompt + output