#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@File    : guide_agent.py
<AUTHOR> <PERSON><PERSON><PERSON><PERSON>(renya<PERSON><PERSON>@baidu.com)
@Time    : 2024/11/15 14:49
@Desc    : 
"""

import json

from apps.app_agent.multi_agent.base.agent import BaseChat
from apps.app_agent.utils.chat_message import ChatMessage
from apps.app_agent.utils.big_model import BigModel
from apps.app_agent.multi_agent.auto_case_generate_agent.reflect_agent.v2_1.prompt import reflect_prompt
from basics.util import logger


class GuideReflectAgent(BaseChat):
    """
    反思Agent
    """

    def __init__(self, chat_message: ChatMessage, mrd: str,
                 action_desc: str, action_thought: str,
                 exec_image_path: str, after_image_path: str,
                 model_name="/ssd1/ep/Qwen2.5-VL-72B-Instruct"):
        """

        :param chat_message: 对话列表
        :param after_image_path: 运行后的截图地址
        """
        super().__init__(name="reflect_agent")
        self.chat_message = chat_message
        self.mrd = mrd
        self.action_desc = action_desc
        self.action_thought = action_thought
        self.exec_image_path = exec_image_path
        self.after_image_path = after_image_path

        self.model_name = model_name

    def prompt_message(self):
        """

        :return:
        """
        # prompt = reflect_prompt_v2(mrd=self.mrd, action_desc=self.action_desc, action_thought=self.action_thought)
        prompt = reflect_prompt(action_desc=self.action_desc)
        logger.info("guide_reflect_prompt: {}".format(prompt))
        # img_paths = [self.exec_image_path, self.after_image_path]
        img_paths = [self.after_image_path]
        logger.info("Reflect Agent对应的图片为:{}".format(img_paths))
        message = self.chat_message
        message.add_user_message(prompt_text=prompt, image_paths=img_paths)
        return message

    def format_answer(self, llm_ans):
        """
        解析回答
        :param llm_ans:
        :return:
        """
        f_ans = json.loads(llm_ans)

        return f_ans

    def main(self):
        """

        :return:
        """
        # 如果输出不符合预期，重新判断一下
        count = 0
        while count < 2:
            chat_message = self.prompt_message()
            temperature = (count + 1) * 0.1
            llm_ans = BigModel(model_name=self.model_name).chat(chat_message=chat_message,
                                                                temperature=temperature,
                                                                response_format='json_object')
            f_ans = self.format_answer(llm_ans)

            if f_ans["result"]:
                break
            else:
                count += 1
                logger.info("执行结果不符合预期，再次进行判断验证")

        if f_ans['result']:
            logger.info("执行结果符合预期")
        else:
            logger.info("Reflect answer is {}, 不符合预期".format(f_ans['result']))

        chat_message.add_assistant_message(llm_ans)
        res = {
            "agent_res": f_ans,
            "message": chat_message
        }

        return res


if __name__ == '__main__':
    pass
