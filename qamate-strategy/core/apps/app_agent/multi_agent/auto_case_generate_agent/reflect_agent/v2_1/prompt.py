#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@File    : prompt.py
<AUTHOR> <PERSON><PERSON><PERSON><PERSON>(<EMAIL>)
@Time    : 2024/11/15 14:49
@Desc    : 
"""


def system_prompt():
    """

    :return:
    """
    sys_prompt = """你是一个具有App自动化测试经验的AI手机操作助手，你需要帮我操作手机并且完成页面的功能点验证。
你拥有基本的自动化测试常识，例如：在输入操作后需要跟确认、完成、发送、搜索等操作；设备执行过程出现意料之外的弹窗、提示框、权限弹窗等需要先点除；菜单或设置页面可能需要滑动查找某些功能等。"""
    return sys_prompt


def reflect_prompt(action_desc):
    """
    data:2025.2.12，改为连续对话，辅助反思Agent更充分的了解上下文信息
    反思agent
    :param action_desc:
    :return:
    """
    prompt = f"""
&& 任务 &&
请根据 **操作后的页面截图**，判断刚才执行的 **单步操作** 是否正确：
- 如果操作正确：
  - 在反馈中给出下一步的操作建议，确保测试流程顺利推进。  
- 如果操作不正确：
  - 明确指出问题（如：目标元素未变化、输入未提交、未按测试步骤执行等）。  
  - 提供正确的操作指导，确保测试流程不偏离预期。
**目标**：确保测试目标 **正确达成**，如有偏差 **及时纠正**，防止流程错误累计。

&& 判断标准 &&
1. **关注单步操作的正确性**，不跨步考虑未执行的操作。  
2. **测试用例中的所有关键步骤必须依序执行**，不能跳步。  
  - 例如：测试路径为 **"1. 打开App → 2. 输入hello → 3. 校验展示"**  
  - 若当前操作 **仅执行了“输入 hello”**，则 **下一步仍然需要执行输入操作**，不能跳过直接校验展示。  
3. **页面未发生变化 ≠ 操作错误**，需结合测试目标判断。  
  - **示例1（预期无变化）**：
    - 用例："点击粉丝数 → 页面无变化"
    - 操作：点击粉丝数后页面未变化  
    - **这是正确行为**，因为用例明确说明页面应保持不变。  
  - **示例2（预期无变化但仍正确）**：
    - 用例："打开 App → 点击 '主页' tab"
    - 操作：默认页面就是主页，点击后无变化  
    - **操作仍正确**，因为虽然页面无变化，但测试路径需要点击该元素。

&& 图片信息 &&
图片是上一步操作：{action_desc} 操作后的截图。
"""
    output = """
&& 输出 &&
请直接输出一个能够被 json.loads 解析的 JSON 格式字符串，不要包含任何代码块标记，必须包含两个字段：
- "result": 布尔值，true表示操作正确，false表示操作错误。
- "feedback": 字符串内容；若操作正确，请给出下一步的操作建议；若操作错误，请说明原因并给出正确操作提示。

输出示例格式：
{
    "result": true,
    "feedback": "下一步建议：在表单中点击提交按钮。"
}
"""
    return prompt + output