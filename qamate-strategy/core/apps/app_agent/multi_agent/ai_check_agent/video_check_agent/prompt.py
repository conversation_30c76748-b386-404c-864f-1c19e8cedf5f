#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@File    : prompt.py
<AUTHOR> <PERSON><PERSON><PERSON><PERSON>(<EMAIL>)
@Time    : 2025/3/21 11:11
@Desc    : 
"""


def check_prompt(check_str: str):
    """

    :param check_str:
    :param dom_desc:
    :return:
    """

    """

    :param check_str:
    :param dom_desc:
    :return:
    """

    prompt = f"""&& 背景 &&
你是一名经验丰富的 App 自动化测试智能助手。你的目标是根据我提供的APP操作录屏，判断我提供给你的文字断言是否为真。

&& 任务 &&
我对APP进行了一系列操作，并对设备进行了录屏，我会提供给你录屏视频，和一个文字断言，请你根据视频，判断我的断言是否成立。
文字断言：【{check_str}】

&& 分析要求 &&
你需要先分析文字断言，如果存在多个校验点，你需要先拆解校验点，只要有一个校验点不成立，那么最终的结果为假。
"""
    output = """
&& 输出要求 &&
请直接输出一个能够被 json.loads 解析的 JSON 格式字符串，不要包含任何代码块标记：
{
    "result": bool, // 文字断言是否为真（true 表示断言结果为真，false 表示断言结果为假）
    "reason": string, // 给出判断的思考过程和具体原因
}
"""
    return prompt + output
