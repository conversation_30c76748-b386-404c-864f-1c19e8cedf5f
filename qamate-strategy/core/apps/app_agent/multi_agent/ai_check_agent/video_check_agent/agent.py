#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@File    : agent.py
<AUTHOR> <PERSON><PERSON><PERSON><PERSON>(renya<PERSON><PERSON>@baidu.com)
@Time    : 2025/3/21 11:10
@Desc    : 
"""
# !/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import os
import shutil
import threading
import time
import uuid

import requests

from apps.app_agent.multi_agent.ai_check_agent.video_check_agent.prompt import check_prompt
from apps.app_agent.multi_agent.base.agent import BaseChat
from apps.app_agent.utils.big_model import QAMateLLM
from apps.app_agent.utils.chat_message import ChatMessage
from apps.app_agent.utils.exception import AgentErrCode, AgentException
from basics.util import logger
from basics.util.bos_utils import BosUtil, LLM_BOS_CONF, CONTENT_JSON
from basics.util.config import CACHE_DIR

MAX_COUNT = 1
MAX_RETRY = 3


class VideoAiCheckAgent(BaseChat):
    """
    大模型校验Agent
    """

    def __init__(self, check_str: str, video_url: str, mode_name="doubao-1.5-vision-pro-250328", ctx=None):
        """
        :param check_str: 待校验字符串
        :param img_paths: 待校验图片信息
        """
        super().__init__(name="ai_check_agent")
        self.check_str = check_str
        self.video_url = video_url
        self.model_name = mode_name

        self.temp_dir = os.path.join(CACHE_DIR, str(uuid.uuid4()))
        self.task_id = str(uuid.uuid4().hex)
        self.task_tag = 'ai_assert'
        self.log_pairs = []
        self.bos_client = BosUtil(**LLM_BOS_CONF)
        self.final_res = None
        self.ctx = ctx

    def _mk_dir(self):
        """

        :return:
        """
        if not os.path.exists(self.temp_dir):
            os.mkdir(self.temp_dir)
        logger.info("temp_dir: {}".format(self.temp_dir))

    def _clear_dir(self):
        """
        清理临时目录
        """
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)

    def upload_llm_log(self):
        """
        上传大模型交互日志
        """

        def upload_all_llm_log():
            """
            单独的线程内上传大模型交互日志
            """
            try:
                log_list = []
                for msg, llm_ans in self.log_pairs:
                    try:
                        # print(msg, llm_ans)
                        msg.append({
                            "role": "assistant",
                            "content": [
                                {
                                    "type": "text",
                                    "text": llm_ans
                                }
                            ],
                        })
                        chat_str = json.dumps(msg, ensure_ascii=False)
                        char_url = self.bos_client.upload_string(chat_str, key=f"llm/log/{self.task_id}/chat.json",
                                                                 content_type=CONTENT_JSON)

                        extra = {
                            'video_url': self.video_url,
                            'llm_ans': json.loads(llm_ans),
                            'check_str': self.check_str
                        }
                        if self.final_res is not None:
                            extra['final_res'] = self.final_res
                        extra_str = json.dumps(extra, ensure_ascii=False)
                        extra_url = self.bos_client.upload_string(extra_str, key=f"llm/log/{self.task_id}/extra.json",
                                                                  content_type=CONTENT_JSON)
                        log_list.append({
                            'taskId': self.task_id,
                            'taskIndex': 0,
                            'taskTag': self.task_tag,
                            'model': self.model_name,
                            'chatUrl': char_url,
                            'extraUrl': extra_url
                        })
                    except Exception as e:
                        logger.error(e)
                try:
                    url = 'https://qamate.baidu-int.com/core/llm/log/create'
                    body = {
                        'logList': log_list
                    }
                    # print(json.dumps(body, ensure_ascii=False, indent=2))
                    headers = {
                        "Content-Type": "application/json",
                        "QAMate-ModuleId": "0",
                        "QAMate-Token": "super-9a796933bf024ebfa0420dce6f864a51"
                    }
                    r = requests.post(url, json=body, headers=headers)
                    # print(self.task_id)
                    logger.info(f'task: {self.task_id}, upload result: {r.json()}')
                except Exception as e:
                    logger.error(e)
            finally:
                self._clear_dir()

        threading.Thread(target=upload_all_llm_log).start()

    def prompt_message(self):
        """
        :return:
        """
        message = ChatMessage()
        message.add_user_message(prompt_text=check_prompt(self.check_str), video_url=self.video_url, fps=2)
        return message

    def format_answer(self, llm_ans):
        """
        解析回答
        :param llm_ans:
        :return:
        """
        f_ans = json.loads(llm_ans)
        return f_ans

    def judge(self):
        """

        :return:
        """
        # prompt
        chat_message = self.prompt_message()

        # answer
        num = 0
        while num < MAX_RETRY:
            num += 1
            try:
                llm_ans = (QAMateLLM(model_name=self.model_name, extra={'usage': 'video_check'}, ctx=self.ctx)
                           .chat(chat_message=chat_message, response_format="json_object"))
                res = self.format_answer(llm_ans)
                self.log_pairs.append((chat_message, llm_ans))
                return res
            except Exception as e:
                logger.error(e)
                logger.error("output error, try again, left %d" % num)
        raise AgentException(err_code=AgentErrCode.LLM_UNEXPECTED_OUPUT)

    def main(self):
        """
        多线程，多数投票的方法
        :return:
        """
        self._mk_dir()
        t1 = int(1000 * time.time())

        results = []

        def works():
            """
            线程函数
            :return:
            """
            result = self.judge()
            results.append(result)

        threads = []

        # 创建线程
        for _ in range(MAX_COUNT):
            thread = threading.Thread(target=works)
            threads.append(thread)
            thread.start()

        # 等待完成
        for thread in threads:
            thread.join()
        t2 = int(1000 * time.time())

        # 投票统计结果
        results_votes = [result["result"] for result in results]

        final_result = max(set(results_votes), key=results_votes.count)

        final_reason = ""

        for res in results:
            if res['result'] == final_result:
                final_reason = res["reason"]
                break

        final_res = {
            "result": final_result,
            "reason": final_reason,
            "cost": t2 - t1,
        }
        logger.info("判断可生成最终结果为：{}".format(final_res))
        self.final_res = final_res
        self.upload_llm_log()
        return final_res


if __name__ == '__main__':
    # url = BosUtil(**LLM_BOS_CONF).upload_file(file_path='/Users/<USER>/Downloads/RPReplay_Final1749546113.MP4')
    res = VideoAiCheckAgent("高考分数线",
                      video_url='https://bj.bcebos.com/newmvp/lazycloud'
                                '/1750837173157-b74584e6-3e75-4b43-b049-8acd6fda68ef.mp4').main()
    print(res)