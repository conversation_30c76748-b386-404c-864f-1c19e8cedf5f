#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@File    : main.py
<AUTHOR> <PERSON><PERSON><PERSON><PERSON>(<EMAIL>)
@Time    : 2025/3/21 14:22
@Desc    : 
"""
from apps.app_agent.multi_agent.ai_check_agent.single_img_check_agent.v1_2.agent import AiCheckAgent
from apps.app_agent.utils.draw import draw_rects


def ai_check(check_str: str, img_paths: list, model_name: str = "qwen2.5-vl-72b-instruct",
             output_path: str = ""):
    """
    智能校验入口
    :param check_str:
    :param img_paths:
    :param output_path:
    :param model_name:
    :return:
    """
    # 判断
    check_tool = AiCheckAgent(check_str=check_str, img_path=img_paths[-1], mode_name=model_name)
    check_res = check_tool.main()

    draw_rects(image_path=img_paths[-1], rects=check_res.get("rects", []), output_path=output_path)

    return check_res


if __name__ == '__main__':
    s = "评论图标下面的评论数是1"
    img = "/Users/<USER>/Downloads/图片/07b8751c-b023-4f50-a9b0-1282d7920be8/25_93663_3.jpg"

    # s = "输入框是否存在文字"
    # img = "/Users/<USER>/Downloads/图片/07b8751c-b023-4f50-a9b0-1282d7920be8/1.jpg"

    # s = "红框内点赞按钮变红"
    # img = "/Users/<USER>/Downloads/图片/07b8751c-b023-4f50-a9b0-1282d7920be8/c374c7803e4d55da24847a012a576331.png"

    # s = "视频评论数为1"
    # img = "/Users/<USER>/Downloads/图片/07b8751c-b023-4f50-a9b0-1282d7920be8/25_93663_3.jpg"

    r = ai_check(check_str=s, img_paths=[img], model_name="qwen2.5-vl-72b-instruct",
                 output_path="vis_qwen2_5-vl-72b.jpg")
    r1 = ai_check(check_str=s, img_paths=[img], model_name="qwen-vl-max-2025-01-25",
                  output_path="vis_qwen_vl_max.jpg")
    r2 = ai_check(check_str=s, img_paths=[img], model_name="gpt-4o-2024-11-20",
                  output_path="vis_gpt_4o.jpg")
    r3 = ai_check(check_str=s, img_paths=[img], model_name="ernie-4.5-8k-preview",
                  output_path="vis_ernie_4_5.jpg")
    print(r)
    print(r1)
    print(r2)
    print(r3)
