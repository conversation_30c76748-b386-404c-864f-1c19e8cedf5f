#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@File    : agent.py
<AUTHOR> <PERSON><PERSON><PERSON><PERSON>(renya<PERSON><PERSON>@baidu.com)
@Time    : 2025/3/21 11:10
@Desc    : 
"""
# !/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import threading

import cv2

from apps.app_agent.multi_agent.base.agent import BaseChat
from apps.app_agent.utils.chat_message import ChatMessage
from apps.app_agent.utils.big_model import QAMateLLM
from apps.app_agent.multi_agent.ai_check_agent.single_img_check_agent.v1_1.prompt import check_prompt
from basics.util import logger
from apps.app_agent.utils.exception import AgentErrCode, AgentException

MAX_COUNT = 3


class AiCheckAgent(BaseChat):
    """
    大模型校验Agent
    """

    def __init__(self, check_str: str, img_path: str, mode_name="doubao-1.5-vision-pro-250328"):
        """

        :param mrd:
        """
        super().__init__(name="ai_check_agent")
        self.check_str = check_str
        self.img_path = img_path
        self.model_name = mode_name

    def prompt_message(self):
        """

        :return:
        """
        prompt = check_prompt(check_str=self.check_str)
        message = ChatMessage()
        message.add_user_message(prompt_text=prompt, image_paths=[self.img_path])
        return message

    def format_answer(self, llm_ans):
        """
        解析回答
        :param llm_ans:
        :return:
        """
        f_ans = json.loads(llm_ans)

        imgcv = cv2.imread(self.img_path)
        img_h, img_w = imgcv.shape[:2]
        rects = []
        for rect in f_ans['rects']:
            x1 = max(int(rect['x1'] * img_w), 0)
            y1 = max(int(rect['y1'] * img_h), 0)
            x2 = min(int(rect['x2'] * img_w), img_w)
            y2 = min(int(rect['y2'] * img_h), img_h)
            rects.append({"x": x1, "y": y1, "w": x2 - x1, "h": y2 - y1})

        f_ans["rects"] = rects

        return f_ans

    def judge(self):
        """

        :return:
        """
        # prompt
        chat_message = self.prompt_message()

        # answer
        num = 0
        while num < MAX_COUNT:
            num += 1
            try:
                # llm_ans = BigModel(model_name=self.model_name).chat(chat_message=chat_message,
                #                                                     response_format="json_object")
                llm_ans = (QAMateLLM(model_name=self.model_name, extra={
                    'usage': 'ai_check_v1_1'
                }).chat(chat_message=chat_message, response_format="json_object"))
                res = self.format_answer(llm_ans)
                return res
            except Exception as e:
                logger.error(e)
                logger.error("output error, try again, left %d" % num)
        raise AgentException(err_code=AgentErrCode.LLM_UNEXPECTED_OUPUT)

    def main(self):
        """
        多线程，多数投票的方法
        :return:
        """

        results = []

        def works():
            """
            线程函数
            :return:
            """
            result = self.judge()
            results.append(result)

        threads = []

        # 创建线程
        for _ in range(1):
            thread = threading.Thread(target=works)
            threads.append(thread)
            thread.start()

        # 等待完成
        for thread in threads:
            thread.join()

        # 投票统计结果
        results_votes = [result["result"] for result in results]

        final_result = max(set(results_votes), key=results_votes.count)

        for res in results:
            if res['result'] == final_result:
                final_reason = res["reason"]
                final_rects = res["rects"]
                break

        final_res = {
            "result": final_result,
            "reason": final_reason,
            "rects": final_rects
        }
        logger.info("判断可生成最终结果为：{}".format(final_res))
        return final_res


if __name__ == '__main__':
    s = "存在红包弹窗"
    img = "/Users/<USER>/Downloads/7fbe546ff738ca1df19d06129.png"
    r = AiCheckAgent(check_str=s, img_path=img).main()
    print(r)
