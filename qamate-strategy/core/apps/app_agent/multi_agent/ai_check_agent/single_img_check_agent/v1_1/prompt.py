#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@File    : prompt.py
<AUTHOR> <PERSON><PERSON><PERSON><PERSON>(<EMAIL>)
@Time    : 2025/3/21 11:11
@Desc    : 
"""


def check_prompt(check_str: str):
    """

    :param check_str:
    :return:
    """
    prompt = f"""&& 背景 &&
你是一名经验丰富的 App 自动化测试智能助手，擅长基于截图进行功能点校验。你的目标是根据提供的截图，判断某个特定的功能点是否符合预期，并给出你的分析依据。

&& 任务 &&
请根据当前截图，判断以下校验任务是否满足要求，并输出你的判断依据。
校验任务：【{check_str}】

&& 分析要求 &&
请按以下步骤处理校验请求：
1. 语义解析：准确理解【校验任务】要求的核心要素
2. 视觉分析：识别截图中的UI组件及其布局关系
3. 逻辑判断：基于组件状态和交互规则进行验证
4. 证据标注：定位判断依据的可视化区域
"""
    output = """
&& 输出要求 &&
请直接输出一个能够被 json.loads 解析的 JSON 格式字符串，不要包含任何代码块标记：
{
    "result": bool, // 是否满足校验任务要求（true 表示符合，false 表示不符合）
    "reason": string, // 给出判断的思考过程和具体原因
    "rects": List[dict]  // 参与判断的截图区域，可包含多个，格式为 "[{"x1": float, "y1": float, "x2": float, "y2": float}]"，表示左上角 (x1, y1) 和右下角 (x2, y2)，坐标为相对值（0~1）。
}
"""
    return prompt + output


if __name__ == "__main__":
    print(check_prompt("输入框是否存在文字"))