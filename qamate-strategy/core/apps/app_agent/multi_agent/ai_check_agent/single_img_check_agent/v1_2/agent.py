#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@File    : agent.py
<AUTHOR> <PERSON><PERSON><PERSON><PERSON>(renya<PERSON><PERSON>@baidu.com)
@Time    : 2025/3/21 11:10
@Desc    : 
"""
# !/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import os
import shutil
import threading
import time
import uuid

import requests

from apps.app_agent.multi_agent.ai_check_agent.single_img_check_agent.v1_2.prompt import check_prompt
from apps.app_agent.multi_agent.base.agent import BaseChat
from apps.app_agent.multi_agent.page_understand_agent.bat_page_understand_agent import BatPageUnderstandAgent
from apps.app_agent.utils.big_model import QAMateLLM
from apps.app_agent.utils.chat_message import ChatMessage
from apps.app_agent.utils.exception import AgentErrCode, AgentException
from basics.util import logger
from basics.util.bos_utils import BosU<PERSON>, LLM_BOS_CONF, CONTENT_JSON
from basics.util.config import CACHE_DIR

MAX_COUNT = 1
MAX_RETRY = 3


class AiCheckAgent(BaseChat):
    """
    大模型校验Agent
    """

    def __init__(self, check_str: str, img_path: str, mode_name="doubao-1.5-vision-pro-250328", ctx=None):
        """

        :param mrd:
        """
        super().__init__(name="ai_check_agent")
        self.check_str = check_str
        self.img_path = img_path
        self.model_name = mode_name

        self.cur_batdom_record = None

        self.temp_dir = os.path.join(CACHE_DIR, str(uuid.uuid4()))
        # 图片解析
        self.grid_img_path = None
        self.cur_batdom_record = None
        self.task_id = str(uuid.uuid4().hex)
        self.task_tag = 'ai_assert'
        self.log_pairs = []
        self.bos_client = BosUtil(**LLM_BOS_CONF)
        self.final_res = None
        self.ctx = ctx

    def _mk_dir(self):
        """

        :return:
        """
        if not os.path.exists(self.temp_dir):
            os.mkdir(self.temp_dir)
        logger.info("temp_dir: {}".format(self.temp_dir))

    def _clear_dir(self):
        """
        清理临时目录
        """
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)

    def upload_llm_log(self):
        """
        上传大模型交互日志
        """

        def upload_all_llm_log():
            """
            单独的线程内上传大模型交互日志
            """
            try:
                log_list = []
                for msg, llm_ans in self.log_pairs:
                    try:
                        # print(msg, llm_ans)
                        msg.append({
                            "role": "assistant",
                            "content": [
                                {
                                    "type": "text",
                                    "text": llm_ans
                                }
                            ],
                        })
                        chat_str = json.dumps(msg, ensure_ascii=False)
                        char_url = self.bos_client.upload_string(chat_str, key=f"llm/log/{self.task_id}/chat.json",
                                                                 content_type=CONTENT_JSON)
                        suffix = os.path.splitext(self.img_path)[-1]
                        origin_url = self.bos_client.upload_image(self.img_path,
                                                                  key=f"llm/log/{self.task_id}/origin{suffix}")
                        grid_url = self.bos_client.upload_image(self.grid_img_path,
                                                                key=f"llm/log/{self.task_id}/grid{suffix}")
                        extra = {
                            'origin_img': origin_url,
                            'grid_img': grid_url,
                            'llm_ans': json.loads(llm_ans),
                            'check_str': self.check_str
                        }
                        if self.final_res is not None:
                            extra['final_res'] = self.final_res
                        extra_str = json.dumps(extra, ensure_ascii=False)
                        extra_url = self.bos_client.upload_string(extra_str, key=f"llm/log/{self.task_id}/extra.json",
                                                                  content_type=CONTENT_JSON)
                        log_list.append({
                            'taskId': self.task_id,
                            'taskIndex': 0,
                            'taskTag': self.task_tag,
                            'model': self.model_name,
                            'chatUrl': char_url,
                            'extraUrl': extra_url
                        })
                    except Exception as e:
                        logger.error(e)
                try:
                    url = 'https://qamate.baidu-int.com/core/llm/log/create'
                    body = {
                        'logList': log_list
                    }
                    # print(json.dumps(body, ensure_ascii=False, indent=2))
                    headers = {
                        "Content-Type": "application/json",
                        "QAMate-ModuleId": "0",
                        "QAMate-Token": "super-9a796933bf024ebfa0420dce6f864a51"
                    }
                    r = requests.post(url, json=body, headers=headers)
                    # print(self.task_id)
                    logger.info(f'task: {self.task_id}, upload result: {r.json()}')
                except Exception as e:
                    logger.error(e)
            finally:
                self._clear_dir()

        threading.Thread(target=upload_all_llm_log).start()

    def _current_image_record_and_grid(self, image_path, grid_path):
        """
        页面建模 + 画框
        :param image_path: 原图存储地址
        :param grid_path: 带有gird图存储地址
        :return:
        """

        page_unds_agent = BatPageUnderstandAgent(image_path=image_path, grid_img_save_path=grid_path)
        dom_info, dom, dom_desc, id_ele_map = page_unds_agent.main()

        batdom_record_res = {
            "info": dom_info,
            "dom": dom,
            "dom_desc": dom_desc,
            "id_ele_map": id_ele_map
        }

        return batdom_record_res

    def prompt_message(self):
        """
        :return:
        """
        prompt = check_prompt(check_str=self.check_str, dom_desc=self.cur_batdom_record["dom_desc"])
        # logger.info("Check Prompt: {}".format(prompt))
        message = ChatMessage()
        message.add_user_message(prompt_text=prompt, image_paths=[self.grid_img_path])
        logger.info("img_paths: {}".format(self.grid_img_path))
        return message

    def format_answer(self, llm_ans):
        """
        解析回答
        :param llm_ans:
        :return:
        """
        f_ans = json.loads(llm_ans)

        rects = []
        for ele_id in f_ans['areas']:
            rect = self.cur_batdom_record['id_ele_map'][ele_id]['rect']
            rects.append(rect)
        f_ans['rects'] = rects
        return f_ans

    def judge(self):
        """

        :return:
        """
        # prompt
        chat_message = self.prompt_message()

        # answer
        num = 0
        while num < MAX_RETRY:
            num += 1
            try:
                llm_ans = (QAMateLLM(model_name=self.model_name, extra={
                    'usage': 'single_img_check'
                }, ctx=self.ctx).chat(chat_message=chat_message, response_format="json_object"))
                # llm_ans = BigModel(model_name=self.model_name).chat(chat_message=chat_message,
                #                                                     response_format="json_object")
                res = self.format_answer(llm_ans)
                self.log_pairs.append((chat_message, llm_ans))
                return res
            except Exception as e:
                logger.error(e)
                logger.error("output error, try again, left %d" % num)
        raise AgentException(err_code=AgentErrCode.LLM_UNEXPECTED_OUPUT)

    def main(self):
        """
        多线程，多数投票的方法
        :return:
        """
        self._mk_dir()
        # 图片解析
        self.grid_img_path = os.path.join(self.temp_dir, "grid.jpg")
        t1 = int(1000 * time.time())
        self.cur_batdom_record = self._current_image_record_and_grid(self.img_path, self.grid_img_path)
        t2 = int(1000 * time.time())

        results = []

        def works():
            """
            线程函数
            :return:
            """
            result = self.judge()
            results.append(result)

        threads = []

        # 创建线程
        for _ in range(MAX_COUNT):
            thread = threading.Thread(target=works)
            threads.append(thread)
            thread.start()

        # 等待完成
        for thread in threads:
            thread.join()
        t3 = int(1000 * time.time())

        # 投票统计结果
        results_votes = [result["result"] for result in results]

        final_result = max(set(results_votes), key=results_votes.count)

        for res in results:
            if res['result'] == final_result:
                final_reason = res["reason"]
                final_rects = res["rects"]
                break

        final_res = {
            "result": final_result,
            "reason": final_reason,
            "rects": final_rects,
            "record_cost": t2 - t1,
            "llm_cost": t3 - t2,
        }
        logger.info("判断可生成最终结果为：{}".format(final_res))
        self.final_res = final_res
        self.upload_llm_log()
        return final_res


# if __name__ == '__main__1':
#     import sys
#
#     sys.path.append('/')
#     import basics.config as config
#
#     config.load_config('/profile.json')
#     s = "输入框是否存在文字"
#     img = "/Users/<USER>/Downloads/00008110-000858E2022A401E_1742564633071_d0249c7c-7648-46bf-86b1-165ef639deaf_screenshot.jpg"
#     r = AiCheckAgent(check_str=s, img_path=img).main()
#     print(r)
#     time.sleep(20)
#
# if __name__ == '__main__':
#     bos_client = BosUtil(**LLM_BOS_CONF)
#     url = bos_client.upload_file(file_path="/Users/<USER>/lab/评测集/evl-3.zip", key="test/evl-3.zip")
#     print(url)
