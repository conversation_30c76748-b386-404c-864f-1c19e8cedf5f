#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@File    : prompt.py
<AUTHOR> <PERSON><PERSON><PERSON><PERSON>(<EMAIL>)
@Time    : 2025/3/21 11:11
@Desc    : 
"""


def check_prompt(check_str: str):
    """

    :param check_str:
    :param dom_desc:
    :return:
    """

    prompt = f"""&& 背景 &&
你是一名经验丰富的 App 自动化测试智能助手，擅长基于截图进行功能点校验。你的目标是根据提供的截图，判断某个特定的功能点是否符合预期，并给出你的分析依据。

&& 任务 &&
我对APP进行了一系列操作，每步操作后，我会得到一张APP截图，接下来会提供给你多张APP截图，和一个文字断言，请你根据这些截图，判断我的断言是否成立。
文字断言：【{check_str}】

&& 分析要求 &&
请按以下步骤处理校验请求：
1. 思考截图列表，判断列表中是否存在和文字断言相关的截图。
2. 进一步分析，与断言相关的截图是第几张到第几张（设截图列表序号从0开始）
3. 进一步分析，断言是否成立，并给出原因
"""
    output = """
&& 输出要求 &&
请直接输出一个能够被 json.loads 解析的 JSON 格式字符串，不要包含任何代码块标记：
{
    "result": bool, // 文字断言是否为真（true 表示断言结果为真，false 表示断言结果为假）
    "reason": string, // 给出判断的思考过程和具体原因
}
"""
    return prompt + output
