# -*- coding: UTF-8 -*-

"""
@create 邱立楷@2022.04.26
"""

import os
import imageio as iio
import numpy as np
import cv2
import traceback
import basics.util.config as system_config

from video.core.runner import TimeRunner
from video.stage.split import get_tendcy
from basics.util import logger
from basics.util.error import JsonrpcError
from video.plugins.operators import ObejctActionToOp



def recognise(system, scene_type, config, frames, frames_storage_dir, app_name="searchbox", task_info="crowd"):
    """ 首尾帧识别

    @param <string> system 操作系统。仅支持：android，iOS
    @param <string> app_name 应用名称。仅支持：baidu，toutiao，quark
    @param <int> scene_type 评测场景类型。仅支持：0 结果页，1 落地页
    @param <dict> frames 分帧数据
    @param <string> frames_storage_dir 分帧存储路径

    @return <int> first 首帧锚点
    @return <int> last 尾帧锚点
    """
    if 'baidu' == app_name:
        app_name = 'searchbox'

    if 0 == scene_type:
        scene_type = app_name + '_jieguoye'
    elif 1 == scene_type:
        scene_type = app_name + '_luodiye'
    elif 11 == scene_type:
        scene_type = app_name + '_jieguoye'
    elif 12 == scene_type:
        scene_type = app_name + '_luodiye'
    elif 16 == scene_type:
        scene_type = app_name + '_tti'

    if 'Android' == system:
        system = 'android'
    elif 'HarmonyOS' == system:
        system = 'android'

    time_runner = TimeRunner(
        system, app_name, scene_type, config, task_info)
    time_runner.start()

    smart_first_frame, smart_last_frame = time_runner.recognize_video(
        frames, frames_storage_dir)

    return {
        "smart_first_frame": smart_first_frame,
        "smart_last_frame": smart_last_frame
    }


def extract(video_file_path, frames_storage_dir, frame_file_type='jpg'):
    """
    视频分帧
    """

    if "/" == frames_storage_dir[-1]:
        frames_storage_dir = frames_storage_dir[0: -1]

    reader = iio.get_reader(video_file_path, "ffmpeg")
    meta = reader.get_meta_data()
    if meta["fps"] > 30:
        reader = iio.get_reader(video_file_path, "ffmpeg", fps=30)
        meta = reader.get_meta_data()

    frames = []
    for index, frame in enumerate(reader):
        frame = cv2.cvtColor(frame, cv2.COLOR_RGB2BGR)
        im = cv2.imencode("." + frame_file_type, frame,
                          [cv2.IMWRITE_JPEG_QUALITY, 50])
        im_byte = np.array(im[1]).tobytes()
        frame_file_path = frames_storage_dir + \
            '/' + str(index) + '.' + frame_file_type
        fp = open(frame_file_path, "wb")
        fp.write(im_byte)
        fp.close()
        frames.append({
            "index": index,
            "size": len(im_byte),
            "path": frame_file_path
        })

    return {
        "fps": meta["fps"],
        "duration": meta["duration"],
        "frame_file_type": frame_file_type,
        "frames": frames
    }

def check_input(fingerpoint, frames_storage_dir):
    """
    @description: 适配按压点位置
    @param: 
    @return: 
    """
    assert type(fingerpoint) == list
    if not fingerpoint or len(fingerpoint) < 2:
        return [0, 0]
    if fingerpoint[0] > 1:
        return fingerpoint
    select_picture = os.listdir(frames_storage_dir)[0]
    image = cv2.imread(os.path.join(frames_storage_dir, select_picture))
    if len(image) == 0:
        return [0, 0]
    else:
        image_height, image_width = image.shape[:2]
        return [fingerpoint[0] * image_width, fingerpoint[1] * image_height]


def find_index(system, config, frames_storage_dir, mode=0, start_index=0, 
               fingerpoint=[540, 268],
               app_name="searchbox", task_info="suiban",
               stage_list=[0, 0]):
    """
    @description: 随版评测场景首尾帧识别算法
    @param: system 可选android/ios
    @param: config 策略标记
    @param: frames_storage_dir 图片路径存储根目录
    @param: fingerpoint 按压位置区域
    @param: mode 0 首尾帧，1仅首帧, 2仅尾帧
    @return: dict格式
    """
    if 'baidu' == app_name:
        app_name = 'searchbox'
    if 'Android' == system:
        system = 'android'
    elif 'HarmonyOS' == system:
        system = 'android'
    if config.find('@') != -1:
        app_name = config.split('@')[0]
        config = config.split('@')[-1]
    
    scene_type = app_name + '_' + config
    
    if scene_type.split('_')[-1] == 'videobegin':
        task_info = 'guiyuan'
    if scene_type.split('_')[-1] == 'videoswipe':
        task_info = 'guiyuan'
    time_runner = TimeRunner(
        system, app_name, scene_type, task_info)
    if system == 'android':
        logger.info(fingerpoint)
        time_runner.set_fingerpoint(check_input(fingerpoint, frames_storage_dir))
    time_runner.set_stage_list(stage_list)
    time_runner.start()
    picnames = os.listdir(frames_storage_dir)
    
    if system_config.IS_RUN_IN_BUNDLE:
        split_tag = '.'
    elif picnames[0].find('_') != -1:
        split_tag = '_'
    else:
        split_tag = '.'
    
    sort_picnames = [item.split(split_tag) for item in picnames if (
        item != 'data.json' and not item.startswith('.'))]
    # 对frames进行排序
    sort_picnames.sort(key=lambda x: int(x[0]))
    frames = [{"path": os.path.join(
        frames_storage_dir, split_tag.join(item))} for item in sort_picnames]
    smart_first_frame, smart_last_frame = time_runner.recognize_video(
        frames, frames_storage_dir, mode, start_index)
    return smart_first_frame, smart_last_frame


def tti(frames_dir, system, start_index=0):
    """ 找寻搜索页渲染完成第一帧

    @param frames_dir {string} 分帧存储目录。要求分帧图片命名格式为 ${frame_index}.jpg
    @param system {string} 手机操作系统。枚举类型，支持：Android, iOS

    @return frame_index {int} 目标帧号    
    """
    frame_index_list = find_index(system, "tti", frames_dir, 2, start_index)
    return frame_index_list[1]

def find_next_stage(index, stage_list):
    """
    @description:
    @param: 
    @return: 
    """
    for item in stage_list:
        if item > index:
            return item
    return stage_list[-1]


def multi_stage_split(system, config, frames_storage_dir, mode=0, start_index=0, 
                    fingerpoint=[540, 268],
                    app_name="searchbox", task_info="crowd", stage_point_list=[0, 0]):
    """
    @description:
    @param: 
    @return: [stage_list, first_end_list]
    """
    # stage_point_list = get_tendcy(frames_storage_dir)
    # logger.info("get tendcy finish!")
    logger.debug(stage_point_list)
    assert type(stage_point_list) == list

    if len(stage_point_list) < 1:
        return [[0, 0], [0, 0], [0, 0]]
    elif len(stage_point_list) == 1:
        return [[stage_point_list[0], stage_point_list[0]]]

    first_end_list = []
    try:
        if config == 'multi':
            logger.info("coldstart start")
            first_coldstart, _ = find_index(system, 'coldstart', frames_storage_dir, 1, 
            start_index=stage_point_list[0], app_name=app_name, task_info=task_info)
            
            _, end_coldstart = find_index(system, 'coldstart', frames_storage_dir, 2, 
            start_index=stage_point_list[1], app_name=app_name, task_info=task_info, stage_list=stage_point_list)
            first_end_list.append((first_coldstart, end_coldstart))
            logger.info("tti start")
            # need to wait for 3s, about 70+ frames
            # 40 means average wait time
            next_end_coldstart = find_next_stage(end_coldstart, stage_point_list)
            first_tti, _ = find_index(system, 'tti', frames_storage_dir, 1, 
            start_index=next_end_coldstart, app_name=app_name, task_info=task_info, stage_list=stage_point_list)

            _, end_tti = find_index(system, 'tti', frames_storage_dir, 2, 
            start_index=first_tti, app_name=app_name, task_info=task_info, stage_list=stage_point_list)
            
            if first_tti < end_coldstart:
                first_tti = next_end_coldstart

            first_end_list.append((first_tti, end_tti))
            logger.info("jieguoye start")
            first_jieguoye, _ = find_index(system, 'jieguoye', frames_storage_dir, 1,
            start_index=stage_point_list[-1], app_name=app_name, task_info=task_info, stage_list=stage_point_list)

            _, end_jieguoye = find_index(system, 'jieguoye', frames_storage_dir, 2,
            start_index=first_jieguoye, app_name=app_name, task_info=task_info, stage_list=stage_point_list)
            
            first_end_list.append((first_jieguoye, end_jieguoye))
            
        if config == 'luodiye':
            logger.info("luodiye start")
            first_luodiye, _ = find_index(system, 'luodiye', frames_storage_dir, 1, 
            start_index=stage_point_list[0], app_name=app_name, task_info=task_info, stage_list=stage_point_list)
            end_luodiye = stage_point_list[-1]
            if first_luodiye == end_luodiye:
                first_luodiye = stage_point_list[0]
            first_end_list.append((first_luodiye, end_luodiye))
    except Exception as e:
        logger.error(traceback.format_exc())
        logger.info(e)
    return first_end_list


def user_custome(frames_dir, info, start_index=0):
    """
    区别于find_index，输入参数减少；用户自定义算法输出结果
    :param frames_dir:
    :param start_index:
    :param fingerpoint:
    :return:
    """
    # 默认算子库为"suiban"
    runner = TimeRunner('suiban')
    op_flow = ObejctActionToOp(info['object'], info['action'])
    # 检查更新按压点
    info["finger_points"] = check_input(info.get("finger_points", []), frames_dir)
    runner.set_start_op(op_flow(info))

    # 用户自定义模式解析首尾帧
    runner.start(mode=1)

    picnames = os.listdir(frames_dir)

    if system_config.IS_RUN_IN_BUNDLE:
        split_tag = '.'
    elif picnames[0].find('_') != -1:
        split_tag = '_'
    else:
        split_tag = '.'

    sort_picnames = [item.split(split_tag) for item in picnames if (
            item != 'data.json' and not item.startswith('.'))]
    # 对frames进行排序
    sort_picnames.sort(key=lambda x: int(x[0]))
    frames = [{"path": os.path.join(
        frames_dir, split_tag.join(item))} for item in sort_picnames]
    start_end_mode = 1 # 默认
    smart_first_frame, smart_last_frame = runner.recognize_video(
        frames, frames_dir, start_end_mode, start_index)
    return smart_first_frame


if __name__ == "__main__":
    app_name = "baidu"
    scene_type = "searchbox_jieguoye"
    system = "ios"
    config = "crowd"
    frames_storage_dir = "/Users/<USER>/Downloads/1f07c268f591de13734f2068575edef4"
    picnames = ["{}.jpg".format(str(i)) for i in range(64)]
    frames = [{"path": os.path.join(frames_storage_dir, item)}
              for item in picnames]
    recognise(system, app_name, scene_type, config,
              frames, frames_storage_dir)
