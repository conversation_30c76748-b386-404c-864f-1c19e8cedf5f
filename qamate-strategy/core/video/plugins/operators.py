# !/usr/bin/env python3
# -*- coding: UTF-8 -*-
#
# Copyright (c) 2022 Baidu.com, Inc. All Rights Reserved
#
"""
Desc    : 枚举算子类别
Author  : liletao(<EMAIL>)
Time    : 2022/12/9 7:03 PM
"""


OPERATOR_CODE = {
    101: 'KeyboardShowOp',
    102: 'KeyboardDisOp',
    103: 'KeyWordAndShowOp',
    104: 'KeyWordOrShowOp',
    105: 'CrossShowOp',
    106: 'CrossDisOp'

}


class ObejctActionToOp():
    """ 对象、操作、截图区域等内容组装为dict形式的算子流"""

    def __init__(self, object, action):
        """ """
        self.object = object
        self.action = action

    def __call__(self, info):
        """  """
        method = 'baseop'
        params = {}
        if self.object == 'keyboard':
            if self.action == 0:
                method = OPERATOR_CODE[101]
            elif self.action == 1:
                method = OPERATOR_CODE[102]
        elif self.object == 'words':
            if self.action == 0:
                if info.get('condition') == 1:
                    method = OPERATOR_CODE[104]
                else:
                    method = OPERATOR_CODE[103]
            params = {'keyword': info.get("words"), 'maxtimes': 40}
        elif self.object == 'cross':
            if self.action == 0:
                method = OPERATOR_CODE[105]
            elif self.action == 1:
                method = OPERATOR_CODE[106]
            params = {'finger_point': info.get('finger_points')}

        return self.to_op_flow(method, params)

    def to_op_flow(self, method, param):
        """
        输入信息转化为算子，infos={"object":"keyboard","action":0}
        :param  method
        :param  param
        :return:
        """
        assert isinstance(method, str)
        assert isinstance(param, dict)
        operation = {}
        operation[method] = param
        return operation
