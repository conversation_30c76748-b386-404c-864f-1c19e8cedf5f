# !/usr/bin/env python3
# -*- coding: UTF-8 -*-
#
# Copyright (c) 2022 Baidu.com, Inc. All Rights Reserved
#
"""
Desc: 归元评测插件
Authors: <AUTHORS>
Date: 2022-08-16
"""
import os
import importlib
import cv2


from video.plugins.base_plugin import BasePlugin
from video.plugins import modelzoo
from video.plugins.modelzoo import *
from video.recogcv.libs.image_api_from_web import template_match
from video.recogcv.libs.feature_map import sift_detect_swipe
from video.recogcv.image_process.template_match import Template
from util import data

gfilelist = []
groot_path = ''

class OpPlugin(BasePlugin):
    """众测注册基类"""

    def __init__(self):
        """
        @description:
        @param: 
        @return: 
        """
        super().__init__()
        self.mod = importlib.import_module(__name__)

    def __call__(self, param):
        """
        @description: 创建注册算子
        @param: 
        @return: 
        """
        return super().create_operators(param, self.mod)

    def set_info(self, file_list, root_path):
        """
        @description:
        @param: 
        @return: 
        """
        global gfilelist
        global groot_path
        gfilelist = file_list
        groot_path = root_path
        modelzoo.gfilelist = file_list
        modelzoo.groot_path = root_path


class ContainTempOp(object):
    """页面包含截图区域的模板图片"""
    def __init__(self, threshold, maxtimes, distemp_path, temp_path, **param):
        """
        @description:
        @param: 
        @return: 
        """
        self.threshold = threshold
        self.maxtimes = maxtimes
        data_path = data.get_data_path()
        # TODO 未来如何暴露给用户
        self.distemp_path = os.path.join(data_path, distemp_path)
        self.temp_path = os.path.join(data_path, temp_path)
        self.param = param

    def __call__(self, start, image_path):
        """
        @description: 
        @param: 
        @return: 
        """
        logger.debug(image_path)
        curr_img = cut_image(image_path, **self.param)
        distemp_img = cv2.imread(self.distemp_path)
        t = Template(distemp_img, threshold=self.threshold or 0.99, sift=False)
        if t.find_temp(curr_img):
            return False
        temp_img = cv2.imread(self.temp_path)
        res = template_match(curr_img, temp_img)
        self.maxtimes -= 1
        if self.maxtimes == 0:
            return True
        if res.get('data'):
            if res['data']['summary']['maxConfidence'] > self.threshold:
                return True


class SwipeStopOp(object):
    """滑动操作停止"""
    def __init__(self, swipe_sample):
        """
        @description:
        @param: 
        @return: 
        """
        self.swipe_sample = swipe_sample

    def __call__(self, start, image_path):
        """
        @description: 间隔三帧采样，判断页面停止滑动
        @param: 
        @return: 
        """
        logger.debug(image_path)
        if not sift_detect_swipe(gfilelist[start + self.swipe_sample], image_path):
            return True
        