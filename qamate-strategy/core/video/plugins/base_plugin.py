# !/usr/bin/env python3
# -*- coding: UTF-8 -*-
#
# Copyright (c) 2022 Baidu.com, Inc. All Rights Reserved
#
"""
Desc: 注册任务插件
Authors: <AUTHORS>
Date: 2022-05-19
"""
import sys
import importlib


class BasePlugin(object):
    """注册算子基类"""
    def __init__(self):
        pass

    def zoo_operators(self):
        """
        @description: 从model zoo中直接创建算子
        @param: 
        @return: 
        """
        package = 'video.plugins.modelzoo'
        spec = importlib.util.find_spec(package)
        mod = (sys.modules.get(package) or importlib._bootstrap._load(spec))
        


    def create_operators(self, param, mod):
        """
        @description: 创建算子
        @param: param 算子参数；mod 模块文件
        @return: 
        """
        assert isinstance(param, list), ('operators must be list')
        ops = []
        # 相关算子都在mod目录下注册
        for operator in param:
            assert isinstance(operator, dict) and len(
                operator) == 1, 'operator format error'
            op_name = list(operator)[0]
            op_param = {} if operator[op_name] is None else operator[op_name]
            if op_name[-4:] == "Copy":
                op = getattr(mod, op_name[:-4])(**op_param)
            else:
                op = getattr(mod, op_name)(**op_param)
            ops.append(op)
        return ops