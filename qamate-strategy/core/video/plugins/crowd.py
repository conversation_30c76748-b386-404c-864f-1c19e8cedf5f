# !/usr/bin/env python3
# -*- coding: UTF-8 -*-
#
# Copyright (c) 2022 Baidu.com, Inc. All Rights Reserved
#
"""
Desc: 众测场景首尾帧plugin
Authors: <AUTHORS>
Date: 2022-05-19
"""

import importlib
import cv2

from video.plugins.base_plugin import BasePlugin
from video.plugins import suiban
from video.plugins.suiban import *
from video.recogcv.classifier.keyboard.index import SearchIosFirstFrame
from video.recogcv.image_process.cutter import cut_image
from video.recogcv.classifier.cross.detect_cross import ocr_is_cross
from video.recogcv.image_process.page_white import IsWhite
from video.recogcv.image_process.template_match import Template
from video.recogcv.image_process import finder
from video.recogcv.libs.image_api_from_web import find_text
from basics.util import logger


gfilelist = []
groot_path = ''

class OpPlugin(BasePlugin):
    """众测注册基类"""

    def __init__(self):
        """
        @description:
        @param: 
        @return: 
        """
        super().__init__()
        self.mod = importlib.import_module(__name__)

    def __call__(self, param):
        """
        @description: 创建注册算子
        @param: 
        @return: 
        """
        return super().create_operators(param, self.mod)

    def set_info(self, file_list, root_path):
        """
        @description:
        @param: 
        @return: 
        """
        global gfilelist
        global groot_path
        gfilelist = file_list
        groot_path = root_path
        suiban.gfilelist = file_list
        suiban.groot_path = root_path


class KeyboardOp(object):
    """键盘搜索注册"""

    def __init__(self):
        """
        @description:
        @param: filelist:文件名称路径列表，root_path:根目录路径
        @return: 
        """
        self.searcher = SearchIosFirstFrame(gfilelist, groot_path)

    def __call__(self, start, image_path):
        """
        @description:
        @param: 
        @return: 
        """
        assert isinstance(image_path, str)
        return self.searcher.single_check(image_path, start)


class WhiteScreenDisMultiOp(object):
    """多分割白屏算子"""

    def __init__(self, threshold_1, threshold_2):
        """
        @description:
        @param: 
        @return: 
        """
        self.threshold_1 = threshold_1
        self.threshold_2 = threshold_2
        self.white_finder = IsWhite()

    def __call__(self, start, image_path):
        """
        @description:
        @param: start 开始帧号， image_path 图片根目录
        @return: 
        """
        count = 0
        for j in range(1, 8, 2):
            image = cut_image(image_path, 0, 0.1 + 0.1 *
                              int(j), 1, 0.2 + 0.1 * int(j))
            if not self.white_finder._is_white_screen_cut(image, num=self.threshold_1, crop_size=5):
                count = count + 1
        if count / 4 >= self.threshold_2:
            return True
        return False


class BaseFindDiffForwTempOp(object):
    """查找diff"""

    def __init__(self):
        pass

    def __call__(self, start, image_path):
        """
        @description: 模板匹配判断前后帧diff，需要微调返回的index
        @param: start 开始帧号， image_path 图片根目录
        @return: 
        """
        t = Template(image_path)
        if not t.match_in(gfilelist[start - 1]):
            return True


class KeyWordShowOp(object):
    """关键词出现"""
    def __init__(self, keyword, maxtimes):
        """
        @description:
        @param: maxtimes 最大持续帧数
        @return: 
        """
        self.keyword = keyword
        self.maxtimes = maxtimes
    
    def __call__(self, start, image_path):
        """
        @description: 只要keywords集合里面命中一个,就返回True
        @param: 
        @return: 
        """
        self.maxtimes -= 1
        image = cv2.imread(image_path)
        # top_img = cut_image(image, 0, 0, 1, 1)
        res = find_text(image, self.keyword)
        if res.get('data') and (res.get('data').get('hit') == True):
            return True
        if self.maxtimes == 0:
            return True

class KeyWordDissOp(object):
    """关键词消失"""
    def __init__(self, keyword, maxtimes):
        """
        @description:
        @param: maxtimes 最大持续帧数
        @return: 
        """
        self.keyword = keyword
        self.maxtimes = maxtimes
    
    def __call__(self, start, image_path):
        """
        @description:
        @param: 
        @return: 
        """
        self.maxtimes -= 1
        image = cv2.imread(image_path)
        # top_img = cut_image(image, 0, 0, 1, 1)
        res = find_text(image, self.keyword)
        if not (res.get('data') and (res.get('data').get('hit') == True)):
            return True
        if self.maxtimes == 0:
            return True

class OcrCrossShowOp(object):
    """
    @description: 查找十字帧出现的场景
    @param: 
    @return: 
    """
    def __init__(self, maxtimes):
        """
        @description:
        @param: 
        @return: 
        """
        self.maxtimes = maxtimes
    
  
    def __call__(self, start, image_path):
        """
        @description:
        @param: 
        @return: 
        """
        logger.debug(image_path)
        self.maxtimes -= 1      
        # print(left/width, top/height, right/width, bottom/height)
        if start + 1 > len(gfilelist) - 1:
            return False
        if ocr_is_cross(image_path) and (not ocr_is_cross(gfilelist[start + 1])):
            # 最后一个十字帧页面, OCR速度较慢
            return True
        if self.maxtimes == 0:
            return True


class HeadJieguoyeSearchboxToutiao(object):
    """用户自定义模板，集成式"""

    def __init__(self, threshold):
        self.threshold = threshold
        self.status = -1
        self.cut_x1 = 0
        self.cut_x2 = 0
        self.cut_y1 = 0
        self.cut_y2 = 0

    def __call__(self, start, image_path):
        """
        @description: 使用status标记
        @param: start 开始帧号， image_path 图片根目录
        @return: 
        """
        logger.debug(image_path)
        if self.status == -1:
            if self.part_one_search(start, image_path):
                self.status = 1
                logger.debug("---start_1=" + str(start))
        elif self.status == 1:
            if self.part_two_search(start, image_path):
                self.status = 2
                
        elif self.status == 201:
            if self.part_two_else_search(start, image_path):
                logger.debug("---start_3=" + str(start))

        elif self.status == 101:
            if self.part_one_else_search(start, image_path):
                logger.debug("---start_3=" + str(start))
                return True
        return False

    def part_one_search(self, start, image_path):
        """
        @description:
        @param: start 开始帧号， image_path 图片根目录
        @return: 
        """
        # 重新寻找start，搜索框右侧出现搜索文字为start_1
        x_1, y_1 = finder.get_text_from_image(
            gfilelist[start], "搜索", 0.75, 0.05, 1, 0.1)
        if x_1 is not None:
            image = cv2.imread(gfilelist[start])
            try:
                height, width, channels = image.shape
            except Exception as e:
                height, width = image.shape
            slices_x = 0.06
            slices_y = 0.02
            self.cut_x1 = (x_1 / width - slices_x) if ((x_1 /
                                                        width - slices_x) > 0) else 0
            self.cut_x2 = (x_1 / width + slices_x) if ((x_1 /
                                                        width + slices_x) < 1) else 1
            self.cut_y1 = (y_1 / height - slices_y) if ((y_1 /
                                                         height - slices_y) > 0) else 0
            self.cut_y2 = (y_1 / height + slices_y) if ((y_1 /
                                                         height + slices_y) < 1) else 1
            return True

    def part_two_search(self, start, image_path):
        """
        @description:寻找众测 安卓端百度App&头条App 结果页场景 的首帧，目前两个App 都可用;
                    输入单帧，判断输入帧是否为首帧
        @param: start 开始帧号， image_path 图片根目录
        @return: 
        """
        image1 = cut_image(gfilelist[start], round(self.cut_x1, 2), round(self.cut_y1, 2),
                           round(self.cut_x2, 2), round(self.cut_y2, 2))
        tem = Template(image1, threshold=self.threshold, sift=False)
        image2 = cut_image(gfilelist[start - 1], round(self.cut_x1, 2), round(self.cut_y1, 2),
                           round(self.cut_x2, 2), round(self.cut_y2, 2))
        # 搜索文案附近是否有diff，有diff则为首帧
        if tem.compare_image(image1, image2, threshold=self.threshold):
            image1 = cut_image(gfilelist[start], 0.7, 0.9, 1, 1)
            tem = Template(image1, threshold=self.threshold, sift=False)
            image2 = cut_image(gfilelist[start - 1], 0.7, 0.9, 1, 1)
            # 右下角，enter、搜索等搜索入口是否有diff，有diff则为首帧
            # TODO OCR搜索太多次
            if tem.compare_image(image1, image2, threshold=self.threshold):
                x_2, y_2 = finder.get_text_from_image(
                    gfilelist[start + 1], "搜索", 0.75, 0.05, 1, 0.1)
                # 搜索文案是否消失，消失则为首帧
                if x_2 is None:
                    return True
            else:
                self.status = 201
        else:
            self.status = 101
            
    def part_two_else_search(self, start, image_path):
        """
        @description:
        @param: start 开始帧号， image_path 图片根目录
        @return: 
        """
        logger.debug("---start_2=" + str(start))
        image1 = cut_image(gfilelist[start], 0.7, 0.9, 1, 1)
        tem = Template(image1, threshold=self.threshold, sift=False)
        image2 = cut_image(gfilelist[start - 1], 0.7, 0.9, 1, 1)
        if not tem.compare_image(image1, image2, threshold=self.threshold):
            return True                
        
    def part_one_else_search(self, start, image_path):
        """
        @description:
        @param: 
        @return: 
        """
        logger.debug("---start_2=" + str(start))
        image1 = cut_image(gfilelist[start], round(self.cut_x1, 2), round(self.cut_y1, 2),
                                   round(self.cut_x2, 2), round(self.cut_y2, 2))

        tem = Template(image1, threshold=self.threshold, sift=False)
        image2 = cut_image(gfilelist[start - 1], round(self.cut_x1, 2), round(self.cut_y1, 2),
                            round(self.cut_x2, 2), round(self.cut_y2, 2))
        if not tem.compare_image(image1, image2, threshold=self.threshold):
            return True

