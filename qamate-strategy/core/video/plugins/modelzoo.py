# !/usr/bin/env python3
# -*- coding: UTF-8 -*-
#
# Copyright (c) 2022 Baidu.com, Inc. All Rights Reserved
#
"""
Desc:
Authors: <AUTHORS>
Date: 2022-08-16
"""

import os
import importlib
import cv2

from video.recogcv.classifier.keyboard.keyboardshow import KeyboardShow
from video.recogcv.classifier.cross.detect_cross import is_cross
from video.plugins.base_plugin import BasePlugin
from video.recogcv.image_process.cutter import cut_image
from video.recogcv.image_process.page_white import IsWhite
from video.recogcv.image_process import finder
from video.recogcv.image_process.template_match import Template
from video.recogcv.libs.page_dom_from_web import is_contain_image
from basics.util import logger


class OpPlugin(BasePlugin):
    """搜索TTI注册基类"""

    def __init__(self):
        """
        @description:
        @param: 
        @return: 
        """
        super().__init__()
        self.mod = importlib.import_module(__name__)

    def __call__(self, param):
        """
        @description: 创建注册算子
        @param: 
        @return: 
        """
        return super().create_operators(param, self.mod)

    def set_info(self, file_list, root_path):
        """
        @description: 设置全局信息
        @param: 
        @return: 
        """
        global gfilelist
        global groot_path
        gfilelist = file_list
        groot_path = root_path


class CrossShowOp(object):
    """
    @description: 查找十字帧出现的场景
    @param: 
    @return: 
    """
    def __init__(self):
        """
        @description:
        @param: 
        @return: 
        """
        pass
    
    def __call__(self, start, image_path):
        """
        @description:
        @param: 
        @return: 
        """
        logger.debug(image_path)
        # 剪前三分之一的图片
        # curr_img = cut_image(image_path, 0, 0, 1, 0.3)
        x_1, y_1 = finder.get_text_from_image(gfilelist[start], "关注", 0, 0, 1, 0.3)
        if x_1 and is_cross(image_path):
            return True


class KeyboardShowOp(object):
    """
    @description:键盘搜索TTI中注册;直到键盘出现为尾帧，额外信息关注点，搜索语音出现
    @param: 
    @return: 
    """

    def __init__(self):
        """
        @description:
        @param: filelist:文件名称路径列表，root_path:根目录路径
        @return: 
        """
        self.searcher = KeyboardShow(gfilelist, groot_path)

    def __call__(self, start, image_path):
        """
        @description:
        @param: 
        @return: 
        """
        assert isinstance(image_path, str)
        return self.searcher.single_check(image_path, start)


class WhiteScreenDisCutOp(object):
    """剪切部分区域，查找白屏消失"""

    def __init__(self, threshold, **param):
        """
        @description:
        @param: 
        @return: 
        """
        self.threshold = threshold
        # crop_size 可能缺省,默认值为10
        self.crop_size = param.get("crop_size") or 10
        if param.get("crop_size") is not None:
            del param["crop_size"]
        self.param = param
        self.white_finder = IsWhite()

    def __call__(self, start, image_path):
        """
        @description: 剪切后调用白屏算法
        @param: 
        @return: 
        """
        logger.debug(image_path)
        image = cut_image(image_path, **self.param)
        if not self.white_finder._is_white_screen_cut(image, num = self.threshold, crop_size = self.crop_size):
            return True

class WhiteScreenCutOp(object):
    """剪切部分区域，查找白屏出现"""

    def __init__(self, threshold, **param):
        """
        @description:
        @param: 
        @return: 
        """
        self.threshold = threshold
        self.crop_size = param.get("crop_size") or 10
        if param.get("crop_size") is not None:
            del param["crop_size"]
        self.param = param
        self.white_finder = IsWhite()

    def __call__(self, start, image_path):
        """
        @description:
        @param: start 开始帧号， image_path 图片根目录
        @return: 
        """
        logger.debug(image_path)
        image = cut_image(image_path, **self.param)
        if self.white_finder._is_white_screen_cut(image, num = self.threshold, crop_size = self.crop_size):
            return True


class FindDiffForwTempOp(object):
    """模板匹配对比向前查找"""

    def __init__(self, threshold, **param):
        """
        @description:
        @param: 
        @return: 
        """
        self.threshold = threshold
        self.param = param

    def __call__(self, start, image_path):
        """
        @description: 模板匹配判断前后帧diff，需要微调返回的index
        @param: start 开始帧号， image_path 图片根目录
        @return: 
        """
        curr_img = cut_image(image_path, **self.param)
        pre_img = cut_image(gfilelist[start - 1], **self.param)
        t = Template(curr_img, threshold=self.threshold or 0.99, sift=False)
        if not t.match_in(pre_img):
            return True

            
class FindDiffBackTempOp(object):
    """模板匹配判断前后两帧发生diff"""

    def __init__(self, threshold, **param):
        self.threshold = threshold
        self.param = param

    def __call__(self, start, image_path):
        """
        @description: 模板匹配判断前后帧diff，需要微调返回的index
        @param: start 开始帧号， image_path 图片根目录
        @return: 
        """
        logger.debug(image_path)
        if start > len(gfilelist) - 2:
            return False
        curr_img = cut_image(image_path, **self.param)
        back_img = cut_image(gfilelist[start + 1], **self.param)
        t = Template(curr_img, threshold=self.threshold or 0.99, sift=False)
        if not t.match_in(back_img):
            return True


class WordShowOp(object):
    """关键词出现"""
    def __init__(self, keyword, maxtimes):
        """
        @description:
        @param: maxtimes 最大持续帧数
        @return: 
        """
        self.keyword = keyword
        self.maxtimes = maxtimes
    
    def __call__(self, start, image_path):
        """
        @description:
        @param: 
        @return: 
        """
        self.maxtimes -= 1
        x_1, y_1 = finder.get_text_from_image(image_path, self.keyword, 0, 0.4, 1, 1)
        if x_1 or self.maxtimes == 0:
            return True

class ContainImageOp(object):
    """页面包含图片"""
    def __init__(self, sample_num, **param):
        """
        @description:
        @param: 
        @return: 
        """
        self.sample_num = sample_num
        self.param = param


    def __call__(self, start, image_path):
        """
        @description: 
        @param: 
        @return: 
        """
        if start + self.sample_num < len(gfilelist) - 1:
            cur_path = gfilelist[start + self.sample_num]
        else:
            cur_path = image_path
        logger.debug(cur_path)
        curr_img = cut_image(cur_path, **self.param)
        hist = cv2.calcHist([curr_img], [0], None, [256], [0, 250])
        if [item > 1000 for item in hist].count(1) < 150:
            return False
        if is_contain_image(curr_img):
            return True

class FindDiffBackSimOp(object):
    """页面相似度查找不同页面"""
    def __init__(self):
        """
        @description:
        @param: 
        @return: 
        """
        pass

    def __call__(self, start, image_path):
        """
        @description: 
        @param: 
        @return: 
        """
        logger.debug(image_path)
        if is_contain_image(image_path):
            return True


class BlackScreenDisCutOp(object):
    """黑屏页面消失（非黑屏页面）"""
    def __init__(self):
        """
        @description:
        @param: 
        @return: 
        """
        pass

    def __call__(self, start, image_path):
        """
        @description: 
        @param: 
        @return: 
        """
        logger.debug(image_path)
        if not is_contain_image(image_path):
            return True

class MiddleStatusJumpOp(object):
    """跳过中间状态,经过黑白屏,兼容直接闪变"""
    def __init__(self, threshold, **param):
        """
        @description:
        @param: 
        @return: 
        """
        self.status = -1
        self.threshold = threshold
        self.crop_size = param.get("crop_size") or 10
        if param.get("crop_size") is not None:
            del param["crop_size"]
        self.param = param
        self.whiter = IsWhite()


    def __call__(self, start, image_path):
        """
        @description:
        @param: 
        @return: 
        """
        image = cut_image(image_path, **self.param)
        if self.status == -1:
            if self.whiter._is_white_screen_cut(image, num = self.threshold, crop_size = self.crop_size):
                # 经过白屏出现，消失的变化
                self.status = 0
            else:
                # 未经过白屏，直接变化
                self.status = 1