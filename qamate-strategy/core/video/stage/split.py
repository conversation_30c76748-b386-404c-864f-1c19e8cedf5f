# !/usr/bin/env python3
# -*- coding: UTF-8 -*-
#
# Copyright (c) 2022 Baidu.com, Inc. All Rights Reserved
#
"""
Desc:
Authors: <AUTHORS>
Date: 2022-09-29
"""
import os
import sys
import cv2
from video.recogcv.image_process.cutter import cut_image

from video.recogcv.image_process.template_match import Template


def get_tendcy(frame_dir, thresh=0.999):
    """
    @description: 通用转场趋势，逐个遍历
    @param: 
    @return: 
    """
    split_tag = '.'
    picnames = [item.split(split_tag) for item in os.listdir(frame_dir)
                if (item != 'data.json' and not item.startswith('.'))]
    picnames.sort(key=lambda x: int(x[0]))
    sort_picnames = [split_tag.join(item) for item in picnames]
    origin_score_list = []

    score_list = []
    stage_first_img = cv2.imread(os.path.join(frame_dir, sort_picnames[0]))
    stage_index = 0
    stages = []
    stage_list = [0]
    for i in range(1, len(sort_picnames)):
        cur_img = cv2.imread(os.path.join(frame_dir, sort_picnames[i]))
        t = Template(cur_img, threshold=thresh, sift=False)
        isshot = t.match_in(stage_first_img)
        origin_score_list.append(isshot)
        if not isshot:
            stage_first_img = cur_img
            stage_list.append(i)
    # print(stage_list)
    # print("score finish ...")
    # ylable = origin_score_list
    # xlable = [i for i in range(len(sort_picnames) - 1)]
    # plt.plot(xlable, ylable)
    # plt.show()
    return stage_list


def merge_offset(stages):
    """
    @description:对于一些连续变化的中见状态[36, 37, 38, 39, 40, 42, 44]合并
    @param: 
    @return: 
    """
    if len(stages) <= 2:
        return stages
    merge_offset = 5
    merge_stage = [stages[0]]
    for i in range(len(stages)):
        if i == 0 or i == len(stages) - 1:
            continue
        if (stages[i] - stages[i - 1] <= merge_offset) and (stages[i + 1] - stages[i] <= merge_offset):
            continue
        else:
            merge_stage.append(stages[i])
    merge_stage.append(stages[-1])
    return merge_stage


def check(left, right, frame_dir, sort_picnames, thresh=0.9, section=[0, 0, 1, 1]):
    """
    @description: 
    @param: 
    @return: 
    """
    left_img = cv2.imread(os.path.join(frame_dir, sort_picnames[int(left)]))
    right_img = cv2.imread(os.path.join(frame_dir, sort_picnames[int(right)]))
    left_img = cut_image(left_img, *section)
    right_img = cut_image(right_img, *section)
    t = Template(left_img, threshold=thresh, sift=False)
    isshot = t.match_in(right_img)
    return isshot


def binary_tendcy(frame_dir, thresh=0.9, merge=True):
    """
    @description: 二分搜索转场点
    @param:
    @return:
    """
    picnames = os.listdir(frame_dir)
    if len(picnames) < 1:
        return []
    if picnames[0].find('_') != -1:
        split_tag = '_'
    else:
        split_tag = '.'

    picnames = [item.split(split_tag) for item in os.listdir(frame_dir)
                if (item != 'data.json' and not item.startswith('.'))]
    picnames.sort(key=lambda x: int(x[0]))
    sort_picnames = [split_tag.join(item) for item in picnames]

    def binary_search(left, right, target):
        """二分搜索最后一个相似位置"""
        while (int(left) < int(right)):
            mid = (left + right) // 2
            if not check(mid, target, frame_dir, sort_picnames, thresh):
                right = mid
            else:
                left = mid + 1
        return left

    res = []
    node = 0
    right = len(sort_picnames)
    while node < len(sort_picnames):
        node = (binary_search(node, right, node))
        res.append(node)
    if merge == False:
        return res[:-1]
    return merge_offset(res)[:-1]


def multi_stage_split(frame_dir):
    """
    @description: 一次调用, 多个步骤显示
    @param: 
    @return: 
    """
    res = {}
    res['100same'] = binary_tendcy(frame_dir, 0.999, merge=False)

    res['90same'] = binary_tendcy(frame_dir, 0.9)
    res['95same'] = binary_tendcy(frame_dir, 0.95)

    print(set(res['95same']) - set(res['100same']))
    print(set(res['90same']) - set(res['100same']))
    return res
