# !/usr/bin/env python3
# -*- coding: UTF-8 -*-
#
# Copyright (c) 2022 Baidu.com, Inc. All Rights Reserved
#
"""
Desc    : 
Authors : lile<PERSON>o(<EMAIL>)
Date    : 2022/12/19 8:52 PM
"""
import os

from video.stage.split import check


def back_diff(frame_dir, thresh=0.9, section=[0, 0, 1, 1]):
    """
    @description: 搜索和尾帧相似的第一帧
    @param:
    @return:
    """
    picnames = os.listdir(frame_dir)
    if len(picnames) < 1:
        return 0
    if picnames[0].find('_') != -1:
        split_tag = '_'
    else:
        split_tag = '.'

    picnames = [item.split(split_tag) for item in os.listdir(frame_dir)
                if (item != 'data.json' and not item.startswith('.'))]
    picnames.sort(key=lambda x: int(x[0]))
    sort_picnames = [split_tag.join(item) for item in picnames][:int(0.95 * len(picnames))]

    def binary_search(left, right, target):
        """二分搜索最后一个相似位置"""
        while (int(left) < int(right)):
            mid = (left + right) // 2
            if not check(mid, target, frame_dir, sort_picnames, thresh, section):
                left = mid + 1
            else:
                right = mid
        return left

    node = 0
    right = len(sort_picnames)
    node = binary_search(node, right, right - 1)
    return node
