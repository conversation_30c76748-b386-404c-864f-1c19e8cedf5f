# !/usr/bin/env python3
# -*- coding: UTF-8 -*-
#
# Copyright (c) 2022 Baidu.com, Inc. All Rights Reserved
#
"""
Desc: 固定的算法执行器
Authors: <AUTHORS>
Date: 2022-05-19
"""

import os
import importlib
import sys

from video.core.picker import TimePicker

from basics.util import data
from basics.util import logger


class TimeRunner(object):
    """
    执行首尾帧运行入口
    """

    def __init__(self, system='Android', app='searchbox',
                 scene='searchbox_tti', task='suiban'):
        """
        初始化
        :param app: app名称
        :param scene: 场景
        """
        self.system = system
        self.app = app
        self.scene = scene
        self.task = task
        self.fingerpoint = []
        self.stage_list = []
        self.start_method_list = []
        self.end_method_list = []

    def set_fingerpoint(self, fingerpoint):
        """
        @description: 设置按压点位置
        @param: 
        @return: 
        """
        self.fingerpoint = fingerpoint

    def set_stage_list(self, stage_list):
        """
        @description:
        @param: 
        @return: 
        """
        self.stage_list = stage_list

    def set_start_op(self, start_op_flow):
        """
        传入首帧算法执行流
        :param start_op_flow:
        :return:
        """
        self.start_method_list = start_op_flow

    def set_end_op(self, end_op_flow):
        """
        传入尾帧算法执行流
        :param end_op_flow:
        :return:
        """
        self.end_method_list = end_op_flow

    def start(self, mode=0):
        """ 初始化 """
        start_method_list = None
        end_method_list = None
        # plugin name默认为suiban
        start_init_index = 5
        if mode == 0:
            # 默认通过算法文件获取执行流
            if 'android' == self.system:
                self.cv_config = data.get('algorithms/{}_android.json'.format(self.task))
            elif 'iOS' or 'ios' == self.system:
                self.cv_config = data.get('algorithms/{}_iOS.json'.format(self.task))

            try:
                if "plugin" in self.cv_config:
                    self.task = self.cv_config['plugin']['name']
                alg_obj = self.cv_config[self.app][self.scene]
                if "start_index" in alg_obj:
                    start_init_index = alg_obj['start_index']
                if "start_algs" in alg_obj:
                    start_method_list = alg_obj['start_algs']
                if "end_algs" in alg_obj:
                    end_method_list = alg_obj['end_algs']
            except Exception as e:
                logger.error("ALGORITHM: Failed 算法配置文件非法，请检查。")
                logger.error(e)
                raise e
        elif mode == 1:
            # 用户自定义模式获取执行流
            try:
                start_method_list = self.start_method_list
                end_method_list = self.end_method_list
            except Exception as e:
                logger.error("用户自定义获取算法执行流失败")
                logger.error(e)

        package = 'video.plugins.{}'.format(self.task)
        spec = importlib.util.find_spec(package)
        mod = (sys.modules.get(package) or importlib._bootstrap._load(spec))
        plugin = mod.OpPlugin()

        # 获取算法运行实例
        self.picker = TimePicker(start_method_list=start_method_list, end_method_list=end_method_list,
                                 plugin=plugin)

        self.picker.set_default_start(start_init_index, self.fingerpoint, self.stage_list)

    def recognize_video(self, frames, image_root_path, mode=0, start_index=0):
        """
        @description:
        @param: mode = 0, 返回首尾帧结果, mode=1 ,仅启动首帧判断; mode=2, 仅启动尾帧判读 
        @return: 
        """
        self.picker.image_path = image_root_path
        # 众测路径传入未改，记录一个todo
        self.picker.plugin.set_info([item['path'] for item in frames], image_root_path)
        self.picker.set_file_list(frames)
        self.picker.start_frame = start_index

        if mode == 0:
            self.picker.find_head()
            self.picker.find_rear(self.picker.start_frame)
        elif mode == 1:
            self.picker.find_head()
            self.picker.end_frame = self.picker.start_frame
        elif mode == 2:
            self.picker.find_rear(self.picker.start_frame)
        else:
            logger.error("recognize video: mode error")

        smart_first_frame = self.picker.start_frame
        smart_last_frame = self.picker.end_frame

        # 触发兜底策略 
        if smart_first_frame is None:
            smart_first_frame = int(len(frames) / 2)
        if smart_last_frame is None:
            smart_last_frame = int(len(frames) / 2)

        logger.debug("start:" + str(smart_first_frame))
        logger.debug("end:" + str(smart_last_frame))
        return smart_first_frame, smart_last_frame
