# !/usr/bin/env python3
# -*- coding: UTF-8 -*-
#
# Copyright (c) 2022 Baidu.com, Inc. All Rights Reserved
#
"""
Desc: 首尾帧执行算法
Authors: <AUTHORS>
Date: 2022-05-19
"""

import os
import sys
import time
import cv2
import traceback

from video.core.searcher import SingleBackwardSearcher, SingleForwardSearcher
from video.core.searcher import <PERSON>ForwardSearcher, StageBackwardSearcher
from video.recogcv.image_process.page_white import IsWhite
from video.recogcv.image_process.cutter import cut_image
from video.recogcv.classifier.keyboard.index import SearchIosFirstFrame

from basics.util import logger
from basics.util import data


class TimePicker(object):
    """
    首尾帧获取算法组装
    """

    def __init__(self, start_method_list=None, end_method_list=None, plugin=None):
        """
        输入配置的开始帧算法列表（包括参数）和结束帧算法列表（包括参数）
        :param start_method_list: {"find_cross_dis_query":["start=0","0.9"]}
        :param end_method_list: {"find_dis_from_back_to_front":["start=0","0.9"]}
        """
        self.start_method_list = start_method_list
        self.end_method_list = end_method_list
        self.init_start_methods()
        self.init_end_methods()
        self.image_path = None
        self.start_frame = None  # 开始帧图片位置
        self.end_frame = None  # 结束帧图片位置
        self.index = None # 当前帧位置
        self.file_list = None
        self.time = -1
        self.end_point = 0
        self.x = 0  # 获取到点击位置x坐标
        self.y = 0  # 获取到点击位置y坐标
        self.default_start = 5
        self.plugin = plugin #一个业务场景对应一个plugin
        self.fingerpoint = []
        self.stage_list = []

    def init_start_methods(self):
        """
        配置起始帧默认算法
        find_cross_dis_query(self, start, threshold=None, slices_x=0.008, y=0.2, slices_y=0.7)
        :return:
        """
        return

    def init_end_methods(self):
        """
        配置尾帧默认算法
        _find_diff_from_back_to_front(self, start, threshold=None):
        :return:
        """
        if not self.end_method_list or not self.end_method_list.keys():
            self.end_method_list = {"FindDiffBackTempOp": [0.7]}

    def set_default_start(self, start, fingerpoint, stage_list):
        """
        设置开始运算图片识别算法的位置
        :param start:
        :return:
        """
        if start:
            self.default_start = int(start)
        if fingerpoint:
            self.fingerpoint = fingerpoint
        if stage_list:
            self.stage_list = stage_list    


    def set_file_list(self, frames):
        """
        @description: 策略执行器
        @param: frames 图片路径
        @return: 
        """
        self.index = self.default_start  # 设置算法初始运行时开始图片位置，不为1是为了降低干扰因素
        logger.debug("start:" + str(self.index))
        # 初始化图片流
        self.file_list = [item['path'] for item in frames]
        if not self.file_list:
            logger.error("ALGORITHM: 无法运行算法，缺少图片流")
            return

    def do_searcher(self, method, param):
        """
        @description: 执行searcher
        @param: method 函数方法名
        @return: 
        """
        # method如果无backward关键字，默认为true
        searcher_flag = 1
        if param.get("stagebackward") is True:
            searcher_flag = 2
            searcher = StageBackwardSearcher(self.index, self.file_list, self.stage_list)
            del param['stagebackward']
        elif param.get("stagebackward") is False:
            searcher_flag = 2
            searcher = StageForwardSearcher(self.index, self.file_list, self.stage_list)
            del param['stagebackward']
        elif param.get("backward") is None:
            searcher = SingleBackwardSearcher(self.index, self.file_list)
        elif param.get("backward") == True:
            searcher = SingleBackwardSearcher(self.index, self.file_list)
            del param['backward']
        else:
            searcher = SingleForwardSearcher(self.index, self.file_list)
            del param['backward']
        if param.get("before_shift_index") is not None:
            searcher.fine_tune_index(param['before_shift_index'])
            del param['before_shift_index']
        after_shift_index = 0
        if param.get("after_shift_index") is not None:
            after_shift_index = param['after_shift_index']
            del param['after_shift_index']
        # 如果method为十字帧，增加输入的“按压点位置”
        if method == 'CrossShowOp':
            param['finger_point'] = self.fingerpoint
        opsbox = [{method:param}]
        op = self.plugin(opsbox)[0]
        run_index = searcher.run(op)
        if searcher_flag == 2:
            run_index = searcher.fine_tune_index(after_shift_index, run_index)
        else:
            run_index = run_index + after_shift_index
        return run_index

    def find_head(self):
        """查询首帧位置"""
        self.index = self.start_frame
        try:
            # TODO start_method_list 类型需要修改
            if self.start_method_list:
                logger.debug("ALGORITHM: start_method is " +
                             str(self.start_method_list.keys()))
                # 根据start_method_list，构建前向后向searcher，若干个searcher组成查询;
                # 集成模式和组装模式, 每次从index位置开始查询
                for (method, param) in self.start_method_list.items():
                    logger.debug("index is {}, next method: {}".format(self.index, method))
                    self.index = self.do_searcher(method, param)
            else:
                logger.debug("ALGORITHM: start_method is None")
        except Exception as e:
            logger.error("ALGORITHM: Error 执行开始帧算法出现异常,请检查算法对应参数是否正确。异常如下：")
            logger.error("--------------------------")
            logger.error(method)
            logger.error(e.args)
            logger.error(traceback.format_exc())
            self.start_frame = None
        self.start_frame = self.index

    def find_rear(self, start):
        """查询尾帧"""
        # start 合法性校验，确保有start传入
        self.index = start
        try:
            if self.end_method_list:
                logger.debug("ALGORITHM: end_method is " +
                             str(self.end_method_list.keys()))
                # 根据end_method_list，构建前向后向searcher，若干个searcher组成查询;
                # 集成模式和组装模式
                for (method, param) in self.end_method_list.items():
                    logger.debug("index is {}, next method: {}".format(self.index, method))
                    self.index = self.do_searcher(method, param)
                    if self.index == -1:
                        self.index = len(self.file_list) - 6
                        break
            else:
                logger.debug("ALGORITHM: end_method is None")
        except Exception as e:
            logger.error("ALGORITHM: Error 执行结束帧算法出现异常,请检查算法对应参数是否正确。异常如下：")
            logger.error("--------------------------")
            logger.error(method)
            logger.error(e.args)
            logger.error(traceback.format_exc())
            self.end_frame = None
        self.end_frame = self.index
