# !/usr/bin/env python3
# -*- coding: UTF-8 -*-
#
# Copyright (c) 2022 Baidu.com, Inc. All Rights Reserved
#
"""
Desc: 搜索器类，用于遍历帧列表
Authors: <AUTHORS>
Date: 2022-05-19
"""

import abc
from basics.util import logger

class BaseSearcher(object):
    """定义首尾帧搜索器"""

    def __init__(self, start_index, file_list):
        """
        @description:
        @param: start_index 起始编号
        @param: file_list 帧路径或帧内容
        @return: 
        """
        self.start = start_index
        self.file_list = file_list

    @abc.abstractclassmethod
    def run(self, func_list):
        """定义从当前点开始的搜索"""
        pass

    def fine_tune_index(self, shift_index):
        """对固定index微调"""
        logger.debug(shift_index)
        if shift_index > 0 and shift_index < 1:
            self.start = int(shift_index * len(self.file_list))
        else:
            self.start = self.start + shift_index
        logger.debug("fine tune index is {}".format(self.start))
        return self.start

class SingleForwardSearcher(BaseSearcher):
    """定义从当前点向前的搜索器"""

    def __init__(self, start_index, file_list):
        """
        @description:
        @param: 
        @return: 
        """
        super().__init__(start_index, file_list)

    def run(self, op):
        """从当前点向前推理"""
        # 起始index合法性校验
        if self.start >= len(self.file_list):
            self.start = len(self.file_list) - 1

        logger.debug("start backward searcher, the index is {}".format(self.start))
        assert type(op) != list, "single forward searcher error."
        for i in range(self.start, -1, -1):
            if op(i, self.file_list[i]):
                return i
        return int(len(self.file_list)/ 2)
    

class StageForwardSearcher(BaseSearcher):
    """定义从当前点向前的搜索器"""

    def __init__(self, start_index, file_list, stage_list):
        """
        @description:
        @param: 
        @return: 
        """
        super().__init__(start_index, file_list)
        self.stage_list = stage_list

    def run(self, op):
        """从当前点向前推理"""
        # 起始index合法性校验
        if self.start >= len(self.file_list):
            self.start = len(self.file_list) - 1

        logger.debug("start stage forward searcher, the index is {}".format(self.start))
        assert type(op) != list, "stage forward searcher error."
        for i in self.stage_list[::-1]:
            if op(i, self.file_list[i]):
                return i
        return int(len(self.file_list)/ 2)

    def fine_tune_index(self, shift_index, cur):
        """
        @description: stage_list偏移
        @param: 
        @return: 
        """
        logger.debug(shift_index)
        for i, val in enumerate(self.stage_list):
            if val == cur:
                cur = self.stage_list[i + shift_index]
                break        
        logger.debug("fine tune index is {}".format(cur))
        return cur


class StageBackwardSearcher(BaseSearcher):
    """定义从当前点向后的搜索器"""

    def __init__(self, start_index, file_list, stage_list):
        """
        @description:
        @param: 
        @return: 
        """
        super().__init__(start_index, file_list)
        self.stage_list = stage_list

    def run(self, op):
        """从当前点向后推理"""
        # 起始index合法性校验
        if not self.file_list:
            return -1
        
        if self.start <= len(self.file_list) - 1:
            logger.debug("start stage backward searcher, the index is {}".format(self.start))
            assert type(op) != list, "single backward searcher error."
            i = self.start
            j = 0
            for ii, item in enumerate(self.stage_list):
                if item >= i:
                    i = item
                    j = ii
                    break
            while i < self.stage_list[-1] and j < len(self.stage_list) - 1:
                # TODO 返回算子状态，而非一个false；
                logger.debug(self.file_list[i])
                res = op(i, self.file_list[i])
                if res == True:
                    return i
                elif res == -1:
                    return -1
                # # 如果为滑动算子，自动位置偏移
                # if op.__class__.__name__ == 'SwipeStopOp':
                #     i += op.swipe_sample
                #     continue
                # if op.__class__.__name__ == 'ContainImageOp':
                #     i += op.sample_num
                #     continue
                i = self.stage_list[j]
                j += 1
        return int(self.stage_list[j])
    
    def fine_tune_index(self, shift_index, cur):
        """
        @description: stage_list偏移
        @param: 
        @return: 
        """
        logger.debug(shift_index)
        for i, val in enumerate(self.stage_list):
            if val == cur:
                if i + shift_index < len(self.stage_list):
                    cur = self.stage_list[i + shift_index]
                break        
        logger.debug("fine tune index is {}".format(cur))
        return cur

class SingleBackwardSearcher(BaseSearcher):
    """定义从当前点向后的搜索器"""

    def __init__(self, start_index, file_list):
        """
        @description:
        @param: 
        @return: 
        """
        super().__init__(start_index, file_list)

    def run(self, op):
        """从当前点向后推理"""
        # 起始index合法性校验
        if not self.file_list:
            return -1
        if self.start <= len(self.file_list) - 1:
            logger.debug("start backward searcher, the index is {}".format(self.start))
            assert type(op) != list, "single backward searcher error."
            i = self.start
            while i < len(self.file_list):
                # TODO 返回算子状态，而非一个false；
                res = op(i, self.file_list[i])
                if res == True:
                    return i
                elif res == -1:
                    return -1
                # 如果为滑动算子，自动位置偏移
                if op.__class__.__name__ == 'SwipeStopOp':
                    i += op.swipe_sample
                    continue
                if op.__class__.__name__ == 'ContainImageOp':
                    i += op.sample_num
                    continue
                i += 1
        return int(len(self.file_list)/ 2)


class ForwardSearcher(BaseSearcher):
    """定义从当前点向前的搜索器"""

    def __init__(self, start_index, file_list):
        """
        @description:
        @param: 
        @return: 
        """
        super().__init__(start_index, file_list)

    def run(self, ops):
        """
        @description:从当前点向前搜索
        @param: 
        @return: 
        """
        assert type(op) == list, "forward searcher error."
        for i in range(self.start, -1, -1):
            for op in ops:
                if op(i, self.file_list[i]):
                    return i
        return int(len(self.file_list)/ 2)


class BackwardSearcher(BaseSearcher):
    """定义从当前点向后的搜索器"""

    def __init__(self, start_index, file_list):
        """
        @description:
        @param: 
        @return: 
        """
        super().__init__(start_index, file_list)

    def run(self, ops):
        """
        @description: 从当前点向后搜索
        @param: 
        @return: 
        """
        for i in range(self.start, len(self.file_list)):
            for op in ops:
                if op(i, self.file_list[i]):
                    return i
        return int(len(self.file_list)/ 2)
