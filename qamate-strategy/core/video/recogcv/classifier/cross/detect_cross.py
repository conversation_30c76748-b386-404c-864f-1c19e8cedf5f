# !/usr/bin/env python3
# -*- coding: UTF-8 -*-
#
# Copyright (c) 2022 Baidu.com, Inc. All Rights Reserved
#
"""
Desc: 判断当前页面为十字帧页面
Authors: <AUTHORS>
Date: 2022-06-29
"""
import cv2
import numpy as np
from video.recogcv.image_process.cutter import cut_image
from video.recogcv.libs.image_api_from_web import find_text
from basics.util import logger

def is_cross(image_input, img_write=False):
    """
    @description: 判断当前页面为十字帧
    @param: 
    @return: 
    """
    if isinstance(image_input, type('')):
        image = cv2.imread(image_input)
    else:
        image = image_input

    image_gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    img_edges = cv2.Canny(image_gray, 30, 250, apertureSize=3)
    lines = cv2.HoughLines(img_edges, 1, np.pi / 180, 120)
    if lines is None:
        return False
    lines1 = lines[:, 0, :]
    angle = set()
    for line in lines1[:]:
        rho, theta = line
        if rho > 75:
            a = np.cos(theta)
            b = np.sin(theta)
            x0 = a * rho
            y0 = b * rho
            x1 = int(x0 + 1000 * (-b))
            y1 = int(y0 + 1000 * (a))
            x2 = int(x0 - 1000 * (-b))
            y2 = int(y0 - 1000 * (a))
            if str(round(theta, 2)) == '0.0':
                if abs(x1 - 200) < 5:
                    angle.add('0.0')
            elif str(round(theta, 2)) == '1.57':
                if abs(y1 - 200) < 5:
                    angle.add('1.57')
            cv2.line(image, (x1, y1), (x2, y2), (0, 0, 255), 2, 4)
    if img_write:
        cv2.imwrite("test.png", image)
    return ('0.0' in angle and '1.57' in angle)


def ocr_is_cross(image_input):
    """
    @description:
    @param: 
    @return: 
    """
    if isinstance(image_input, type('')):
        image = cv2.imread(image_input)
    else:
        image = image_input

    top_img = cut_image(image, 0, 0, 1, 0.15)
    res = find_text(top_img, ["P:1/1"])
    if not res:
        logger.error("ocr detect cross: ocr find_text return None")
        return False
    if res.get('data') and (res.get('data').get('hit') == True):
        return True
    return False
