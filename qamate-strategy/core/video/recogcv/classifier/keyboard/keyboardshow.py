# !/usr/bin/env python3
# -*- coding: UTF-8 -*-
#
# Copyright (c) 2022 Baidu.com, Inc. All Rights Reserved
#
"""
Desc: 页面加载过程中，键盘出现
Authors: <AUTHORS>
Date: 2022-06-29
"""
import os
import cv2
from basics.util import logger as log
from video.recogcv.classifier.keyboard.index import is_type, is_diff_with_back_image
from video.recogcv.classifier.keyboard.commons.horizental import split_by_horizontal_line


class KeyboardShow(object):
    """
    @description:
    @param: 
    @return: 
    """
    def __init__(self, picture_list, root_path, mode=0):
        """
        初始化图片
        :param root_path:
        :param picture_list:
        :param mode 0 键盘上滑，mode 1 键盘下降
        """
        self.root_path = root_path
        self.keyboard_len_start = 0
        self.start = 0
        self.pre_image = None
        self.status = 0
        self.picture_list = picture_list
        self.mode = mode

    def single_check(self, img_path, start):
        """
        @description: 单个图片，输出是否是首帧
        @param: 
        @return: 
        """
        if not os.path.exists(img_path):
            log.error("{} is not exist".format(img_path))
        image = cv2.imread(img_path)
        self.start = start
        if image is None:
            return False
        if start < 3 and is_type(image, "keyboard"):
            self.status = 2
            level_info = split_by_horizontal_line(
                image, width_ratio=0.9, filter_height=0.5, src_path=None)
            # level_info = prepare(self.root_path, picname)
            self.keyboard_len_start = level_info[-1][-1] - level_info[-1][0]
        if self.start >= len(self.picture_list) - 6:
            # 到整个文件夹的末端了，用户非正常配置算法文件
            log.debug("传入的起始帧号{}，文件夹长度{}，首帧算法异常".format(
                str(self.start), str(len(self.picture_list))))
            return -1
        if self.status == 0 and self.mode == 0:
            log.debug("keyboard show, first start analyse {}".format(str(start)))
            if self.keyboard_show(image, img_path):
                return True
        if self.status == 0 and self.mode == 1:
            log.debug("keyboard dis, first start analyse {}".format(str(start)))
            if self.keyboard_dis(image, img_path):
                return True
        return False


    def keyboard_show(self, image, picname):
        """
        找到含有键盘的图片，且和后一帧全图不发生变化，键x盘稳定
        :param picname:
        :return:
        """
        # step1: 从第一张截图逐个找图片，检查目录index是否溢出
        if self.start + 2 < len(self.picture_list):
            next_picname = self.picture_list[self.start + 2]
        else:
            return False
        if is_type(image, "keyboard") and is_type(os.path.join(self.root_path, next_picname), "keyboard"):
            log.debug("keyboard start analyse {}".format(picname))
            # step2: 和后一帧图片对比不变化
            back_img_name = self.picture_list[self.start + 1]
            back_img = cv2.imread(os.path.join(self.root_path, back_img_name))
            if not is_diff_with_back_image(back_img, image):
                return True
        return False

    def keyboard_dis(self, image, picname):
        """
        查找键盘开始下降的第一帧
        :param image:
        :param picname:
        :return:
        """
        if self.start + 2 < len(self.picture_list):
            next_picname = self.picture_list[self.start + 2]
        else:
            return False
        if is_type(image, "keyboard") and is_type(os.path.join(self.root_path, next_picname), "keyboard"):
            log.debug("keyboard start analyse {}".format(picname))
            # step2: 和后一帧图片对比 开始 变化
            back_img_name = self.picture_list[self.start + 1]
            back_img = cv2.imread(os.path.join(self.root_path, back_img_name))
            if is_diff_with_back_image(back_img, image):
                return True
        return False