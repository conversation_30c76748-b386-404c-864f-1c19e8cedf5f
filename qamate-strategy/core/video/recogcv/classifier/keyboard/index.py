# !/usr/bin/env python3
# -*- coding: UTF-8 -*-
#
# Copyright (c) 2021 Baidu.com, Inc. All Rights Reserved
#
"""
Desc:
Authors: <AUTHORS>
Date:    2022/3/21
"""
import os
import cv2
import sys

from basics.util import logger as log
from video.recogcv.classifier.keyboard.processor.predict_cls import single_infer
from video.recogcv.classifier.keyboard.commons.horizental import split_by_horizontal_line
from video.recogcv.classifier.keyboard.commons.page_blank import PageBlankImpl
from basics.util import data
from skimage.metrics import structural_similarity
from functools import singledispatch


@singledispatch
def is_type(image, flag):
    """
    判断非键盘类图片
    :param image:
    :return:
    """
    type_map = {"keyboard": 1, "others": 0}
    thresh = {"keyboard": 0.5, "others": 0.5}
    configinfo = data.get('keyboard/inference_cls_0324.yaml')
    class_ids, scores = single_infer(configinfo, image)
    return class_ids[0] == type_map[flag] and scores[0] > thresh[flag]


@is_type.register(str)
def _a(imgname, flag):
    image = cv2.imread(imgname)
    if image is None:
        return False
    return is_type(image, flag)


def is_diff_with_back_image(back_image_array, current_image_array):
    """
    和帧后图片对比
    :param back_image_array:
    :param current_image_array:
    :return:
    """
    score, diff_data = structural_similarity(
        back_image_array, current_image_array, full=True, multichannel=True)
    # log.info("same score is " + str(score))
    return score < 0.96


class SearchIosFirstFrame():
    """搜索ios结果页键盘变化的首帧"""

    def __init__(self, picture_list, root_path):
        """
        初始化图片
        :param root_path:
        :param picture_list:
        """
        self.root_path = root_path
        self.keyboard_len_start = 0
        self.start = 0
        self.pre_image = None
        self.status = 0
        self.picture_list = picture_list


    def single_check(self, img_path, start):
        """
        @description: 单个图片，输出是否是首帧
        @param: 
        @return: 
        """
        if not os.path.exists(img_path):
            log.error("{} is not exist".format(img_path))
        image = cv2.imread(img_path)
        self.start = start
        if image is None:
            return False
        if start < 3 and is_type(image, "keyboard"):
            self.status = 2
            level_info = split_by_horizontal_line(
                image, width_ratio=0.9, filter_height=0.5, src_path=None)
            # level_info = prepare(self.root_path, picname)
            self.keyboard_len_start = level_info[-1][-1] - level_info[-1][0]
        if self.start >= len(self.picture_list) - 6:
            # 到整个文件夹的末端了，用户非正常配置算法文件
            # log.error("传入的起始帧号{}，文件夹长度{}，首帧算法异常".format(
            #     str(self.start), str(len(self.picture_list))))
            return False
        if self.status == 0:
            log.debug("first start analyse {}".format(str(start)))
            if self.part_one_search(image, img_path):
                self.status = 1
                return False
        elif self.status == 1:
            log.debug("second start analyse {}".format(str(self.start)))
            next_picname = self.picture_list[self.start + 5]
            if is_type(image, "others") and is_type(os.path.join(self.root_path, next_picname), "others"):
                log.debug("is not key board !")
                return True
            if self.part_two_search(image, img_path):
                self.status = 2
                return False
        elif self.status == 2:
            log.debug("third start analyse {}".format(str(self.start)))
            next_picname = self.picture_list[self.start + 5]
            if is_type(image, "others") and is_type(os.path.join(self.root_path, next_picname), "others"):
                log.debug("is not key board !")
                return True
            if self.part_three_search(image, img_path):
                return True
        else:
            pass
        return False

    def part_one_search(self, image, picname):
        """
        找到含有键盘的图片，且和后一帧全图不发生变化，键x盘稳定
        :param picname:
        :return:
        """
        # step1: 从第一张截图逐个找图片，检查目录index是否溢出
        if self.start + 2 < len(self.picture_list):
            next_picname = self.picture_list[self.start + 2]
        else:
            return False
        if is_type(image, "keyboard") and is_type(os.path.join(self.root_path, next_picname), "keyboard"):
            log.debug("keyboard start analyse {}".format(picname))
            # step2: 前一帧图片对比不变化
            back_img_name = self.picture_list[self.start + 1]
            back_img = cv2.imread(os.path.join(self.root_path, back_img_name))
            if not is_diff_with_back_image(back_img, image):
                return True
        return False

    def part_two_search(self, image, picname):
        """
        键盘稳定页面不变化后，搜索文字出现，键盘上半部分变化;
        复制粘贴页面没有搜索文字出现。
        :param image
        :param picname:
        :return:
        """
        half_height = int(len(image) * 0.5)
        cut_top = image[0:half_height, ::, ::]

        back_img_name = self.picture_list[self.start + 1]
        # if not os.path todo
        back_img = cv2.imread(os.path.join(self.root_path, back_img_name))
        if back_img is None:
            return False
        cut_up_back_img = back_img[:half_height, ::, ::]
        # 如果键盘上半部分不变，则返回false
        if not is_diff_with_back_image(cut_up_back_img, cut_top):
            return False
        # 如果上半部分变化，判断空白率；上半部分空白了，则第二阶段结束，键盘不变化了
        # cv2.imwrite("test.png",cut_top)
        blank_rate = PageBlankImpl(cut_top).calculate_pageblank_rate()
        # log.info("page blank rate is {}".format(str(blank_rate)))
        if blank_rate > 0.6:
            level_info = split_by_horizontal_line(
                image, width_ratio=0.9, filter_height=0.5, src_path=None)
            # level_info = prepare(self.root_path, picname)
            self.keyboard_len_start = level_info[-1][-1] - level_info[-1][0]
            return True
        return False

    def part_three_search(self, image, picname):
        """
        找到键盘不变化且上半部分有搜索文字的index之后，查找键盘区域下降，且页面上半部分变化
        有些场景直接一上来就是第三阶段
        :param image
        :param picname:
        :return:
        """
        # 图片已经不变化，下一阶段查找
        # step3: 分割图片，获得y轴区域位置
        level_info = split_by_horizontal_line(
            image, width_ratio=0.9, filter_height=0.5, src_path=None)
        half_height = int(len(image) * 0.5)
        up_part = image[:half_height, ::, ::]
        # 随着页面像素变化,分割可能有所变化，最后一部分的位置需要下降。
        self.keyboard_len = level_info[-1][-1] - level_info[-1][0]
        log.debug("keyboard change {}".format(
            self.keyboard_len_start - self.keyboard_len))
        log.debug(self.keyboard_len)
        blank_rate = PageBlankImpl(image).calculate_pageblank_rate()
        log.debug("page blank rate is {}".format(str(blank_rate)))
        # 找到键盘位置下降或上方变为空白的位置，键盘位置分割可能不太准。

        if blank_rate > 0.6:
            return True
        up_blank = PageBlankImpl(up_part).calculate_pageblank_rate()

        back_img_name = self.picture_list[self.start + 2]
        # if not os.path todo
        back_img = cv2.imread(os.path.join(self.root_path, back_img_name))
        if back_img is None:
            return False
        cut_up_back_img = back_img[:half_height, ::, ::]
        # change and to or
        if up_blank > 0.55 and (
                not is_diff_with_back_image(cut_up_back_img,
                                            up_part)) and self.keyboard_len_start - self.keyboard_len > 4:
            return True
        # keyboard change should be hard
        return False


def recognition(frames, frames_storage_dir):
    # 创建 iOS 键盘下降/消失帧识别器
    searcher = SearchIosFirstFrame(frames_storage_dir)
    result = {
        "is_recognition": False,
        "smart_frame_index": int(len(frames) / 2)
    }
    for index in range(len(frames)):
        frame = frames[index]
        if searcher.single_check(frame["path"], index):
            result["is_recognition"] = True
            result["smart_frame_index"] = index
            break
    return result
