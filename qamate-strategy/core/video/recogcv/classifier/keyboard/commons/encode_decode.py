# -*- coding: utf-8 -*-

"""
@create
"""

import base64

import numpy as np


def np_to_b64(images):
    """
    np_to_b64
    """
    img_str = base64.b64encode(images).decode('utf8')
    return img_str, images.shape


def b64_to_np(b64str, revert_params):
    """
    b64_to_np
    """
    shape = revert_params["shape"]
    dtype = revert_params["dtype"]
    dtype = getattr(np, dtype) if isinstance(str, type(dtype)) else dtype
    data = base64.b64decode(b64str.encode('utf8'))
    data = np.fromstring(data, dtype).reshape(shape)
    return data
