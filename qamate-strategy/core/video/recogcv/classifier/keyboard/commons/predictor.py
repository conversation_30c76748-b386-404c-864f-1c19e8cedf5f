# !/usr/bin/env python3
# -*- coding: UTF-8 -*-
#
# Copyright (c) 2021 Baidu.com, Inc. All Rights Reserved
#
"""
Desc:
Authors: <AUTHORS>
Date:    2022/3/21
"""
import os
import cv2
import sys
import pkgutil

__dir__ = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.abspath(os.path.join(__dir__, '../')))
from basics.util import logger
from basics.util import data
path = os.path.abspath(os.path.dirname(__file__))


class Predictor(object):
    """预测算子基类"""

    def __init__(self, args, inference_model_dir=None):
        if inference_model_dir is not None:
            inference_model_dir = path + '/' + inference_model_dir
        # HALF precission predict only work when using tensorrt
        if args["use_fp16"] is True:
            assert args["use_tensorrt"] is True
        self.args = args
        if self.args.get("use_opencv", False):
            self.predictor = self.create_opencv_predictor(
                args, inference_model_dir)

    def predict(self, image):
        raise NotImplementedError

    def create_opencv_predictor(self, args, inference_model_dir=None):
        """创建opencv预测算子"""
        path = data.get_data_path()
        # logger.debug(path)
        inference_model_dir = os.path.abspath(os.path.join(path, args["inference_model_dir"]))
        # logger.debug(inference_model_dir)
        model_file = os.path.abspath(os.path.join(
            inference_model_dir, "inference.onnx"))
        if cv2.__version__ < '4.1':
            logger.error("opencv version less than 4.1 ")
            raise ImportError
        predictor = cv2.dnn.readNetFromONNX(model_file)
        return predictor
