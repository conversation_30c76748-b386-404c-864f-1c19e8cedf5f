#
# Copyright (c) 2021 Baidu.com, Inc. All Rights Reserved
#
"""
Desc:            
Authors: <AUTHORS>
Date:    2022/4/6
"""
import random
import cv2


class PageBlankImpl(object):
    """
    页面面积纯色检测
    """

    def __init__(self, imgcv):
        """
        init
        :param imgcv:
        :return:
        """
        # 将图片缩放到固定大小，这样避免因为不同大小图片导致校验不准的问题
        width = imgcv.shape[1]
        height = imgcv.shape[0]
        rate = 660 / width


        self.imgcv = cv2.resize(imgcv, (int(width * rate), int(height * rate)))
        cv2.normalize(self.imgcv, self.imgcv, 255, 230, cv2.NORM_MINMAX, -1)
        self.pageblank_ker_size, self.pageblank_interval_w, self.pageblank_interval_h = self.get_cfg_params()

    def main(self):
        """
        main
        :return: status / result
        """
        ratio = self.calculate_pageblank_rate()
        return round(ratio, 2)

    def calculate_pageblank_rate(self):
        """
        calculate_pageblank_rate
        :return: float
        """
        interval_with = int(self.imgcv.shape[1] / self.pageblank_interval_w)
        interval_height = int(self.imgcv.shape[0] / self.pageblank_interval_h)
        dic = {}
        no = 0
        for row in range(1, int(self.pageblank_interval_h)):
            for col in range(1, int(self.pageblank_interval_w)):

                tmp = self.calculate_pix(row * interval_height, col * interval_with, no)
                no += 1

                if tmp in dic.keys():
                    dic[tmp] += 1
                else:
                    dic[tmp] = 1
        same_count = max(dic.items(), key=lambda x: x[1])[1]
        return same_count * 1.0 / (self.pageblank_interval_w * self.pageblank_interval_h)

    def calculate_pix(self, x, y, no):
        """
        calculate_pix
        :param x:
        :param y:
        :return:
        """
        ran = random.randint(0, 10)
        tmp_img = self.imgcv[x - self.pageblank_ker_size - ran:x + self.pageblank_ker_size - ran,
                  y - self.pageblank_ker_size - ran:y + self.pageblank_ker_size - ran]
        # cv2.imwrite("tempimg_{}.png".format(no), tmp_img)

        h, w, s = tmp_img.shape
        res = 0
        for row in range(h):
            for col in range(w):
                res = res + sum(tmp_img[row, col])
        return res

    def get_cfg_params(self):
        """
        dynamically adjust the number of detection points and the size of the detection processor
         according to the size of the picture
        :return: param1: collecting processor size ;param2: horizontal acquisition number;
                param3: longitudinal acquisition number
        """
        ker_size = 20
        interval_w = 7
        interval_h = 14

        width = self.imgcv.shape[1]
        common_w = 660
        rate = width / common_w
        ker_size, interval_w, interval_h = int(rate * ker_size), int(rate * interval_w), int(rate * interval_h)
        if ker_size <= 0:
            ker_size = 10
        if interval_w <= 0:
            interval_w = 3
        if interval_h <= 0:
            interval_h = 3
        return ker_size, interval_w, interval_h