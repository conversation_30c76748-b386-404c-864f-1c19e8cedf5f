# !/usr/bin/env python3
# -*- coding: UTF-8 -*-
#
# Copyright (c) 2022 Baidu.com, Inc. All Rights Reserved
#
"""
Desc:
Authors: <AUTHORS>
Date: 2022-06-10
"""

import os
import numpy as np


def get_image_list(img_file):
    imgs_lists = []
    if img_file is None or not os.path.exists(img_file):
        raise Exception("not found any img file in {}".format(img_file))

    img_end = ['jpg', 'png', 'jpeg', 'JPEG', 'JPG', 'bmp']
    if os.path.isfile(img_file) and img_file.split('.')[-1] in img_end:
        imgs_lists.append(img_file)
    elif os.path.isdir(img_file):
        for single_file in os.listdir(img_file):
            if single_file.split('.')[-1] in img_end:
                imgs_lists.append(os.path.join(img_file, single_file))
    if len(imgs_lists) == 0:
        raise Exception("not found any img file in {}".format(img_file))
    imgs_lists = sorted(imgs_lists)
    return imgs_lists


def get_image_list_from_label_file(image_path, label_file_path):
    imgs_lists = []
    gt_labels = []
    with open(label_file_path, "r") as fin:
        lines = fin.readlines()
        for line in lines:
            image_name, label = line.strip("\n").split()
            label = int(label)
            imgs_lists.append(os.path.join(image_path, image_name))
            gt_labels.append(int(label))
    return imgs_lists, gt_labels
