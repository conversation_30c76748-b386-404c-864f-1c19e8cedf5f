#
# Copyright (c) 2021 Baidu.com, Inc. All Rights Reserved
#
"""
Desc:            
Authors: <AUTHORS>
Date:    2022/3/31
"""
import cv2
import numpy as np

class Line():
    """
    直线
    """

    def __init__(self, x0, y0, x1, y1):
        self.x0 = int(x0)
        self.y0 = int(y0)
        self.x1 = int(x1)
        self.y1 = int(y1)
        self.width = self.x1 - self.x0 + 1
        self.height = self.y1 - self.y0 + 1


def split_by_horizontal_line(image_cv, width_ratio, filter_height, src_path):
    """
    把截图按照横向分割线分成多个区域
    :param filter_height:
    :param area_output_path:
    :param line_output_path:
    :param src_path:
    :param image_cv:
    :param width_ratio:
    :param clip_rect_list: list [ClipInfo,] 区域列表
    :return:
    """
    line_list = []
    img_width = image_cv.shape[1]
    img_height = image_cv.shape[0]
    lines = detect_horizontal_line(image_cv=image_cv,
                                   width_ratio=width_ratio, filter_height=filter_height,
                                   image_path=src_path)
    for line in lines:
        new_line = Line(0, line.y0, img_width - 1, line.y0)
        line_list.append(new_line)

    if len(line_list) == 0:
        line_list.append(Line(0, 0, img_width - 1, 0))

    start_line = line_list[0]
    if not (start_line.x0 == 0 and start_line.y0 == 0
            and start_line.x1 == img_width - 1 and start_line.y1 == 0):
        line_list.insert(0, Line(0, 0, img_width - 1, 0))

    end_line = line_list[-1]
    if not (end_line.x0 == 0 and end_line.y0 == img_height - 1
            and end_line.x1 == img_width - 1 and end_line.y1 == img_height - 1):
        line_list.append(Line(0, img_height - 1, img_width - 1, img_height - 1))

    area_list = list()
    for i in range(0, len(line_list) - 1):
        cur_line = line_list[i]
        next_line = line_list[i + 1]
        left = cur_line.x0
        top = cur_line.y0
        width = img_width - 1
        height = next_line.y1 - cur_line.y0
        # todo
        hori_area = [top, top + height]
        # hori_area.index = i
        area_list.append(hori_area)

        # cv2.rectangle(image_cv, (left, top), (left + width, top + height), (0, 0, 255), 3)

    # cv2.imwrite("./tests/temp01.png", image_cv)
    return area_list


def draw_lines(imgcv, line_list, output_path,
               color, line_size):
    """
    在图片中画直线
    :param imgcv:输入图片
    :param line_list:list [Line, ]
    :param output_path: 输出图片路径
    :param line_size: 直线的高度/宽度
    :param color:直线的颜色(b,g,r)
    :return:
    """
    new_image = imgcv.copy()
    for line in line_list:
        new_image = cv2.line(new_image, (line.x0, line.y0),
                             (line.x1, line.y1), color, line_size)

    if output_path:
        cv2.imwrite(output_path, new_image)
    return new_image


def detect_horizontal_line(image_cv,
                           width_ratio, filter_height, image_path):
    """
    检测图片中的直线
    :param filter_height:
    :param line_output_path:
    :param image_path:
    :param clip_rec_list: List
    :param width_ratio: float 宽度比例
    :param image_cv: cv2.imread(file_path, cv2.IMREAD_COLOR)
    :return: list 直线列表
    """
    roi_gray = cv2.cvtColor(image_cv, cv2.COLOR_BGR2GRAY)
    canny = cv2.Canny(roi_gray, 0, 0)
    lines = cv2.HoughLinesP(canny, 1, np.pi / 2, 50, None, image_cv.shape[1] * width_ratio, 5)
    img_height, img_width = roi_gray.shape
    if lines is None:
        return []
    line_list = list()
    for line in lines:
        for x1, y1, x2, y2 in line:
            # 只要横向的直线, 直线上下均为纯色
            if y1 == y2:
                line_list.append(Line(x1, y1, x2, y2))

    # 横向贯穿整个图片，且霍夫变换无法检测出的直线采用逐行扫描方式获取
    for i in range(1, img_height - 1):
        if len(set(roi_gray[i])) == 1:
            if (len(set(roi_gray[i - 1])) == 1 and set(roi_gray[i - 1]) != set(roi_gray[i])) or \
                    (len(set(roi_gray[i + 1])) == 1 and set(roi_gray[i + 1]) != set(roi_gray[i])):
                line_list.append(Line(0, i, img_width, i))

    # log.debug("line_list: %s" % line_list)

    if len(line_list) == 0:
        return []
    line_list = sorted(line_list, key=lambda li: li.y0)
    final_line_list = list()
    final_line_list.append(line_list[0])

    # 过滤距离过近的两条直线
    for index in range(1, len(line_list)):
        if abs(line_list[index].y0 - line_list[index - 1].y0) > filter_height:
            final_line_list.append(line_list[index])

    # log.info("final_line_list: %s" % final_line_list)

    # 过滤部分无效的直线
    # final_line_list = filter_line(final_line_list, clip_rec_list, img_width)

    line_output_path = './test02.png'

    # draw_lines(image_cv, final_line_list, line_output_path,(0,0,255),3)

    return final_line_list


