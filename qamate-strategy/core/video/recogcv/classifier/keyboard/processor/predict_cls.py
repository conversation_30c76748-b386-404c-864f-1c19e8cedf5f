import os
import sys

__dir__ = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.abspath(os.path.join(__dir__, '../')))

import cv2
import numpy as np

from basics.util import logger
from video.recogcv.classifier.keyboard.commons import config
from video.recogcv.classifier.keyboard.commons.predictor import Predictor
from video.recogcv.classifier.keyboard.processor.preprocess import create_operators
from video.recogcv.classifier.keyboard.processor.postprocess import build_postprocess


class ClsPredictor(Predictor):
    """
    Class predictor类
    """

    def __init__(self, config):
        """
        初始化
        :param config:
        """
        super().__init__(config["Global"])

        self.preprocess_ops = []
        self.postprocess = None
        if "PreProcess" in config:
            if "transform_ops" in config["PreProcess"]:
                self.preprocess_ops = create_operators(config["PreProcess"][
                                                           "transform_ops"])
        if "PostProcess" in config:
            self.postprocess = build_postprocess(config["PostProcess"])

        # for whole_chain project to test each repo of paddle
        self.benchmark = config["Global"].get("benchmark", False)
        if self.benchmark:
            import auto_log
            import os
            pid = os.getpid()
            size = config["PreProcess"]["transform_ops"][1]["CropImage"][
                "size"]
            self.auto_logger = auto_log.AutoLogger(
                model_name=config["Global"].get("model_name", "cls"),
                model_precision='fp16'
                if config["Global"]["use_fp16"] else 'fp32',
                batch_size=config["Global"].get("batch_size", 1),
                data_shape=[3, size, size],
                save_path=config["Global"].get("save_log_path",
                                               "./auto_log.log"),
                inference_config=self.config,
                pids=pid,
                process_name=None,
                gpu_ids=None,
                time_keys=[
                    'preprocess_time', 'inference_time', 'postprocess_time'
                ],
                warmup=2)

    def predict(self, images):
        use_opencv = self.args.get("use_opencv", False)

        if self.benchmark:
            self.auto_logger.times.start()
        if not isinstance(images, (list,)):
            images = [images]
        for idx in range(len(images)):
            for ops in self.preprocess_ops:
                images[idx] = ops(images[idx])
        image = np.array(images)
        if self.benchmark:
            self.auto_logger.times.stamp()

        if use_opencv:
            blob = cv2.dnn.blobFromImage(image[0])  # 单帧推理
            self.predictor.setInput(blob)
            batch_output = self.predictor.forward()

        if self.benchmark:
            self.auto_logger.times.stamp()
        if self.postprocess is not None:
            batch_output = self.postprocess(batch_output)
        if self.benchmark:
            self.auto_logger.times.end(stamp=True)
        return batch_output


def main(config, image_list):
    cls_predictor = ClsPredictor(config)
    # image_list = get_image_list(config["Global"]["infer_imgs"])

    batch_imgs = []
    batch_names = []
    cnt = 0
    for idx, img_path in enumerate(image_list):
        img = cv2.imread(img_path)
        if img is None:
            logger.warning(
                "Image file failed to read and has been skipped. The path: {}".
                    format(img_path))
        else:
            img = img[:, :, ::-1]
            batch_imgs.append(img)
            img_name = os.path.basename(img_path)
            batch_names.append(img_name)
            cnt += 1

        if cnt % config["Global"]["batch_size"] == 0 or (idx + 1) == len(image_list):
            if len(batch_imgs) == 0:
                continue
            batch_results = cls_predictor.predict(batch_imgs)
            for number, result_dict in enumerate(batch_results):
                filename = batch_names[number]
                clas_ids = result_dict["class_ids"]
                scores_str = "[{}]".format(", ".join("{:.2f}".format(
                    r) for r in result_dict["scores"]))
                label_names = result_dict["label_names"]
                print("{}:\tclass id(s): {}, score(s): {}, label_name(s): {}".
                      format(filename, clas_ids, scores_str, label_names))
            batch_imgs = []
            batch_names = []
    # if cls_predictor.benchmark:
    #     cls_predictor.auto_logger.report()
    return


def single_infer(config, image):
    cls_predictor = ClsPredictor(config)
    if image is None:
        logger.warning("Image file failed to read and has been skipped")
    else:
        img = image[:, :, ::-1]
    results = cls_predictor.predict(img)[0]
    class_ids = results["class_ids"]
    scores = results["scores"]
    # print(class_ids)
    # print(scores)
    return class_ids, scores


if __name__ == "__main__":
    args = config.parse_args()
    args.config = '../configs/inference_cls_0324.yaml'
    image_list = ['../130.jpg']

    config = config.get_config(args.config, overrides=args.override, show=True)
    main(config, image_list)
