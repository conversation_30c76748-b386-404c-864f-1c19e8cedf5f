# !/usr/bin/env python3
# -*- coding: UTF-8 -*-
#
# Copyright (c) 2022 Baidu.com, Inc. All Rights Reserved
#
# Desc:
# Authors: <AUTHORS>
# Date: 2022-05-18
#
import cv2

def cut_image(image, x1, y1, x2, y2, flags=None, morphologyEx=False):
    """
    :param image:
    :param x_1:   支持绝对位置以及相对位置切图
    :param y_1:
    :param x_2:
    :param y_2:
    :param flags: imread读入参数，设置cv2.IMREAD_GRAYSCALE灰化后减少噪声
    :param morphologyEx:打开黑帽运算，突出特征点不明显的区域，高亮特征点多的区域
    :return:
    """
    if isinstance(image, type('')):
        if flags is not None and morphologyEx:
            image = cv2.imread(image, flags, morphologyEx)
        elif flags is not None:
            image = cv2.imread(image, flags)
        else:
            image = cv2.imread(image)
    else:
        image = image
    if morphologyEx:
        try:
            height, width, channels = image.shape
        except Exception as e:
            height, width = image.shape
    else:
        try:
            height, width, channels = image.shape
        except Exception as e:
            height, width = image.shape
    if 0 < x1 <= 1:
        x1 = width * x1
    if 0 < x2 <= 1:
        x2 = width * x2
    if 0 < y1 <= 1:
        y1 = height * y1
    if 0 < y2 <= 1:
        y2 = height * y2

    return image[int(y1):int(y2), int(x1):int(x2)]