# !/usr/bin/env python3
# -*- coding: UTF-8 -*-
#
# Copyright (c) 2022 Baidu.com, Inc. All Rights Reserved
#
"""
Desc: 模板匹配
Authors: <AUTHORS>
Date: 2022-06-07
"""

import numpy as np

import cv2
import six
import skimage

if skimage.__version__ > "0.16.1":
    """兼容不同版本的 skimage """
    from skimage.metrics import structural_similarity as compare_ssim
else:
    from skimage.measure import compare_ssim


from basics.util import logger
from video.recogcv.libs import aircv
from video.recogcv.libs.error import BaseError

class ST(object):
    "模板匹配参数设置"
    CVSTRATEGY = ["tpl"]
    THRESHOLD = 0.6  # [0, 1]
    THRESHOLD_STRICT = 0.7  # [0, 1]
    OPDELAY = 0.1
    FIND_TIMEOUT = 20
    FIND_TIMEOUT_TMP = 3


class Template(object):
    """
    模板匹配
    """

    def __init__(self, filename, threshold=None,
                 rgb=False, is_image_cut=False, sift=True, return_confidence=False):
        self.filename = filename
        self.filepath = filename
        self.threshold = threshold or ST.THRESHOLD
        self.rgb = rgb
        self.sift = sift
        self.is_image_cut = is_image_cut
        self.return_confidence = return_confidence


    def match_in(self, screen):
        match_result = self._cv_match(screen)
        return True if match_result else None


    def _cv_match(self, screen):
        # in case image file not exist in current directory:
        if isinstance(self.filepath, type('')):
            image = self._imread()
        else:
            image = self.filepath
        if isinstance(image, six.text_type):
            image = cv2.imread(image)
        if self.is_image_cut:
            image = self.image_cut(image)
        if isinstance(screen, type('')):
            screen = cv2.imread(screen)
            screen = self.image_cut(screen)
        image = self._resize_image(image, screen)

        ret = None
        for method in ST.CVSTRATEGY:
            # 原图，resize 与否对最终效果影响待研究
            if method == "tpl":
                ret = self._try_match(self.find_temp, screen)
            else:
                logger.warning("Undefined method in CV_STRATEGY: %s", method)
                pass
            if ret:
                break
        return ret
    
    def find_temp(self, screen):
        """
        @description: opencv实现模板匹配
        @param: 
        @return: 
        """
        if isinstance(self.filename, type('')):
            temp_img = cv2.imread(self.filename)
        else:
            temp_img = self.filename
        gray_temp_img = cv2.cvtColor(temp_img, cv2.COLOR_BGR2GRAY)
        if isinstance(screen, type('')):
            screen = cv2.imread(screen)
            screen = self.image_cut(screen)
        img_gray = cv2.cvtColor(screen, cv2.COLOR_BGR2GRAY)
        res = cv2.matchTemplate(img_gray, gray_temp_img, cv2.TM_CCOEFF_NORMED)
        min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(res)
        if np.max(img_gray) == np.min(img_gray):
            max_val = 0
        # 选出模板匹配最优区域
        max_sim = 0
        if max_val > max_sim:
            x1, y1 = max_loc
            w1, h1 = img_gray.shape
            if np.max(img_gray[y1: y1 + h1, x1:x1 + w1]) == np.min(img_gray[y1: y1 + h1, x1:x1 + w1]):
                pass
            max_sim = max_val
        logger.debug("template confidence is {}".format(max_sim))
        return max_sim >= self.threshold 

    @staticmethod
    def image_cut(image):
        try:
            height, width, channels = image.shape
        except Exception as e:
            height, width = image.shape
        return image[int(height / 30):height, 0:width]

    @staticmethod
    def _try_match(method, *args, **kwargs):
        try:
            ret = method(*args, **kwargs)
        except BaseError as err:
            logger.error(err)
            return None
        else:
            return ret

    def _imread(self):
        return cv2.imread(self.filepath)

    def cocos_min_strategy(self, sch_resolution, src_resolution):
        """图像缩放规则: 按照长宽最小比率缩放."""
        # 输入参数: w-h待缩放图像的宽高，sch_resolution为待缩放图像的来源分辨率
        #           src_resolution 待适配屏幕的分辨率
        # 需要分别算出对设计分辨率的缩放比，进而算出src\sch有效缩放比。
        scale = min(1.0 * src_resolution[0] / sch_resolution[0], 1.0 * src_resolution[1] / sch_resolution[1])
        w_re, h_re = int(sch_resolution[0] * scale), int(sch_resolution[1] * scale)
        return w_re, h_re

    def _resize_image(self, image, screen):
        """模板匹配中，将输入的截图适配成 等待模板匹配的截图."""

        self.resolution = aircv.get_resolution(image)
        screen_resolution = aircv.get_resolution(screen)
        # 如果分辨率一致，则不需要进行im_search的适配:
        if tuple(self.resolution) == tuple(screen_resolution):
            return image

        # 分辨率不一致则进行适配，默认使用cocos_min_strategy:
        w_re, h_re = self.cocos_min_strategy(self.resolution, screen_resolution)
        # 确保w_re和h_re > 0, 至少有1个像素:
        w_re, h_re = max(1, w_re), max(1, h_re)
        image = cv2.resize(image, (w_re, h_re))
        return image

    def compare_image(self, imageA, imageB, threshold):
        """
        :param imageA:
        :param imageB:
        :param threshold:
        :return:    by weihao04  使用结构性算法对比图片相似度
        """
        grayA = cv2.cvtColor(imageA, cv2.COLOR_BGR2GRAY)
        grayB = cv2.cvtColor(imageB, cv2.COLOR_BGR2GRAY)
        grayA = cv2.Canny(grayA, 255, 255)
        grayB = cv2.Canny(grayB, 255, 255)
        (score, diff) = compare_ssim(grayA, grayB, full=True)
        logger.debug("SSIM: %s" % str(score))
        return score >= threshold