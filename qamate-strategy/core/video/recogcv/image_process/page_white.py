# !/usr/bin/env python3
# -*- coding: UTF-8 -*-
#
# Copyright (c) 2022 Baidu.com, Inc. All Rights Reserved
#
"""
Desc:
Authors: <AUTHORS>
Date: 2022-05-20
"""

import cv2
import colorsys
from PIL import Image
from basics.util import logger


class IsWhite(object):
    """check IsWhite"""

    def __init__(self):
        pass

    def get_dominant_color(self, image):
        """
        获取图片的main color 底色
        """
        image = Image.fromarray(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
        if image:
            # image = Image.open(image)
            image = image.convert('RGBA')
            (x, y) = image.size
            if x * y > 200 * 200:
                image.thumbnail((200, 200))

            max_score = 0
            dominant_color = 0

            for count, (r, g, b, a) in image.getcolors(image.size[0] * image.size[1]):
                saturation = colorsys.rgb_to_hsv(
                    r / 255.0, g / 255.0, b / 255.0)[1]
                y = min(abs(r * 2104 + g * 4130 + b *
                        802 + 4096 + 131072) >> 13, 235)
                y = (y - 16.0) / (235 - 16)
                score = (saturation + 0.1) * count

                if score > max_score:
                    max_score = score
                    dominant_color = (r, g, b)
            return dominant_color

    def _is_white_screen_by_point(self, image, crop_size=10):
        """
        判断图片切片区域是否为底色
        :param image:
        :return:
        """
        r, g, b = self.get_dominant_color(image)
        if isinstance(image, type('')):
            image = cv2.imread(image)
        try:
            height, width, channels = image.shape
        except Exception as e:
            height, width = image.shape
        # logger.error((int(height), int(width)))
        image = cv2.resize(image, (int(height / crop_size),
                           int(width / crop_size)), interpolation=cv2.INTER_CUBIC)
        try:
            height, width, channels = image.shape
        except Exception as e:
            height, width = image.shape
        for k in range(0, height):
            for l in range(0, width):
                x, y, z = image[k, l]
                if x >= b * 0.95 and x <= b * 1.05 and y >= g * 0.95 \
                        and y <= g * 1.05 and z >= r * 0.95 and z <= r * 1.05:
                    continue
                else:
                    return False
        return True

    def white_screen_ratio(self, image, w=10, h=10, crop_size=10):
        """
        返回图片底色占比
        :param image: 输入图片
        :param w: 横坐标切片个数
        :param h:  纵坐标切片个数
        :return:
        """
        count = 0
        if isinstance(image, str):
            image = cv2.imread(image)
        try:
            height, width, channels = image.shape
        except Exception as e:
            height, width = image.shape
        for i in range(0, w):
            for j in range(0, h):
                m = image[int(height * j / h):int(height * (j + 1) / h),
                          int(width * i / w):int(width * (i + 1) / w)]
                if self._is_white_screen_by_point(m, crop_size = crop_size):
                    count += 1
        logger.debug(f"图片底色所占比例：{float(count) / float(w * h)}")
        return float(count) / float(w * h)

    def _is_white_screen_cut(self, file, num=0.6, w=10, h=10, crop_size=10, black=False):
        """
        图片file判断底色在整张图片占比,返回True,否则返回False
        :param file: 
        :param num:  期望底色所占比例
        :param w: 横坐标切片个数
        :param h: 纵坐标切片个数
        :return:
        """
        if isinstance(file, type('')):
            image = cv2.imread(file)
        else:
            image = file
        try:
            height, width, channels = image.shape
        except Exception as e:
            height, width = image.shape
            logger.error(e)
        # cut image
        image = image[int(height / 30):height, 0:width] # 这里为啥还要再截断一部分？目的是为了后续计算方便
        ratio = self.white_screen_ratio(image, w, h, crop_size)
        return ratio > num

if __name__ == '__main__':
    is_white = IsWhite()
    img = '/home/<USER>/liletao/babp_data/android_toutiao_luodiye/01/47.jpg'

    print(is_white._is_white_screen_cut(img,crop_size=2))
