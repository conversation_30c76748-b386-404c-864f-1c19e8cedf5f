# !/usr/bin/env python3
# -*- coding: UTF-8 -*-
#
# Copyright (c) 2023 Baidu.com, Inc. All Rights Reserved
#
"""
Desc    : 实现百度场景页面卡片分割
Authors : liletao(<EMAIL>)
Date    : 2023/2/9 4:51 PM
"""

import cv2
import numpy as np


def card_cutter(image_input):
    "输出分割卡片的矩形区域"
    if isinstance(image_input, type('')):
        image = cv2.imread(image_input)
    else:
        image = image_input

    image_gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    img_edges_strong = cv2.Canny(image_gray, 1, 250, apertureSize=7)
    img_edges_weak = cv2.Canny(image_gray, 1, 250, apertureSize=3)

    img_diff = img_edges_strong - img_edges_weak

    lines = cv2.HoughLinesP(img_diff, 1, np.pi / 180, 70)

    if lines is None:
        return False
    line_pos = {}
    card_pos = []
    for line in lines:
        x1, y1, x2, y2 = line[0]
        if abs(y1 - y2) < 4 and abs(x2 - x1) > 0.7 * (len(img_diff[0])):
            # cv2.line(image, (x1, y1), (x2, y2), (255, 0, 255), 2)
            line_pos[y1] = [x1, y1, x2, y2]
    key_item = sorted(list(line_pos.keys()))
    print(key_item)
    for i in range(1, len(key_item)):
        if key_item[i] - key_item[i - 1] > len(img_diff) * 0.2:
            # x1,y1,x2,y2
            card_pos.append([line_pos[key_item[i - 1]][0], line_pos[key_item[i - 1]][1], line_pos[key_item[i]][2],
                             line_pos[key_item[i]][3]])
            cv2.rectangle(image, (line_pos[key_item[i - 1]][0], line_pos[key_item[i - 1]][1]),
                          (line_pos[key_item[i]][2],
                           line_pos[key_item[i]][3]), (255, 0, 255), 4)
        else:
            continue

    cv2.imwrite("./test.png", image)
    return card_pos


if __name__ == '__main__':
    card_cutter("/Users/<USER>/Desktop/pictures/506E8E42888212221852D95958B27602.jpg")
