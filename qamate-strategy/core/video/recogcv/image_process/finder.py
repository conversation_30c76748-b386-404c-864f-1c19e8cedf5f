# !/usr/bin/env python3
# -*- coding: UTF-8 -*-
#
# Copyright (c) 2022 Baidu.com, Inc. All Rights Reserved
#
"""
Desc: 文本查找
Authors: <AUTHORS>
Date: 2022-05-19
"""

import base64
import json
import os
import sys
import time
import re
import cv2
import basics.util.config as config

from video.recogcv.openapi.ocr import getWords
from video.recogcv.image_process.cutter import cut_image
from basics.util import logger

image_tmp = os.path.join(config.CACHE_DIR)
if not os.path.exists(image_tmp):
    os.makedirs(image_tmp)

def get_text_from_image(image, text, x1=0, y1=0, x2=1, y2=1):
    """
    @description: 从目标图片截图，获取查找文字位置
    @param: 
    @return: 
    """
    time.sleep(1)
    if not image or not os.path.exists(image):
        logger.error(f"查找的源图片不存在。图片路径：{image}")
        return None, None
    image_shape = cv2.imread(image)
    if image_shape is not None:
        x_win = image_shape.shape[1]
        y_win = image_shape.shape[0]
    else:
        x_win = 0
        y_win = 0
    if x1 > 0 or y1 > 0 or x2 < 1 or y2 < 1:
        image_cut = os.path.join(image_tmp, 'cut.png')
        image = cut_image(image, x1, y1, x2, y2, morphologyEx=True)
        cv2.imwrite(image_cut, image)
    else:
        image_cut = image
    if not text or len(text) <= 0:
        logger.info("find_text_from_image关键字为空")
        return None, None
    if image_cut:
        file = open(image_cut, 'rb')
        imagebase = base64.b64encode(file.read())
        file.close()
        try:
            query = once_image_text_by_ocr(imagebase)
            x, y = single_word_pos(query, text)
            x = x + x1 * x_win
            y = y + y1 * y_win
            return x, y
        except Exception as e:
            logger.error("CASE: find_text 手机屏幕截图格式错误。")
            logger.error(e)
    logger.debug(f"CASE: find_text 没有找到关键字[{text}]对应的坐标")
    return None, None


def once_image_text_by_ocr(imagebase):
    """
    @description:
    @param: 
    @return: 
    """
    if sys.version_info < (3, 9):
        query = json.loads(getWords(imagebase))
    else:
        query = json.loads(getWords(imagebase))
    if 'error_code' in list(query.keys()):
        logger.debug("error_code in getWords!")
        if sys.version_info < (3, 9):
            query = json.loads(getWords(imagebase))
        else:
            query = json.loads(getWords(imagebase))
        return None, None
        
    if 'words_result' not in list(query.keys()):
        time.sleep(1)
        query = json.loads(getWords(imagebase))
    return query

def single_word_pos(query, text):
    """
    @description: 查找单个文字的位置
    @param: 
    @return: 
    """
    if 'words_result' not in list(query.keys()):
        logger.info(f"CASE: find_text OCR未找到关键字:{text}")
        return None, None
    
    for i in range(0, len(query['words_result'])):
        words = query['words_result'][i]['words']
        if words.find(text) > -1:
            x = 0
            y = 0
            n = -1
            words_list = list(text)
            for char in words_list:
                for j in range(n + 1, len(query['words_result'][i]['chars'])):
                    if query['words_result'][i]['chars'][j]['char'] == char:
                        n = j
                        if x == 0:
                            x = int(query['words_result'][i]['chars'][j]['location']['left'])
                            y = int(query['words_result'][i]['chars'][j]['location']['top']) + \
                                int(query['words_result'][i]['chars'][j]['location']['height']) / 2
                        else:
                            x = x / 2 + int(
                                query['words_result'][i]['chars'][j]['location']['left']) / 2
                        break
                    else:
                        continue
            logger.debug("CASE: 使用 OCR find location %s in %s of windows: %s, %s" % (
                text, words, str(x), str(y)))
            return x, y