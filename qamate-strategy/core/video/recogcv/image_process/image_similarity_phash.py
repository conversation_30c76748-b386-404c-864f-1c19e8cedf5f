# !/usr/bin/python3

# -*- coding: utf-8 -*-

"""
@ModuleName: image_similarity
@author: <PERSON><PERSON><PERSON><PERSON><PERSON> (<EMAIL>)
@date: 2020-10-29 18:59:33
@desc: 基于PHash的图片相似度计算
@wiki: http://wiki.baidu.com/pages/viewpage.action?pageId=1277307889
"""

import collections

import cv2
import numpy as np


class ImageSimilarityPhash():
    """
    基于PHash的图片相似度计算
    """
    def __init__(self, image1_path, image2_path):
        """
        @description:
        @param: 
        @return: 
        """
        self.img1_cv = cv2.imread(image1_path)
        self.img2_cv = cv2.imread(image2_path)

    def main(self):
        """
        插件执行入口
        :return: float
        """
        ratio = self.get_img_similarity()
        return round(ratio, 4)

    def get_img_similarity(self):
        """
        获取相似度
        :return:
        """
        
        img1_area = self.img1_cv.shape[0] * self.img1_cv.shape[1]
        img2_area = self.img2_cv.shape[0] * self.img2_cv.shape[1]

        phash1 = self.get_phash(self.img1_cv)
        phash2 = self.get_phash(self.img2_cv)
        similarity = 1 - sum([ch1 != ch2 for ch1, ch2 in zip(phash1, phash2)]) * 1. / (32 * 32 / 4)

        # resize后的相似度，再乘以图片的面积比例作为系数，减小图片结构相似带来的误差
        # ratio = img1_area / img2_area if img1_area < img2_area else img2_area / img1_area

        return similarity

    def flatten(self, x):
        """
        :param x:
        :return:
        """
        result = []
        for el in x:
            if isinstance(el, collections.Iterable) and not isinstance(el, str):
                result.extend(self.flatten(el))
            else:
                result.append(el)

        return result

    def get_phash(self, imgcv):
        """
        计算phash值
        :param imgcv:
        :return:
        """
        img2gray = cv2.cvtColor(imgcv, cv2.COLOR_BGR2GRAY)
        img = cv2.resize(img2gray, (64, 64), interpolation=cv2.INTER_CUBIC)
        h, w = img.shape[:2]
        vis0 = np.zeros((h, w), np.float32)
        vis0[:h, :w] = img
        vis1 = cv2.dct(cv2.dct(vis0))
        vis1.resize(32, 32)
        img_list = self.flatten(vis1.tolist())
        avg = sum(img_list) * 1. / len(img_list)
        avg_list = ['0' if i < avg else '1' for i in img_list]
        return ''.join(['%x' % int(''.join(avg_list[x:x + 4]), 2) for x in range(0, 32 * 32, 4)])
