#!/usr/bin/python
# coding= utf-8
"""innerToken.py"""

from __future__ import absolute_import
import datetime
import time
import hashlib


class InnerToken:
    TOKEN_TYPE = '11'
    timestamp = ''

    def sign(self, appid, uid, sk):
        md5 = hashlib.md5()
        self.timestamp = str(time.mktime(datetime.datetime.now().timetuple())).split('.')[0]
        md5.update((self.timestamp + str(uid) + str(appid) + str(sk)).encode("utf-8"))
        return md5.hexdigest()

    def generateToken(self, appid, uid, sk):
        sign = self.sign(appid, uid, sk)
        token = self.TOKEN_TYPE + '.' + sign + '.' + self.timestamp + '.' + str(uid) + "-" + str(appid)
        return token


