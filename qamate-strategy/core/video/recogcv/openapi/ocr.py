#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
@author: weijingjing

"""
from __future__ import absolute_import
import six.moves.urllib.request, six.moves.urllib.parse, six.moves.urllib.error
import six.moves.urllib.request, six.moves.urllib.error, six.moves.urllib.parse

from video.recogcv.openapi.InnerToken import *
from enum import Enum


appid = '17083234'
uid = '0'
sk = 'MMxCWyWRnzs9px6gn1eC3RtFsYGzluOS'


def getWords(image):
    p = InnerToken()
    token = p.generateToken(appid, uid, sk)
    url = 'http://inner.openapi.baidu.com/rest/2.0/vis-ocr/v1/ocr/general_s2_1?access_token=' + token
    data = {'image': image, 'recognize_granularity': 'small'}
    data = six.moves.urllib.parse.urlencode(data).encode("utf-8")
    req = six.moves.urllib.request.Request(url, data)
    try:
        response = six.moves.urllib.request.urlopen(req, timeout=10).read()
        return response
    except Exception as e:
        return '{}'
