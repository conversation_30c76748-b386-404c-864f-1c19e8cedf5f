# !/usr/bin/env python3
# -*- coding: UTF-8 -*-
#
# Copyright (c) 2022 Baidu.com, Inc. All Rights Reserved
#
"""
Desc:
Authors: <AUTHORS>
Date: 2022-05-19
"""


class BaseError(Exception):
    """错误问题基类"""
    def __init__(self, value):
        self.value = value

    def __str__(self):
        return repr(self.value)


class AirtestError(BaseError):
    """
        This is Airtest BaseError
    """
    pass


class TargetNotFoundError(AirtestError):
    """
        This is TargetNotFoundError BaseError
        When something is not found
    """
    pass


class ScriptParamError(AirtestError):
    """
        This is ScriptParamError BaseError
        When something goes wrong
    """
    pass


class AdbError(Exception):
    """
        This is AdbError BaseError
        When ADB have something wrong
    """

    def __init__(self, stdout, stderr):
        self.stdout = stdout
        self.stderr = stderr

    def __str__(self):
        return "stdout[%s] stderr[%s]" % (self.stdout, self.stderr)


class AdbShellError(AdbError):
    """
        adb shell error
    """
    pass


class DeviceConnectionError(BaseError):
    """
        device connection error
    """
    DEVICE_CONNECTION_ERROR = r"error:\s*((device \'\w+\' not found)|(cannot connect to daemon at [\w\:\s\.]+ Connection timed out))"
    pass


class ICmdError(Exception):
    """
        This is ICmdError BaseError
        When ICmd have something wrong
    """

    def __init__(self, stdout, stderr):
        self.stdout = stdout
        self.stderr = stderr

    def __str__(self):
        return "stdout[%s] stderr[%s]" % (self.stdout, self.stderr)


class MinicapError(BaseError):
    """
        This is MinicapError BaseError
        When Minicap have something wrong
    """
    pass


class MinitouchError(BaseError):
    """
        This is MinitouchError BaseError
        When Minicap have something wrong
    """
    pass


class PerformanceError(BaseError):
    pass

class TargetNotFoundError(AirtestError):
    """
        This is TargetNotFoundError BaseError
        When something is not found
    """
    pass


class BaseError(Exception):
    """Base class for exceptions in this module."""

    def __init__(self, message=""):
        self.message = message

    def __repr__(self):
        return repr(self.message)


class FileNotExistError(BaseError):
    """Image does not exist."""
    pass


class TemplateInputError(BaseError):
    """Resolution input is not right."""
    pass

class NoSIFTModuleError(BaseError):
    """Resolution input is not right."""
    pass

class NoSURFModuleError(BaseError):
    """Resolution input is not right."""
    pass


class NoSiftMatchPointError(BaseError):
    """Exception raised for errors 0 sift points found in the input images."""
    pass
class NoSurfMatchPointError(BaseError):
    """Exception raised for errors 0 sift points found in the input images."""
    pass

class SiftResultCheckError(BaseError):
    """Exception raised for errors 0 sift points found in the input images."""
    pass


class HomographyError(BaseError):
    """In homography, find no mask, should kill points which is duplicate."""
    pass