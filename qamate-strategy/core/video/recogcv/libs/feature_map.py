# !/usr/bin/env python3
# -*- coding: UTF-8 -*-
#
# Copyright (c) 2022 Baidu.com, Inc. All Rights Reserved
#
"""
Desc:
Authors: <AUTHORS>
Date: 2022-09-13
"""

from matplotlib import pyplot as plt
import cv2
import numpy as np


def bgr_rgb(img):
    """
    @description:
    @param: 
    @return: 
    """
    (r, g, b) = cv2.split(img)
    return cv2.merge([b, g, r])


def orb_detect(image_a, image_b):
    """
    @description:
    @param: 
    @return: 
    """
    # feature match
    orb = cv2.ORB_create()
    kp1, des1 = orb.detectAndCompute(image_a, None)
    kp2, des2 = orb.detectAndCompute(image_b, None)
    # create BFMatcher object
    bf = cv2.BFMatcher(cv2.NORM_HAMMING, crossCheck=True)
    # Match descriptors.
    matches = bf.match(des1, des2)
    # Sort them in the order of their distance.
    matches = sorted(matches, key=lambda x: x.distance)
    # Draw first 10 matches.
    img3 = cv2.drawMatches(image_a, kp1, image_b, kp2,
    matches[:100], None, flags=2)
    return bgr_rgb(img3)
 
def sift_detect_swipe(img1, img2, detector='surf'):
    """
    @description: img1为滑动后的图片，img2为滑动前的图片
    @param: 
    @return: 
    """
    if isinstance(img1, str) and isinstance(img2, str):
        img1 = cv2.imread(img1)
        img2 = cv2.imread(img2)

    sift = cv2.SIFT_create()
    # find the keypoints and descriptors with SIFT
    kp1, des1 = sift.detectAndCompute(img1, None)
    kp2, des2 = sift.detectAndCompute(img2, None)
    # BFMatcher with default params
    bf = cv2.BFMatcher()
    matches = bf.knnMatch(des1, des2, k=2)
    # Apply ratio test
    good = [[m] for m, n in matches if m.distance < 0.5 * n.distance]
    
    new_good = []
    src_pts = np.float32([kp1[m[0].queryIdx].pt for m in good]).reshape(-1, 2)
    dst_pts = np.float32([kp2[m[0].trainIdx].pt for m in good]).reshape(-1, 2)

    match_num = len(src_pts)
    shot_feature = 0
    for idx in range(match_num):
        if (int(src_pts[idx][0]) - int(dst_pts[idx][0])) == 0:
            if (int(src_pts[idx][1]) < int(dst_pts[idx][1])):
                shot_feature += 1
                new_good.append(good[idx])
    # print(shot_feature , match_num)
    return shot_feature / match_num > 0.1

    # cv2.drawMatchesKnn expects list of lists as matches.
    # img3 = cv2.drawMatchesKnn(img1, kp1, img2, kp2, new_good, None, flags=2)       
    # return bgr_rgb(img3)


if __name__ == "__main__":
    # load image
    image_a = cv2.imread('/Users/<USER>/Desktop/frames/250_1662616981715.jpg')
    image_b = cv2.imread('/Users/<USER>/Desktop/frames/260_1662616981945.jpg')
    # ORB
    # img = orb_detect(image_a, image_b)
    # SIFT or SURF
    res = sift_detect_swipe(image_b, image_a)
    print(res)
    # plt.imshow(img)
    # plt.show()