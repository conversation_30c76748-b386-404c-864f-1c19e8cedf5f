# !/usr/bin/env python3
# -*- coding: UTF-8 -*-
#
# Copyright (c) 2022 Baidu.com, Inc. All Rights Reserved
#
"""
Desc:
Authors: <AUTHORS>
Date: 2022-07-27
"""

import base64
import json

import cv2
import numpy as np
import requests
from functools import singledispatch


def opencvimg_to_base64(image_np):
    """
    @description:
    @param: 
    @return: 
    """
    image = cv2.imencode('.jpg', image_np)[1]
    image_code = str(base64.b64encode(image))[2:-1]
    return image_code

def image_to_base64(image_path: str) -> str:
    """
    根据图片地址转换成base64
    :param image_path:
    :return:
    """
    img_file = open(image_path, 'rb')
    img = img_file.read()
    img_file.close()
    img_base64_str = bytes.decode(base64.b64encode(img))
    return img_base64_str


def __get_result_from_kirin(param: dict) -> dict:
    """
    从麒麟获取结果
    """
    url = "http://kirin.baidu-int.com/api/tool/210833"
    headers = {"Content-Type": "application/json"}
    param['platform_code'] = 4
    data = {
        "parameter": json.dumps(param),
        "name": "guyouda",
        "token": "e8e7085b0fa84e719a6db49918868171"
    }
    result = requests.post(url=url, data=json.dumps(data), headers=headers)
    kirin_response = json.loads(result.content)
    icheck_result = json.loads(kirin_response["result"]["response"])
    return icheck_result


def __cover_rect_with_color(result: dict, img_cv: np.ndarray, output_path: str):
    """
    输出图片的部分区域到文件,
    :param result: 页面解析结果
    :param output_path: str 输出图片绝对路径
    :param img_cv: 图像数组
    :return: bool
    """

    for horizontal_area in result.get("data", {}).get("rows", []):
        for element in horizontal_area.get("element_list", []):

            if element.get("type", "") == "IMAGE":
                img_cv[element["top"]:element["bottom"], element["left"]:element["right"], 0] = 0
                img_cv[element["top"]:element["bottom"], element["left"]:element["right"], 1] = 0
                img_cv[element["top"]:element["bottom"], element["left"]:element["right"], 2] = 255
            else:
                img_cv[element["top"]:element["bottom"], element["left"]:element["right"], 0] = 255
                img_cv[element["top"]:element["bottom"], element["left"]:element["right"], 1] = 0
                img_cv[element["top"]:element["bottom"], element["left"]:element["right"], 2] = 0

    cv2.imwrite(output_path, img_cv)

@singledispatch
def to_base64(image_np):
    return opencvimg_to_base64(image_np)

@to_base64.register(str)
def _to_base64(image_path: str):
    return image_to_base64(image_path)

def is_contain_image(image):
    """
    @description: 判断是否包含图片
    @param: 
    @return: bool值，是否包含图片
    """
    param = {
        "app_name": "generate_page_dom_tree",
        "image_type": "base64",
        "image": to_base64(image),
        "clip_conf": "1,3,0_0,7,3_1,3,0_0,7,0",
        "bg_color": "fixed_220,255_220,255_220,255",
        "needs_filter": True,
        "filter_width": 0,
        "filter_height": 10,
        "needs_agg_words_horizontal": True,
        "needs_agg_words_vertical": True,
        "area_split_line_ratio": 0.8
    }
    result = __get_result_from_kirin(param)
    # __cover_rect_with_color(result, image, './result.png')
    img_height = result['data']['height']
    img_width = result['data']['width']
    item_pos = set()
    for horizontal_area in result.get("data", {}).get("rows", []):
        for element in horizontal_area.get("element_list", []):
            # if int(element.get('top')) > 0.3 * img_height:
            item_pos.add((int(element.get('top')), int(element.get('height')) + int(element.get('top'))))
    
    sort_pos = list(item_pos)
    if len(sort_pos) == 0:
        return False
    sort_pos.sort(key=lambda x:int(x[0]))
    first = sort_pos[0][0]
    last = sort_pos[0][1]
    thresh = 0.3 * img_height
    # print(sort_pos)
    for item in sort_pos[1:]:
        # print(abs(item[0]-last), img_height*0.3)
        if item[1] < last:
            continue
        if abs(item[0]-last) > thresh:
            # print("False")
            return False
        last = item[1]
    # print("True")
    return True
    


def get_page_dom(image_path: str):
    """
    @description:
    @param: 
    @return: 
    """
    param = {
        "app_name": "generate_page_dom_tree",
        "image_type": "base64",
        "image": image_to_base64(image_path),
        "clip_conf": "1,3,0_0,7,3_1,3,0_0,7,0",
        "bg_color": "fixed_220,255_220,255_220,255",
        "needs_filter": True,
        "filter_width": 0,
        "filter_height": 10,
        "needs_agg_words_horizontal": True,
        "needs_agg_words_vertical": True,
        "area_split_line_ratio": 0.8
    }
    response = __get_result_from_kirin(param)
    return response

if __name__ == '__main__':
    get_page_dom("/Users/<USER>/Desktop/feed/tuwen/6/14_1659012772754.png", "./output.png")
