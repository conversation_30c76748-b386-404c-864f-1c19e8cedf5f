# !/usr/bin/env python3
# -*- coding: UTF-8 -*-
#
# Copyright (c) 2022 Baidu.com, Inc. All Rights Reserved
#
"""
Desc: 调取icheck云侧能力获取页面相似度
Authors: <AUTHORS>
Date: 2022-08-09
"""

import base64
import json

import cv2
import numpy as np
import requests
from video.recogcv.openapi.ocr import getWords
from functools import singledispatch

def image_to_base64(image_path: str) -> str:
    """
    根据图片地址转换成base64
    :param image_path:
    :return:
    """
    img_file = open(image_path, 'rb')
    img = img_file.read()
    img_file.close()
    img_base64_str = bytes.decode(base64.b64encode(img))
    return img_base64_str


def get_result_from_kirin(param: dict) -> dict:
    """
    从麒麟获取结果
    """
    url = "http://kirin.baidu-int.com/api/tool/210833"
    headers = {"Content-Type": "application/json"}
    param['platform_code'] = 4
    data = {
        "parameter": json.dumps(param),
        "name": "guyouda",
        "token": "e8e7085b0fa84e719a6db49918868171"
    }
    result = requests.post(url=url, data=json.dumps(data), headers=headers)
    kirin_response = json.loads(result.content)
    if kirin_response["result"].get("response", ""):
        icheck_result = json.loads(kirin_response["result"].get("response", ""))
        return icheck_result



def similarity_phash(image1_path: str, image2_path: str):
    """
    @description: 感知哈希获得页面相似度
    @param: 
    @return: 
    """
    param = {
        "app_name": "image_similarity_phash",
        "image_type": "base64",
        "image1": image_to_base64(image1_path),
        "image2": image_to_base64(image2_path)
    }
    return get_result_from_kirin(param)

@singledispatch
def find_text(image_np, keywords):
    param = {
        "app_name": "text_locate",
        "image_type": "base64",
        "image": opencvimg_to_base64(image_np),
        "keyWords": keywords        
    }
    return get_result_from_kirin(param)


@find_text.register(str)
def _find_text(image_path: str, keywords: list):
    """
    @description:
    @param: 
    @return: 
    """
    image_np = cv2.imread(image_path)
    return find_text(image_np, keywords)



def get_ocr(image_path:str):
    """
    @description:
    @param: 
    @return: 
    """
    param = {
        "app_name": "ocr",
        "image_type": "base64",
        "image": image_to_base64(image_path)
    }

    return get_result_from_kirin(param)

def opencvimg_to_base64(image_np):
    """
    @description:
    @param: 
    @return: 
    """
    image = cv2.imencode('.jpg', image_np)[1]
    image_code = str(base64.b64encode(image))[2:-1]
    return image_code


@singledispatch
def template_match(source_image_np, image_np):
    """
    @description:
    @param: 
    @return: 
    """
    param = {
        "app_name": "TempMatchCNN",
        "image_type": "base64",
        "image": opencvimg_to_base64(source_image_np),
        "template": opencvimg_to_base64(image_np)
    }
    return get_result_from_kirin(param)

@template_match.register(str)
def _template_match(source_image_path:str, temp_image_path:str):
    """
    @description:
    @param: 
    @return: 
    """
    temp_img_np = cv2.imread(temp_image_path)
    source_image_np = cv2.imread(source_image_path)
    return template_match(source_image_np, temp_img_np)