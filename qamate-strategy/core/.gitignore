__pycache__
*.tar.gz
.DS_Store
.idea
scripts/build
scripts/build.sh
scripts/main.spec
evaluate/txtfile/*
evaluate/pictures_keyboard
util/android_sousuo
venv/
venv_86/
test.png
param
package_outputs
package-lock.json
evaluate/*.png
image/ui_dom/*.png
image/ui_dom/*.txt
tests/*.png
tests/*.jpg
tests/*.json
tests/data
tests/demo
tests/wget-log
tests/node_match_debug/
.history
**/__pycache__
**/log_data
**/*.png
**/*.bak
**/*.json !model !featrues
**/*.jpg
**/*.txt !model/**/*.txt
.vscode/
baks/
**/*.out
