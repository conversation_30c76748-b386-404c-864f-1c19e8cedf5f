#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@Desc    :   
@File    :   pyinstall_build.py
@Time    :   2023-08-23
<AUTHOR>   x<PERSON><PERSON><PERSON>@baidu.com
"""
import os
import json


def format_file_name(file_name):
    """
    格式化，文件名
    """
    # print(file_name)
    final_name = file_name.replace("../features", "features")
    final_name = final_name.replace("../basics", "basics")
    final_name = final_name.split(".")[0]
    final_name = final_name.replace("/", ".")
    # print(final_name)
    return "--hidden-import {}".format(final_name)


def list_files(path, file_list):
    """
    获取目标目录
    """
    items = os.listdir(path)
    dirs = []

    for item in items:
        item_path = os.path.join(path, item)
        if os.path.isdir(item_path):
            dirs.append(item_path)
        else:
            if item[0:2] == "__":
                continue
            if item.split(".")[-1] != "py":
                continue
            file_list.append(format_file_name(item_path))

    for dir in dirs:
        list_files(dir, file_list)


def build_command(file_list):
    """
    命令行生成
    """
    # command = "/Library/Frameworks/Python.framework/Versions/3.9/bin/pyinstaller " + \
    #     "../main.py --add-data '../normal_icon_detect:normal_icon_detect' " + \
    #     "--add-data '../normal_element_detect:normal_element_detect' " + \
    #     "--add-data '../normal_card_detect:normal_card_detect' " + \
    #     "--add-data '../cnn_similarity:cnn_similarity' " + \
    #     "{}".format(" ".join(file_list)) + \
    #     "  -y --windowed --clean --distpath ../../../qamate-native/core/public/py"

    # command = "/Library/Frameworks/Python.framework/Versions/3.9/bin/pyinstaller " + \
    #     "../main.py --add-data '../basics/normal_icon_detect:basics/normal_icon_detect' " + \
    #     "--add-data '../basics/normal_element_detect:basics/normal_element_detect' " + \
    #     "--add-data '../basics/normal_card_detect:basics/normal_card_detect' " + \
    #     "--add-data '../basics/cnn_similarity:basics/cnn_similarity' " + \
    #     "{}".format(" ".join(file_list)) + \
    #     "  -y --windowed --clean --distpath ../../../qamate-native/core/public/py"
    # command = "/Library/Frameworks/Python.framework/Versions/3.9/bin/pyinstaller " + \
    # "../main.py --add-data '../basics/normal_icon_detect:basics/normal_icon_detect' " + \
    # "--add-data '../basics/normal_element_detect:basics/normal_element_detect' " + \
    # "--add-data '../basics/normal_card_detect:basics/normal_card_detect' " + \
    # "--add-data '../basics/cnn_similarity:basics/cnn_similarity' " + \
    # "{}".format(" ".join(file_list)) + \
    # "  -y --windowed --clean --distpath ../../../qamate-native/core/public/py"

    command = (
        "../venv/bin/pyinstaller "
        + "../main.py "
        + "{}".format(" ".join(file_list))
        + "  -y --windowed --clean --distpath ../package_outputs/output/py\n"
    )

    return command


if __name__ == "__main__":
    file_list = []
    list_files("../features", file_list)
    # list_files("../basics", file_list)
    # print(file_list)
    command = build_command(file_list)

    # 生成 build.sh
    f = open("build.sh", "w")
    f.write(
        "cd .. && rm -rf package_outputs/ && mkdir package_outputs && cd scripts\n"
        + "../venv/bin/python3.9 package_models.py\n"
        + 'find . -name ".DS_Store" -print -delete &&\n'
        + "{}".format(command)
        + "cd ../package_outputs/output/py && chmod -R 777 . && cd -\n"
        + "cp ../version.json ../package_outputs/\n"
        + "cd ../package_outputs/\n"
        + "tar czvf server.tar.gz version.json output/\n"
        + "rm -rf version.json output/\n"
    )
    f.close()
