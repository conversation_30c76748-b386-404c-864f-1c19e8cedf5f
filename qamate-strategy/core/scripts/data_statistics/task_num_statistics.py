#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@Desc    :   用于按季度统计自动化用例的执行数量
@File    :   task_num_statistics.py
@Time    :   2024-12-27
<AUTHOR>   x<PERSON><PERSON><PERSON>@baidu.com
"""
from statistisc_db_util import DB

import json


cache_pool_type = {}

def tmp_get_v3_task_type(plan_params, db_client):
    """
    临时获取3.0的云端任务类型
    """
    plan_info = json.loads(plan_params)
    pool_list = plan_info["planParams"]["poolList"]
    if len(pool_list) == 0:
        return 0
    pool_id = pool_list[0]
    pool_type = None
    if pool_id in cache_pool_type:
        pool_type = cache_pool_type[pool_id]
    else:
        sql = "select task_type from core_device_pool where id = {};".format(pool_id)
        raw = db_client.select(sql)
        if len(raw) == 0:
            pool_type = 2
        else:
            pool_type = raw[0][0]
        cache_pool_type[pool_id] = pool_type
    
    if pool_type == 2:
        return 0
    else:
        return 1


def task_num_statistics(start_time, end_time):
    """
    按季度统计存量自动化用例的数量
    """
    # start_time = '2024-01-01 00:00:00'
    # end_time = '2024-03-31 23:59:59'
    print("start_time: {}, end_time: {}".format(start_time, end_time))

    lazyone_db = DB("lazyone")
    # qamate 2.0数据统计
    # 计划数量
    lazyone_plan_num = 0
    # 云端
    sql = "select id from lazycloud_plan where create_time >= " + \
        "'{}' and create_time <= '{}' and type = 1;".format(start_time, end_time)
    raw = lazyone_db.select(sql)
    lpo_list = []
    for row in raw:
        lpo_list.append(row[0])
    lazyone_plan_num_online = len(lpo_list)
    # 本地
    sql = "select id from lazycloud_plan where create_time >=" + \
        " '{}' and create_time <= '{}' and type = 3;".format(start_time, end_time)
    raw = lazyone_db.select(sql)
    lpl_list = []
    for row in raw:
        lpl_list.append(row[0])
    lazyone_plan_num_local = len(lpl_list)
    
    lazyone_plan_num = lazyone_plan_num_online + lazyone_plan_num_local
    print("lazyone 计划数量：{}".format(lazyone_plan_num))
    print("lazyone 云端计划数量：{}".format(lazyone_plan_num_online))
    print("lazyone 本地计划数量：{}".format(lazyone_plan_num_local))

    # 执行数量
    lazyone_task_num = 0
    lazyone_task_online = 0
    lazyone_task_local = 0

    sql = "select id, plan_id from lazycloud_case where status in (2, 3, 4) and " + \
        "create_time >= '{}' and create_time <= '{}';".format(start_time, end_time)
    raw = lazyone_db.select(sql)
    for row in raw:
        plan_id = row[1]
        if plan_id in lpo_list:
            lazyone_task_online += 1
        elif plan_id in lpl_list:
            lazyone_task_local += 1
    lazyone_task_num = lazyone_task_online + lazyone_task_local
    print("lazyone 执行数量：{}".format(lazyone_task_num))
    print("lazyone 云端执行数量：{}".format(lazyone_task_online))
    print("lazyone 本地执行数量：{}".format(lazyone_task_local))

    # qamate 3.0数量统计
    core_db = DB("core")
    # 计划数量
    core_plan_num = 0
    core_plan_online = 0
    core_plan_local = 0

    cpo = []
    cpl = []

    start_time_stamp = core_db.time_trans(start_time)
    end_time_stamp = core_db.time_trans(end_time)
    sql = "select id, plan_params from core_cloud_plan where create_time >= " + \
        "{} and create_time <= {};".format(start_time_stamp, end_time_stamp)
    raw = core_db.select(sql)
    for row in raw:
        plan_id = row[0]
        plan_type = tmp_get_v3_task_type(row[1], core_db)
        if plan_type == 0:
            cpl.append(plan_id)
        elif plan_type == 1:
            cpo.append(plan_id)
    core_plan_local = len(cpl)
    core_plan_online = len(cpo)
    core_plan_num = core_plan_local + core_plan_online
    print("core 计划数量：{}".format(core_plan_num))
    print("core 云端计划数量：{}".format(core_plan_online))
    print("core 本地计划数量：{}".format(core_plan_local))

    # 执行数量
    core_task_num = 0
    core_task_online = 0
    core_task_local = 0

    sql = "select id, plan_id from core_cloud_task where status in (4, 5, 6) and create_time >= " + \
        "{} and create_time <= {};".format(start_time_stamp, end_time_stamp)
    raw = core_db.select(sql)
    for row in raw:
        plan_id = row[1]
        if plan_id in cpo:
            core_task_online += 1
        elif plan_id in cpl:
            core_task_local += 1
    core_task_num = core_task_online + core_task_local
    print("core 执行数量：{}".format(core_task_num))
    print("core 云端执行数量：{}".format(core_task_online))
    print("core 本地执行数量：{}".format(core_task_local))



if __name__ == "__main__":
    # start_time = '2024-01-01 00:00:00'
    # end_time = '2024-03-31 23:59:59'
    # time_list = [
    #     ('2024-01-01 00:00:00', '2024-03-31 23:59:59'),
    #     ('2024-04-01 00:00:00', '2024-06-30 23:59:59'),
    #     ('2024-07-01 00:00:00', '2024-09-30 23:59:59'),
    #     ('2024-10-01 00:00:00', '2024-12-31 23:59:59')
    # ]
    time_list = [
        ('2024-10-01 00:00:00', '2024-12-30 23:59:59'),
    ]
    for time_info in time_list:
        start_time = time_info[0]
        end_time = time_info[1]
        task_num_statistics(start_time, end_time)