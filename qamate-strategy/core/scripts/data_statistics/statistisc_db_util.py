#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@Desc    :   数据统计-db操控基类
@File    :   statistisc_db_util.py
@Time    :   2024-12-27
<AUTHOR>   x<PERSON><PERSON><PERSON>@baidu.com
"""

import pymysql
import time


class DB(object):
    """
    数据查询类
    """
    def __init__(self, db_type='lazyone'):
        """
        初始化
        """
        self.db_connection = None
        if db_type == 'lazyone':
            self.db_connection = pymysql.connect(
                host="lazyone0000-backup.xdb.all.serv",
                port=8445,
                user="lazyone_read",
                password="ueo+oJqhb:zfB",
                charset="utf8",
                database="lazyone"
            )
        elif db_type == 'core':
            self.db_connection = pymysql.connect(
                host="qamate0000-offline.xdb.all.serv",
                port=4932,
                user="core_read",
                password="+p3+hBgcrj",
                charset="utf8",
                database="qamate"
            )
        self.cursor = self.db_connection.cursor()

    def __del__(self):
        """
        析构函数
        """
        # 关闭游标和数据库连接
        self.cursor.close()
        self.db_connection.close()

    def select(self, sql):
        """
        查询数据
        :param sql: SQL 语句
        :return: 结果集
        """
        # 执行 SQL 查询
        self.cursor.execute(sql)
        # 获取查询结果
        result = self.cursor.fetchall()
        #  
        return result
    
    def time_trans(self, time_str):
        """
        将时间字符串转为时间戳
        """
        time_tuple = time.strptime(time_str, "%Y-%m-%d %H:%M:%S")
        timestamp = time.mktime(time_tuple)
        return timestamp