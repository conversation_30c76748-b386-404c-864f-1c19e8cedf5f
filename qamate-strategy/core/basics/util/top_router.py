#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@Desc    :   
@File    :   main_router.py
@Time    :   2024-01-29
<AUTHOR>   xuz<PERSON><PERSON>@baidu.com
"""
import json


from basics.util.error import JsonrpcError
from basics.util import logger
from features.router import main_router
from features.cv_services.cs_controller import CO<PERSON><PERSON><PERSON><PERSON> as CS_CONTROLLER
from features.record_replay.previous_versions.router_jsonrpc import CONTROLLER as PV_CONTROLLER


def correct_version(method, params):
    """
    临时方案，纠正 V1、V2版本的兼容问题
    """
    action_list = method.split(".")
    action_name = action_list[-1]

    # 如果是非ios_node_match的方法，直接透传
    if action_name not in ["ios_node_match", "node_match"]:
        return method

    # 如果是ios_node_match的方法，进行下版本的修正
    dom = params[0]["step_info"]["dom"]
    
    # 判断是否是 V1 版本
    if "parents" not in dom:
        real_method = "record_replay.ios_node_match"
        method = "record_replay.ios_node_match"
        source_path = params[0]["step_info"]["findInfo"]["findPath"]
        source_path.append(params[0]["step_info"]["findInfo"]["findNode"]["nodeInfo"][0])
        params[0]["source_path"] = source_path
        logger.info("source_path: {}".format(source_path))
        return real_method
    
    # 判断是否是 V2 版本
    if "dom_info" not in dom:
        real_method = "record_replay_v2.ios_node_match"
        return real_method

    # 根据 dom_info 信息选择正确的版本
    dom_info = dom["dom_info"]
    logger.info("dom_info: {}".format(json.dumps(dom_info)))
    version = dom_info["version"][1:]
    version_list = version.split(".")
    if version_list[1] == "0" and int(version_list[0]) <= 4:
        version_str = "v{}".format(version_list[0])
    else:
        version_str = "v{}_{}".format(version_list[0], version_list[1])
    real_method = "record_replay_{}.ios_node_match".format(version_str)
    return real_method


def check_method_version(method):
    """
    判断是否是2.0+版本
    """
    if method.startswith("video"):
        return False
    method_list = method.split(".")
    # 判断是都是v开头
    if method_list[0][0] == "v":
        return True
    elif method_list[0] == "record_replay" and method_list[1] in ("record", "replay"):
        return True
    else:
        return False
    

def get_method_result(jsonrpc, id, method, params):
    """
    获取方法的结果
    """
    res = {
        "jsonrpc": jsonrpc,
        "id": id,
        "error": None
    }
    check_res = check_method_version(method) 
    if check_res is True:
        res["result"] = main_router(method, params[0])
        logger.info("replay result: {}".format(json.dumps(res["result"], ensure_ascii=False)))
    else:
        method = correct_version(method, params)
        logger.info("correct method:{}".format(method))
        if method in CS_CONTROLLER:
            res["result"] = CS_CONTROLLER[method](*params)
            logger.info("replay result: {}".format(json.dumps(res["result"], ensure_ascii=False)))
        elif method in PV_CONTROLLER:
            res["result"] = PV_CONTROLLER[method](*params)
            logger.info("replay result: {}".format(json.dumps(res["result"], ensure_ascii=False)))
        else:
            raise JsonrpcError(-32601, "Method not found",
                            {"method": method, "params": params})
    return res

if __name__ == '__main__':
    r = check_method_version('video')
    print(r)