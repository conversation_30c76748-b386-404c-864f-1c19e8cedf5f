#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@File    : data.py
<AUTHOR> <PERSON><PERSON><PERSON><PERSON>(renya<PERSON><PERSON>@baidu.com)
@Time    : 2025/7/25 19:36
@Desc    : 由 lazyPerf 代码库迁移而来
"""

import os
import pkgutil
import yaml
import json
import basics.util.config as config
import sys
import importlib


def get(resource, encoding="UTF-8", errors="strict"):
    """ 获取配置文件信息 """
    if config.IS_RUN_IN_BUNDLE:
        package = "conf"
        resource_content_bin = pkgutil.get_data(package, resource)
    else:
        DIR_PATH = os.path.dirname(os.path.abspath(__file__))
        CONFIG_PATH = os.path.join(DIR_PATH, "../conf/", resource)
        fp = open(CONFIG_PATH, "rb")
        resource_content_bin = fp.read()

    resource_content = resource_content_bin.decode(encoding, errors)
    if '.yaml' == resource[-5:]:
        resource_content = yaml.load(resource_content, Loader=yaml.SafeLoader)
    elif '.json' == resource[-5:]:
        resource_content = json.loads(resource_content)
    return resource_content


def get_data_path():
    """ 获取conf路径 """
    path = None
    if config.IS_RUN_IN_BUNDLE:
        package = "conf"
        spec = importlib.util.find_spec(package)
        if spec is None:
            return None
        loader = spec.loader
        if loader is None or not hasattr(loader, 'get_data'):
            return None
        mod = (sys.modules.get(package) or
               importlib._bootstrap._load(spec))
        if mod is None or not hasattr(mod, '__file__'):
            return None
        path = os.path.dirname(mod.__file__)

    else:
        __dir__ = os.path.dirname(os.path.abspath(__file__))
        path = os.path.join(__dir__, "../conf")
    return path
