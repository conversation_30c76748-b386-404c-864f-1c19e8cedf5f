# !/usr/bin/env python3
# -*- coding: UTF-8 -*-
################################################################################
#
# Copyright (c) 2024 Baidu.com, Inc. All Rights Reserved
#
################################################################################
"""
常用图像相关的工具

Authors: <AUTHORS>
Date:    2024/01/24
"""

import base64
import os
import re

import cv2
import numpy as np
import requests

from basics.util import logger
from basics.util.obj import ICheckObj


class Image(ICheckObj):
    """
    UI分析图像对象
    """

    def __init__(self, imgcv: np.ndarray = None, base64_str: str = None,
                 path: str = None, url: str = None):
        """
        init
        """
        self.__imgcv = None
        self.__base64 = None
        self.__path = None
        self.__url = None
        self.__width = None
        self.__height = None

        # imgcv是 数组，不能使用 if imgcv:
        # ValueError: The truth value of an array with more than one element is ambiguous.Use a.any() or a.all()
        if imgcv is not None:
            self.imgcv = imgcv

        if base64_str:
            self.base64 = base64_str

        if url:
            self.url = url

        if path:
            self.path = path

    def save_image(self, path: str):
        """
        保存图片到本地
        :param path: 本地绝对路径
        :return:
        """
        if self.imgcv is not None:
            cv2.imwrite(path, self.imgcv)
            self.path = path
            return

    @property
    def imgcv(self) -> np.ndarray:
        """
        获取图片数组对象
        采用延迟加载机制，在使用的情况下再初始化
        :return:
        """
        if self.__imgcv is None and self.__base64:
            self.__imgcv = base64_to_imgcv(self.__base64)

        if self.__imgcv is None and self.__path:
            self.__imgcv = cv2.imread(self.__path)

        if self.__imgcv is None and self.__url:
            self.__imgcv = url_to_imgcv(self.__url)

        return self.__imgcv

    @property
    def base64(self) -> str:
        """
        获取图片base64字符串
        采用延迟加载机制，在使用的情况下再初始化
        :return:
        """
        if self.__base64 is None and self.__imgcv is not None:
            self.__base64 = imgcv_to_base64(self.__imgcv)

        if self.__base64 is None and self.__path:
            self.__base64 = image_to_base64(self.__path)

        if self.__base64 is None and self.__url:
            self.__base64 = url_to_base64(self.__url)

        return self.__base64

    @base64.setter
    def base64(self, _base64: str):
        if not _base64 or not isinstance(_base64, str):
            raise Exception("base64类型必须为str")

    @imgcv.setter
    def imgcv(self, _imgcv: np.ndarray):
        if not isinstance(_imgcv, np.ndarray):
            raise Exception("imgcv类型必须为np.ndarray")
        self.__imgcv = _imgcv

    @property
    def url(self) -> str:
        return self.__url

    @url.setter
    def url(self, _url: str):
        if not _url or not re.match(r'https?:/{2}\w.+$', _url):
            raise Exception("参数错误：图片URL[{}]错误".format(_url))
        self.__url = _url

    @property
    def path(self) -> str:
        return self.__path

    @path.setter
    def path(self, _path):
        if not _path or not os.path.isfile(_path):
            raise Exception("{}不存在".format(_path))
        self.__path = _path

    @property
    def width(self) -> int:
        if self.__width is None and self.imgcv is not None:
            self.__width = self.imgcv.shape[1]

        return self.__width

    @width.setter
    def width(self, _width):
        if not isinstance(_width, int) or _width <= 0:
            raise Exception("图片宽度参数[{}]错误".format(_width))
        self.__width = _width

    @property
    def height(self) -> int:
        if self.__height is None and self.imgcv is not None:
            self.__height = self.imgcv.shape[0]

        return self.__height

    @height.setter
    def height(self, _height):
        if not isinstance(_height, int) or _height <= 0:
            raise Exception("图片高度参数[{}]错误".format(_height))
        self.__height = _height

    def to_dict(self) -> dict:
        """
        此处重写此方法，是为了避免标准输出打印过多的 base64 和 imgcv
        :return:
        """
        return {
            "path": self.path,
            "url": self.url
        }


def image_path_to_base64(image_path):
    """
    将图片转为 base64格式
    :param image_path:
    :return:
    """
    with open(image_path, "rb") as image_file:
        return base64.b64encode(image_file.read()).decode('utf-8')


def url_to_imgcv(img_url: str, timeout: int = 10, img_type: int = cv2.IMREAD_COLOR) -> np.ndarray:
    """
    图片URL地址
    :param img_type: 图片类型，详见cv2.IMREAD_XXX
        cv2.IMREAD_COLOR      3通道BRG图片
        cv2.IMREAD_GRAYSCALE  1通道灰度
    :param img_url:
    :param timeout: 下载图片超时时间，默认10秒
    :return:
    """
    __check_url(img_url)

    try_count = 3
    response = __get_url_response(img_url, timeout, try_count)
    if 200 <= response.status_code < 300:
        imgcv = cv2.imdecode(np.frombuffer(response.content, np.uint8), img_type)
        return imgcv

    raise Exception("download image from [{}] error".format(img_url))


def url_to_image_path(img_url: str, save_path: str, timeout: int = 10):
    """
    图片URL 下载到本地
    :param img_url:
    :param save_path: 保存地址
    :param timeout:
    :return
    """
    __check_url(img_url)

    try_count = 3
    response = __get_url_response(img_url, timeout, try_count)
    if 200 <= response.status_code < 300:
        with open(save_path, "wb") as fi:
            fi.write(response.content)
        # logger.info("download [{}] from [{}]".format(save_path, img_url))
    else:
        logger.error("download fail: url[{}]".format(img_url))
        raise Exception("下载图片失败:{}".format(img_url))


def url_to_base64(image_url):
    """
    将图片链接转为 base64格式
    :param image_url:
    :return:
    """
    try:
        # Download the image from the URL
        response = requests.get(image_url)
        response.raise_for_status()  # Raise an exception for HTTP errors

        # Convert the image to base64
        image_data = response.content
        base64_encoded = base64.b64encode(image_data).decode('utf-8')

        return base64_encoded

    except Exception as e:
        return f"Error: {str(e)}"


def base64_to_imgcv(img_base64: str) -> np.ndarray:
    """
    base64_to_imgcv
    :param img_base64:
    :return:
    """
    img_data = base64.b64decode(img_base64)
    nparr = np.frombuffer(img_data, np.uint8)
    img = cv2.imdecode(nparr, cv2.COLOR_BGR2RGB)
    return img


def imgcv_to_base64(imgcv: np.ndarray) -> str:
    """
    imgCV_to_base64
    :param imgcv:
    :return:
    """
    image = cv2.imencode('.png', imgcv)[1]
    img_base64 = str(base64.b64encode(image))[2:-1]
    return img_base64


def image_to_base64(image_path: str) -> str:
    """
    根据图片地址转换成base64
    :param image_path:
    :return:
    """
    img_file = open(image_path, 'rb')
    img = img_file.read()
    img_file.close()
    img_base64_str = bytes.decode(base64.b64encode(img))
    return img_base64_str


def __check_url(url: str):
    """
    检查URL是否正确
    :param url: URL
    :return: None
    :raise AgentException
    """
    if not url or not re.match(r'https?:/{2}\w.+$', url):
        raise Exception("图片URL错误:{}".format(url))


def __get_url_response(url: str, timeout: int = 10, retry: int = 0):
    """
    获取URL的内容
    :param url: url
    :param timeout: 超时
    :param retry: 重试次数
    :return:
    :raise AgentException
    """
    count = -1

    response = None

    while count < retry:
        count += 1
        try:
            logger.info("begin send request, url:%s", url)
            response = requests.get(url, timeout=timeout)
            logger.warning("http response code: %d, url: %s", response.status_code, url)
        except Exception as e:
            logger.error(e)
            continue

        if response is None:
            raise Exception("下载图片异常:{}".format(url))

        if response.status_code == 404:
            raise Exception("图片文件不存在:{}".format(url))

        if not response.content:
            raise Exception("图片文件为空: {}".format(url))

        return response