# !/usr/bin/env python3
# -*- coding: UTF-8 -*-
#
# Copyright (c) 2024 Baidu.com, Inc. All Rights Reserved
#
"""
Bos 相关内容

Authors: <AUTHORS>
Date:    2024/04/24
"""


import datetime
import hashlib
import os
import traceback
import base64
from uuid import uuid4

import numpy as np
import cv2

from baidubce.auth.bce_credentials import BceCredentials
from baidubce.bce_client_configuration import BceClientConfiguration
from baidubce.services.bos.bos_client import BosClient

from basics.util import logger

BOS_CONF = {
    'url': 'https://bj.bcebos.com',
    'ak': '4f715364863640238377db4c0e4ceb01',
    'sk': '043e447f0a6d4fae855f685f35fcf9cc',
    'bucket': 'newmvp'
}

LLM_BOS_CONF = {
    'url': 'https://bj.bcebos.com',
    'ak': '5b3cf2568aae43e4a2e6097501db0a23',
    'sk': '9741ff39639943c6b416f3ece3f306c2',
    'bucket': 'icheck-data'
}

# 上传的文件类型
CONTENT_PNG = "image/png"
CONTENT_JPEG = "image/jpeg"
CONTENT_JPG = "image/jpeg"
CONTENT_GIF = "image/gif"
CONTENT_XML = "text/xml"
CONTENT_TEXT = "text/plain"
CONTENT_HTML = "text/html"
CONTENT_JSON = "application/json"


def is_file_exist(file_path: str, throw_exception=False):
    """
    校验文件是否存在
    :param file_path: 文件路径
    :param throw_exception: 文件不存在的情况是否直接抛出异常
    :return: bool
    """
    result = os.path.isfile(file_path)
    if throw_exception and not result:
        raise Exception("file_path:{}".format(file_path))

    return result


class BosUtil(object):
    """
    BOS工具类
    """

    def __init__(self, url: str, ak: str, sk: str, bucket: str):
        self.url = url
        self.ak = ak
        self.sk = sk
        self.bucket = bucket

    def get_client(self):
        """
        获取client
        :return:
        """
        config = BceClientConfiguration(credentials=BceCredentials(self.ak, self.sk), endpoint=self.url)
        bos_client = BosClient(config)
        return bos_client

    def upload_file(self, file_path: str, key=None, content_type=None) -> str:
        """
        上传文件到bos
        :param file_path: 文件本地绝对路径
        :param key: 在BOS上存储的文件名字
        :param content_type: 文件类型
        :return: 文件URL地址
        """
        is_file_exist(file_path, True)

        try:
            bos_client = self.get_client()
            if not key:
                time_info = datetime.datetime.now().strftime("%Y%m%d%H%M%S")
                unique_flag = hashlib.md5(
                    (str(datetime.datetime.now()) + file_path).encode(encoding='UTF-8')
                ).hexdigest()
                key = "{}-{}-{}".format(time_info, unique_flag, file_path.split("/")[-1])

            res = bos_client.put_object_from_file(bucket=self.bucket, key=key, file_name=file_path,
                                                  content_type=content_type)
            # logger.info(res)
            result_url = "{}/{}/{}".format(self.url, self.bucket, key)
            logger.info(result_url)
            return result_url
        except Exception as e:
            raise Exception("上传文件到bos发生错误：{}".format(e))

    def upload_image(self, image_path: str, key: str = None, content_type=CONTENT_PNG) -> str:
        """
        从本地文件上传图片
        :param image_path: 图片路径
        :param content_type: 文件类型
        :return: 图片URL地址
        """
        return self.upload_file(image_path, key=key, content_type=content_type)

    def upload_imgcv(self, imgcv: np.ndarray, key: str = None) -> str:
        """
        从numpy.ndarray图像数组上传图片
        :param imgcv: np.ndarray  cv.COLOR_BGR
        :param key: 保存的文件名
        :return: 图片URL地址
        """
        try:
            bos_client = self.get_client()
            if not key:
                time_info = datetime.datetime.now().strftime("%Y%m%d%H%M%S")
                unique_flag = hashlib.md5(str(datetime.datetime.now()).encode(encoding='UTF-8')).hexdigest()
                key = "{}-{}".format(time_info, unique_flag)

            # 转化为图片二进制字节数组
            data = cv2.imencode(".png", imgcv)[1].tobytes()

            # 获取文件数据MD5
            md5 = hashlib.md5()
            md5.update(data)
            content_md5 = base64.standard_b64encode(md5.digest())

            # 上传
            res = bos_client.put_object(bucket_name=self.bucket, key=key, data=data, content_length=len(data),
                                        content_md5=content_md5, content_type=CONTENT_PNG)
            # logger.info(res)
            result_url = "{}/{}/{}".format(self.url, self.bucket, key)
            logger.info(result_url)
            return result_url
        except Exception as e:
            raise Exception("上传图片到bos发生错误：{}".format(e))

    def upload_string(self, data: str, key: str = None, content_type=CONTENT_TEXT) -> str:
        """
        上传文本
        :param content_type:
        :param data: 文本字符串
        :param key: 保存的文件名
        :return: 图片URL地址
        """
        try:
            bos_client = self.get_client()
            if not key:
                time_info = datetime.datetime.now().strftime("%Y%m%d%H%M%S")
                unique_flag = hashlib.md5(str(datetime.datetime.now()).encode(encoding='UTF-8')).hexdigest()
                key = "{}-{}".format(time_info, unique_flag)

            # 上传
            res = bos_client.put_object_from_string(bucket=self.bucket, key=key,
                                                    data=data, content_type=content_type)
            logger.info(res)
            result_url = "{}/{}/{}".format(self.url, self.bucket, key)
            logger.info(result_url)
            return result_url
        except Exception as e:
            raise Exception("上传文本到bos发生错误:{}".format(e))


def __get_client():
    """
    获取BOSUtil
    :return:
    """
    url = BOS_CONF['url']
    ak = BOS_CONF['ak']
    sk = BOS_CONF['sk']
    bucket = BOS_CONF['bucket']

    return BosUtil(url, ak, sk, bucket)


def __generate_key() -> str:
    """
    生成保存在BOS的文件名
    :return:
    """
    date_info = datetime.datetime.now().strftime("%Y%m%d")
    unique_flag = hashlib.md5(str(datetime.datetime.now()).encode(encoding='UTF-8')).hexdigest()
    key = "{}/{}".format(date_info, unique_flag)
    return key


def upload_file(file_path: str, content_type: str = None, key: str = None) -> str:
    """
    上传文件到bos
    :param key: 保存在BOS的文件名
    :param file_path: 文件本地绝对路径
    :param content_type: 文件类型
    :return: 文件URL地址
    """
    if not os.path.exists(file_path):
        raise Exception("{}不存在".format(file_path))

    try:

        if not key:
            key = __generate_key()

        bos_util = __get_client()
        return bos_util.upload_file(file_path, key, content_type)
    except Exception as e:
        logger.error("上传文件到BOS失败, error:{}".format(e))
        logger.error(traceback.format_exc())
        raise Exception("上传文件到BOS失败, error:{}".format(e))


def upload_image(image_path: str, key: str = None, content_type: str = CONTENT_PNG) -> str:
    """
    上传图片
    :param content_type:
    :param key:
    :param image_path: 图片路径
    :return: 图片URL地址
    """
    return upload_file(file_path=image_path, content_type=content_type, key=key)


def upload_imgcv(imgcv: np.ndarray, key: str = None) -> str:
    """
    上传图片
    :param key:
    :param imgcv: 图片数组
    :return: 图片URL地址
    """
    try:

        if not key:
            key = __generate_key()

        bos_util = __get_client()
        return bos_util.upload_imgcv(imgcv, key)
    except Exception as e:
        logger.error(e)
        logger.error(traceback.format_exc())
        raise Exception("上传文件到BOS失败, error:{}".format(e))


if __name__ == '__main__':
    upload_image("/Users/<USER>/data/页面理解-通用卡片/新增物料/1-24/IMG_0093.PNG")
