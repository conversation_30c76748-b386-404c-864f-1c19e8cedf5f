#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@File    : image_merge.py
<AUTHOR> <PERSON><PERSON><PERSON><PERSON>(renyan<PERSON>@baidu.com)
@Time    : 2025/4/11 15:55
@Desc    : 
"""

from typing import List

import cv2
import numpy as np

from basics.util import logger


class ImageMerge(object):
    """
    图片拼接
    """

    def __init__(self, imgcv_list: List[np.ndarray], dir_path=None):
        """

        :param imgcv_list:
        """
        self.imgcv_list = imgcv_list
        self.dir_path = dir_path

    def get_top(self, images: List[np.ndarray]) -> int:
        """
        获取顶部公共区域信息
        :param images:
        :return: 顶部区域结束的位置，不包含
        """
        tops = []
        for i in range(0, len(images) - 1):
            cur_img = images[0]
            next_img = images[i + 1]

            diff = cur_img - next_img
            for j in range(0, diff.shape[0]):
                if not diff[j].any():
                    continue
                tops.append(j)
                break

        # 图片完全一致
        if len(tops) == 0:
            return 0

        # 去除最大值最小值
        # if len(tops) >= 3:
        #     tops = sorted(tops)[1:-1]

        # 取众数
        # counts_top = np.bincount(tops)
        # top = np.argmax(counts_top)
        top = np.min(tops)

        logger.info("tops: %s, top: %s", tops, top)
        return top

    def get_bottom(self, images: List[np.ndarray], top: int) -> int:
        """
        获取底部公共区域信息
        :param top:
        :param images:
        :return: 底部区域开始的位置，包含
        """

        bottoms = []
        for i in range(0, len(images) - 1):
            cur_img = images[i]
            next_img = images[i + 1]

            diff = cur_img - next_img
            # 去除随机噪声的影响
            # diff = cv2.medianBlur(diff, 3)
            diff[diff > 230] = 0
            diff[diff < 25] = 0

            # 避免滚动条影响判断
            width = diff.shape[1]
            diff = diff[:, 0:int(width * 0.9), :]

            for j in range(diff.shape[0] - 1, -1, -1):
                if not diff[j].any():
                    continue
                bottoms.append(j)
                break

        if len(bottoms) == 0:
            return images[0].shape[0]

        # 去除最大值最小值
        # if len(bottoms) >= 3:
        #     bottoms = sorted(bottoms)[1:-1]

        bottoms = list(filter(lambda x: x > top, bottoms))

        # 取众数
        # counts_bottom = np.bincount(bottoms)
        # bottom = np.argmax(counts_bottom)
        bottom = np.max(bottoms)
        logger.info("bottoms: %s, bottom: %s", bottoms, bottom)
        return bottom

    def merge_pixel_diff(self, top: np.ndarray, bottom: np.ndarray, win_size=100, ratio=0.6) -> List:
        """
        像素diff匹配，速度快，准确度100%
        由于部分设备相关的未知原因，会有部分肉眼不可见的像素diff导致无法匹配上，不建议使用此方法
        :param top:
        :param bottom:
        :param win_size:
        :param ratio:
        :return:
        """
        top = cv2.cvtColor(top, cv2.COLOR_BGR2GRAY)
        bottom = cv2.cvtColor(bottom, cv2.COLOR_BGR2GRAY)
        base = top[-win_size:, 0:int(top.shape[1] * ratio)]
        position = [0, 0]

        for i in range(bottom.shape[0] - win_size):
            comp = bottom[i:i + win_size, 0:int(bottom.shape[1] * ratio)]
            diff = base - comp

            if diff.max() > 50:
                continue

            position[0] = i + win_size
            position[1] = 1

            break

        return position

    def get_similarity(self, source, template) -> List:
        """
        获取模板匹配结果
        :param source: 原图
        :param template: 模板图
        :return: 最佳匹配度，起始高度位置
        """
        res = cv2.matchTemplate(source, template, cv2.TM_CCOEFF_NORMED)
        min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(res)
        max_val = min(max(0.00001, max_val), 1)
        return round(max_val, 5), max_loc[1]

    def merge_template_match(self, top: np.ndarray, bottom: np.ndarray, win_size=100) -> List[int]:
        """
        使用模板匹配拼接
        :param top:
        :param bottom:
        :param win_size:
        :return:
        """
        # 转灰度，提高处理速度
        top = cv2.cvtColor(top, cv2.COLOR_BGR2GRAY)
        bottom = cv2.cvtColor(bottom, cv2.COLOR_BGR2GRAY)

        # 获取匹配定位滑块
        base = top[-win_size:]

        # 获取最佳模板匹配点
        ratio, tp = self.get_similarity(bottom, base)
        position = [tp + win_size, ratio]

        return position

    def merge_shift_match(self, top: np.ndarray, bottom: np.ndarray, win_size=100, max_step=30) -> List[int]:
        """
        动态滑动匹配
        生成一个特征匹配滑块，依次滑动匹配，滑动步长动态计算生成
        :param top:
        :param bottom:
        :param win_size:
        :param max_step:
        :return:
        """
        position = [0, 0, 0]

        # 获取匹配定位滑块
        base = top[-win_size:, ]

        i = 0
        while i < bottom.shape[0] - win_size + 1:
            comp = bottom[i:i + win_size]
            max_val = self.get_similarity(comp, base)[0]
            if max_val >= position[1]:
                position[1] = max(max_val, position[1])
                position[0] = i + win_size

            # 动态调整步长，提高处理速度
            # 距离目标区域越近，窗口滑动速度越慢
            i += min(int(1 / max_val), max_step)

            # 准确度达到99.95%以上的时候，精确移动，并且动态的分析当前位置以及前后的结果，以寻找最优解
            if max_val < 0.9995:
                continue
            if self.get_similarity(bottom[i + 1:i + 1 + win_size], base)[0] >= max_val:
                continue

            break
        return position

    def merge_match(self, top: np.ndarray, bottom: np.ndarray, win_size=250, ratio=0.6, max_step=15) -> List[int]:
        """
        模板匹配拼接
        :param top:
        :param bottom:
        :param win_size:
        :param ratio:
        :param max_step:
        :return:
        """

        # 每次匹配移动距离
        move_distance = 70

        # 转灰度，提高处理速度
        top = cv2.cvtColor(top, cv2.COLOR_BGR2GRAY)
        bottom = cv2.cvtColor(bottom, cv2.COLOR_BGR2GRAY)

        # 待拼接区域小于滑块高度
        win_size = min(win_size, bottom.shape[0])

        # 获取匹配定位滑块
        base = top[-win_size:, 0:int(top.shape[1] * ratio)]

        # 如果完全匹配，直接返回拼接点
        top_tail = 0
        similarity = 0
        # similarity, tp = get_similarity(bottom, base)
        # if similarity >= 0.995:
        #     return [tp + win_size, similarity, top_tail]

        # 避免弹窗影响匹配，减小区域二次进行
        # similarity, tp = get_similarity(bottom, top[-win_size:, 0:int(top.shape[1] * ratio * 0.5)])
        # if similarity >= 0.995:
        #     return [tp + win_size, similarity, top_tail]

        # !!可优化：添加一个空白检测，如果是空白滑块，则调整滑块位置

        # 避免空白界面及页面加载不一致情况，在top底部开始向上滑动滑块，步长为50
        while top_tail < top.shape[0] / 2 and similarity < 0.995:
            base = top[top.shape[0] - win_size - top_tail:top.shape[0] - top_tail, 0:int(top.shape[1] * ratio)]
            # 空白检测（统计灰度特性）
            hist = cv2.calcHist([base], [0], None, [256], [0, 256])
            max = 0
            for i in hist:
                if i > max:
                    max = i
            # 如果是白块(灰度完全一致),滑块向上移动
            if max > base.shape[0] * base.shape[1] * 0.99 or base.shape[0] * base.shape[1] == 0 \
                    or np.max(base) - np.min(base) < 2:
                top_tail += move_distance
                continue
            similarity, tp = self.get_similarity(bottom, base)
            if similarity >= 0.995:
                return [tp + win_size, similarity, top_tail]
            top_tail += move_distance

        similarity, tp = self.get_similarity(bottom, top[-win_size:, 0:int(top.shape[1] * ratio * 0.5)])
        if similarity >= 0.995:
            return [tp + win_size, similarity, top_tail]

        return self.merge_shift_match(
            top[:, 0:int(top.shape[1] * ratio)],
            bottom[:, 0:int(bottom.shape[1] * ratio)],
            win_size, max_step)

    def main(self, threshold=0.8):
        """
        执行入口
        :param threshold:
        :return:
        """

        # 避免几乎没有滑动过的情况
        new_imgcv_list = [self.imgcv_list[0]]
        for i in range(1, len(self.imgcv_list)):
            img_diff = self.imgcv_list[i] - new_imgcv_list[-1]
            hist = cv2.calcHist([img_diff], [0], None, [256], [0, 256])
            similar_ratio = hist[0] / self.imgcv_list[0].shape[0] / self.imgcv_list[0].shape[1]
            logger.info("similar_ratio: %s", similar_ratio)
            if similar_ratio < 0.97:
                new_imgcv_list.append(self.imgcv_list[i])

        imgcv_list = new_imgcv_list
        top = self.get_top(imgcv_list)
        bottom = self.get_bottom(imgcv_list, top)

        # 再次验证页面未滑动（公共底超过页面一半）
        if (bottom < imgcv_list[0].shape[0] / 2):
            return imgcv_list[0]

        top_img = imgcv_list[0][:top + 1]
        bottom_img = imgcv_list[-1][bottom + 1:]

        cur_img = imgcv_list[0][top: bottom + 1]
        for i in range(1, len(imgcv_list)):
            next_img = imgcv_list[i][top: bottom + 1]

            position = self.merge_match(cur_img, next_img)
            logger.info("match_res: %s", position)
            # 如果最大匹配度小于 ${threshold}, 则认为无法匹配上, 直接进行原图拼接
            if position[1] <= threshold:
                position[0] = 0

            cur_img = np.vstack((cur_img[0:cur_img.shape[0] - position[2], ], next_img[position[0]:, ]))

            if self.dir_path:
                cv2.imwrite("{}/cur_img_{}_{}.png".format(self.dir_path, str(i - 1), str(i)), cur_img)

        cur_img = np.vstack((top_img, cur_img, bottom_img))

        return cur_img
