#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@Desc    :
@File    :   replay_widget_model.py
@Time    :   2025-05-31
<AUTHOR>   xuz<PERSON><PERSON>@baidu.com
"""
import traceback

from basics.util import logger
from .basic import PROJECT_VERSION, DEPEND_VERSION
from features.record_replay.base.execute_base import ExecuteBase
from features.record_replay.base.local_strategies_errcode import ErrCode
from .record_widget_model import RecordWidgetModelExecute
from features.router import main_router
import copy


class ReplayWidgetModelExecute(ExecuteBase):
    """
    通用回放接口，module_name路由具体回放模块
    """

    def __init__(self):
        """
        初始化
        """
        super().__init__()

    def get_replay_widget(self, params):
        """
        调建模接口，获取回放页面结果

        :param params: 输入参数
        :return: 格式化后的参数
        """
        rwm = RecordWidgetModelExecute()
        record_params = copy.deepcopy(params)
        record_params['module_name'] = 'record_widget_model'
        replay_rwm = rwm.main(record_params)
        if replay_rwm["code"] != ErrCode.Success.Code:
            return None, replay_rwm

        replay_dom = replay_rwm["data"]["dom"]
        return replay_dom, replay_rwm

    def pre_process(self, params):
        """
        预处理
        TODO
          1. 增加距离回放相关的参数，标记该图标不是基于index回放，而是基于距离回放
          2. 给回放时的建模设置回放标记
        """
        params["version"] = DEPEND_VERSION
        params['module_info']['step_info']['findInfo']['byDistance'] = True
        params['replaying'] = True




    def main(self, params):
        """
        主流程函数
        """
        try:
            self.pre_process(params)
            replay_dom, replay_err_result = self.get_replay_widget(params)
            logger.debug("[Batdom Debug]replay_dom: {}".format(replay_dom))
            params['module_info']['replay_dom'] = replay_dom
            if replay_dom is None:
                # 报错，返回建模失败结果
                return replay_err_result
            return main_router("v4.record_replay.replay", params)
        except Exception as e:
            logger.error("replay_widget_model uncaught Error: {}".format(traceback.format_exc()))
            result = {
                "code": ErrCode.UnknownError.Code,
                "version": PROJECT_VERSION,
                "msg": ErrCode.UnknownError.Msg,
                "show_msg": ErrCode.UnknownError.ShowMsg
            }
            return result