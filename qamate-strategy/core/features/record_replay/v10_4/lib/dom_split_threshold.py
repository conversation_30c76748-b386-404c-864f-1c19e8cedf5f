#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@Desc    :   
@File    :   dom_split_threshold.py
@Time    :   2023-08-24
<AUTHOR>   x<PERSON><PERSON><PERSON>@baidu.com
"""

class DomSplitThreshold(object):
    """
    dom_split_threshold 的阈值常量
    """
    # 判断两个区域是否基本重合的阈值，百分比结果(0-100%)
    ALMOST_OVERLAP_RATIO = 35

    # 判读opencv矩形与其他组件重合的阈值，百分比结果(0-100%)
    CVE_OVERLAP_RATIO = 3

    # 辅助判断两个区域是否重合，偏移量的绝对像素值
    ALMOST_OVERLAP_PIXEL_OFFSET = 10

    # 一个元素最多可以和多少个其它元素相交
    MAX_CROSS_NUM = 50

    # 可识别元素的最小高度
    MIN_HEIGHT = 5

    # 可识别元素的最小宽度
    MIN_WIDTH = 5

    # 相对位置比较，每个维度的比例差的上限，百分比结果(0-100%)
    EACH_DIMENSION_DIFF_LIMIT = 20.0

    # 相对位置比较，四维 diff 之和的上限，百分比结果(0-100%)
    ALL_DIMENSION_DIFF_LIMIT = 30.0

    # 行模式下，去除顶行的元素，百分比结果(0-100%)
    LINE_WIDTH_DELTA = 1.0

    # 行模式下，去除顶行的元素，百分比结果(0-100%)
    MAX_COVERAGE_HEIGHT = 95.0

    # 判断node区域是否包含文本，文本区域和node区域的重合部分，占文本区域的比例，百分比结果(0-100%)
    CONTAINS_TEXT_RATIO = 50.0

    # 判断视觉元素是否包含在某一dom line 内，占视觉元素的比例，百分比结果(0-100%）
    LINE_CONTAINS_VISION_RATIO = 50.0

    # 特殊type，不进行重新赋值
    SPECIAL_CELL_TYPES = ["CellWordsOcr", "CellIcon", "CellComponent"]

    # 判断两个元素是否相交 —— 用于行列顺序判断 ，百分比结果(0-100%）
    ELEMENT_OVERLAP_RATIO = 30

    # 判断回放时候的两行之间的间隔差的百分比(因为除以了屏幕高度)和录制之后的间隔差百分比的差的绝对值，如果
    # 小于这个阈值，说明他们可以被匹配上
    LINE_GAP_RATIO = 0.5

    # 相对位置比较，每个维度的比例差的上限，百分比结果(0-100%)
    # SUBGRAPH_EACH_DIMENSION_DIFF_LIMIT = 4.5
    SUBGRAPH_EACH_DIMENSION_DIFF_LIMIT = 7.0

    # 相对位置比较，四维 diff 之和的上限，百分比结果(0-100%)
    SUBGRAPH_ALL_DIMENSION_DIFF_LIMIT = 10.0

    # 判断父节点是否需要保留，既子节点面积填充父节点面积的百分比，百分比结果(0-100%)
    PARENT_NODE_FILL_RATIO = 40.0

    # 初步判断一个元素的长宽比是否在可以接收的范围内的系列阈值
    # 判断是否进行长宽的判断，就是判断是否长大于宽或者宽大于长
    CHECK_WIDTH_HEIGHT_LONGER_FLAG = 0.6
    # 长宽比的阈值
    WIDTH_HEIGHT_LIMIT_RATIO = 0.5
    # 尺寸比的阈值
    SIZE_LIMIT_RATIO = 2.0

    # 边向量判断，第4维，判断元素间的高度的相对比例，是否超过阈值
    VECTOR_RELATIVE_HEIGHT_LIMIT = 0.5

    # opencv切割矩形判断，最小宽高比例
    CV_RECT_WIDTH_LIMIT = 0.04
    CV_RECT_HEIGHT_LIMIT = 0.019

    # opencv矩形偏移角度限制
    CV_RECT_ANGLE_LIMIT = 5

    # beta、icon、ele模型的confidence阈值
    MIN_OBJ_DETECT_CONF = 0.5

    # beta/icon 与 ocr字符检测框超过这个阈值时，去掉ocr结果
    MAX_OCR_ICON_OVERLAP = 0.95

    # 兜底匹配时的相似度阈值
    FALLBACK_SIMILARITY_THRESH = 0.92

