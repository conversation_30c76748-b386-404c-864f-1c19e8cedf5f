#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@Desc    :  EasyDL 物体检测 调用模型公有云API Python3实现
@File    :   easydl_model.py
@Time    :   2024-08-23
<AUTHOR>   x<PERSON><PERSON><PERSON>@baidu.com
"""
import json
import base64
import traceback

import requests
import time

from basics.util import logger
"""
使用 requests 库发送请求
使用 pip（或者 pip3）检查我的 python3 环境是否安装了该库，执行命令
  pip freeze | grep requests
若返回值为空，则安装该库
  pip install requests
"""
API_KEY = "7d84FXJTFhoPzwChFyFLoIWN"
SECRET_KEY = "PAx55NLOtdxYppLEJpxwSYLMHryxPsoT"


class EasyDLModel:
    """
    EasyDL 模型调用类
    """
    def __init__(self):
        """
        初始化
        """
        self.access_token = None

    def get_access_token(self):
        """
        获取 access_token
        """
        auth_url = "https://aip.baidubce.com/oauth/2.0/token?grant_type=client_credentials"\
               "&client_id={}&client_secret={}".format(API_KEY, SECRET_KEY)
        auth_resp = requests.get(auth_url)
        auth_resp_json = auth_resp.json()
        self.access_token = auth_resp_json["access_token"]

    def format_result_newapp(self, result_list):
        """
        格式化 newapp 的建模结果
        """
        name_mapping = {
            "input_box": "input_box_model"
        }
        
        format_result_list = []
        
        for item in result_list:
            if item["name"] not in name_mapping:
                continue

            format_iterm = {
                "rect": {
                    "x": item["location"]["left"],
                    "y": item["location"]["top"],
                    "width": item["location"]["width"],
                    "height": item["location"]["height"]
                },
                "name": name_mapping[item["name"]],
                "score": item["score"]
            }
            format_result_list.append(format_iterm)
        return format_result_list

    def model_newapp(self, image_path):
        """
        newapp业务模型接口
        """
        params = {"threshold": 0.8}
        with open(image_path, 'rb') as f:
            base64_data = base64.b64encode(f.read())
            base64_str = base64_data.decode('UTF8')
        params["image"] = base64_str
        # 服务详情 中的 接口地址
        model_api_url = "https://aip.baidubce.com/rpc/2.0/ai_custom/v1/detection/model_newapp"
        if not self.access_token:
            self.get_access_token()

        request_url = "{}?access_token={}".format(model_api_url, self.access_token)
        for i in range(3):
            try:
                response = requests.post(url=request_url, json=params)
                response_json = response.json()

                # 超过每日额度
                if response_json.get("error_code") == 17:
                    logger.error(response_json)
                    break

                result_list = self.format_result_newapp(response_json["results"])
                return result_list
            except Exception as e:
                logger.error(traceback.format_exc())
                logger.error(e)
                time.sleep(1)
                continue
        raise Exception("easyDL sever error")
    

if __name__ == '__main__':
    easydl_model = EasyDLModel()
    image_path = "1.PNG"
    demo_list = easydl_model.model_newapp(image_path)
    print(json.dumps(demo_list, indent=4))

