#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@Desc    :   
@File    :   calculate_rect_area.py
@Time    :   2024-02-22
<AUTHOR>   x<PERSON><PERSON><PERSON>@baidu.com
"""


class Interval:
    """
    计算类
    """
    def __init__(self, start, end):
        """
        初始化
        """
        self.start = start
        self.end = end

def calculate_area(rectangles):
    """
    计算面积
    """
    if not rectangles:
        return 0

    # 提取边界线段
    vertical_lines = []
    horizontal_lines = []
    for rect in rectangles:
        vertical_lines.append(Interval(rect[0], rect[2]))
        vertical_lines.append(Interval(rect[0], rect[2]))
        horizontal_lines.append(Interval(rect[1], rect[3]))
        horizontal_lines.append(Interval(rect[1], rect[3]))

    # 对线段进行排序
    vertical_lines.sort(key=lambda x: x.start)
    horizontal_lines.sort(key=lambda x: x.start)

    # 扫描线算法
    def merge_intervals(intervals):
        """
        扫描线算法
        """
        merged = []
        for interval in intervals:
            if not merged or interval.start > merged[-1].end:
                merged.append(interval)
            else:
                merged[-1].end = max(merged[-1].end, interval.end)
        return merged

    def calculate_length(intervals):
        """
        计算长度
        """
        length = 0
        for interval in intervals:
            length += interval.end - interval.start
        return length

    vertical_merged = merge_intervals(vertical_lines)
    horizontal_merged = merge_intervals(horizontal_lines)

    # 计算总面积
    area = calculate_length(vertical_merged) * calculate_length(horizontal_merged)

    return area

if __name__ == "__main__":

    # 示例用法
    rectangles = [(1, 1, 3, 3), (2, 2, 4, 4), (3, 3, 5, 5)]
    total_area = calculate_area(rectangles)
    print("Total area:", total_area)