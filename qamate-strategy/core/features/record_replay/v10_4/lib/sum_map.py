#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
累加和数组，用于快速计算重叠度
"""

import numpy as np

class SMap:
    """
    累加和矩阵，用于快速计算一个矩形和多个矩形的重叠面积
    """

    def __init__(self, shape, rects):
        self.smap = self.build_smap(shape, rects)

    def cross_area(self, rect):
        """
        利用累加和矩阵快速求出重叠面积
        """
        x1, y1, x2, y2 = rect
        return self.smap[x2 + 1][y2 + 1] - self.smap[x1, y2 + 1] - self.smap[x2 + 1, y1] + self.smap[x1, y1]

    def build_smap(self, shape, rects):
        """
        smap是一个累加和数组
        """
        sx, sy = shape
        b = np.zeros(shape=shape)
        for r in rects:
            x1, y1, x2, y2 = r
            x2 = min(sx, x2 + 1)
            y2 = min(sy, y2 + 1)
            b[x1:x2, y1:y2] = 1

        # 首先，沿着axis=0（行方向）计算累积和
        p = b.cumsum(axis=0)
        # 接着，沿着axis=1（列方向）计算累积和，得到最终的二维累积和
        p = p.cumsum(axis=1)
        p = np.insert(p, 0, np.zeros(p.shape[1]), axis=0)
        p = np.insert(p, 0, np.zeros(p.shape[0]), axis=1)
        return p


if __name__ == '__main__':
    p = np.array([[1, 0, 0], [2, 4, 5], [3, 5, 6]])
    p = np.insert(p, 0, np.zeros(p.shape[1]), axis=0)
    p = np.insert(p, 0, np.zeros(p.shape[0]), axis=1)
    print(p)