#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@Desc    :
@File    :   record_widget_model.py
@Time    :   2024-05-28
<AUTHOR>   <EMAIL>
"""
import json
import re
import traceback

import cv2

from basics.util import logger
from features.record_replay.base.batdom_node_type import BatdomNodeType
from features.record_replay.base.execute_base import ExecuteBase
from features.record_replay.base.local_strategies_errcode import ErrCode
from .basic import PROJECT_VERSION, DEPEND_VERSION
from .lib.check_node_detail import cut_img_by_rect
from .lib.dom_split_threshold import DomSplitThreshold as DST
from features.router import main_router
from .lib.name_reflector import get_beta_icon_name_dict, get_icon_name_dict, get_ele_name_dict
from ...cv_services.state_check import detect_switch_state


class RecordWidgetModelExecute(ExecuteBase):
    """
    通用建模接口，返回全部输入的控件，供前端自由选择
    """

    def __init__(self):
        """
        初始化
        """
        super().__init__()
        self.height = None
        self.width = None
        self.os_type = None
        self.goal_feature_dict = None
        self.used_widget_list = None
        self.name_dict = None

        # FIXME newapp业务专属模型是一个临时方案，现在没有投入使用
        # self.easydl_model = EasyDLModel()
        self.init_name_dict()

    def init_name_dict(self):
        """
        初始化控件名称字典
        """
        dict1 = get_beta_icon_name_dict()
        dict2 = get_icon_name_dict()
        dict3 = get_ele_name_dict()
        self.name_dict = {**dict1, **dict2, **dict3}

    def pre_process(self, params):
        """
        预处理
        """
        params["version"] = DEPEND_VERSION
        self.os_type = params["module_info"]["os_type"]
        self.used_widget_list = []

        widget_info = params["module_info"]["widget_info"]
        used_ocr_info = widget_info["ocr_info"].get("used", False)
        if used_ocr_info:
            # 传入了ocr_info
            self.used_widget_list.append("ocr_info")

        used_beta_info = widget_info["beta_info"].get("used", False)
        if used_beta_info:
            # 传入了beta_info
            self.used_widget_list.append("beta_info")

        used_icon_info = widget_info["icon_info"].get("used", False)
        if used_icon_info:
            # 传入了icon_info
            self.used_widget_list.append("icon_info")

        used_ele_info = widget_info["ele_info"].get("used", False)
        if used_ele_info:
            # 传入了组件ele_info
            self.used_widget_list.append("ele_info")

        used_dom_info = widget_info["dom_info"].get("used", False)
        if used_dom_info:
            # 传入了dom_info，提取dfe, doe控件
            self.used_widget_list.append("dom_info")

        img = cv2.imread(params["module_info"]["image_path"])
        self.height = img.shape[0]
        self.width = img.shape[1]
        self.imgcv = img

        if self.os_type == 1:
            # Android
            self.goal_feature_dict = {
                "text": [],
                "content-desc": [],
                "resource-id": [],
            }
        elif self.os_type == 2:
            # iOS
            self.goal_feature_dict = {
                "label": [],
                "name": [],
                "type": [
                    'Image', 'ScrollView', 'StaticText', 'Button', 'CollectionView', 'Cell', 'Table', 'TabBar'
                ]
            }

    def parse_result(self, result):
        """
        解析结果
        """

        ocr_info = []
        beta_info = []
        icon_info = []
        ele_info = []
        dfe_info = []
        doe_info = []
        cve_info = []
        logger.debug("record_widget_model_execute_result: {}".format(result))

        for item in result['data']['dom']['children']:
            if item['type'] in ['Text', 'TextArea']:
                ocr_info.append(item)
            elif item['type'] == 'Icon':
                if item['ext'].get('name', '').startswith('beta_'):
                    beta_info.append(item)
                else:
                    icon_info.append(item)
            elif item['type'] == 'Component':
                ele_info.append(item)
            elif item['type'] == 'DFE':
                dfe_info.append(item)
            elif item['type'] == 'DOE':
                doe_info.append(item)
            elif item['type'] == 'CVE':
                cve_info.append(item)

        return ocr_info, beta_info, icon_info, ele_info, dfe_info, doe_info, cve_info

    def process_beta_info(self, beta_info, merge_list):
        """
        beta_info 的后处理
        """
        beta_info.sort(key=lambda x: (x["rect"]["y"], x["rect"]["x"]))
        for item in beta_info:
            if 'cname' not in item['ext']:
                item['ext']['cname'] = (self.name_dict.
                                        get(item['ext']['name'].replace('_beta', ''), item['ext']['name']))
        merge_list.extend(beta_info)

    def get_node_cross_area(self, p, c):
        """
        获取 p 和 c 的相交面积
        """
        p_x1 = p["rect"]["x"]
        p_x2 = p["rect"]["x"] + p["rect"]["w"]
        p_y1 = p["rect"]["y"]
        p_y2 = p["rect"]["y"] + p["rect"]["h"]

        c_x1 = c["rect"]["x"]
        c_x2 = c["rect"]["x"] + c["rect"]["w"]
        c_y1 = c["rect"]["y"]
        c_y2 = c["rect"]["y"] + c["rect"]["h"]
        # 计算重叠部分面积
        maxx = max(p_x1, c_x1)
        minx = min(p_x2, c_x2)
        maxy = max(p_y1, c_y1)
        miny = min(p_y2, c_y2)
        cross_area = 0
        if minx > maxx and miny > maxy:
            cross_area = (maxx - minx) * (maxy - miny)
        return cross_area

    def check_element_overlap(self, node, word, threshold=DST.ALMOST_OVERLAP_RATIO):
        """
        判断两个元素是否几乎重叠
        """
        node_area = node["rect"]["w"] * node["rect"]["h"]
        word_area = word["rect"]["w"] * word["rect"]["h"]

        # 重合部分面积
        cross_area = self.get_node_cross_area(node, word)

        # 取node和word面积的并集
        union_area = node_area + word_area - cross_area

        word_cross_ratio = 0
        if word_area > 0:
            word_cross_ratio = 100.0 * cross_area / union_area

        if word_cross_ratio >= threshold:
            return True

        # # 如果存在包含的关系，也返回True
        # if cross_area >= word_area or cross_area >= node_area:
        #     return True

        return False

    def check_overlap_with_existing(self, existing_list, element, appointed_types=None):
        """
        检查元素是否与res中的任何元素重叠
        """
        for existing_element in existing_list:
            if appointed_types is not None and existing_element["type"] not in appointed_types:
                continue
            if existing_element.get('ext', {}).get('name', '') in ['picture']:
                continue
            if self.check_element_overlap(element, existing_element):
                return True
        return False

    def check_component_icon_overlap_normal_icon(self, existing_list, element):
        """
        检查一个组件的icon是否与普通icon有重叠
        """
        assert element["type"] == 'Component' and element["ext"]["name"] == 'icon'
        normal_icon_list = [e for e in existing_list if e['type'] == BatdomNodeType.ICON.name]
        for normal_icon in normal_icon_list:
            # if self.get_self_overlap_ratio(element["rect"], normal_icon["rect"]) > 0.8 or \
            #         self.get_self_overlap_ratio(normal_icon["rect"], element["rect"]) > 0.8:
            #     return True
            if self.check_element_overlap(normal_icon, element, threshold=15):
                return True
        return False

    def get_self_overlap_ratio(self, r1, r2):
        """
        计算两个矩形r1和r2的交集面积占r1面积的百分比
        r1,r2形如{'x': 82, 'y': 44, 'w': 87, 'h': 33}
        """
        x1 = max(r1["x"], r2["x"])
        y1 = max(r1["y"], r2["y"])
        x2 = min(r1["x"] + r1["w"], r2["x"] + r2["w"])
        y2 = min(r1["y"] + r1["h"], r2["y"] + r2["h"])
        if x1 >= x2 or y1 >= y2:
            return 0.0
        else:
            overlap_area = (x2 - x1) * (y2 - y1)
            return overlap_area / (r1["w"] * r1["h"])

    def contains_chinese(self, text):
        """
        校验字符串中是否包含中文字符。

        :param text: 要校验的字符串
        :return: 如果包含中文字符返回True，否则返回False
        """
        # 使用正则表达式匹配中文字符
        pattern = re.compile(r'[\u4e00-\u9fff]')
        return bool(pattern.search(text))

    def check_ocr_in_picture_or_icon(self, existing_list, element):
        """
        检查一个文本是否有80%的面积在图片内
        补充1: 如果键盘、图片内的文本包含中文，保留
        """
        assert element["type"] in ["Text", "TextArea"]
        pic_list = [e for e in existing_list if e.get('type') == 'Icon' or
                    e.get('ext', {}).get('name', None) in ['picture']]
        icon_list = [e for e in existing_list if e.get('type') == 'Icon' or
                     e.get('ext', {}).get('name', None) in ['icon']]
        text = element.get('ext', {}).get('text', '')
        is_contains_chinese = self.contains_chinese(text)
        for pic in pic_list:
            if is_contains_chinese:
                continue
            if self.get_self_overlap_ratio(element["rect"], pic["rect"]) > 0.8:
                logger.info(f"ocr {element} in picture: {pic}")
                return True
        for icon in icon_list:
            ext_name = icon.get('ext', {}).get('name', '')
            if ext_name in ('keyboard', 'picture') and is_contains_chinese:
                # 如果是键盘，且包含中文，保留
                continue
            if self.get_self_overlap_ratio(element["rect"], icon["rect"]) > 0.8:
                logger.info(f"ocr {element} in icon: {icon}")
                return True
        return False

    def check_ocr_in_keyboard(self, existing_list, element):
        """
        如果纯数字、字母的文本在键盘组件内部，去掉
        """
        assert element["type"] in ["Text", "TextArea"]
        keyboard_list = [e for e in existing_list if e.get('ext', {}).get('name', None) == 'keyboard']

        number_pattern = r'^\d+$'
        alpha_pattern = r'^[a-zA-Z]+$'
        for keyboard in keyboard_list:
            text = element['ext']['text']
            if (re.fullmatch(number_pattern, text) or re.fullmatch(alpha_pattern, text)) and \
                    self.get_self_overlap_ratio(element["rect"], keyboard["rect"]) > 0.8:
                logger.info(f"ocr {element} in picture: {keyboard}")
                return True
        return False

    def process_icon_info(self, icon_info, merge_list):
        """
        处理icon信息
        """

        def add_button_switch_state(element):
            if element['ext']['name'].replace('_beta', '') == 'botton_switch':
                cut_img = cut_img_by_rect(self.imgcv, element["rect"])
                if detect_switch_state(cut_img) is True:
                    element['ext']['name'] = 'button_switch_on'
                    element['ext']['cname'] = '开关按钮-开'
                else:
                    element['ext']['name'] = 'button_switch_off'
                    element['ext']['cname'] = '开关按钮-关'

        icon_info.sort(key=lambda x: (x["rect"]["y"], x["rect"]["x"]))
        for element in icon_info:
            if 'cname' not in element['ext']:
                element['ext']['cname'] = (self.name_dict.
                                           get(element['ext']['name'].replace('_beta', ''), element['ext']['name']))
            add_button_switch_state(element)
            if not self.check_overlap_with_existing(merge_list, element):
                merge_list.append(element)
            else:
                logger.info("overlapping icon element: {}".format(element))

    def process_component_info(self, ele_info, merge_list):
        """
        处理组件信息
        给组件标上英文名
        """
        for element in ele_info:
            if 'cname' not in element['ext']:
                element['ext']['cname'] = (self.name_dict.
                                           get(element['ext']['name'].replace('_beta', ''), element['ext']['name']))
            if self.check_overlap_with_existing(merge_list, element):
                logger.info("overlapping component element: {}".format(element))
            elif element['ext']['name'] == 'icon' and \
                    self.check_component_icon_overlap_normal_icon(merge_list, element):
                logger.info("overlapping component-icon with icon element: {}".format(element))
            else:
                merge_list.append(element)

    def process_ocr_info(self, ocr_info, merge_list):
        """
        处理ocr信息
        """
        for element in ocr_info:
            if self.check_overlap_with_existing(merge_list, element, [BatdomNodeType.ICON.name]):
                logger.info("Single_feature overlapping icon element: {}".format(element))
            elif self.check_ocr_in_picture_or_icon(merge_list, element):
                logger.info("Single_feature ocr element in image: {}".format(element))
            elif self.check_ocr_in_keyboard(merge_list, element):
                logger.info("Single_feature ocr alpha or number element in keyboard: {}".format(element))
            else:
                merge_list.append(element)

    def process_dfe_info(self, dfe_info, merge_list):
        """
        处理dom信息
        """
        dfe_info.sort(key=lambda x: (x["rect"]["y"], x["rect"]["x"]))
        merge_list.extend(dfe_info)

    def rebuild_structure(self, ocr_info, beta_info, icon_info, ele_info, dfe_info, doe_info, cve_info):
        """
        重新构建结构
        """

        def iter_item(item_list):
            for item in item_list:
                yield item
                if "children" in item:
                    yield from iter_item(item["children"])

        merge_list = []
        self.process_beta_info(beta_info, merge_list)
        # icon 的再录入
        self.process_icon_info(icon_info, merge_list)
        # 组件 的再录入
        self.process_component_info(ele_info, merge_list)
        # 文本 的再录入
        self.process_ocr_info(ocr_info, merge_list)
        # dom 的再录入
        self.process_dfe_info(dfe_info, merge_list)

        # 控件排序
        merge_list.sort(key=lambda x: (x["rect"]["y"], x["rect"]["x"]))
        # 添加唯一标志, 同时打上single标签，此外，对icon、dfe的index进行重排序

        icon_index_map = {}
        dfe_index_map = {}
        id = 0
        for iterm in iter_item(merge_list):
            id += 1
            iterm["debug"]["id"] = id
            if 'single' not in iterm['ext']['tag']:
                iterm["ext"]["tag"].append("single")
            if iterm["type"] == 'Icon':
                icon_name = iterm["ext"]["name"].replace("_beta", "")
                icon_index_map[icon_name] = icon_index_map.get(icon_name, -1) + 1
                iterm["ext"]["index"] = icon_index_map[icon_name]
            elif iterm["type"] == 'DFE':
                sorted_key = ""
                features = iterm["ext"]["feature"]
                for key in self.goal_feature_dict:
                    feature_val = features.get(key, {}).get('data', None)
                    if feature_val is not None and feature_val != "":
                        sorted_key += "{}_{}|".format(key, feature_val)
                dfe_index_map[sorted_key] = dfe_index_map.get(sorted_key, -1) + 1
                dom_index = dfe_index_map[sorted_key]
                iterm["ext"]["index"] = dom_index
        return merge_list

    def conv_component_to_icon_info(self, ele_info):
        """
        将部分组件类别大类直接改成Icon
        """
        ele_icon_info = []
        new_ele_info = []
        for ele in ele_info:
            if ele['type'] == 'Component' and \
                    ele['ext']['name'] in ("search_box", "input_box", "comment_box", "keyboard", "picture"):
                ele['type'] = 'Icon'
                if ele['ext']['name'] == 'comment_box':
                    ele['ext']['name'] = 'input_box'
                ele_icon_info.append(ele)
            else:
                new_ele_info.append(ele)
        return new_ele_info, ele_icon_info

    def remove_special_icon_in_keyboard(self, icon_info):
        """
        移除键盘中的特殊icon（例如close - 这个是由x字母变成的）
        """
        remove_list = []
        for ele in icon_info:
            if ele['ext']['name'] == 'keyboard':
                for icon in icon_info:
                    if icon['ext']['name'] == 'close':
                        if self.get_self_overlap_ratio(icon["rect"], ele["rect"]) > 0.8:
                            # 如果icon-close的面积有80%都在键盘内部，旧去掉
                            # icon_info.remove(icon)
                            remove_list.append(icon)
        for rm_icon in remove_list:
            icon_info.remove(rm_icon)

    def remove_small_widget(self, *args):
        """
        移除极小控件
        """
        height_limit = int(self.height * 0.008)

        for arg in args:
            rm_list = []
            for ele in arg:
                if ele['type'] == 'TextArea':
                    for child in ele['children']:
                        # 如果TextArea中
                        if child['rect']['h'] < height_limit:
                            rm_list.append(ele)
                            break
                elif ele['rect']['h'] < height_limit:
                    rm_list.append(ele)

            for rm_ele in rm_list:
                arg.remove(rm_ele)

    def post_process(self, result):
        """
        后置处理
        1. 检查一个组件的icon是否与普通icon有重叠，如果有，则将该组件的icon去掉
        2. 检查一个文本是否有80%的面积在图片、图标、组件-图标内，如果有，把文字去掉
        3. 如果纯数字、字母的文本在键盘（keyboard）组件内部，去掉
        4. "search_box", "input_box", "comment_box", "keyboard”四个组件类别大类直接改成Icon
        5. 所有"comment_box”改成 "input_box"
        6. 原先只有在多控件中暴露的类别，在10_3全部暴露，而且只能是单控件
        7. 相比10_1新增了easydl(newapp专属业务模型)的检测结果（先不加）
        """

        ocr_info, beta_info, icon_info, ele_info, dfe_info, doe_info, cve_info = self.parse_result(result)
        self.remove_small_widget(ocr_info, beta_info, icon_info, ele_info, dfe_info, doe_info, cve_info)
        new_ele_info, ele_icon_info = self.conv_component_to_icon_info(ele_info)
        ele_info = new_ele_info
        icon_info.extend(ele_icon_info)

        self.remove_special_icon_in_keyboard(icon_info)

        new_union_dom = self.rebuild_structure(ocr_info, beta_info, icon_info, ele_info, dfe_info, doe_info, cve_info)
        # 组合结构, 添加页面信息
        res_data = {
            "type": "Page",
            "ext": {"id": 0},
            "debug": {"id": 0},
            "rect": {
                "x": 0,
                "y": 0,
                "w": self.width,
                "h": self.height
            },
            "children": new_union_dom
        }
        result = {
            "code": ErrCode.Success.Code,
            "msg": ErrCode.Success.Msg,
            "data": {
                "dom": res_data,
                "info": {
                    "version": PROJECT_VERSION,
                    "used_widget_list": self.used_widget_list,
                    "name": "recordWidgetModel",
                    "sub_version": "batdom_agent"
                }
            }
        }
        logger.debug("[Batdom Debug]result: {}".format(json.dumps(result)))
        return result

    def main(self, params):
        """
        主流程函数
        """
        try:
            self.pre_process(params)
            result = main_router("v4.record_replay.record", params)
            return self.post_process(result)
        except Exception as e:
            logger.error("record_widget_model uncaught Error: {}".format(traceback.format_exc()))
            result = {
                "code": ErrCode.UnknownError.Code,
                "msg": ErrCode.UnknownError.Msg
            }
            return result
