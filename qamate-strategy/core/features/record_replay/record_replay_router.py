#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@Desc    :   
@File    :   record_replay_router.py
@Time    :   2023-08-22
<AUTHOR>   x<PERSON><PERSON><PERSON>@baidu.com
"""
import traceback

from .base.local_strategies_errcode import ErrCode
from .base.sub_router_base import SubRouterBase
from basics.util import logger
import json

DEFAULT_VERSION = None

class RecordReplayRouter(SubRouterBase):
    """
    二级路由 - 从基类继承
    """
    def format_version(self, router_str, params):
        """
        检验版本参数是否合法 & 进行转换
        """
        global DEFAULT_VERSION
        # 默认使用最新版本
        DEFAULT_VERSION = "v9.5"
        # default_version = "v9.4"
        # default_version = "v9.3"
        # default_version = "v9.2"
        # default_version = "v9.1"
        # default_version = "v9.0"
        # default_version = "v8.0"
        # default_version = "v2.0"
        if router_str.split('.')[0] == "record_replay":
            # 从batdom11.0后，method变成record_replay.replay
            DEFAULT_VERSION = "v11.0"
        else:
            api_version = router_str.split(".")[0]
            if api_version == "v4":
                # v4接口，支持 v10.0 以及后续版本
                # default_version = "v10.0"
                DEFAULT_VERSION = "v10.1"
        
        version_str = None
        # 参数获取优先级：
        # 从参数获取版本信息
        if "version" in params:
            version_str = params["version"]
            logger.info("get version from params: {}".format(version_str))
        # 从step_info字段取版本信息
        elif "step_info" in params:
            step_version = self.get_dominfo_from_stepinfo(params["step_info"])
            if step_version is not None:
                version_str = step_version
                logger.info("get version from step_info: {}".format(version_str))
        elif "module_info" in params and "step_info" in params["module_info"]:
            step_version = self.get_dominfo_from_stepinfo(params["module_info"]["step_info"])
            if step_version is not None:
                version_str = step_version
                logger.info("get version from step_info: {}".format(version_str))

        
        # 使用默认版本
        if version_str is None:
            version_str = DEFAULT_VERSION
            logger.info("use default version: {}".format(version_str))
        
        logger.info("version: {}".format(version_str))
        try:
            if version_str[0] != "v":
                raise Exception()
            version_list = version_str[1:].split(".")
            if len(version_list) < 2:
                raise Exception()
            version = "v{}".format("_".join(version_list[0: 2]))
            # version = "v8_0_experiment"
            return version
        except:
            return False
        
    def get_dominfo_from_stepinfo(self, stepinfo):
        """
        从stepinfo中获取dom信息，dom信息放在dom字段中的根目录下
        """
        # logger.info("stepinfo: {}".format(json.dumps(stepinfo)))
        # 第一版本没有version值
        version = "v2.0"
        # version = "v9.1"
        if 'recordInfo' in stepinfo:
            version = stepinfo["recordInfo"]["version"]
            return version

        dom = stepinfo["dom"]
        if "domInfo" in stepinfo:
            version = stepinfo["domInfo"]["version"]
        elif "dom_info" in dom and "version" in dom["dom_info"]:
            version = dom["dom_info"]["version"]
        return version

    def main(self, router_str, params):
        """
        主要执行 - 版本管理，重构subRouter方法
        针对 v3、v4版本的接口路由
        """
        # 校验参数合法性 & 进行格式转换
        version = self.format_version(router_str, params)
        if version is False:
            result = {
                "code": ErrCode.MethodVersionError.Code,
                "msg": ErrCode.MethodVersionError.Msg
            }
            return result

        router_list = router_str.split(".")
        if len(router_list) < 2:
            result = {
                "code": ErrCode.MethodNotFound.Code,
                "msg": ErrCode.MethodNotFound.Msg
            }
            logger.error("[record_replay_router] router length < 2, router_list = {}".format(router_list))
            return result

        if router_list[0] == "record_replay":
            method_class_str = router_list[0]
            sub_method = params["module_name"].replace("_v2", "")
        else:
            method_class_str = router_list[1]
            # v3接口, 获取sub_method
            sub_method = router_list[2]

            # v4接口, 根据module_name参数路由sub_method
            if router_list[0] == "v4":
                sub_method = params["module_name"]

        sub_method_file = "features.{}.{}.{}".format(
            method_class_str, version, sub_method
        )
        sub_method_class_name = "{}Execute".format(self.str_hump(sub_method))

        logger.info("method_class_str:{} sub_method:{} sub_method_file:{} sub_method_class_name:{}".format(
            method_class_str, sub_method, sub_method_file, sub_method_class_name
        ))

        # 通过二级方法，进行版本寻址
        try:
            sub_method_import = __import__(sub_method_file, fromlist=True)
            sub_method_class = getattr(sub_method_import, sub_method_class_name)
            sub_method = sub_method_class()
            result = sub_method.main(params)
        except ModuleNotFoundError: # pylint:disable=undefined-variable
            logger.error(traceback.format_exc())
            result = {
                "code": ErrCode.MethodNotFound.Code,
                "msg": ErrCode.MethodNotFound.Msg.format(router_str)
            }
        except Exception as e:
            logger.error(e)
            logger.error(traceback.format_exc())
            result = {
                "code": ErrCode.UnknownError.Code,
                "msg": ErrCode.UnknownError.Msg
            }
        finally:
            return result