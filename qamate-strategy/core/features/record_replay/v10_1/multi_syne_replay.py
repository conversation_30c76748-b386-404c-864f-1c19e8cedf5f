#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@Desc    :   
@File    :   multi_syne_replay.py
@Time    :   2024-02-21
<AUTHOR>   x<PERSON><PERSON><PERSON>@baidu.com
"""
import json
import traceback

import cv2

from basics import config
from basics.image.ui.img_similarity import is_target_from_similarity
from basics.util import logger
from .basic import PROJECT_VERSION
from .lib import subgraph_search as ss
from .lib.check_node_detail import check_overlap, crop_image_by_largest_contour, get_target_chars
from .lib.dom_split_threshold import DomSplitThreshold as DST
from .lib.graph_struct import GraphStruct
from ..base.execute_base import ExecuteBase
from ..base.local_strategies_errcode import ErrCode
from ...cv_services.circle_detect import detect_circles
from ...cv_services.sam_detect import fast_sam_detect


class MultiSyneReplayExecute(ExecuteBase):
    """
    多控件协同定位，回放类
    """

    def __init__(self):
        """
        初始化类 
        """
        super().__init__()
        self.find_nodes_dict = {}
        self.replay_nodes_dict = {}
        self.fallback_replay_nodes_dict = {}
        self.cut_image_info = {}
        self.match_cache = {}
        self.step_info_scale = 1

    def copy_node_exclude(self, copy_node, exclude_keys):
        """
        排除指定属性的复制
        """
        node = {}
        for key in copy_node.keys():
            if key not in exclude_keys:
                node[key] = copy_node[key]
        return node

    def check_params_by_model_type(self, params, model_type):
        """
        根据model_type判断入参是否完整
        """
        if int(model_type) == 2:
            if "root_elements" not in params:
                return False
        return True

    def traversal_find_record_nodes(self, record_dom):
        """
        遍历树，寻找目标节点
        """
        logger.debug("[Batdom Debug] record_dom: {}".format(json.dumps(record_dom)))
        if record_dom["debug"]["id"] in self.find_nodes_dict:
            id = record_dom["debug"]["id"]
            self.find_nodes_dict[id]["rect"] = record_dom["rect"]
            self.find_nodes_dict[id]["find_mark"] = True

        if "children" in record_dom:
            for child in record_dom["children"]:
                self.traversal_find_record_nodes(child)

        return

    def cut_img_by_rect(self, image, rect):
        """
        根据 rect 裁剪图片
        """
        # 获取裁剪后的图片
        x, y, w, h = rect["x"], rect["y"], rect["w"], rect["h"]
        x1, y1, x2, y2 = x, y, x + w, y + h
        x2, y2 = min(x2, image.shape[1]), min(y2, image.shape[0])
        return image[y1:y2, x1:x2, :]

    def get_chosen_nodes(self, step_info):
        """
        获取选择 nodes 的列表
        """
        find_nodes = step_info["findInfo"]["findNode"]
        logger.info("find_nodes: {}".format(json.dumps(find_nodes)))

        nodes_num = 0
        for find_node in find_nodes:
            # 如果是组件类型，且需要进行特征比对，则预先完成图片切割
            if find_node["featureFlag"] is True:
                if find_node["detailFeature"]["type"] == "Component":
                    find_node_image = self.cut_img_by_rect(self.record_image, find_node["detailFeature"]["rect"])
                    self.cut_image_info[find_node["id"]] = find_node_image

            nodes_num += 1
            self.find_nodes_dict[find_node["id"]] = find_node
            self.find_nodes_dict[find_node["id"]]["type"] = find_node["detailFeature"]["type"]

        # 遍历全树，获取 nodes 列表
        self.traversal_find_record_nodes(step_info["dom"])

        for node_id in self.find_nodes_dict:
            if self.find_nodes_dict[node_id]["find_mark"] is False:
                logger.info("not find node: {}".format(json.dumps(self.find_nodes_dict[node_id])))
                return False, nodes_num
        return True, nodes_num

    def get_precise_text_area(self, goal_text_info, text_dom_infos):
        """
        :param goal_text_info: 用户设置的目标文本信息
        :param text_dom_infos: 回放时识别到的文本信息
        """
        def get_text_info_from_chars(text, chars):
            """
            根据chars生成控件信息
            """
            x1 = min([c['rect']['left'] for c in chars])
            y1 = min([c['rect']['top'] for c in chars])
            x2 = max([c['rect']['left'] + c['rect']['width'] for c in chars])
            y2 = max([c['rect']['top'] + c['rect']['height'] for c in chars])
            x, y, w, h = int(x1), int(y1), int(x2 - x1), int(y2 - y1)
            return {
                'type': 'Text',
                'ext': {'id': -1, 'tag': ['single', 'multiple', 'visual'], 'text': text},
                'debug': {'id': -1, 'parents': [0]},
                'rect': {'x': x, 'y': y, 'w': w, 'h': h},
                'children': [],
                'charset': chars
            }

        def get_right_text_infos(current_text_info, all_text_infos):
            """
            获取同一行右边的所有文本控件
            """
            y_center = int(current_text_info['rect']['y'] + current_text_info['rect']['h'] / 2)
            cur_x = int(current_text_info['rect']['x'])
            right_text_infos = []
            for text_info in all_text_infos:
                x = int(text_info['rect']['x'])
                y1, y2 = int(text_info['rect']['y']), int(text_info['rect']['y'] + text_info['rect']['h'])
                if y1 <= y_center <= y2 and x > cur_x:
                    right_text_infos.append(text_info)

            sorted(right_text_infos, key=lambda item: item['rect']['x'])
            return right_text_infos

        # 0-相等、1-包含、2-正则
        match_type = goal_text_info['detailFeature']['matchType']
        goal_text = goal_text_info['detailFeature']['ext']['text']

        new_text_infos = []
        if match_type == 0:
            return new_text_infos
        elif match_type == 1:
            for text_dom_info in text_dom_infos:
                dom_text = text_dom_info['ext']['text']
                if dom_text == goal_text:
                    # 如果完全相等，说明能够匹配
                    continue
                elif dom_text in goal_text:
                    # 如果goal_text包含dom_text，属于分裂的情况，需要向右补足
                    tmp_dom_text = dom_text
                    # 获取text_dom_info右边的所有文本控件，并从左到右排序
                    right_text_infos = get_right_text_infos(text_dom_info, text_dom_infos)
                    if len(right_text_infos) == 0:
                        # 如果右边没有文本了，就直接跳过
                        continue
                    # 获取能完整包含goal_text的text列表
                    spl_infos = [text_dom_info]
                    cover = False
                    for rti in right_text_infos:
                        tmp_dom_text += rti['ext']['text']
                        spl_infos.append(rti)
                        if goal_text in tmp_dom_text:
                            cover = True
                            break
                    if not cover:
                        # 如果不能完整包含，就直接跳过
                        continue
                    # 如果能完整包含，就把spl_infos合并
                    merge_cs = []
                    for si in spl_infos:
                        merge_cs.extend(si['charset'])
                    mx1 = min([c['rect']['left'] for c in merge_cs])
                    mx2 = max([c['rect']['left'] + c['rect']['width'] for c in merge_cs])
                    my1 = min([c['rect']['top'] for c in merge_cs])
                    my2 = max([c['rect']['top'] + c['rect']['height'] for c in merge_cs])
                    merge_text_info = {
                        'type': 'Text',
                        'ext': {'id': -1, 'tag': ['single', 'multiple', 'visual'], 'text': tmp_dom_text},
                        'debug': {'id': -1, 'parents': [0]},
                        'rect': {'x': mx1, 'y': my1, 'w': mx2 - mx1, 'h': my2 - my1},
                        'children': [],
                        'charset': merge_cs
                    }
                    new_cs = get_target_chars(merge_text_info, goal_text, regex=False)
                    nt = get_text_info_from_chars(goal_text, new_cs)
                    new_text_infos.append(nt)
                elif goal_text in dom_text:
                    # 如果dom_text包含goal_text，属于合并的情况, 需要从dom_text中单独摘出来
                    cs = get_target_chars(text_dom_info, goal_text, regex=False)
                    nt = get_text_info_from_chars(goal_text, cs)
                    new_text_infos.append(nt)
                else:
                    # 如果不存在包含关系，说明不匹配
                    continue
        elif match_type == 2:
            pass

        return new_text_infos

    def tmp_build_fallback_replay_nodes_dict(self):
        """
        构建临时 self.replay_nodes_dict
        """

        fallback_dom_infos = list(self.replay_nodes_dict.values())

        # 执行文本精确匹配算法
        text_dom_infos = [info for info in fallback_dom_infos if info['type'] == 'Text']
        find_text_infos = [info for info in self.find_nodes_dict.values() if info['type'] == 'Text']
        if len(find_text_infos) > 0:
            for find_info in find_text_infos:
                new_text_infos = self.get_precise_text_area(find_info, text_dom_infos)
                fallback_dom_infos.extend(new_text_infos)
            # 对新增的控件进行
            max_id = max([max(item['debug']['id'], item['ext']['id']) for item in fallback_dom_infos])
            for fi in fallback_dom_infos:
                if fi['debug']['id'] == -1:
                    max_id += 1
                    fi['debug']['id'] = max_id
                    fi['ext']['id'] = max_id

        # 判断选择的文本里是否存在“l”，如果存在，一律替换成I
        if len(find_text_infos) > 0:
            has_l_or_I = False
            for find_info in find_text_infos:
                if ('l' in find_info['detailFeature']['ext']['text'] or
                        'I' in find_info['detailFeature']['ext']['text']):
                    find_info['detailFeature']['ext']['text'] = (
                        find_info['detailFeature']['ext']['text'].replace('l', 'I'))
                    has_l_or_I = True
            if has_l_or_I:
                for ti in text_dom_infos:
                    if 'l' in ti['ext']['text']:
                        ti['ext']['text'] = ti['ext']['text'].replace('l', 'I')
                        for c in ti.get('charset', []):
                            if c['word'] == 'l':
                                c['word'] = 'I'

        # 执行circle检测算法，并去重后加入fallback_replay_nodes_dict
        circles = detect_circles(self.replay_image)
        # print('before c', len(circles))
        circles = check_overlap(fallback_dom_infos, circles)
        # print('after c', len(circles))
        fallback_dom_infos.extend(circles)

        # 如果用户开启了useFastSAM开关，则执行fastSAM检测算法，并去重后加入fallback_replay_nodes_dict
        if self.settings['useFastSAM']:
            sam_list = fast_sam_detect(self.replay_image)
            # print('before s', len(sam_list))
            sam_list = check_overlap(fallback_dom_infos, sam_list)
            # print('after s', len(sam_list))
            fallback_dom_infos.extend(sam_list)

        for info in fallback_dom_infos:
            rep_id = info['debug']['id']
            self.fallback_replay_nodes_dict[rep_id] = info

    def tmp_build_replay_nodes_dict(self, replay_dom):
        """
        构建临时 self.replay_nodes_dict
        """
        merge_ocr = "mergeOCR" in self.settings and self.settings["mergeOCR"]

        id = replay_dom["debug"]["id"]
        add = True
        if merge_ocr and replay_dom["type"] == "Text":
            add = False
        if not merge_ocr and replay_dom["type"] == "TextArea":
            add = False

        if add:
            # self.replay_nodes_dict[id] = self.copy_node_exclude(replay_dom, ["children"])
            self.replay_nodes_dict[id] = replay_dom
        # logger.info("DEBUG_TEST: {}".format(json.dumps(self.replay_nodes_dict[id]["rect"])))

        if "children" in replay_dom:
            for child in replay_dom["children"]:
                self.tmp_build_replay_nodes_dict(child)

        return

    def get_match_area(self, match_area_list):
        """
        获取match_nodes的最大外框
        """
        if len(match_area_list) <= 0:
            return {}

        match_area_max_rect = self.copy_node_exclude(match_area_list[0]["rect"], [])
        for i in range(1, len(match_area_list)):
            match_area_rect = match_area_list[i]["rect"]
            if match_area_rect["x"] < match_area_max_rect["x"]:
                match_area_max_rect["w"] = \
                    match_area_max_rect["x"] + match_area_max_rect["w"] - match_area_rect["x"]
                match_area_max_rect["x"] = match_area_rect["x"]

            if match_area_rect["y"] < match_area_max_rect["y"]:
                match_area_max_rect["h"] = \
                    match_area_max_rect["y"] + match_area_max_rect["h"] - match_area_rect["y"]
                match_area_max_rect["y"] = match_area_rect["y"]

            if match_area_rect["x"] + match_area_rect["w"] > \
                    match_area_max_rect["x"] + match_area_max_rect["w"]:
                match_area_max_rect["w"] = \
                    match_area_rect["x"] + match_area_rect["w"] - match_area_max_rect["x"]

            if match_area_rect["y"] + match_area_rect["h"] > \
                    match_area_max_rect["y"] + match_area_max_rect["h"]:
                match_area_max_rect["h"] = \
                    match_area_rect["y"] + match_area_rect["h"] - match_area_max_rect["y"]

        return {"rect": match_area_max_rect}

    def format_result(self, final_node_info_list, record_find_nodes_graph):
        """
        规范结果格式
        """
        # path_node
        result = {
            "code": 0,
            "version": PROJECT_VERSION,
            "data": {}
        }
        match_nodes = []
        match_area_list = []
        action_flag = False
        # record_find_nodes_graph = GraphStruct()
        for i in range(len(final_node_info_list)):
            replay_node = self.copy_node_exclude(final_node_info_list[i], [])

            record_node_id = record_find_nodes_graph.node_id_list[i]
            record_node = record_find_nodes_graph.nodes_dict[record_node_id]
            logger.info("record_node_id: {}".format(record_node_id))
            logger.info("record_node: {}".format(json.dumps(record_node)))
            match_nodes.append({
                "id": replay_node["debug"]["id"],
                "rect": {
                    "x": replay_node["rect"]["x"],
                    "y": replay_node["rect"]["y"],
                    "w": replay_node["rect"]["w"],
                    "h": replay_node["rect"]["h"],
                },
                "record_id": record_node["id"],
                "record_rect": {
                    "x": record_node["rect"]["x"],
                    "y": record_node["rect"]["y"],
                    "w": record_node["rect"]["w"],
                    "h": record_node["rect"]["h"],
                }
            })

            match_area_list.append(replay_node)

            if "actionNode" in record_node and record_node["actionNode"] is True:
                action_area = replay_node["rect"]
                if 'target_ocr_chars' in replay_node:
                    # 如果是文本-包含/正则，会被塞一个target_ocr_char字段
                    cs = replay_node['target_ocr_chars']
                    x1 = min([c['rect']['left'] for c in cs])
                    y1 = min([c['rect']['top'] for c in cs])
                    x2 = max([c['rect']['left'] + c['rect']['width'] for c in cs])
                    y2 = max([c['rect']['top'] + c['rect']['height'] for c in cs])

                    action_area = {
                        'x': x1,
                        'y': y1,
                        'w': x2 - x1,
                        'h': y2 - y1
                    }

                result["data"]["action_area"] = action_area
                action_flag = True

        logger.info("result: {}".format(json.dumps(result)))
        result["data"]["match_nodes"] = match_nodes
        match_area = self.get_match_area(match_area_list)
        logger.info("match_area: {}".format(json.dumps(match_area)))
        if action_flag is False:
            result["data"]["action_area"] = {
                "x": match_area["rect"]["x"],
                "y": match_area["rect"]["y"],
                "w": match_area["rect"]["w"],
                "h": match_area["rect"]["h"],
            }

        logger.info("action_flag:{} action_area:{}".format(action_flag, json.dumps(result["data"]["action_area"])))
        return result

    def scale_step_info_dom(self, step_info_dom):
        """
        递归放大step_info 中的 dom 的信息
        """
        step_info_dom["rect"]["x"] = self.step_info_scale * step_info_dom["rect"]["x"]
        step_info_dom["rect"]["y"] = self.step_info_scale * step_info_dom["rect"]["y"]
        step_info_dom["rect"]["w"] = self.step_info_scale * step_info_dom["rect"]["w"]
        step_info_dom["rect"]["h"] = self.step_info_scale * step_info_dom["rect"]["h"]

        if "children" in step_info_dom:
            for child in step_info_dom["children"]:
                self.scale_step_info_dom(child)

    def scale_find_nodes(self, find_nodes):
        """
        放大find_nodes中的信息
        """
        for find_node in find_nodes:
            find_node["detailFeature"]["rect"]["x"] = self.step_info_scale * find_node["detailFeature"]["rect"]["x"]
            find_node["detailFeature"]["rect"]["y"] = self.step_info_scale * find_node["detailFeature"]["rect"]["y"]
            find_node["detailFeature"]["rect"]["w"] = self.step_info_scale * find_node["detailFeature"]["rect"]["w"]
            find_node["detailFeature"]["rect"]["h"] = self.step_info_scale * find_node["detailFeature"]["rect"]["h"]

    def format_step_info(self, step_info):
        """
        因为 native 缩小了 step_info 中的 dom 的信息，所以需要进行下缩放
        """
        try:
            self.step_info_scale = step_info["deviceInfo"]["screenSize"]["scale"]
            logger.info("step_info_scale: {}".format(self.step_info_scale))
        except:
            return ErrCode.StepInfoScaleGetError.Code, ErrCode.StepInfoScaleGetError.Msg, \
                ErrCode.StepInfoScaleGetError.ShowMsg
        self.scale_step_info_dom(step_info["dom"])
        self.scale_find_nodes(step_info["findInfo"]["findNode"])
        return 0, "", ""

    def _build_match_cache(self, fallback=False):
        """
        构建一般特征比对match_cache或兜底特征比对match_cache
        Args:
            fallback:

        Returns:

        """
        if not fallback:
            nodes_dict = self.replay_nodes_dict
            thresh = None
        else:
            nodes_dict = self.fallback_replay_nodes_dict
            thresh = DST.FALLBACK_SIMILARITY_THRESH

        find_list = []
        for k, v in self.find_nodes_dict.items():
            if v['type'] == 'Component':
                # 正常情况下，只把component-icon加入特征比对
                find_list.append(v)
            if v['type'] == 'Icon' and fallback:
                # 只有兜底时，才会把Icon模型的结果也加入特征比对
                find_list.append(v)

        if len(find_list) == 0:
            return

        rep_match_list = []
        # 回放匹配列表
        for k, v in nodes_dict.items():
            if v['type'] == 'Icon' and fallback:
                # 只有兜底时，才会把Icon模型的结果也加入特征比对
                rep_match_list.append(v)
            if v['type'] == 'Component':
                # 正常情况下，只把component-icon加入特征比对
                rep_match_list.append(v)

        rep_cut_img_list = []

        # rep_match_list批量切图
        for v in rep_match_list:
            rep_cut_img_list.append(self.cut_img_by_rect(self.replay_image, v['rect']))
        # 批量执行特征比对能力, 构建特征比对match_cache
        for fv in find_list:
            rec_id = fv['detailFeature']['ext']['id']
            rec_cut_img = self.cut_img_by_rect(self.record_image, fv['detailFeature']['rect'])
            results = is_target_from_similarity(rec_cut_img, [(0, cut_img) for cut_img in rep_cut_img_list],
                                                thresh=thresh)
            for i, r in enumerate(results):
                rep_id = rep_match_list[i]['debug']['id']
                self.match_cache[(rec_id, rep_id)] = (bool(r[2]), float(r[1]))

        if fallback and config.get_use_crop_in_fall_back():
            for fv in find_list:
                rec_id = fv['detailFeature']['ext']['id']
                rec_cut_img = self.cut_img_by_rect(self.record_image, fv['detailFeature']['rect'])
                rec_cut_img = crop_image_by_largest_contour(rec_cut_img)
                results = is_target_from_similarity(rec_cut_img, [(0, cut_img) for cut_img in rep_cut_img_list],
                                                    thresh=thresh)
                for i, r in enumerate(results):
                    rep_id = rep_match_list[i]['debug']['id']
                    k = (rec_id, rep_id)
                    old_sim = self.match_cache[k][1]
                    if float(r[1]) > old_sim:
                        self.match_cache[k] = (bool(r[2]), float(r[1]))

    def _fallback_search(self, params):
        """
        正常回放失败时的兜底逻辑
        Returns:
        """
        record_find_nodes_graph = GraphStruct(self.record_image.shape[0], self.record_image.shape[1])
        record_find_nodes_graph.graph_build(self.find_nodes_dict)
        logger.info("record_find_nodes_graph: {}".format(json.dumps(record_find_nodes_graph.line_matrix)))

        # 基于replay的结构树，构建临时self.replay_nodes_dict
        self.replay_dom = params["replay_dom"]  # 通过建模接口拿到的回放页面dom
        logger.info("finish replay_dom build")

        # 这里构建时要加入新检测框, 比如圆检测、fastsam检测
        self.tmp_build_fallback_replay_nodes_dict()

        # 使用fallback_replay_nodes_dict构建兜底比对match_cache
        self._build_match_cache(fallback=True)

        fallback_replay_graph = GraphStruct(self.replay_image.shape[0], self.replay_image.shape[1])
        fallback_replay_graph.graph_build(self.fallback_replay_nodes_dict)
        # logger.info("replay_graph: {}".format(json.dumps(replay_graph.line_matrix)))
        logger.info("replay_graph nodes_num: {}".format(fallback_replay_graph.nodes_num))

        final_node_match = ss.subgraph_search(
            fallback_replay_graph, record_find_nodes_graph, self.cut_image_info,
            {"record_image": self.record_image, "replay_image": self.replay_image},
            fallback=True,
            match_cache=self.match_cache
        )
        if final_node_match is None:
            result = {
                "code": ErrCode.StrictMatchError.Code,
                "version": PROJECT_VERSION,
                "msg": ErrCode.StrictMatchError.Msg,
                "show_msg": ErrCode.StrictMatchError.ShowMsg
            }
            return result

        final_node_info_list = ss.format_graph_node_match(final_node_match, fallback_replay_graph)
        logger.info("final_node_info_list: {}".format(json.dumps(final_node_info_list)))
        result = self.format_result(final_node_info_list, record_find_nodes_graph)
        return result

    def main(self, params):
        """
        主流程函数
        """
        try:
            self.settings = {
                "mergeOCR": False,
                "useFastSAM": False
            }
            if "findInfo" in params["step_info"]:
                if "mergeOCR" in params["step_info"]["findInfo"]:
                    self.settings["mergeOCR"] = params["step_info"]["findInfo"]["mergeOCR"]
                if "useFastSAM" in params["step_info"]["findInfo"]:
                    self.settings["useFastSAM"] = params["step_info"]["findInfo"]["useFastSAM"]

            # 缩放下step_info和findNode
            code, msg, show_msg = self.format_step_info(params["step_info"])
            if code != 0:
                result = {
                    "code": code,
                    "version": PROJECT_VERSION,
                    "msg": msg,
                    "show_msg": show_msg
                }
                return result

            # self.vision_info = params["vision_info"]
            self.image_path = params["image_path"]
            self.case_image_path = params["case_image_path"]

            self.record_image = cv2.imread(self.case_image_path)
            self.replay_image = cv2.imread(self.image_path)

            # 校验图片是否合法
            if self.record_image is None:
                result = {
                    "code": ErrCode.InputImageError.Code,
                    "version": PROJECT_VERSION,
                    "msg": ErrCode.InputImageError.Msg.format(self.case_image_path),
                    "show_msg": ErrCode.InputImageError.ShowMsg
                }
                return result

            if self.replay_image is None:
                result = {
                    "code": ErrCode.InputImageError.Code,
                    "version": PROJECT_VERSION,
                    "msg": ErrCode.InputImageError.Msg.format(self.image_path),
                    "show_msg": ErrCode.InputImageError.ShowMsg
                }
                return result

            # 获取目标元素信息
            # 开始处理
            # 获取选择 nodes 的列表 -> self.find_nodes_dict
            get_flag, nodes_num = self.get_chosen_nodes(params["step_info"])
            logger.info("find_nodes_dict: {}".format(json.dumps(self.find_nodes_dict)))
            logger.info("record screen height: {}".format(self.record_image.shape[0]))
            logger.info("record screen width: {}".format(self.record_image.shape[1]))
            logger.debug("cut_image_info keys: {}".format(self.cut_image_info.keys()))
            if get_flag is False or self.find_nodes_dict is {}:
                result = {
                    "code": ErrCode.RecordGoalNodesFindError.Code,
                    "version": PROJECT_VERSION,
                    "msg": ErrCode.RecordGoalNodesFindError.Msg,
                    "show_msg": ErrCode.RecordGoalNodesFindError.ShowMsg
                }
                return result
            if nodes_num < 2:
                result = {
                    "code": ErrCode.NodesNumError.Code,
                    "version": PROJECT_VERSION,
                    "msg": ErrCode.NodesNumError.Msg,
                    "show_msg": ErrCode.NodesNumError.ShowMsg
                }
                return result

            record_find_nodes_graph = GraphStruct(self.record_image.shape[0], self.record_image.shape[1])
            record_find_nodes_graph.graph_build(self.find_nodes_dict)
            logger.info("record_find_nodes_graph: {}".format(json.dumps(record_find_nodes_graph.line_matrix)))

            # 基于replay的结构树，构建临时self.replay_nodes_dict
            self.replay_dom = params["replay_dom"]  # 通过建模接口拿到的回放页面dom
            logger.info("finish replay_dom build")
            self.tmp_build_replay_nodes_dict(self.replay_dom)

            # 使用replay_nodes_dict构建一般特征比对match_cache
            self._build_match_cache()

            replay_graph = GraphStruct(self.replay_image.shape[0], self.replay_image.shape[1])
            replay_graph.graph_build(self.replay_nodes_dict)
            # logger.info("replay_graph: {}".format(json.dumps(replay_graph.line_matrix)))
            logger.info("replay_graph nodes_num: {}".format(replay_graph.nodes_num))

            final_node_match = ss.subgraph_search(
                replay_graph, record_find_nodes_graph, self.cut_image_info,
                {"record_image": self.record_image, "replay_image": self.replay_image},
                find_nodes_count=nodes_num,
                match_cache=self.match_cache
            )
            logger.info("node_match: {}".format(json.dumps(final_node_match)))

            if final_node_match is None:
                # 触发兜底逻辑
                return self._fallback_search(params)

            final_node_info_list = ss.format_graph_node_match(final_node_match, replay_graph)
            logger.info("final_node_info_list: {}".format(json.dumps(final_node_info_list)))
            result = self.format_result(final_node_info_list, record_find_nodes_graph)

            return result

        except Exception as e:
            logger.error("structure_match uncaught Error: {}".format(traceback.format_exc()))
            result = {
                "code": ErrCode.UnknownError.Code,
                "version": PROJECT_VERSION,
                "msg": ErrCode.UnknownError.Msg,
                "show_msg": ErrCode.UnknownError.ShowMsg
            }
            return result
