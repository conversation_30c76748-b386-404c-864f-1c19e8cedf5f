#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@Desc    :   
@File    :   check_node_detail.py
@Time    :   2024-03-01
<AUTHOR>   xuz<PERSON><PERSON>@baidu.com
"""
import json
import re
import time
import traceback

import cv2
import numpy as np
import math

from basics import config
from basics.util import logger
from basics.image.ui.img_similarity import is_target_from_similarity
from .dom_split_threshold import DomSplitThreshold as DST

def find_by_index(find_list, goal_feature):
    """
    根据index从候选节点中找到最合适的节点
    """
    find_list.sort(key=lambda x: (x["rect"]["y"], x["rect"]["x"]))
    find_index = 0
    if "index" in goal_feature["ext"]:
        find_index = goal_feature["ext"]["index"]
    if find_index < len(find_list):
        return find_list[find_index]
    else:
        return None

def find_by_dist(find_list, goal_feature, shape_info):
    """
    根据网格位置从候选节点中找到符合条件的位置最接近的节点
    """

    def get_dist(x0, y0, x1, y1):
        """
        获取距离
        """
        # return max(abs(x0 - x1), abs(y0 - y1))
        return math.sqrt((x0 - x1) ** 2 + (y0 - y1) ** 2)

    w0, h0 = shape_info["record"]["width"], shape_info["record"]["height"]
    w1, h1 = shape_info["replay"]["width"], shape_info["replay"]["height"]
    r0 = goal_feature["rect"]
    x0, y0 = r0["x"], r0["y"]
    cx0, cy0 = (x0 + r0["w"] / 2) / w0, (y0 + r0["h"] / 2) / h0


    min_dist = None
    target_ele = None

    for replay_info in find_list:
        r1 = replay_info["rect"]
        x1, y1 = r1["x"], r1["y"]
        cx1, cy1 = (x1 + r1["w"] / 2) / w1, (y1 + r1["h"] / 2) / h1
        # dist = get_dist(x0, y0, x1, y1)
        dist = get_dist(cx0, cy0, cx1, cy1)
        if target_ele is None or dist < min_dist:
            min_dist = dist
            target_ele = replay_info
    return target_ele


def crop_image_by_largest_contour(img, threshold1=50, threshold2=150):
    """
    输入一个图标的小图，输出仅保留最小包络矩形（矩形不能是斜的）的小图，即切掉背景白边（白边是泛指）的小图
    背景：
    yolo模型切割出来的不是很精确，可能带白边
    fastsam切割出来的区域比较精确
    这两个部分进行特征比对，可能会失败，所以先要吧yolo模型的结果切掉白边
    """
    # 转换为灰度图像
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

    # 应用Canny边缘检测
    edges = cv2.Canny(gray, threshold1, threshold2)

    # 查找轮廓
    contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

    # 如果没有找到轮廓，返回原始图像
    if not contours:
        return img

        # 根据轮廓面积找到最大的轮廓
    c = max(contours, key=cv2.contourArea)

    # 找到边界框
    x, y, w, h = cv2.boundingRect(c)

    # 裁剪图像
    cropped_img = img[y:y + h, x:x + w]

    return cropped_img


def cut_img_by_rect(image, rect):
    """
    根据 rect 裁剪图片
    """
    # 获取裁剪后的图片
    x, y, w, h = rect["x"], rect["y"], rect["w"], rect["h"]
    x1, y1, x2, y2 = x, y, x + w, y + h
    x2, y2 = min(x2, image.shape[1]), min(y2, image.shape[0])
    return image[y1:y2, x1:x2, :]


def check_width_height_ratio(info_a, info_b):
    """
    检查长宽比是否在预期范围内 
    """
    result = True
    # 判断长宽比是否符合预期
    if abs(info_a["hw_ratio"] - info_b["hw_ratio"]) > DST.WIDTH_HEIGHT_LIMIT_RATIO:
        result = False
        return result

    # 如果长宽比的值，超过某个阈值，则需要判断，是否都是长大于宽或者宽大于长
    if info_a["hw_ratio"] > DST.CHECK_WIDTH_HEIGHT_LONGER_FLAG and \
            info_b["hw_ratio"] > DST.CHECK_WIDTH_HEIGHT_LONGER_FLAG:
        check_res = (info_a["h"] - info_a["w"]) * (info_b["h"] - info_b["w"])
        if check_res >= 0:
            return result
        else:
            result = False
            return result
    else:
        return result


def check_width_height_length(record_len_r, replay_len_r):
    """
    判断下宽或者高相对屏幕的比例，不至于差的太多
    """
    return True


def check_node_self(record_node_info, replay_node_info, image_shape_info):
    """
    检查节点本身是否可以匹配
    """
    check_flag = True

    # 同质type，在不叠加视觉特征的情况下，可以匹配；如叠加视觉特征，check_node_feature方法会检查type进行兜底
    matchable_types = ["Icon", "Component"]

    if replay_node_info["type"] == record_node_info["detailFeature"]["type"]:
        pass
    elif (replay_node_info["type"] in matchable_types and
          record_node_info["detailFeature"]["type"] in matchable_types):
        pass
    else:
        check_flag = False
        return check_flag

    # 文本类型暂时不参与比较
    if replay_node_info["type"] in ["Text", "TextArea"]:
        return check_flag

    # 检查下节点的长宽比是否符合预期
    record_hw_ratio = abs(
        math.log(record_node_info["detailFeature"]["rect"]["h"]) - \
        math.log(record_node_info["detailFeature"]["rect"]["w"])
    )
    record_hw_info = {
        "h": record_node_info["detailFeature"]["rect"]["h"],
        "w": record_node_info["detailFeature"]["rect"]["w"],
        "hw_ratio": record_hw_ratio
    }

    replay_hw_ratio = abs(
        math.log(replay_node_info["rect"]["h"]) - \
        math.log(replay_node_info["rect"]["w"])
    )

    replay_hw_info = {
        "h": replay_node_info["rect"]["h"],
        "w": replay_node_info["rect"]["w"],
        "hw_ratio": replay_hw_ratio
    }

    logger.debug("record_hw_ratio: {}".format(record_hw_ratio))

    check_width_height_res = check_width_height_ratio(record_hw_info, replay_hw_info)
    logger.debug("check_width_height_res: {}".format(check_width_height_res))
    if check_width_height_res is False:
        check_flag = False
        return check_flag

    # 检查下节点的大小是否符合预期
    record_height_ratio = record_node_info["detailFeature"]["rect"]["h"] / image_shape_info["record"]["height"]
    record_width_ratio = record_node_info["detailFeature"]["rect"]["w"] / image_shape_info["record"]["width"]
    logger.debug("record_height_ratio: {}".format(record_height_ratio))
    logger.debug("record_width_ratio: {}".format(record_width_ratio))

    replay_height_ratio = replay_node_info["rect"]["h"] / image_shape_info["replay"]["height"]
    replay_width_ratio = replay_node_info["rect"]["w"] / image_shape_info["replay"]["width"]
    logger.debug("replay_height_ratio: {}".format(replay_height_ratio))
    logger.debug("replay_width_ratio: {}".format(replay_width_ratio))

    # 确保永远是长边比短边
    size_ratio_h = record_height_ratio / replay_height_ratio
    size_ratio_h = size_ratio_h if size_ratio_h >= 1.0 else 1.0 / size_ratio_h
    size_ratio_w = record_width_ratio / replay_width_ratio
    size_ratio_w = size_ratio_w if size_ratio_w >= 1.0 else 1.0 / size_ratio_w

    if max(size_ratio_h, size_ratio_w) > DST.SIZE_LIMIT_RATIO:
        check_flag = False
        return check_flag
    return check_flag


def check_node_detail(record_node_info,
                      replay_node_info,
                      cut_image_info,
                      image_info,
                      on_match=None,
                      match_cache=None,
                      fallback=False
                      ):
    """
    检查节点细节是否符合预期
    """
    # logger.debug("replay_node_info: {}".format(json.dumps(replay_node_info)))
    # logger.debug("record_node_info: {}".format(json.dumps(record_node_info)))
    image_shape_info = {}
    image_shape_info["record"] = {
        "height": image_info["record_image"].shape[0],
        "width": image_info["record_image"].shape[1]
    }
    image_shape_info["replay"] = {
        "height": image_info["replay_image"].shape[0],
        "width": image_info["replay_image"].shape[1]
    }

    check_flag = check_node_self(record_node_info, replay_node_info, image_shape_info)
    if check_flag is False:
        return check_flag

    if record_node_info["featureFlag"] is False:
        # logger.info("check_node_detail pos_0 result:{}".format(check_flag))
        return check_flag
    cut_image = None

    if record_node_info["id"] in cut_image_info:
        cut_image = cut_image_info[record_node_info["id"]]
    check_flag = check_node_feature(
        image_info["replay_image"], replay_node_info, record_node_info["detailFeature"],
        input_record_image=cut_image, index_flag=False, on_match=on_match, match_cache=match_cache,
        fallback=fallback
    )

    return check_flag


def compute_prefix_function(pattern):
    """
    计算模式字符串的部分匹配表（也称为前缀函数或π函数）。

    参数:
        pattern (str): 模式字符串

    返回:
        list: 部分匹配表，一个整数列表
    """
    m = len(pattern)
    pi = [0] * m
    j = 0
    for i in range(1, m):
        while j > 0 and pattern[j] != pattern[i]:
            j = pi[j - 1]
        if pattern[j] == pattern[i]:
            j += 1
        pi[i] = j
    return pi


def kmp_search(all_char_set, pattern):
    """
    使用KMP算法在文本字符串中查找模式字符串的所有出现位置。

    参数:
        text (str): 文本字符串
        pattern (str): 模式字符串

    返回:
        list: 包含所有匹配开始位置的整数列表
    """
    n = len(all_char_set)
    m = len(pattern)
    pi = compute_prefix_function(pattern)
    q = 0
    matches = []  # 用于存储所有匹配的索引
    for i in range(n):
        while q > 0 and pattern[q] != all_char_set[i]['word']:
            q = pi[q - 1]
        if pattern[q] == all_char_set[i]['word']:
            q += 1
        if q == m:  # 找到了一个完整的匹配
            matches.append(i - m + 1)  # 添加匹配开始的位置
            q = pi[q - 1]  # 重置q以继续搜索
    return matches


def get_target_chars(replay_dom_info, record_goal_text, regex=False):
    """获取目标点击字符"""
    all_char_set = []
    if replay_dom_info['type'] == 'TextArea':
        for child in replay_dom_info['children']:
            all_char_set.extend([c for c in child['charset']])
    else:
        all_char_set.extend([c for c in replay_dom_info['charset']])

    sorted(all_char_set, key=lambda c: c['rect']['left'])
    if regex:
        matches = list(re.finditer(record_goal_text, replay_dom_info["ext"]["text"]))
        if len(matches) > 0:
            return all_char_set[matches[0].start(): matches[0].end()]
    else:
        matches = kmp_search(all_char_set, record_goal_text)
        if len(matches) > 0:
            return all_char_set[matches[0]: matches[0] + len(record_goal_text)]
    return None


def build_match_cache(goal_feature, rep_list, record_image, replay_image, match_cache, fallback=False):
    """
    构建match_cache
    """
    try:
        thresh = None
        if fallback:
            thresh = DST.FALLBACK_SIMILARITY_THRESH
        rep_cut_img_list = []

        # rep_match_list批量切图
        for v in rep_list:
            rep_cut_img_list.append(cut_img_by_rect(replay_image, v['rect']))
        rec_id = goal_feature['ext']['id']
        rec_cut_img = cut_img_by_rect(record_image, goal_feature['rect'])
        results = is_target_from_similarity(rec_cut_img, [(0, cut_img) for cut_img in rep_cut_img_list], thresh=thresh)
        for i, r in enumerate(results):
            rep_id = rep_list[i]['debug']['id']
            match_cache[(rec_id, rep_id)] = (bool(r[2]), float(r[1]))

        if fallback and config.get_use_crop_in_fall_back():
            cropped_rec_cut_img = crop_image_by_largest_contour(rec_cut_img)
            results = is_target_from_similarity(cropped_rec_cut_img, [(0, cut_img) for cut_img in rep_cut_img_list],
                                                thresh=thresh)
            for i, r in enumerate(results):
                rp_id = rep_list[i]['debug']['id']
                k = (rec_id, rp_id)
                if k in match_cache and float(r[1]) > match_cache[k][1]:
                    match_cache[(rec_id, rp_id)] = (bool(r[2]), float(r[1]))
    except:
        logger.error(traceback.format_exc())


def check_overlap(exists_infos, target_infos):
    """
    如果是文本，计算 重叠面积 / target_info面积
    如果不是文本，计算 iou
    然后进行去重
    """
    res_infos = []
    array = np.asarray([[x['rect']['x'], x['rect']['y'], x['rect']['x'] + x['rect']['w'], x['rect']['y'] +
                         x['rect']['h']] for x in exists_infos if x['type'] not in ('TextArea', 'Text')])
    for t in target_infos:
        tr = np.asarray([[t['rect']['x'], t['rect']['y'], t['rect']['x'] + t['rect']['w'], t['rect']['y'] +
                          t['rect']['h']]])
        lt = np.maximum(tr[:, np.newaxis, :2], array[:, :2])  # [1,n,2]
        rb = np.minimum(tr[:, np.newaxis, 2:], array[:, 2:])  # [1,n,2]
        wh = (rb - lt + 1).clip(min=0)  # [1,n,2]
        inter = wh[:, :, 0] * wh[:, :, 1]  # [1,n]
        # 计算boxes1和boxes2的面积
        area1 = (tr[:, 2] - tr[:, 0] + 1) * (tr[:, 3] - tr[:, 1] + 1)  # [1,]
        area2 = (array[:, 2] - array[:, 0] + 1) * (array[:, 3] - array[:, 1] + 1)  # [n,]
        iou = (inter / (area1[:, np.newaxis] + area2 - inter)).squeeze()
        if np.max(iou) <= DST.ALMOST_OVERLAP_RATIO / 100.0:
            res_infos.append(t)
            array = np.concatenate((array, tr))

    target_infos = res_infos
    res_infos = []
    ocr_infos = []
    for x in exists_infos:
        if x['type'] == 'Text':
            if 'charset' in x and len(x['charset']) == 1 and x['charset'][0]['prob'] < 0.95:
                # 因为有些图标(如发送图标)被ocr识别为单字符(7)，造成fastsam切割结果被去重，无法召回
                # 所以将置信度低的单个ocr字符不用于fastsam检测框去重
                continue
            ocr_infos.append(x)
        if x['type'] == 'TextArea':
            if 'children' in x and len(x['children']) > 0:
                for cx in x['children']:
                    if cx['type'] == 'Text':
                        if 'charset' in cx and len(cx['charset']) == 1 and cx['charset'][0]['prob'] < 0.95:
                            continue
                        ocr_infos.append(cx)

    array = np.asarray([[x['rect']['x'], x['rect']['y'], x['rect']['x'] + x['rect']['w'], x['rect']['y'] +
                         x['rect']['h']] for x in ocr_infos])
    for t in target_infos:
        if len(ocr_infos) == 0:
            res_infos.append(t)
            break
        tr = np.asarray([[t['rect']['x'], t['rect']['y'], t['rect']['x'] + t['rect']['w'], t['rect']['y'] +
                          t['rect']['h']]])
        lt = np.maximum(tr[:, np.newaxis, :2], array[:, :2])  # [1,n,2]
        rb = np.minimum(tr[:, np.newaxis, 2:], array[:, 2:])  # [1,n,2]
        wh = (rb - lt + 1).clip(min=0)  # [1,n,2]
        inter = wh[:, :, 0] * wh[:, :, 1]  # [1,n]
        area1 = (tr[:, 2] - tr[:, 0] + 1) * (tr[:, 3] - tr[:, 1] + 1)  # [1,]
        r = (inter / area1[:, np.newaxis]).squeeze()
        if np.max(r) <= DST.ALMOST_OVERLAP_RATIO / 100.0:
            res_infos.append(t)
            array = np.concatenate((array, tr))

    return res_infos


def fallback_check_node_feature(raw_replay_image, replay_dom_infos, goal_feature,
                                input_record_image=None, shape_info=None, settings=None):
    """
    单控件匹配的兜底策略，当第一轮匹配失败时触发
    当用户选择的控件为组件或icon类别时，允许和组件/Icon类别进行特征比对
    1. 如果选择的是组件，则返回相似度最大的匹配结果
    2. 如果选择的是icon，则返回相似的结果中，符合index的结果
    """
    by_distance = settings['byDistance']

    thresh = DST.FALLBACK_SIMILARITY_THRESH
    goal_type = goal_feature["type"]
    record_image = input_record_image
    match_cache = {}
    rec_cut_img = cut_img_by_rect(record_image, goal_feature['rect'])
    if goal_type in ('Component', 'Icon'):
        rep_list = [node for node in replay_dom_infos if node['type'] in ('Component', 'Icon')]
        build_match_cache(goal_feature, rep_list, record_image, raw_replay_image, match_cache, fallback=True)
    # rec_cut = cut_img_by_rect(record_image, goal_feature['rect'])
    # cv2.imwrite("/Users/<USER>/test/crops/rec_cut.png", rec_cut)
    rec_id = goal_feature['ext']['id']
    if goal_type == 'Component':
        match_list = [rep_dom for rep_dom in replay_dom_infos if rep_dom['type'] in ('Icon', 'Component')]
        most_similar = None
        max_sim = 0
        for match_dom in match_list:
        #     rep_cut = cut_img_by_rect(raw_replay_image, match_dom['rect'])
        #     cv2.imwrite("/Users/<USER>/test/crops/rep_cut_{}.png".format(match_dom['debug']['id']), rep_cut)
            rep_id = match_dom['debug']['id']
            if (rec_id, rep_id) in match_cache:
                # 如果命中缓存，就直接查询缓存结果
                check_result, similarity = match_cache[(rec_id, rep_id)]
                if check_result:
                    if similarity > max_sim:
                        max_sim = similarity
                        most_similar = match_dom
            else:
                replay_image = cut_img_by_rect(raw_replay_image, match_dom["rect"])
                if replay_image.shape[0] <= 0 or replay_image.shape[1] <= 0:
                    logger.info("error replay_dom_info: {}".format(json.dumps(match_dom)))
                    return None
                ori_result = is_target_from_similarity(rec_cut_img, [(0, replay_image)], thresh)
                similarity, check_result = ori_result[0][1], ori_result[0][2]
                if check_result:
                    if similarity > max_sim:
                        max_sim = similarity
                        most_similar = match_dom
        return most_similar
    elif goal_type == 'Icon':
        # 因为icon list中也有可能存在name匹配的图标，只是因为index达不到，才在第一轮匹配失败
        # 所以兜底时，需要把component,icon混排进行匹配
        # match_idx = -1
        ele_list = [rep_dom for rep_dom in replay_dom_infos
                    if rep_dom['type'] == 'Icon' or rep_dom['type'] == 'Component']
        ele_list.sort(key=lambda x: (x["rect"]["y"], x["rect"]["x"]))
        find_list = []

        for ele_dom in ele_list:
            rep_id = ele_dom['debug']['id']
            if ele_dom['type'] == 'Icon' and ele_dom["ext"]["name"] == goal_feature["ext"]["name"]:
                find_list.append(ele_dom)
            else:
                if (rec_id, rep_id) in match_cache:
                    # 如果命中缓存，就直接查询缓存结果
                    check_result, similarity = match_cache[(rec_id, rep_id)]
                    if check_result:
                        find_list.append(ele_dom)
                else:
                    replay_image = cut_img_by_rect(raw_replay_image, ele_dom["rect"])
                    if replay_image.shape[0] <= 0 or replay_image.shape[1] <= 0:
                        logger.info("error replay_dom_info: {}".format(json.dumps(ele_dom)))
                        return None
                    ori_result = is_target_from_similarity(rec_cut_img, [(0, replay_image)], thresh)
                    similarity, check_result = ori_result[0][1], ori_result[0][2]
                    if check_result:
                        find_list.append(ele_dom)
        if by_distance is True:
            find_by_dist(find_list, goal_feature, shape_info)
        else:
            return find_by_index(find_list, goal_feature)
    else:
        return None



def is_type_match(replay_dom_info, goal_feature, fallback=False):
    """
    判断两个节点是否类型匹配
    Args:
        replay_dom_info:
        goal_feature:
        fallback:

    Returns:

    """
    if replay_dom_info["type"] == goal_feature["type"]:
        return True
    if fallback:
        return replay_dom_info["type"] in ('Icon', 'Component') and goal_feature["type"] in ('Icon', 'Component')
    return False


def check_node_feature(raw_replay_image, replay_dom_info, goal_feature, \
                       input_record_image=None, index_flag=True, on_match=None, match_cache=None, fallback=False):
    """
    判断单控件特征是否匹配
    """
    # print("replay_dom_type: {}".format(replay_dom_info["type"]))
    cache_on, rec_id, rep_id = False, None, None
    if fallback:
        thresh = DST.FALLBACK_SIMILARITY_THRESH
    else:
        thresh = None
    if match_cache is not None:
        # print(match_cache)
        try:
            rec_id = goal_feature['ext']['id']
            rep_id = replay_dom_info['debug']['id']
            if rec_id is not None and rep_id is not None:
                cache_on = True
                if (rec_id, rep_id) in match_cache:
                    # 组件特征比对时，如果命中了缓存，也会发生回调
                    if on_match is not None:
                        on_match({'similarity': match_cache[(rec_id, rep_id)][1],
                                  'dom_info': replay_dom_info,
                                  'res': match_cache[(rec_id, rep_id)][0]})
                    return match_cache[(rec_id, rep_id)][0]
        except:
            # traceback.print_exc()
            logger.error(traceback.format_exc())

    result = False
    # 先判断类型
    if not is_type_match(replay_dom_info, goal_feature, fallback=fallback):
        return result

    # 根据不同的类型，进行不同的判断
    # logger.debug("goal_feature: {}".format(json.dumps(goal_feature)))
    # logger.debug("replay_dom_info: {}".format(json.dumps(replay_dom_info)))
    if replay_dom_info["type"] == "Icon":
        # Icon 类型，name不等于others，name去掉_beta后缀
        if replay_dom_info["ext"]["name"].replace("_beta", "") == goal_feature["ext"]["name"].replace("_beta", ""):
            if index_flag is False:
                # 多控件场景下默认传的是false
                result = True
            else:
                if replay_dom_info["ext"]["index"] == goal_feature["ext"]["index"]:
                    result = True
        return result

    elif ((replay_dom_info["type"] == "Component") or
          (replay_dom_info["type"] != goal_feature["type"] and not index_flag and fallback)):
        # 要么建模-回放节点都是组件，要么建模回放type不相等触发兜底逻辑
        # 组件类型 & name = others的icon类型
        record_image = input_record_image
        if input_record_image is None:
            record_image = goal_feature["image"]
        replay_image = cut_img_by_rect(raw_replay_image, replay_dom_info["rect"])
        if replay_image.shape[0] <= 0 or replay_image.shape[1] <= 0:
            logger.info("error replay_dom_info: {}".format(json.dumps(replay_dom_info)))
            return result
        # logger.info("input is_target_from_similarity")
        # cv2.imwrite("record_image.png", record_image)
        # cv2.imwrite("replay_image.png", replay_image)
        ori_result = is_target_from_similarity(record_image, [(0, replay_image)], thresh)
        # logger.info("finish is_target_from_similarity")
        # exit()
        # except:
        #     logger.info("record_image_size: {}".format(record_image.shape))
        #     logger.info("replay_image_size: {}".format(replay_image.shape))
        #     exit()
        # ori_result = is_target_from_similarity(record_image, [(0, replay_image)])
        check_result = ori_result[0][2]
        if check_result:
            result = True
        if cache_on:
            match_cache[(rec_id, rep_id)] = (result, float(ori_result[0][1]))
        if on_match is not None:
            on_match({'similarity': ori_result[0][1], 'dom_info': replay_dom_info, 'res': result})
        return result

    elif replay_dom_info["type"] in ["Text", "TextArea"]:
        # 文本类型
        # 目标文本
        record_goal_text = goal_feature["ext"]["text"]
        # 回放文本
        replay_text = replay_dom_info["ext"]["text"]
        match_type = goal_feature["matchType"]
        if match_type == 0:
            # 完全匹配
            if record_goal_text == replay_text:
                result = True
        elif match_type == 1:
            # 包含匹配
            if replay_text.find(record_goal_text) != -1:
                cs = get_target_chars(replay_dom_info, record_goal_text, regex=False)
                if on_match:
                    on_match({'target_ocr_chars': cs})
                result = True
        elif match_type == 2:
            # 正则匹配
            if re.search(record_goal_text, replay_text):
                cs = get_target_chars(replay_dom_info, record_goal_text, regex=True)
                if on_match:
                    on_match({'target_ocr_chars': cs})
                result = True
        return result

    elif replay_dom_info["type"] == "DFE":
        # DFE 类型
        for key in goal_feature["ext"]["feature"]:
            if goal_feature["ext"]["feature"][key]["chosen"] is not True:
                continue
            if key in replay_dom_info["ext"]["feature"] and \
                    goal_feature["ext"]["feature"][key]["data"] == replay_dom_info["ext"]["feature"][key]["data"]:
                continue
            else:
                result = False
                return result
        if index_flag is True and replay_dom_info["ext"]["index"] != goal_feature["ext"]["index"]:
            result = False
            return result
        result = True
        return result

    result = True
    return result
