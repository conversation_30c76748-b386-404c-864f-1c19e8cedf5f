#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@Desc    :   
@File    :   local_strategies_errcode.py
@Time    :   2023-08-23
<AUTHOR>   <EMAIL>
"""
class ResultCodeMsg(object):
    """
    错误信息基类
    """
    def __init__(self, code, msg, show_msg=""):
        """
        初始化
        """
        self.Code = code
        self.Msg = msg
        self.ShowMsg = show_msg

class ErrCode(object):
    """
    local_strategies 的错误码常量类
    """
    Success = ResultCodeMsg(0, "success", "success")

    UnknownError = ResultCodeMsg(200000, "未知异常", "系统异常")

    MethodNotFound = ResultCodeMsg(200001, "策略名称不存在: {}", "系统异常")

    MethodVersionError = ResultCodeMsg(200002, "策略版本信息异常或者不存在: {}", "系统异常")

    ParamsLost = ResultCodeMsg(200003, "策略入参缺失: {}", "系统异常")

    RecordGoalNodesFindError = ResultCodeMsg(200004, "录制页面的目标控件获取失败", "系统异常")

    StrictMatchError =  ResultCodeMsg(200005, "回放页面目标控件寻找失败", "匹配失败")

    ReplayBuildError =  ResultCodeMsg(200006, "回放页面建模失败", "系统异常")
    
    StepInfoScaleGetError =  ResultCodeMsg(200007, "获取步骤信息中的scale失败", "系统异常")

    FormatResultError =  ResultCodeMsg(200008, "格式化结果失败", "系统异常")

    NodesNumError =  ResultCodeMsg(200009, "多控件协同定位选择的控件数需要两个及以上", "多控件协同定位选择的控件数需要两个及以上")

    InputImageError =  ResultCodeMsg(200010, "输入图片异常: {}", "输入图片异常")

    ProcTimeoutError = ResultCodeMsg(200011, "处理超时: {}", "处理超时")

    # 匹配失败子问题分布
    RecordSingleLineMatchError = ResultCodeMsg(201001, "录制页面目标单行匹配失败，第{}行在回放页面无法匹配", "匹配失败")

    RecordMultiLineMatchError = ResultCodeMsg(201002, "录制页面目标多行间匹配失败，第{}和{}行在回放页面无法匹配", "匹配失败")

    RecordSingleElementMatchError = ResultCodeMsg(201003, "录制页面单目标控件匹配失败，id:{}控件在回放页面无法匹配", "匹配失败")

    ActionAreaNotInBaseError = ResultCodeMsg(201004, "目标控件控件不在基准区域内", "匹配失败")

    # UI自动化录制失败子问题分布
    UiRecordImagePathError = ResultCodeMsg(203000, "UI自动化录制失败，图片路径错误", "系统异常")

    UiMatchError = ResultCodeMsg(203001, "UI匹配失败", "匹配失败")

    UiMatchUserInfoError = ResultCodeMsg(203002, "匹配过程用户解析失败", "匹配失败")

    UiMatchResponseRectError = ResultCodeMsg(203005, "匹配返回坐标解析失败", "匹配失败")

    UiUserInputError = ResultCodeMsg(203003, "通用建模输入信息错误", "输入失败")

    UiInterMatchError = ResultCodeMsg(203006, "UI内部错误匹配失败", "匹配失败")

    # 弹窗点除相关问题
    PopupDetectFailed = ResultCodeMsg(204000, "弹窗能力调用失败", "弹窗能力调用失败")

    PopupDetectNotHit = ResultCodeMsg(204001, "没有检测到弹窗", "没有检测到弹窗")

    
