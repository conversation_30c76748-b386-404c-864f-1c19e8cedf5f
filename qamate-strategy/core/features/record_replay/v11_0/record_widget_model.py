#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@Desc    :
@File    :   record_widget_model.py
@Time    :   2024-05-28
<AUTHOR>   <EMAIL>
"""
import json
import traceback

import cv2

from basics.image.ui.business_exclusive_detect import BusinessExclusiveDetect
from basics.image.ui.math_position import cal_iou
from basics.text_process import merge_ocr, ocr_pattern
from basics.util import logger
from .basic import PROJECT_VERSION
from .lib.sum_map import SMap
from .multi_syne_record import MultiSyneRecordExecute
from ..base.batdom_node_type import BatdomNodeType
from ..base.execute_base import ExecuteBase
from ..base.local_strategies_errcode import ErrCode
from .single_feature_record import SingleFeatureRecordExecute
from .lib.dom_split_threshold import DomSplitThreshold as DST
from .lib.dom_element_handle import DomElementHandle
from ..base.widget_tag import SINGLE, MULTIPLE, VISUAL, SYSTEM


class RecordWidgetModelExecute(ExecuteBase):
    """
    通用建模接口，返回全部输入的控件，供前端自由选择
    """

    def __init__(self):
        """
        初始化
        """
        super().__init__()
        self.width = 0
        self.height = 0
        self.goal_feature_dict = {}
        self.diff_codes = []

    def extract_basic_info(self, params):
        """
        提取一些基本信息
        """
        img = cv2.imread(params["image_path"])
        self.height = img.shape[0]
        self.width = img.shape[1]

    def params_check(self, params, module_info_params, widget_info_params):
        """
        入参校验
        """
        lost_params = []

        if "module_info" not in params:
            return False, "module_info"

        for module_info in module_info_params:
            if module_info not in params["module_info"]:
                lost_params.append(module_info)

        if "widget_info" in params["module_info"]:
            for widget_info in widget_info_params:
                if widget_info not in params["module_info"]["widget_info"]:
                    lost_params.append(widget_info)

        if len(lost_params) == 0:
            return True, ""
        else:
            return False, ",".join(lost_params)

    def add_raw_id(self, dom, tmp_id):
        """
        添加一个临时id用于去重
        """
        tmp_id[0] += 1
        dom["tmp_id"] = tmp_id[0]
        if "children" in dom:
            for child in dom["children"]:
                self.add_raw_id(child, tmp_id)

    def get_dom_all_element(self, os_type, root_elements):
        """
        获取特定的有效dom元素
        """
        dom = {}
        if os_type == 1:
            # Android
            self.goal_feature_dict = {
                "text": [],
                "content-desc": [],
                "resource-id": [],
            }
        elif os_type == 2:
            # iOS
            self.goal_feature_dict = {
                "label": [],
                "name": [],
                "type": [
                    'Image', 'ScrollView', 'StaticText', 'Button', 'CollectionView', 'Cell', 'Table', 'TabBar'
                ]
            }
        self.dom_handler = DomElementHandle(self.width, self.height, os_type, self.goal_feature_dict)

        if os_type == 1:
            # Android
            dom = self.dom_handler.android_init(root_elements)
        elif os_type == 2:
            # iOS
            dom = root_elements

        # 添加一个临时id用于去重
        raw_id = [-1]
        self.add_raw_id(dom, raw_id)
        logger.debug("input_raw_dom: {}".format(json.dumps(dom)))

        # 先对dom进行前置过滤
        self.dom_handler.dom_pre_filter(dom)
        logger.debug("dom_pre_filter: {}".format(json.dumps(dom)))

        dfe_info = []
        self.dom_handler.recursive_gdfe(dom, dfe_info)
        logger.debug("dfe_info: {}".format(dfe_info))
        doe_info = []
        doe_info = self.dom_handler.get_dom_other_element(dom, dfe_info)
        logger.info("doe_info_len: {}".format(len(doe_info)))
        logger.debug("doe_info: {}".format(doe_info))

        return dfe_info, doe_info

    def format_ocr_info(self, ocr_info):
        """
        格式化文本信息
        """
        ocr_list = []
        ext_icon_list = []
        ext_ele_list = []

        try:
            ocr_ret = ocr_info["ret"]
            for ret in ocr_ret:
                word = ret["word"]
                if len(word) == 1:
                    # 如果是单个特殊字符，将类型提升为icon或Component
                    if word in ('x', 'X', '✗', '✘', '×', '✕', '☓', '✖'):
                        iterm = {
                            "type": "Icon",
                            "rect": {
                                "x": ret["rect"]["left"],
                                "y": ret["rect"]["top"],
                                "width": ret["rect"]["width"],
                                "height": ret["rect"]["height"]
                            },
                            "name": "close",
                            "tag": [SINGLE, MULTIPLE, VISUAL]
                        }
                        ext_icon_list.append(iterm)
                        continue
                    if word in ('+', '十', '✚', '✛', '✜', '✝'):
                        iterm = {
                            "type": "Icon",
                            "rect": {
                                "x": ret["rect"]["left"],
                                "y": ret["rect"]["top"],
                                "width": ret["rect"]["width"],
                                "height": ret["rect"]["height"]
                            },
                            "name": "add",
                            "tag": [SINGLE, MULTIPLE, VISUAL]
                        }
                        ext_icon_list.append(iterm)
                        continue
                    if word in ('一', '-', '_', '‐', '‑', '‒', '–', '—', '―', '−', '¯', 'ˉ', '⎯', '₋'):
                        iterm = {
                            "type": "Component",
                            "rect": {
                                "x": ret["rect"]["left"],
                                "y": ret["rect"]["top"],
                                "width": ret["rect"]["width"],
                                "height": ret["rect"]["height"]
                            },
                            "name": "icon",
                            "tag": [SINGLE, MULTIPLE, VISUAL]
                        }
                        ext_ele_list.append(iterm)
                        continue

                w = ocr_pattern.drop_not_in_pattern(word)
                if len(w) == 0:
                    continue
                new_charset = [c for c in ret['charset'] if c['word'] in w]
                iterm = {
                    "type": "Text",
                    "rect": {
                        "x": ret["rect"]["left"],
                        "y": ret["rect"]["top"],
                        "width": ret["rect"]["width"],
                        "height": ret["rect"]["height"]
                    },
                    "word": w,
                    "charset": new_charset,
                    "tag": [SINGLE, MULTIPLE, VISUAL]
                }
                ocr_list.append(iterm)
        except:
            logger.error("format_ocr_info Error: {}".format(traceback.format_exc()))
        return ocr_list, ext_icon_list, ext_ele_list

    def format_beta_info(self, beta_info):
        """
        格式化文本信息
        """
        beta_list = []
        try:
            detect_res = beta_info.get("detect_res", [])
            for ret in detect_res:
                iterm = {
                    "type": BatdomNodeType.ICON.name,
                    "product_id": ret.get("product_id", None),
                    "rect": {
                        "x": ret["left"],
                        "y": ret["top"],
                        "width": ret["width"],
                        "height": ret["height"]
                    },
                    "conf": ret["confidence"],
                    "name": ret["name"],
                    "tag": [SINGLE, MULTIPLE, VISUAL]
                }
                beta_list.append(iterm)
        except:
            logger.error("format_beta_info Error: {}".format(traceback.format_exc()))
        return beta_list

    def format_icon_info(self, icon_info, comp_info):
        """
        格式化文本信息
        """
        icon_list = []
        try:
            detect_res = icon_info["detect_res"]
            for ret in detect_res:
                iterm = {
                    "type": "Icon",
                    "rect": {
                        "x": ret["left"],
                        "y": ret["top"],
                        "width": ret["width"] - 1,
                        "height": ret["height"] - 1
                    },
                    "name": ret["name"],
                    "tag": [SINGLE, MULTIPLE, VISUAL]
                }
                if 'diff_code' in ret:
                    iterm["diffCode"] = ret["diff_code"]
                    self.diff_codes.append(ret["diff_code"])
                icon_list.append(iterm)

            # 组件的search box也算作icon
            for ret in comp_info["detect_res"]:
                if ret["name"] == "search_box":
                    iterm = {
                        "type": "Icon",
                        "rect": {
                            "x": ret["left"],
                            "y": ret["top"],
                            "width": ret["width"] - 1,
                            "height": ret["height"] - 1
                        },
                        "name": ret["name"],
                        "tag": [SINGLE, MULTIPLE, VISUAL]
                    }
                    icon_list.append(iterm)
        except:
            pass
        return icon_list

    def format_component_info(self, component_info):
        """
        格式化文本信息
        """
        component_list = []
        try:
            detect_res = component_info["detect_res"]
            for ret in detect_res:
                if ret["name"] == "search_box":
                    # 排除search box
                    continue
                iterm = {
                    "type": "Component",
                    "rect": {
                        "x": ret["left"],
                        "y": ret["top"],
                        "width": ret["width"] - 1,
                        "height": ret["height"] - 1
                    },
                    "name": ret["name"],
                    "tag": [SINGLE, MULTIPLE, VISUAL]
                }
                component_list.append(iterm)
        except:
            pass
        return component_list

    def format_dfe_info(self, dfe_info):
        """
        格式化dfe信息
        """
        try:
            for dfe in dfe_info:
                dfe["tag"] = [SINGLE, MULTIPLE, SYSTEM]
        except:
            pass

    def format_doe_info(self, doe_info):
        """
        格式化dfe信息
        """
        try:
            for doe in doe_info:
                doe["tag"] = [MULTIPLE, SYSTEM]
        except:
            pass

    def remove_ocr_overlap(self, raw_ocr_info: list, smap):
        """
        检查ocr识别到的文字符是否在icon模型或beta模型中，如果在，就过滤掉这些字符，更新ocr行矩形框
        参考：https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/3LoFUu-M_a/LWyELLqriZ/NXeWhHUhXpQN6m
        smap: beta和icon的所有矩形框构建出来的累加和矩阵,用于减少判断重叠的时间复杂度
        """

        def reshape_ocr(chars):
            """
            重新计算ocr矩形的面积
            """

            x1, y1, x2, y2 = None, None, None, None
            for ch in chars:
                rect = ch["rect"]
                r = [rect["left"], rect["top"], rect["left"] + rect["width"], rect["top"] + rect["height"]]
                if x1 is None:
                    x1, y1, x2, y2 = r
                x1 = min(x1, r[0])
                y1 = min(y1, r[1])
                x2 = max(x2, r[2])
                y2 = max(y2, r[3])
            return x1, y1, x2, y2

        # 待删除列表
        remove_list = []
        # 待添加列表
        add_list = []

        for ocr in raw_ocr_info:
            logger.debug("ocr {}".format(ocr))
            charset = ocr["charset"][:]
            old_len = len(charset)
            groups = [[]]
            for ch in ocr["charset"]:
                rect = ch["rect"]
                r = [rect["left"], rect["top"], rect["left"] + rect["width"], rect["top"] + rect["height"]]
                area = (r[2] - r[0]) * (r[3] - r[1])
                cross_area = smap.cross_area(rect=r)
                ov = cross_area / (area + 1e-6)
                if ov > DST.MAX_OCR_ICON_OVERLAP:
                    logger.debug("remove ocr char, ocr {}, char {}".format(ocr, ch))
                    charset.remove(ch)
                    # 如果文本被从中间截断，就分成多组charset
                    groups.append([])
                else:
                    groups[-1].append(ch)
            new_len = len(charset)

            if new_len < old_len:
                remove_list.append(ocr)
                for group in groups:
                    if len(group) == 0:
                        continue
                    x1, y1, x2, y2 = reshape_ocr(group)
                    new_ocr = {
                        "rect": {
                            "left": x1,
                            "top": y1,
                            "width": x2 - x1,
                            "height": y2 - y1
                        },
                        "charset": group,
                        "word": "".join([c["word"] for c in group])
                    }
                    add_list.append(new_ocr)

        for rm in remove_list:
            logger.debug("remove ocr line {}".format(rm))
            raw_ocr_info.remove(rm)
        for ad in add_list:
            logger.debug("add ocr line {}".format(ad))
            raw_ocr_info.append(ad)

    def remove_ocr_in_icon(self, icon_info, beta_info, ocr_info):
        """
        除去与icon模型或beta模型检测框重叠的ocr字符，并reshape行矩形框
        """
        tmp_rects = []
        raw_icon_info = icon_info.get("detect_res", [])
        raw_ocr_info = ocr_info.get("ret", [])
        raw_beta_info = beta_info.get("detect_res", [])
        for icon in raw_icon_info:
            logger.debug("icon: {}".format(icon))
            x1, y1, x2, y2 = icon['left'], icon['top'], icon['left'] + icon['width'], icon['top'] + icon['height']
            tmp_rects.append([x1, y1, x2, y2])
        for icon in raw_beta_info:
            x1, y1, x2, y2 = icon['left'], icon['top'], icon['left'] + icon['width'], icon['top'] + icon['height']
            tmp_rects.append([x1, y1, x2, y2])
        # 构建累加和矩阵
        smap = SMap(shape=(self.width, self.height), rects=tmp_rects)
        self.remove_ocr_overlap(raw_ocr_info, smap=smap)

    def remove_low_conf_obj(self, icon_info, beta_info, ele_info):
        """
        FIXME 临时逻辑暂时加在这里，后续会放入更合适的位置
            移除低置信度低的目标检测框，beta、icon、element模型的识别结果都会过滤
        """

        def rm_low_conf_rects(rect_list: list):
            rm_list = [r for r in rect_list if r["confidence"] < DST.MIN_OBJ_DETECT_CONF]
            logger.debug("remove {} object".format(len(rm_list)))
            for rm in rm_list:
                rect_list.remove(rm)

        raw_icon_info = icon_info.get("detect_res", [])
        rm_low_conf_rects(raw_icon_info)
        raw_beta_info = beta_info.get("detect_res", [])
        rm_low_conf_rects(raw_beta_info)
        raw_ele_info = ele_info.get("detect_res", [])
        rm_low_conf_rects(raw_ele_info)

    def remove_charset(self, ocr_info):
        """
        如果是对录制页面建模，就调用这个逻辑，删除所有charset避免json过大
        如果是对回放页面建模，就不调用这个逻辑，为回放的点击坐标提供依据
        """
        for text_area in ocr_info:
            for text in text_area['children']:
                del text['charset']

    def add_unique_ids(self, widgets, current_id):
        """
        为控件列表中的每个控件及其子控件添加递增的unique id。

        :param widgets: 控件列表
        :param current_id: 起始id
        :return: 无
        """
        for item in widgets:
            item['id'] = current_id[0]  # 添加id字段
            current_id[0] += 1

            # 如果控件有子控件，则递归地添加id
            if 'children' in item:
                self.add_unique_ids(item['children'], current_id)

    def format_params(self, params):
        """
        格式化输入参数，并返回格式化后的参数。

        :param params: 输入参数
        :return: 格式化后的参数
        """
        new_param = {}
        ocr_info_raw = {}
        beta_info_raw = {}
        icon_info_raw = {}
        ele_info_raw = {}
        dfe_info = []
        doe_info = []
        used_widget_list = []

        # 提取输入组件的原信息
        widget_info = params["module_info"]["widget_info"]
        used_ocr_info = widget_info["ocr_info"].get("used", False)
        if used_ocr_info:
            # 传入了ocr_info
            ocr_info_raw = widget_info["ocr_info"]["data"]
            used_widget_list.append("ocr_info")

        used_beta_info = widget_info["beta_info"].get("used", False)
        if used_beta_info:
            # 传入了beta_info
            beta_info_raw = widget_info["beta_info"]["data"]
            used_widget_list.append("beta_info")

        used_icon_info = widget_info["icon_info"].get("used", False)
        if used_icon_info:
            # 传入了icon_info
            icon_info_raw = widget_info["icon_info"]["data"]
            used_widget_list.append("icon_info")

        used_ele_info = widget_info["ele_info"].get("used", False)
        if used_ele_info:
            # 传入了组件ele_info
            ele_info_raw = widget_info["ele_info"]["data"]
            used_widget_list.append("ele_info")

        used_dom_info = widget_info["dom_info"].get("used", False)
        if used_dom_info:
            # 传入了dom_info，提取dfe, doe控件
            dom_info = widget_info["dom_info"]["data"]
            dfe_info, doe_info = self.get_dom_all_element(params["module_info"]["os_type"], dom_info)
            used_widget_list.append("dom_info")
            logger.info("dfe_info_len: {}, doe_info_len: {}".format(len(dfe_info), len(doe_info)))

        # ocr和图标模型处理
        # 除去与icon模型或beta模型检测框重叠的ocr字符（必须在这个阶段执行，format后会丢失charset信息）
        self.remove_ocr_in_icon(icon_info_raw, beta_info_raw, ocr_info_raw)
        # FIXME 临时逻辑 过滤beta模型、icon模型、component模型所有低confidence的结果
        # self.remove_low_conf_obj(icon_info_raw, beta_info_raw, ele_info_raw)

        # 格式化输入信息, 加tag
        ocr_info = []
        beta_info = []
        icon_info = []
        ele_info = []
        # 通过ocr提升为icon或ele的列表
        ocr_icon_info = []
        ocr_ele_info = []

        if used_ocr_info:
            ocr_info, ocr_icon_info, ocr_ele_info = self.format_ocr_info(ocr_info_raw)
        if used_beta_info:
            beta_info = self.format_beta_info(beta_info_raw)
        if used_ele_info:
            icon_info = self.format_icon_info(icon_info_raw, ele_info_raw)
        if used_ele_info:
            ele_info = self.format_component_info(ele_info_raw)
        self.format_dfe_info(dfe_info)
        self.format_doe_info(doe_info)

        icon_info.extend(ocr_icon_info)
        ele_info.extend(ocr_ele_info)

        # 合并ocr信息
        ocr_info = merge_ocr.merge_rects_in_origin_format(ocr_info)
        if 'replaying' not in params or not params['replaying']:
            self.remove_charset(ocr_info)

        new_param['ocr_info'] = ocr_info
        new_param['beta_info'] = beta_info
        new_param['icon_info'] = icon_info
        new_param['ele_info'] = ele_info
        new_param['dfe_info'] = dfe_info
        new_param['doe_info'] = doe_info

        # 添加debug用的id，之后用于单控件/多控件建模结果的union
        current_id = [1]
        for key in new_param.keys():
            self.add_unique_ids(new_param[key], current_id)

        new_param['image_path'] = params["module_info"]["image_path"]
        new_param['os_type'] = params["module_info"]["os_type"]
        new_param['replaying'] = params.get('replaying', False)

        return new_param, used_widget_list

    def get_union_dom(self, sfr_dom, msr_dom):
        """
        合并单控件和多控件的结果，并返回合并后的结果。

        :param sfr_dom: 单控件结果
        :param msr_dom: 多控件结果
        :return: 合并后的结果
        """
        logger.debug("sfr_dom: {}".format(sfr_dom))
        logger.debug("msr_dom: {}".format(msr_dom))

        union_dom = {}
        # 遍历多控件结果，加入union字典中；注：多控件目前是list，如果增加children层级，需要特别处理
        for widget in msr_dom:
            logger.debug("[Batdom Debug] msr widget:{}".format(widget))
            widget_id = widget["ext"]["id"]
            union_dom[widget_id] = widget

        # 遍历单控件结果，进行合并
        for widget in sfr_dom:
            logger.debug("[Batdom Debug] sfr widget:{}".format(widget))
            widget_id = widget["ext"]["id"]
            if widget_id not in union_dom:
                union_dom[widget_id] = widget

        # 将合并后的字典转换回列表形式
        return list(union_dom.values())

    def update_debug_id(self, union_dom, id_ref=None):
        """
        FIXME 临时逻辑，更新debug_id
            解决debug_id冲突问题
        """
        if id_ref is None:
            id_ref = [0]
        if len(union_dom) == 0:
            return
        for widget in union_dom:
            if 'debug' in widget:
                id_ref[0] += 1
                widget['debug']['id'] = id_ref[0]
            if 'children' in widget:
                self.update_debug_id(widget['children'], id_ref)

    def main(self, params):
        """
        主流程函数
        """
        try:
            # 入参校验
            params_check, lost_params = self.params_check(
                params,
                ["widget_info", "os_type", "image_path"],
                ["ocr_info", "icon_info", "ele_info", "beta_info", "dom_info"]
            )
            if params_check is False:
                logger.error("record_widget_model missing params: {}".format(lost_params))
                result = {
                    "code": ErrCode.ParamsLost.Code,
                    "msg": ErrCode.ParamsLost.Msg.format(lost_params)
                }
                return result

            logger.debug("[Batdom Debug] raw_params: {}".format(params))

            # 如果params传递了product_id，则调用easy_detect进行检测
            if "easy_detect" in params:
                detect_tool = BusinessExclusiveDetect(params["easy_detect"])
                img = params["module_info"]["image_path"]
                current_res = detect_tool.main(img)
                beta_info = params["module_info"]["widget_info"]["beta_info"]["data"]

                if "detect_res" in beta_info:
                    # 去重
                    for c_item in current_res:
                        is_repeat = False
                        index = 0
                        for item in beta_info["detect_res"]:
                            if c_item["name"] == item["name"]:
                                index += 1
                                # 计算iou，大于0.5则认为是重复
                                if cal_iou(item, c_item) > 0.5:
                                    is_repeat = True
                                    break

                        # 如果不重复，则添加，并且编号接在beta_info之后；重复则不添加
                        if is_repeat is False:
                            c_item["index"] = index
                            beta_info["detect_res"].append(c_item)
                else:
                    beta_info["detect_res"] = current_res

            # 格式化输入参数：提取入参，ocr处理、ocr合并，图标模型筛选，添加 id
            self.extract_basic_info(params["module_info"])
            new_param, used_widget_list = self.format_params(params)
            logger.debug("[Batdom Debug] new_params: {}".format(new_param))

            # 调用单控件建模
            sfr = SingleFeatureRecordExecute(self.goal_feature_dict)
            sfr_result = sfr.main(new_param)
            if sfr_result["code"] != ErrCode.Success.Code:
                return sfr_result
            sfr_dom = sfr_result["dom"]

            # 调用多控件建模
            msr = MultiSyneRecordExecute(self.goal_feature_dict)
            msr_result = msr.main(new_param)
            if msr_result["code"] != ErrCode.Success.Code:
                return msr_result
            mfr_dom = msr_result["dom"]

            union_dom = self.get_union_dom(sfr_dom["children"], mfr_dom["children"])
            self.update_debug_id(union_dom)

            # 组合结构, 添加页面信息
            res_data = {
                "type": "Page",
                "ext": {"id": 0},  # 页面id为0
                "debug": {"id": 0},
                "rect": {
                    "x": 0,
                    "y": 0,
                    "w": self.width,
                    "h": self.height
                },
                "children": union_dom
            }
            result = {
                "code": ErrCode.Success.Code,
                "msg": ErrCode.Success.Msg,
                "data": {
                    "dom": res_data,
                    "info": {
                        "version": PROJECT_VERSION,
                        "used_widget_list": used_widget_list,
                        "name": "recordWidgetModel",
                    }
                }
            }
            logger.debug("[Batdom Debug]result: {}".format(json.dumps(result)))

            return result

        except Exception as e:
            logger.error("record_widget_model uncaught Error: {}".format(traceback.format_exc()))
            result = {
                "code": ErrCode.UnknownError.Code,
                "msg": ErrCode.UnknownError.Msg
            }
            return result
