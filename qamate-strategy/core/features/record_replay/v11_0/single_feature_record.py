#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@Desc    :   
@File    :   single_feature_record.py
@Time    :   2024-02-09
<AUTHOR>   x<PERSON><PERSON><PERSON>@baidu.com
"""
import traceback
import json
import cv2

from basics.util import logger
from basics.text_process import merge_ocr, ocr_pattern
from .lib.name_reflector import get_icon_name_dict, get_beta_icon_name_dict
from ..base.batdom_node_type import BatdomNodeType
from ..base.widget_tag import SINGLE, MULTIPLE, VISUAL, SYSTEM
from ..base.execute_base import ExecuteBase
from ..base.local_strategies_errcode import ErrCode
from .lib.dom_split_threshold import DomSplitThreshold as DST
from .lib.dom_element_handle import DomElementHandle
from .lib.sum_map import SMap

from .basic import PROJECT_VERSION
import numpy as np


class SingleFeatureRecordExecute(ExecuteBase):
    """
    录制过程类
    """

    def __init__(self, goal_feature_dict):
        """
        初始化
        """
        super().__init__()
        self.debug_flag = False
        self.goal_feature_dict = goal_feature_dict
        self.width = 0
        self.height = 0
        self.dom_handler = None

    def extract_basic_info(self, params):
        """
        提取一些基本信息
        """
        img = cv2.imread(params["image_path"])
        self.height = img.shape[0]
        self.width = img.shape[1]

    def check_params_by_model_type(self, params, model_type):
        """
        根据model_type判断入参是否完整
        """
        if int(model_type) == 2:
            if "root_elements" not in params:
                return False
        return True

    def copy_node_exclude(self, copy_node, exclude_keys):
        """
        排除指定属性的复制
        """
        node = {}
        for key in copy_node.keys():
            if key not in exclude_keys:
                node[key] = copy_node[key]
        return node

    def recursive_gdfe(self, dom, goal_dom_list):
        """
        递归获取制定的元素
        """
        for dom_feature in self.goal_feature_dict:
            if dom_feature in dom and dom[dom_feature] is not None:
                # 过滤掉空字符串
                if dom[dom_feature] == "":
                    continue

                # 有指定值范围的，进行过滤
                if len(self.goal_feature_dict[dom_feature]) > 0:
                    if dom[dom_feature] not in self.goal_feature_dict[dom_feature]:
                        continue

                goal_dom_list.append(self.copy_node_exclude(dom, ["children"]))

        if "children" in dom:
            for child in dom["children"]:
                self.recursive_gdfe(child, goal_dom_list)

    def recursive_android_dom_elements(self, raw_node):
        """
        递归获取 Android 的 elements 列表
        """
        processed_dom = {}
        try:
            attributes = raw_node["_attributes"]
            # 处理需要保留的属性信息
            processed_dom["type"] = attributes["class"].split(".")[-1]

            # 处理其它属性
            for key in attributes.keys():
                if key in ["class", "bounds"]:
                    continue
                processed_dom[key] = attributes[key]

            # 获取坐标信息
            bounds_str = attributes["bounds"]
            rect_str = bounds_str.replace("][", ",").replace("[", "").replace("]", "")
            rect_list = rect_str.split(",")
            processed_dom["rect"] = {
                "x": int(rect_list[0]),
                "y": int(rect_list[1]),
                "width": int(rect_list[2]) - int(rect_list[0]),
                "height": int(rect_list[3]) - int(rect_list[1])
            }
        except:
            return None

        # children
        processed_dom["children"] = []
        if "node" in raw_node:
            nodes = raw_node["node"]
            if type(nodes) is dict:
                child = self.recursive_android_dom_elements(nodes)
                if child is not None:
                    processed_dom["children"].append(child)
            elif type(nodes) is list:
                for node in nodes:
                    child = self.recursive_android_dom_elements(node)
                    if child is not None:
                        processed_dom["children"].append(child)
        return processed_dom

    def android_init(self, dom):
        """
        Android 初始化
        """
        res_dom = {
            "type": "App",
            "children": []
        }

        # 最外层数据处理
        main_area = dom["rect"]
        res_dom["rect"] = {
            "x": 0,
            "y": 0,
            "width": main_area["x"] + main_area["width"],
            "height": main_area["y"] + main_area["height"],
        }

        if "hierarchy" in dom and "node" in dom["hierarchy"]:
            if type(dom["hierarchy"]["node"]) is list:
                for node in dom["hierarchy"]["node"]:
                    child = self.recursive_android_dom_elements(node)
                    if child is not None:
                        res_dom["children"].append(child)
            elif type(dom["hierarchy"]["node"]) is dict:
                child = self.recursive_android_dom_elements(dom["hierarchy"]["node"])
                if child is not None:
                    res_dom["children"].append(child)
        return res_dom

    def get_dom_feature_element(self, os_type, dom_info):
        """
        获取特定的有效dom元素
        """
        dom = {}
        if os_type == 1:
            # Android
            self.goal_feature_dict = {
                "text": [],
                "content-desc": [],
                "resource-id": [],
            }
        elif os_type == 2:
            # iOS
            self.goal_feature_dict = {
                "label": [],
                "name": [],
                "type": [
                    'Image', 'ScrollView', 'StaticText', 'Button', 'CollectionView', 'Cell', 'Table', 'TabBar'
                ]
            }

        logger.debug("dom_info: {}".format(json.dumps(dom_info)))
        self.dom_handler = DomElementHandle(self.width, self.height, os_type, self.goal_feature_dict)

        if os_type == 1:
            # Android
            dom = self.dom_handler.android_init(dom_info)
        elif os_type == 2:
            # iOS
            dom = dom_info

        logger.debug("raw_dom: {}".format(json.dumps(dom)))
        self.dom_handler.dom_pre_filter(dom)
        logger.debug("dom_pre_filter: {}".format(json.dumps(dom)))

        dfe_info = []
        self.dom_handler.recursive_gdfe(dom, dfe_info)
        return dfe_info

    def format_ocr_info(self, ocr_info):
        """
        格式化文本信息
        """
        ocr_list = []
        try:
            ocr_ret = ocr_info["ret"]
            for ret in ocr_ret:
                w = ocr_pattern.drop_not_in_pattern(ret["word"])
                if len(w) == 0:
                    continue
                new_charset = [c for c in ret['charset'] if c['word'] in w]
                iterm = {
                    "type": "Text",
                    "rect": {
                        "x": ret["rect"]["left"],
                        "y": ret["rect"]["top"],
                        "width": ret["rect"]["width"],
                        "height": ret["rect"]["height"]
                    },
                    "word": w,
                    "charset": new_charset,
                    "tag": [SINGLE, MULTIPLE, VISUAL]
                }
                ocr_list.append(iterm)
        except:
            logger.error("format_ocr_info Error: {}".format(traceback.format_exc()))
        return ocr_list

    def format_beta_info(self, beta_info):
        """
        格式化文本信息
        """
        beta_list = []
        try:
            detect_res = beta_info.get("detect_res", [])
            for ret in detect_res:
                iterm = {
                    "type": BatdomNodeType.ICON.name,
                    "rect": {
                        "x": ret["left"],
                        "y": ret["top"],
                        "width": ret["width"],
                        "height": ret["height"]
                    },
                    "conf": ret["confidence"],
                    "name": ret["name"],
                    "tag": [SINGLE, MULTIPLE, VISUAL]
                }
                beta_list.append(iterm)
        except:
            logger.error("format_beta_info Error: {}".format(traceback.format_exc()))
        return beta_list

    def format_icon_info(self, icon_info, comp_info):
        """
        格式化文本信息
        """
        icon_list = []
        try:
            detect_res = icon_info["detect_res"]
            for ret in detect_res:
                iterm = {
                    "type": "Icon",
                    "rect": {
                        "x": ret["left"],
                        "y": ret["top"],
                        "width": ret["width"] - 1,
                        "height": ret["height"] - 1
                    },
                    "name": ret["name"],
                    "tag": [SINGLE, MULTIPLE, VISUAL]
                }
                icon_list.append(iterm)

            # 组件的search box也算作icon
            for ret in comp_info["detect_res"]:
                if ret["name"] == "search_box":
                    iterm = {
                        "type": "Icon",
                        "rect": {
                            "x": ret["left"],
                            "y": ret["top"],
                            "width": ret["width"] - 1,
                            "height": ret["height"] - 1
                        },
                        "name": ret["name"],
                        "tag": [SINGLE, MULTIPLE, VISUAL]
                    }
                    icon_list.append(iterm)
        except:
            pass
        return icon_list

    def format_component_info(self, component_info):
        """
        格式化文本信息
        """
        component_list = []
        try:
            detect_res = component_info["detect_res"]
            for ret in detect_res:
                if ret["name"] == "search_box":
                    # 排除search box
                    continue
                iterm = {
                    "type": "Component",
                    "rect": {
                        "x": ret["left"],
                        "y": ret["top"],
                        "width": ret["width"] - 1,
                        "height": ret["height"] - 1
                    },
                    "name": ret["name"],
                    "tag": [SINGLE, MULTIPLE, VISUAL]
                }
                component_list.append(iterm)
        except:
            pass
        return component_list

    def add_debug_info(self, dom, index_list, parent_ids):
        """
        增加debug信息
        """
        index_list[0] += 1
        index = index_list[0]

        dom["debug"] = {
            "id": index,
            "parents": parent_ids,
        }

        newparent_ids = []
        for id in parent_ids:
            newparent_ids.append(id)
        newparent_ids.append(index)

        if "children" in dom:
            for child in dom["children"]:
                self.add_debug_info(child, index_list, newparent_ids)

    def get_node_cross_area(self, p, c):
        """
        获取 p 和 c 的相交面积
        """
        p_x1 = p["rect"]["x"]
        p_x2 = p["rect"]["x"] + p["rect"]["w"]
        p_y1 = p["rect"]["y"]
        p_y2 = p["rect"]["y"] + p["rect"]["h"]

        c_x1 = c["rect"]["x"]
        c_x2 = c["rect"]["x"] + c["rect"]["w"]
        c_y1 = c["rect"]["y"]
        c_y2 = c["rect"]["y"] + c["rect"]["h"]
        # 计算重叠部分面积
        maxx = max(p_x1, c_x1)
        minx = min(p_x2, c_x2)
        maxy = max(p_y1, c_y1)
        miny = min(p_y2, c_y2)
        cross_area = 0
        if minx > maxx and miny > maxy:
            cross_area = (maxx - minx) * (maxy - miny)
        return cross_area

    def check_element_overlap(self, node, word):
        """
        判断两个元素是否几乎重叠
        """
        node_area = node["rect"]["w"] * node["rect"]["h"]
        word_area = word["rect"]["w"] * word["rect"]["h"]

        # 重合部分面积
        cross_area = self.get_node_cross_area(node, word)

        # 取node和word面积的并集
        union_area = node_area + word_area - cross_area

        word_cross_ratio = 0
        if word_area > 0:
            word_cross_ratio = 100.0 * cross_area / union_area

        if word_cross_ratio >= DST.ALMOST_OVERLAP_RATIO:
            return True

        # # 如果存在包含的关系，也返回True
        # if cross_area >= word_area or cross_area >= node_area:
        #     return True

        return False

    def check_overlap_with_existing(self, res, element):
        """
        检查元素是否与res中的任何元素重叠
        """
        for existing in res:
            if self.check_element_overlap(element, existing):
                return True
        return False

    def remove_ocr_overlap(self, raw_ocr_info: list, smap):
        """
        检查ocr识别到的文字符是否在icon模型或beta模型中，如果在，就过滤掉这些字符，更新ocr行矩形框
        参考：https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/3LoFUu-M_a/LWyELLqriZ/NXeWhHUhXpQN6m
        smap: beta和icon的所有矩形框构建出来的累加和矩阵,用于减少判断重叠的时间复杂度
        """

        def reshape_ocr(chars):
            """
            重新计算ocr矩形的面积
            """

            x1, y1, x2, y2 = None, None, None, None
            for ch in chars:
                rect = ch["rect"]
                r = [rect["left"], rect["top"], rect["left"] + rect["width"], rect["top"] + rect["height"]]
                if x1 is None:
                    x1, y1, x2, y2 = r
                x1 = min(x1, r[0])
                y1 = min(y1, r[1])
                x2 = max(x2, r[2])
                y2 = max(y2, r[3])
            return x1, y1, x2, y2

        # 待删除列表
        remove_list = []
        # 待添加列表
        add_list = []

        for ocr in raw_ocr_info:
            logger.debug("ocr {}".format(ocr))
            charset = ocr["charset"][:]
            old_len = len(charset)
            groups = [[]]
            for ch in ocr["charset"]:
                rect = ch["rect"]
                r = [rect["left"], rect["top"], rect["left"] + rect["width"], rect["top"] + rect["height"]]
                area = (r[2] - r[0]) * (r[3] - r[1])
                cross_area = smap.cross_area(rect=r)
                ov = cross_area / area
                if ov > DST.MAX_OCR_ICON_OVERLAP:
                    logger.debug("remove ocr char, ocr {}, char {}".format(ocr, ch))
                    charset.remove(ch)
                    # 如果文本被从中间截断，就分成多组charset
                    groups.append([])
                else:
                    groups[-1].append(ch)
            new_len = len(charset)

            if new_len < old_len:
                remove_list.append(ocr)
                for group in groups:
                    if len(group) == 0:
                        continue
                    x1, y1, x2, y2 = reshape_ocr(group)
                    new_ocr = {
                        "rect": {
                            "left": x1,
                            "top": y1,
                            "width": x2 - x1,
                            "height": y2 - y1
                        },
                        "charset": group,
                        "word": "".join([c["word"] for c in group])
                    }
                    add_list.append(new_ocr)

        for rm in remove_list:
            logger.debug("remove ocr line {}".format(rm))
            raw_ocr_info.remove(rm)
        for ad in add_list:
            logger.debug("add ocr line {}".format(ad))
            raw_ocr_info.append(ad)

    def build_structure(self, ocr_info, beta_info, icon_info, ele_info, dfe_info, replaying=False):
        """
        组合元素，并且完成 index 排序
        """
        res_children = []

        # beta_info 的录入
        beta_icon_name_dict = get_beta_icon_name_dict()
        beta_info.sort(key=lambda x: (x["rect"]["y"], x["rect"]["x"]))
        beta_index_dict = {}
        for beta in beta_info:
            if beta["name"] not in beta_index_dict:
                beta_index_dict[beta["name"]] = -1
            beta_index_dict[beta["name"]] += 1
            beta_index = beta_index_dict[beta["name"]]
            element = {
                "type": BatdomNodeType.ICON.name,
                "ext": {
                    "name": "{}_beta".format(beta["name"]),
                    "cname": "{}_beta".format(beta_icon_name_dict.get(beta["name"], beta["name"])),
                    "index": beta_index,
                    "product_id": beta.get("product_id", None),
                    "tag": beta["tag"],
                    "id": beta["id"],
                },
                "debug": {},
                "rect": {
                    "x": beta["rect"]["x"],
                    "y": beta["rect"]["y"],
                    "w": beta["rect"]["width"],
                    "h": beta["rect"]["height"],
                },
                "children": []
            }
            res_children.append(element)

        # icon_info 的排序 & 录入
        icon_name_dict = get_icon_name_dict()
        icon_info.sort(key=lambda x: (x["rect"]["y"], x["rect"]["x"]))
        icon_index_dict = {}
        for icon in icon_info:
            if icon["name"] not in icon_index_dict:
                icon_index_dict[icon["name"]] = -1
            icon_index_dict[icon["name"]] += 1
            icon_index = icon_index_dict[icon["name"]]
            element = {
                "type": "Icon",
                "ext": {
                    "name": icon["name"],
                    "cname": icon_name_dict.get(icon["name"], icon["name"]),
                    "index": icon_index,
                    "tag": icon["tag"],
                    "id": icon["id"],
                },
                "debug": {},
                "rect": {
                    "x": icon["rect"]["x"],
                    "y": icon["rect"]["y"],
                    "w": icon["rect"]["width"],
                    "h": icon["rect"]["height"],
                },
                "children": []
            }
            if not self.check_overlap_with_existing(res_children, element):
                res_children.append(element)
            else:
                logger.info("Single_feature overlapping icon element: {}".format(element))

        # 拆分component_info: component_stable_info加入dom输出的稳定组件中
        component_stable_info = []
        component_stable_list = ["icon"]  # 稳定组件
        logger.info("component_stable_list: {}".format(json.dumps(component_stable_list)))
        for component in ele_info:
            if component["name"] in component_stable_list:
                component_stable_info.append(component)
        logger.info("single_feature component_icon: {}".format(component_stable_info))

        # 稳定component_stable_info 的录入
        for component in component_stable_info:
            element = {
                "type": BatdomNodeType.COMPONENT.name,
                "ext": {
                    "name": component["name"],
                    "tag": [SINGLE, MULTIPLE, VISUAL],
                    "id": component["id"],
                },
                "debug": {},
                "rect": {
                    "x": component["rect"]["x"],
                    "y": component["rect"]["y"],
                    "w": component["rect"]["width"],
                    "h": component["rect"]["height"],
                },
                "children": []
            }
            if not self.check_overlap_with_existing(res_children, element):
                res_children.append(element)
            else:
                logger.info("Single_feature overlapping component element: {}".format(element))

        # ocr_info 的录入
        for ocr in ocr_info:
            element = {
                "type": ocr['type'],
                "ext": {
                    "text": ocr["word"],
                    "tag": ocr["tag"],
                    "id": ocr["id"],
                },
                "debug": {},
                "rect": {
                    "x": ocr["rect"]["x"],
                    "y": ocr["rect"]["y"],
                    "w": ocr["rect"]["width"],
                    "h": ocr["rect"]["height"],
                },
                "children": []
            }
            if 'charset' in ocr:
                element['charset'] = ocr['charset']
            if ocr['children'] and len(ocr['children']) > 0:
                for item in ocr['children']:
                    child = {
                        "type": item['type'],
                        "ext": {
                            "text": item["word"],
                            "tag": item["tag"],
                            "id": item["id"],
                        },
                        "debug": {},
                        "rect": {
                            "x": item["rect"]["x"],
                            "y": item["rect"]["y"],
                            "w": item["rect"]["width"],
                            "h": item["rect"]["height"],
                        },
                        "children": []
                    }
                    element['children'].append(child)
                    if 'charset' in item:
                        child['charset'] = item['charset']
            if replaying or not self.check_overlap_with_existing(res_children, element):
                res_children.append(element)
            else:
                logger.info("Single_feature overlapping ocr element: {}".format(element))

        # dom_info 的排序 & 录入
        dfe_info.sort(key=lambda x: (x["rect"]["y"], x["rect"]["x"]))
        dom_index_dict = {}
        for dom in dfe_info:
            sorted_key = ""
            feature_dict = {}
            for key in self.goal_feature_dict:
                if key in dom and dom[key] is not None and dom[key] != "":
                    sorted_key += "{}_{}|".format(key, dom[key])
                    feature_dict[key] = {
                        "data": dom[key],
                        "chosen": True
                    }

            if sorted_key not in dom_index_dict:
                dom_index_dict[sorted_key] = -1
            dom_index_dict[sorted_key] += 1
            dom_index = dom_index_dict[sorted_key]

            element = {
                "type": "DFE",
                "ext": {
                    "index": dom_index,
                    "feature": feature_dict,
                    "tag": [SINGLE, MULTIPLE, SYSTEM],
                    "id": dom["id"]
                },
                "debug": {},
                "rect": {
                    "x": dom["rect"]["x"],
                    "y": dom["rect"]["y"],
                    "w": dom["rect"]["width"],
                    "h": dom["rect"]["height"],
                },
                "children": []
            }
            res_children.append(element)
            # if not self.check_overlap_with_existing(res_children, element):
            #     res_children.append(element)
            # else:
            #     logger.info("Single_feature overlapping dom element: {}".format(element))

        # 组合结构
        res_data = {
            "type": "Page",
            "ext": {},
            "debug": {},
            "rect": {
                "x": 0,
                "y": 0,
                "w": self.width,
                "h": self.height
            },
            "children": []
        }
        res_data["children"] = res_children

        # 增加index和父节点信息
        index_list = [-1]
        parent_ids = []
        self.add_debug_info(res_data, index_list, parent_ids)

        return res_data

    def remove_ocr_in_icon(self, icon_info, beta_info, ocr_info):
        """
        除去与icon模型或beta模型检测框重叠的ocr字符，并reshape行矩形框
        """
        tmp_rects = []
        raw_icon_info = icon_info.get("detect_res", [])
        raw_ocr_info = ocr_info.get("ret", [])
        raw_beta_info = beta_info.get("detect_res", [])
        for icon in raw_icon_info:
            logger.debug("icon: {}".format(icon))
            x1, y1, x2, y2 = icon['left'], icon['top'], icon['left'] + icon['width'], icon['top'] + icon['height']
            tmp_rects.append([x1, y1, x2, y2])
        for icon in raw_beta_info:
            x1, y1, x2, y2 = icon['left'], icon['top'], icon['left'] + icon['width'], icon['top'] + icon['height']
            tmp_rects.append([x1, y1, x2, y2])
        # 构建累加和矩阵
        smap = SMap(shape=(self.width, self.height), rects=tmp_rects)
        self.remove_ocr_overlap(raw_ocr_info, smap=smap)

    def remove_low_conf_obj(self, icon_info, beta_info, ele_info):
        """
        FIXME 临时逻辑暂时加在这里，后续会放入更合适的位置
            移除低置信度低的目标检测框，beta、icon、element模型的识别结果都会过滤
        """

        def rm_low_conf_rects(rect_list: list):
            rm_list = [r for r in rect_list if r["confidence"] < DST.MIN_OBJ_DETECT_CONF]
            logger.debug("remove {} object".format(len(rm_list)))
            for rm in rm_list:
                rect_list.remove(rm)

        raw_icon_info = icon_info.get("detect_res", [])
        rm_low_conf_rects(raw_icon_info)
        raw_beta_info = beta_info.get("detect_res", [])
        rm_low_conf_rects(raw_beta_info)
        raw_ele_info = ele_info.get("detect_res", [])
        rm_low_conf_rects(raw_ele_info)

    def remove_charset(self, ocr_info):
        """
        如果是对录制页面建模，就调用这个逻辑，删除所有charset避免json过大
        如果是对回放页面建模，就不调用这个逻辑，为回放的点击坐标提供依据
        """
        for text_area in ocr_info:
            for text in text_area['children']:
                del text['charset']

    def main(self, params):
        """
        主要执行过程
        """
        try:
            # 提取基本信息：图片长、宽
            self.extract_basic_info(params)

            ocr_info = params["ocr_info"]
            beta_info = params["beta_info"]
            icon_info = params["icon_info"]
            ele_info = params["ele_info"]
            dfe_info = params["dfe_info"]

            # 组合元素，排序，返回建模结果
            res_dom = self.build_structure(ocr_info, beta_info, icon_info, ele_info, dfe_info, replaying=params.get(
                'replaying', False))
            result = {
                "code": ErrCode.Success.Code,
                "msg": ErrCode.Success.Msg,
                "dom": res_dom
            }
            return result

        except Exception as e:
            logger.error("single_feature_record uncaught Error: {}".format(traceback.format_exc()))
            result = {
                "code": ErrCode.UnknownError.Code,
                "msg": ErrCode.UnknownError.Msg,
                "dom": {}
            }
            return result
