#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@Desc    :
@File    :   execute_base.py
@Time    :   2024-05-10
<AUTHOR>   <EMAIL>
"""
import math


class GridLocator:
    """
    栅格定位器
    """
    def __init__(self, img_width, img_height, grid_rows=24, grid_cols=12):
        self.img_width = img_width
        self.img_height = img_height
        self.grid_rows = grid_rows
        self.grid_cols = grid_cols
        self.grid_width = img_width / grid_cols
        self.grid_height = img_height / grid_rows
    def calculate_grid_position(self, component, grid_width, grid_height):
        """
        计算组件所在的栅格位置
        """
        x_min = component['rect']['x']
        y_min = component['rect']['y']
        x_max = x_min + component['rect']['w']
        y_max = y_min + component['rect']['h']

        # 计算左上角点所在的栅格位置
        grid_x_min = math.ceil(x_min / grid_width)
        grid_y_min = math.ceil(y_min / grid_height)

        # 计算右下角点所在的栅格位置
        grid_x_max = math.ceil(x_max / grid_width)
        grid_y_max = math.ceil(y_max / grid_height)

        # 计算组件所占的栅格位置
        grid_pos = {
            "x": grid_x_min,
            "y": grid_y_min,
            "w": grid_x_max - grid_x_min + 1,
            "h": grid_y_max - grid_y_min + 1
        }

        return grid_pos

    def assign_grid_positions(self, components):
        """
        给所有组件分配栅格位置
        """
        for component in components:
            grid_pos = self.calculate_grid_position(component, self.grid_width, self.grid_height)
            component['ext']['grid_position'] = grid_pos
            if component['children'] and len(component['children']) > 0:
                # 对children递归计算栅格位置（目前仅ocr有children）
                self.assign_grid_positions(component['children'])

def component_in_target_grid(component, target):
    """
    判断组件是否在目标栅格范围内, 偏移量不超过1格
    """
    if component["ext"]["grid_position"] is None or target["ext"]["grid_position"] is None:
        # 未分配栅格位置，无法判断，返回False
        return False, -1

    # 获取组件的栅格位置
    component_grid = component['ext']['grid_position']
    target_grid = target['ext']['grid_position']

    # 提取组件和目标栅格的位置信息
    comp_x = component_grid['x']
    comp_y = component_grid['y']
    comp_w = component_grid['w']
    comp_h = component_grid['h']

    target_x = target_grid['x']
    target_y = target_grid['y']
    target_w = target_grid['w']
    target_h = target_grid['h']

    # 计算边界绝对值差距
    left_diff = abs(comp_x - target_x)
    top_diff = abs(comp_y - target_y)
    right_diff = abs((comp_x + comp_w) - (target_x + target_w))
    bottom_diff = abs((comp_y + comp_h) - (target_y + target_h))
    # 计算栅格总偏移量
    grid_deviation = left_diff + top_diff + right_diff + bottom_diff

    if left_diff <= 1 and top_diff <= 1 and right_diff <= 1 and bottom_diff <= 1:
        return True, grid_deviation
    else:
        return False, -1



