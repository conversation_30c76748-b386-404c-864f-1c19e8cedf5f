#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@Desc    :   
@File    :   survey_dom_feature_choose.py
@Time    :   2024-02-20
<AUTHOR>   <EMAIL>
"""
import json


def get_type_list(type_list, dom):
    """
    获取type列表
    """
    if "type" in dom and dom["type"] not in type_list:
        type_list.append(dom["type"])
    if "children" in dom:
        for child_dom in dom["children"]:
            get_type_list(type_list, child_dom)


file_list = [
    "/Users/<USER>/Desktop/ios_baidu_index.json",
    "/Users/<USER>/Desktop/ios_baidu_persional.json",
    "/Users/<USER>/Desktop/ios_tieba_index.json",
    "/Users/<USER>/Desktop/ios_tieba_persional.json",
    "/Users/<USER>/Desktop/ios_wenku_index.json",
    "/Users/<USER>/Desktop/ios_wenku_persional.json",
]

type_list = []
for file_name in file_list:
    with open(file_name, 'r') as f:
        data = json.load(f)
        get_type_list(type_list, data)

print(type_list)
