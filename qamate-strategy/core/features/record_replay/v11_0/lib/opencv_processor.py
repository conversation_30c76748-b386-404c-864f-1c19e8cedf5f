#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@Desc    :   use opencv to process image
@File    :   opencv_processor.py
@Time    :   2024-03-12
<AUTHOR>   <EMAIL>
"""

import cv2
import numpy as np
from .dom_split_threshold import Dom<PERSON><PERSON><PERSON><PERSON><PERSON>eshold as DST

def extract_cv_rect(image_path):
    """
     用传统cv切割图片，返回矩形的五个参数
     [x, y, w, h, angle]
    """

    # 返回结果
    opencv_list = []

    # 读取图像
    image = cv2.imread(image_path)

    # 将图像转换为灰度图
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    # 使用高斯滤波去除噪声
    blurred = cv2.GaussianBlur(gray, (5, 5), 0)
    # 使用Canny边缘检测器检测边缘
    edges = cv2.Canny(blurred, 5, 20)

    # 执行轮廓检测
    contours, _ = cv2.findContours(edges.copy(), cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

    # 循环处理每个检测到的轮廓
    for contour in contours:
        # 计算轮廓的周长
        perimeter = cv2.arcLength(contour, True)

        # 使用多边形拟合来近似轮廓
        approx = cv2.approxPolyDP(contour, 0.02 * perimeter, True)

        # 获取矩形的外接矩形
        x, y, w, h = cv2.boundingRect(approx)

        # 设定最小尺寸阈值
        min_size = 3000

        # 检查尺寸是否太小
        if w * h < min_size:
            continue  # 跳过尺寸太小的矩形

        # 如果近似的轮廓有四个顶点，那么它可能是矩形
        if len(approx) == 4:
            opencv_list.append([int(x), int(y), int(w), int(h), 0])

        else:
            rect = cv2.minAreaRect(contour)

            # 提取矩形的尺寸和旋转角度
            (x, y), (w, h), angle = rect

            # 检查倾斜角度是否超过5度
            if abs(angle) % 90 > 5:
                continue  # 跳过倾斜角度过大的矩形

            # 如果矩形的尺寸和倾斜角度都符合要求，则进行后续处理
            # 例如，绘制矩形的边界框
            box = cv2.boxPoints(rect)
            box = np.intp(box)

            # 计算边界框的左上角和右下角坐标
            min_x = np.min(box[:, 0])
            min_y = np.min(box[:, 1])
            max_x = np.max(box[:, 0])
            max_y = np.max(box[:, 1])

            # 边界框的宽度和高度
            box_width = max_x - min_x
            box_height = max_y - min_y

            opencv_list.append([int(min_x), int(min_y), int(box_width), int(box_height), angle])

    return cv_filter(opencv_list, image)

def cv_filter(opencv_list, image):
    """
     删减opencv_list，去除太小或者旋转角度大的矩形
    """
    result = []
    height, width, channels = image.shape

    min_w = width * DST.CV_RECT_WIDTH_LIMIT
    min_h = height * DST.CV_RECT_HEIGHT_LIMIT

    for rect in opencv_list:
        x, y, w, h, angle = rect

        # 筛选掉 长宽太小的矩形
        if w < min_w or h < min_h:
            continue

        # 检查倾斜角度是否超过5度
        if abs(angle) % 90 > DST.CV_RECT_ANGLE_LIMIT:
            continue  # 跳过倾斜角度过大的矩形

        result.append([x, y, w, h])

    return result


def get_node_cross_area(p, c):
    """
    获取 p 和 c 的相交面积
    """
    p_x1 = p["rect"]["x"]
    p_x2 = p["rect"]["x"] + p["rect"]["w"]
    p_y1 = p["rect"]["y"]
    p_y2 = p["rect"]["y"] + p["rect"]["h"]

    c_x1 = c["rect"]["x"]
    c_x2 = c["rect"]["x"] + c["rect"]["w"]
    c_y1 = c["rect"]["y"]
    c_y2 = c["rect"]["y"] + c["rect"]["h"]
    # 计算重叠部分面积
    maxx = max(p_x1, c_x1)
    minx = min(p_x2, c_x2)
    maxy = max(p_y1, c_y1)
    miny = min(p_y2, c_y2)
    cross_area = 0
    if minx > maxx and miny > maxy:
        cross_area = (maxx - minx) * (maxy - miny)
    return cross_area

def check_elements_contain_cve(node_a, node_b):
    """
    判断 node_a 是否包含 node_b (cve)
    """
    a_area = node_a["rect"]["w"] * node_a["rect"]["h"]
    b_area = node_b["rect"]["w"] * node_b["rect"]["h"]

    # 重合部分面积
    cross_area = get_node_cross_area(node_a, node_b)

    if cross_area >= b_area and cross_area < a_area:
        return True

    return False
