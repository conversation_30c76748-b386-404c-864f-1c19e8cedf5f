#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@Desc    :   
@File    :   graph_struct.py
@Time    :   2023-11-10
<AUTHOR>   x<PERSON><PERSON><PERSON>@baidu.com
"""
import math

from .dom_split_threshold import Dom<PERSON><PERSON><PERSON><PERSON><PERSON>eshold as DST


class GraphStruct(object):
    """
    图结构类
    """

    def __init__(self, height=1, width=1):
        """
        初始化函数
        """
        self.line_matrix = []
        self.nodes_dict = {}
        self.node_id_list = []
        self.nodes_num = 0
        self.width = width
        self.height = height

    def get_t_value(self, node_a, node_b):
        """
        获取向量中的t值
        """
        if node_a["type"] == "Text" or node_b["type"] == "Text":
            t = 0
            return t
        x_a = node_a["rect"]["x"]
        r_a = node_a["rect"]["x"] + node_a["rect"]["w"]
        x_b = node_b["rect"]["x"]
        r_b = node_b["rect"]["x"] + node_b["rect"]["w"]
        if x_a >= x_b and x_a < r_b:
            t = 0
            return t

        if x_a >= r_b:
            t = 1
            return t

        if r_a >= x_b:
            t = 0
            return t

        if r_a < x_b:
            t = 1
            return t

    def line_build(self, node_a, node_b):
        """
        获取边的向量值，a->b
        """
        line_vector = []
        # x, y, z, t
        # t = 1: a和b的右侧对齐
        # t = 0: a和b的左侧对齐，如果a和b任意一个是text类型，则默认是t=1
        t = self.get_t_value(node_a, node_b)
        x = node_a["rect"]["y"] - node_b["rect"]["y"]
        y = node_a["rect"]["y"] + node_a["rect"]["h"] - \
            (node_b["rect"]["y"] + node_b["rect"]["h"])
        z = 0
        if t == 0:
            # z = abs(node_a["rect"]["x"] - node_b["rect"]["x"])
            z = node_a["rect"]["x"] - node_b["rect"]["x"]
        else:
            # if node_a["rect"]["x"] >= (node_b["rect"]["x"] + node_b["rect"]["w"]):
            #     z = node_b["rect"]["x"] + node_b["rect"]["w"] - node_a["rect"]["x"]
            # else:
            z = node_a["rect"]["x"] + node_a["rect"]["w"] - node_b["rect"]["x"]

        x = 100.0 * x / self.height
        y = 100.0 * y / self.height
        z = 100.0 * z / self.width
        # line_vector = [x, y, z, t]

        # 额外参数
        # q1：相对高度比例
        q_1 = abs(math.log(node_a["rect"]["h"]) - math.log(node_b["rect"]["h"]))

        line_vector = [x, y, z, t, q_1]

        return line_vector

    def check_node_has_detail(self, node_info):
        """
        检查节点是否含有特征匹配元素
        """
        check_flag = False

        if "detailDetection" not in node_info:
            # logger.info("check_node_detail pos_0 result:{}".format(check_flag))
            return check_flag

        detail_detection = node_info["detailDetection"]
        if len(detail_detection) < 3:
            # logger.info("check_node_detail pos_1 result:{}".format(check_flag))
            return check_flag

        if detail_detection[0] == 0:
            # logger.info("check_node_detail pos_2 result:{}".format(check_flag))
            return check_flag

        check_flag = True

        return check_flag

    def graph_build(self, nodes_dict):
        """
        图构建
        """
        # 把含有特征匹配的节点放在开头
        for node_id in nodes_dict:
            if self.check_node_has_detail(nodes_dict[node_id]) is True:
                self.node_id_list.append(node_id)
                self.nodes_dict[node_id] = nodes_dict[node_id]
                self.nodes_dict["g_id"] = len(self.node_id_list) - 1
            else:
                continue

        for node_id in nodes_dict:
            if node_id in self.nodes_dict:
                continue
            self.node_id_list.append(node_id)
            self.nodes_dict[node_id] = nodes_dict[node_id]
            self.nodes_dict["g_id"] = len(self.node_id_list) - 1

        self.nodes_num = len(self.node_id_list)

        # 构建初始图
        self.line_matrix = []
        for i in range(self.nodes_num):
            self.line_matrix.append([])

        for i in range(self.nodes_num):
            for _ in range(self.nodes_num):
                self.line_matrix[i].append(None)

        # 构建边
        for i in range(self.nodes_num):
            for j in range(self.nodes_num):
                if i == j:
                    continue
                line = self.line_build(
                    self.nodes_dict[self.node_id_list[i]],
                    self.nodes_dict[self.node_id_list[j]]
                )
                self.line_matrix[i][j] = line

    def check_line_vector(self, sub_line_vector, line_vector, find_nodes_count=-1):
        """
        检查向量是否可以相等
        [x, y, z, t]
        """
        if (find_nodes_count >= DST.WIDE_THRESHOLD):
            res_flag, acc_diff_value, diff_list = self.check_line_vector_core_v2(sub_line_vector, line_vector,
                 subgraph_each_dimension_diff_limit=DST.SUBGRAPH_EACH_DIMENSION_DIFF_WIDE_LIMIT,
                 vector_relative_height_limit=DST.VECTOR_RELATIVE_HEIGHT_WIDE_LIMIT,
                 subgraph_all_dimension_diff_limit=DST.SUBGRAPH_ALL_DIMENSION_DIFF_WIDE_LIMIT)
        else:
            res_flag, acc_diff_value, diff_list = self.check_line_vector_core_v2(sub_line_vector, line_vector)
        return res_flag, acc_diff_value, diff_list

    def check_line_vector_core_v1(self, sub_line_vector, line_vector):
        """
        检查向量是否可以相等
        [x, y, z, t]
        """
        res_flag = False
        acc_diff_value = 0
        diff_list = []
        if sub_line_vector[3] != line_vector[3]:
            acc_diff_value = 999
            # logger.info("DEBUG_TEST slv:{} lv:{}".format(sub_line_vector[-1], line_vector[-1]))
            return res_flag, acc_diff_value, diff_list

        for i in range(3):
            diff_value = abs(sub_line_vector[i] - line_vector[i])
            diff_list.append(diff_value)
            if diff_value > DST.SUBGRAPH_EACH_DIMENSION_DIFF_LIMIT:
                acc_diff_value = 999
                break
            acc_diff_value += diff_value

        if acc_diff_value > DST.SUBGRAPH_ALL_DIMENSION_DIFF_LIMIT:
            res_flag = False
        else:
            res_flag = True

        return res_flag, acc_diff_value, diff_list

    def check_line_vector_core_v2(self, sub_line_vector, line_vector,
                                  subgraph_each_dimension_diff_limit=DST.SUBGRAPH_EACH_DIMENSION_DIFF_LIMIT,
                                  vector_relative_height_limit=DST.VECTOR_RELATIVE_HEIGHT_LIMIT,
                                  subgraph_all_dimension_diff_limit=DST.SUBGRAPH_ALL_DIMENSION_DIFF_LIMIT):
        """
        检查向量是否可以相等
        [x, y, z, t]
        """
        res_flag = False
        acc_diff_value = 0
        diff_list = []
        if sub_line_vector[3] != line_vector[3]:
            acc_diff_value = 999
            # logger.info("DEBUG_TEST slv:{} lv:{}".format(sub_line_vector[-1], line_vector[-1]))
            return res_flag, acc_diff_value, diff_list

        for i in range(3):
            diff_value = abs(sub_line_vector[i] - line_vector[i])
            diff_list.append(diff_value)
            if diff_value > subgraph_each_dimension_diff_limit:
                acc_diff_value = 999
                break
            acc_diff_value += diff_value

        if abs(sub_line_vector[4] - line_vector[4]) > vector_relative_height_limit:
            acc_diff_value = 999

        if acc_diff_value > subgraph_all_dimension_diff_limit:
            res_flag = False
        else:
            res_flag = True

        return res_flag, acc_diff_value, diff_list