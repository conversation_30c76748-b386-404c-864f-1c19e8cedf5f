#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@Desc    :   
@File    :   subgraph_search.py
@Time    :   2023-11-13
<AUTHOR>   xuz<PERSON><PERSON>@baidu.com
"""
import json
import traceback
import cv2
import numpy as np

from basics.util import logger
from .graph_struct import GraphStruct
from .dom_split_threshold import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> as DST
from basics.image.ui.img_similarity import is_same_mark, little_same_val
from .check_node_detail import check_node_feature, check_node_detail


def format_ocr_info(vision_info):
    """
    格式化文本信息
    """
    ocr_list = []
    try:
        ocr_ret = vision_info["ocr_info"]["ret"]
        for ret in ocr_ret:
            iterm = {
                "rect": {
                    "x": ret["rect"]["left"],
                    "y": ret["rect"]["top"],
                    "w": ret["rect"]["width"],
                    "h": ret["rect"]["height"]
                },
                "word": ret["word"]
            }
            ocr_list.append(iterm)
    except:
        logger.error("format_ocr_info Error: {}".format(traceback.format_exc()))
    return ocr_list


def get_node_cross_area(p, c):
    """
    获取 p 和 c 的相交面积
    """
    p_x1 = p["rect"]["x"]
    p_x2 = p["rect"]["x"] + p["rect"]["w"]
    p_y1 = p["rect"]["y"]
    p_y2 = p["rect"]["y"] + p["rect"]["h"]

    c_x1 = c["rect"]["x"]
    c_x2 = c["rect"]["x"] + c["rect"]["w"]
    c_y1 = c["rect"]["y"]
    c_y2 = c["rect"]["y"] + c["rect"]["h"]
    # 计算重叠部分面积
    maxx = max(p_x1, c_x1)
    minx = min(p_x2, c_x2)
    maxy = max(p_y1, c_y1)
    miny = min(p_y2, c_y2)
    cross_area = 0
    if minx > maxx and miny > maxy:
        cross_area = (maxx - minx) * (maxy - miny)
    return cross_area


def check_elements_overlap_word(node, word):
    """
    判断两个元素是否几乎重叠
    """
    word_area = word["rect"]["w"] * word["rect"]["h"]

    # 重合部分面积
    cross_area = get_node_cross_area(node, word)

    word_cross_ratio = 0
    if word_area > 0:
        word_cross_ratio = 100.0 * cross_area / word_area

    if word_cross_ratio >= DST.CONTAINS_TEXT_RATIO:
        return True

    return False


def check_replay_text(detail_detection, replay_node, vision_info):
    """
    校验文本
    """
    # 寻找replay_node范围内的文本信息
    ocr_list = format_ocr_info(vision_info)
    # logger.info("ocr_list: {}".format(json.dumps(ocr_list)))

    replay_node_ocr = []
    for ocr in ocr_list:
        overlap_flag = check_elements_overlap_word(replay_node, ocr)
        if overlap_flag is True:
            replay_node_ocr.append(ocr["word"])

    find_flag = False
    goal_word = detail_detection[2]
    for word in replay_node_ocr:
        if detail_detection[1] == 1:
            if word.find(goal_word) != -1:
                find_flag = True
                return find_flag
        if detail_detection[1] == 2:
            if word == goal_word:
                find_flag = True
                return find_flag

    return find_flag


def debug_draw_path_finding(case_img, step_img, path_no, vlaue):
    """
    debug 中间过程 - 画图
    """
    debug_img = np.zeros(
        (
            max(case_img.shape[0], step_img.shape[0]),
            step_img.shape[1] + case_img.shape[1],
            3
        )
    )
    for y in range(max(case_img.shape[0], step_img.shape[0])):
        if y < case_img.shape[0]:
            for x in range(case_img.shape[1]):
                debug_img[y][x] = case_img[y][x]

        if y < step_img.shape[0]:
            for x in range(step_img.shape[1]):
                debug_img[y][x + case_img.shape[1]] = \
                    step_img[y][x]

    debug_file_dir = "./debug_{}_value_{}.jpg".format(path_no, vlaue)
    cv2.imwrite(debug_file_dir, debug_img)
    logger.info("debug_path_finding: {}".format(debug_file_dir))


def check_replay_image_same(detail_detection, record_node, replay_node, image_info):
    """
    检测小区域图片相似度
    """
    if detail_detection[1] == 0:
        return True

    # 切割目标区域
    # 截取小区域图片
    record_raw_img = image_info["record_image"]
    record_image_area = np.zeros((
        record_node["rect"]["h"], record_node["rect"]["w"], 3
    ), dtype=np.uint8)
    for line_no in range(record_node["rect"]["h"]):
        for column_no in range(record_node["rect"]["w"]):
            record_image_area[line_no][column_no] = \
                record_raw_img[line_no + record_node["rect"]["y"]][column_no + record_node["rect"]["x"]]

    replay_raw_img = image_info["replay_image"]
    replay_image_area = np.zeros((
        replay_node["rect"]["h"], replay_node["rect"]["w"], 3
    ), dtype=np.uint8)
    for line_no in range(replay_node["rect"]["h"]):
        for column_no in range(replay_node["rect"]["w"]):
            replay_image_area[line_no][column_no] = \
                replay_raw_img[line_no + replay_node["rect"]["y"]][column_no + replay_node["rect"]["x"]]

    same_res = little_same_val(record_image_area, replay_image_area)
    # DEBUG_TEST
    # debug_draw_path_finding(record_image_area, replay_image_area, int(time.time()), same_res)

    logger.info("same_res: {}".format(same_res))
    same_flag = False
    if same_res > 0.4:
        same_flag = True
    logger.info("same_flag: {}".format(same_flag))
    return same_flag


def select_node_match(node_match_list, node_match_info_list, graph, subgraph):
    """
    对于存在多个node_match的列表，选择一个node_match
    """
    if len(node_match_list) <= 0:
        return None

    # 选择node_match_list中，node_match_info_list中的diff最小的node_match
    min_diff = node_match_info_list[0]["diff"]
    min_diff_list = []
    min_diff_list.append(node_match_info_list[0]["diff_list"])
    min_indexs = [0]
    for i in range(1, len(node_match_info_list)):
        if node_match_info_list[i]["diff"] < min_diff:
            min_indexs = [i]
            min_diff = node_match_info_list[i]["diff"]
            min_diff_list = []
            min_diff_list.append(node_match_info_list[i]["diff_list"])

        elif node_match_info_list[i]["diff"] == min_diff:
            min_indexs.append(i)
            min_diff_list.append(node_match_info_list[i]["diff_list"])

    logger.info("min_indexs: {}".format(json.dumps(min_indexs)))
    logger.info("min_diff: {}".format(min_diff))
    logger.info("min_diff_list: ")
    for i in min_diff_list[0]:
        logger.info("v1:{} sum_diff:{}".format(json.dumps(i), sum(i)))
    if len(min_indexs) == 1:
        return node_match_list[min_indexs[0]]

    # 如果存在多个min_indexs，则选择第一个
    return node_match_list[min_indexs[0]]


def subgraph_search(graph, subgraph, cut_image_info, image_info, find_nodes_count=-1, fallback=False, match_cache=None):
    """
    :param graph:
    :param subgraph:
    :param fallback: 是否是兜底匹配策略

    :return:

    Args:
        match_cache: 用于剪枝，双循环特征比对会消耗大量时间，
                已经比对过特征的节点直接缓存，避免重复匹配, key : (rec_id, rep_id), val: True/False
                参考文档：https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/3LoFUu-M_a/LWyELLqriZ/UjABJQ1kEZWAMs

    """
    if match_cache is None:
        match_cache = {}
    image_ext_info = {
        "record": {
            "width": image_info["record_image"].shape[1],
            "height": image_info["record_image"].shape[0]
        },
        "replay": {
            "width": image_info["replay_image"].shape[1],
            "height": image_info["replay_image"].shape[0]
        }
    }

    # # debug - 1. 初始化
    # graph = graph_struct(graph)
    # subgraph = graph_struct(subgraph)

    # 开始进行子图匹配，本函数算法主要基于VF2
    # node_match_list 是匹配的列表，内容为[[x0, x1, x2, …], [y0, y1, y2, …], ……]
    # 表示 x0 为 graph.node_id_list中的某个节点的index，它和subgraph.node_id_list中的第0个节点匹配
    node_match_list = []
    node_match_info_list = []
    # 特除处理第0轮的种子元素
    sub_i = 0
    sub_node_id = subgraph.node_id_list[sub_i]
    sub_node_info = subgraph.nodes_dict[sub_node_id]

    for g_i in range(graph.nodes_num):
        g_node_id = graph.node_id_list[g_i]
        g_node_info = graph.nodes_dict[g_node_id]

        def on_ocr_match(param):
            """ocr匹配到的时候的闭包回调"""
            if 'target_ocr_chars' in param:
                g_node_info['target_ocr_chars'] = param['target_ocr_chars']

        check_node_detail_res = check_node_detail(sub_node_info,
                                                  g_node_info,
                                                  cut_image_info,
                                                  image_info,
                                                  on_match=on_ocr_match,
                                                  match_cache=match_cache,
                                                  fallback=fallback
                                                  )
        logger.debug("check_node_detail_res: {}".format(check_node_detail_res))
        if check_node_detail_res:
            node_match_list.append([g_i])
            node_match_info_list.append({
                "diff": 0,
                "diff_list": []
            })
    logger.debug("STEP_0 node_match_list len: {}".format(len(node_match_list)))

    # 基于第0轮的匹配结果，进行后续的匹配
    for sub_i in range(1, subgraph.nodes_num):
        logger.debug("node_no: {} ===================================================".format(sub_i))
        sub_node_id = subgraph.node_id_list[sub_i]
        sub_node_info = subgraph.nodes_dict[sub_node_id]
        # 选择 sub_line_vector 进行匹配
        sub_line_vector = subgraph.line_matrix[sub_i - 1][sub_i]

        logger.debug("record sub_line_vector:{}".format(sub_line_vector))
        logger.debug("record sub_node_info:{}".format(sub_node_info))
        # print("sub_id: {}".format(sub_node_info["id"]))

        # DEBUG_TEST
        # lsni = subgraph.node_id_list[sub_i - 1]
        # lsni_info = subgraph.nodes_dict[lsni]
        # logger.info("DEBUG_TEST sub_i_l sub_i: {} {}".format(
        #     json.dumps(lsni_info["rect"]), json.dumps(sub_node_info["rect"]))
        # )
        # logger.info("DEBUG_TEST sub_line_vector: {}".format(json.dumps(sub_line_vector)))

        # 遍历match_list，每组node_match中，比较node_match的sub_i - 1的节点，是否存在和
        # subgraph中的 (sub_i - 1) - sub_i 这条边，相一致的边
        # 如果存在，加入到node_match中，如果有多个，则分多个node_match 加入
        # 如果不存在，则舍弃这个node_match

        new_node_match_list = []
        new_node_match_info_list = []
        # print("sub_i: {}".format(sub_i))
        for node_match_i in range(len(node_match_list)):
            node_match = node_match_list[node_match_i]
            # print("node_match len: {}".format(len(node_match)))
            g_i = node_match[-1]
            g_node_id = graph.node_id_list[g_i]
            g_node_info = graph.nodes_dict[g_node_id]
            for g_new_i in range(graph.nodes_num):
                if g_new_i in node_match:
                    continue

                g_new_node_id = graph.node_id_list[g_new_i]
                g_new_node_info = graph.nodes_dict[g_new_node_id]

                def on_ocr_match_2(param):
                    """ocr匹配到的时候的闭包回调"""
                    if 'target_ocr_chars' in param:
                        g_new_node_info['target_ocr_chars'] = param['target_ocr_chars']

                if check_node_detail(sub_node_info,
                                     g_new_node_info,
                                     cut_image_info,
                                     image_info,
                                     on_match=on_ocr_match_2,
                                     match_cache=match_cache,
                                     fallback=fallback) is False:
                    continue

                g_line_vector = graph.line_matrix[g_i][g_new_i]
                check_line_vector_res, diff, diff_list = graph.check_line_vector(
                    sub_line_vector, g_line_vector, find_nodes_count)

                # DEBUG_TEST
                # logger.debug("----------------------------------------------")
                # logger.debug("check_vector: {}".format(g_line_vector))
                # logger.debug("check_line_vector_res: {}".format(check_line_vector_res))
                # logger.debug("diff: {}".format(diff))
                # logger.debug("diff_list: {}".format(diff_list))

                # 如果匹配失败，直接退出
                if check_line_vector_res is False:
                    continue
                else:
                    pass
                    # logger.debug("DEBUG_TEST glv:{} diff:{} list:{}".format(json.dumps(g_line_vector), diff, diff_list))

                recheck_res = True

                # 如果匹配成功，则比较
                # sub_i 和 [0, 1, …, sub_i - 2] 的向量差
                # node_match中，g_i 和 [0, 1, …, sub_i - 2] 的向量差
                # sub_i 和 len(node_match) 是一样的，因为sub_i 是从0开始的
                if sub_i >= 2:
                    for j in range(sub_i - 2 + 1):
                        # 获取sub的边
                        rc_sub_line_vector = subgraph.line_matrix[j][sub_i]

                        # 获取g的边
                        rc_g_i = node_match[j]
                        rc_g_line_vector = graph.line_matrix[rc_g_i][g_new_i]

                        recheck_res, diff, diff_list = graph.check_line_vector(
                            rc_sub_line_vector, rc_g_line_vector, find_nodes_count)
                        if recheck_res is False:
                            break

                logger.debug("recheck_res: {}".format(recheck_res))

                if check_line_vector_res is True and recheck_res is True:
                    logger.debug("add vector glv:{} diff:{} list:{}".format(json.dumps(g_line_vector), diff, diff_list))
                    # print("{} {} g_line_vector: {}".format(g_node_info["id"], g_new_node_info["id"], g_line_vector))

                    new_node_match = []
                    for i in node_match:
                        new_node_match.append(i)
                    new_node_match.append(g_new_i)
                    new_node_match_list.append(new_node_match)

                    new_node_match_info = {
                        "diff": node_match_info_list[node_match_i]["diff"] + diff,
                        "diff_list": node_match_info_list[node_match_i]["diff_list"] + [diff_list]
                    }
                    new_node_match_info_list.append(new_node_match_info)

                logger.debug("----------------------------------------------")

        # 更新node_match_list
        node_match_list = new_node_match_list
        node_match_info_list = new_node_match_info_list
        logger.debug("DEBUG_TEST STEP_{} node_match_list len: {}".format(sub_i, len(node_match_list)))

        if len(node_match_list) <= 0:
            break

    # 如果node_match_list中存在多个元素，进行筛选
    # logger.info("final node_match_list: {}".format(json.dumps(node_match_list)))
    # logger.info("node_match_info_list: {}".format(json.dumps(node_match_info_list)))
    node_match = select_node_match(node_match_list, node_match_info_list, graph, subgraph)

    return node_match


def format_graph_node_match(node_match, graph):
    """
    格式化输出结果
    """
    node_info_list = []
    for g_i in node_match:
        g_node_id = graph.node_id_list[g_i]
        g_node_info = graph.nodes_dict[g_node_id]
        node_info_list.append(g_node_info)
    return node_info_list