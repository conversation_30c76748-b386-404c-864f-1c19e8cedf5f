"""
Desc:
Authors: <AUTHORS>
Date:    2024/3/25
"""

import os

from basics.util import logger


# def generate_dict_from_files(folder_path, english_filename, chinese_filename):
#     """
#     从指定文件夹下的两个文件中生成字典。
#
#     Args:
#         folder_path (str): 目标文件夹路径。
#         english_filename (str): 英文文件名。
#         chinese_filename (str): 中文文件名。
#
#     Returns:
#         dict: 以英文为key，中文为value的字典。
#
#     Raises:
#         ValueError: 当英文文件和中文文件的行数不一致时抛出异常。
#     """
#
#     __dir__ = os.path.dirname(os.path.abspath(__file__))
#     # 回退四个目录到 'core'
#     # 注：用的相对路径，如果之后操作文件路径变化，需要及时修改
#     relative_path_to_core = os.path.join(__dir__, os.pardir)
#     path_to_target_folder = os.path.join(relative_path_to_core, folder_path)
#
#     english_file_path = os.path.join(path_to_target_folder, english_filename)
#     chinese_file_path = os.path.join(path_to_target_folder, chinese_filename)
#     logger.info("english_file_path: {}".format(english_file_path))
#
#     # 遍历当前工作目录及其所有子目录
#     # for dirpath, dirnames, filenames in os.walk(relative_path_to_core):
#     #     # 使用logging记录目录路径
#     #     logger.info("Directory path: {}".format(dirpath))
#
#     with open(english_file_path, 'r', encoding='utf-8') as ef, open(chinese_file_path, 'r', encoding='utf-8') as cf:
#         english_lines = ef.readlines()
#         chinese_lines = cf.readlines()
#
#         # 去除行尾的换行符
#         english_lines = [line.strip() for line in english_lines]
#         chinese_lines = [line.strip() for line in chinese_lines]
#
#         # 检查中文名行数是否小于英行数，如果是，则用“其他”补齐中文名
#         line_diff = len(english_lines) - len(chinese_lines)
#         if line_diff > 0:
#             logger.error('{}, en&cn lines differ, en length = {}, cn length = {}'.
#                          format(path_to_target_folder, len(english_lines), len(chinese_lines)))
#             for i in range(line_diff):
#                 chinese_lines.append('其他')
#
#         # 生成字典并返回
#         return dict(zip(english_lines, chinese_lines))

def get_icon_name_dict():
    """
    从指定的文件路径中读取icon的英文和中文描述，并返回一个字典，其中英文描述作为键，中文描述作为值。

    Returns:
        dict: 包含英文描述和中文描述的字典，英文描述作为键，中文描述作为值。
    """
    # file_path = 'cname_dict/icon'
    # en_class = 'classes.txt'
    # zh_class = 'chinese_classes.txt'
    # icon_dict = generate_dict_from_files(file_path, en_class, zh_class)

    # 先写死dict，之后优化
    icon_dict = {
        "live": "直播",
        "add": "加",
        "camera": "相机",
        "headset": "耳机",
        "baidu": "度熊",
        "back_home": "返回首页",
        "video": "视频",
        "mine": "我的",
        "microphone": "语音",
        "menu_shu": "菜单-竖",
        "menu_heng": "菜单-横",
        "menu": "菜单",
        "arrow_left": "左箭头",
        "arrow_right": "右箭头",
        "arrow_up": "上箭头",
        "arrow_down": "下箭头",
        "edit": "编辑",
        "zan": "点赞",
        "comment": "评论",
        "collect": "收藏",
        "transmit": "转发",
        "share": "分享",
        "find": "查找",
        "download": "下载",
        "delect": "删除",
        "dislike": "踩",
        "setting": "设置",
        "close": "关闭",
        "mute": "静音",
        "home": "home",
        "wechat": "微信",
        "friend_circle": "朋友圈",
        "tip_off": "报警",
        "scan": "扫一扫",
        "botton_switch": "开关按钮",
        "play": "播放",
        "alarm": "闹钟",
        "like": "喜欢",
        "refresh": "刷新",
        "red_bag": "红包",
        "trumpet": "喇叭",
        "weibo": "微博",
        "AI": "AI"
    }

    return icon_dict

def get_beta_icon_name_dict():
    """
    从指定的文件路径中读取beta_icon的英文和中文描述，并返回一个字典，其中英文描述作为键，中文描述作为值。

    Returns:
        dict: 包含英文描述和中文描述的字典，英文描述作为键，中文描述作为值。
    """
    # file_path = 'cname_dict/beta'
    # en_class = 'classes.txt'
    # zh_class = 'chinese_classes.txt'
    # beta_icon_dict = generate_dict_from_files(file_path, en_class, zh_class)

    # 先写死dict，之后优化
    beta_icon_dict = {
        "AI": "AI",
        "pause": "暂停",
        "camera_switch": "相机切换",
        "filter": "过滤",
        "shopping_cart": "购物车",
        "shake": "摇一摇",
        "setting": "设置",
        "like": "喜欢",
        "gift": "礼物",
        "solid_gesture": "实心手套",
        "arrow_right_dot": "点状向右箭头",
        "arrow_right_copy": "重复向右箭头",
        "unlock": "解锁",
        "more": "更多",
        "icon": "图标",
        "arrow_up_copy": "重复向上箭头",
        "fullscreen": "全屏",
        "transparent_gesture": "透明手势",
        "botton_switch": "开关按钮",
        "menu_heng": "菜单-横",
        "arrow_down": "下箭头",
        "progress_bar": "进度条",
        "menu": "菜单",
        "mute": "静音",
        "add": "加"
    }

    return beta_icon_dict