#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@Desc    :   
@File    :   dom_other_element_handle.py
@Time    :   2024-02-21
<AUTHOR>   <EMAIL>
"""
import numpy as np
import json

from basics.util import logger
from .dom_split_threshold import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> as DST
from .calculate_rect_area import calculate_area


class DomElementHandle(object):
    """
    对dom中的额外元素进行获取的类
    """
    def __init__(self, width, height, os_type, goal_feature_dict):
        """
        初始化
        """
        super().__init__()
        self.debug_flag = False
        self.width = width
        self.height = height
        self.os_type = os_type
        self.goal_feature_dict = goal_feature_dict
        logger.debug("DomElementHandle self.height: {}".format(self.height))
        logger.debug("DomElementHandle self.width: {}".format(self.width))

    def recursive_android_dom_elements(self, raw_node):
        """
        递归获取 Android 的 elements 列表
        """
        processed_dom = {}
        try:
            attributes = raw_node["_attributes"]
            # 处理需要保留的属性信息
            processed_dom["type"] = attributes["class"].split(".")[-1]
            
            # 处理其它属性
            for key in attributes.keys():
                if key in ["class", "bounds"]:
                    continue
                processed_dom[key] = attributes[key]

            # 获取坐标信息
            bounds_str = attributes["bounds"]
            rect_str = bounds_str.replace("][", ",").replace("[", "").replace("]", "")
            rect_list = rect_str.split(",")
            processed_dom["rect"] = {
                "x": int(rect_list[0]),
                "y": int(rect_list[1]),
                "width": int(rect_list[2]) - int(rect_list[0]),
                "height": int(rect_list[3]) - int(rect_list[1])
            }
        except:
            return None

        # children
        processed_dom["children"] = []
        if "node" in raw_node:
            nodes = raw_node["node"]
            if type(nodes) is dict:
                child = self.recursive_android_dom_elements(nodes)
                if child is not None:
                    processed_dom["children"].append(child)
            elif type(nodes) is list:
                for node in nodes:
                    child = self.recursive_android_dom_elements(node)
                    if child is not None:
                        processed_dom["children"].append(child)
        return processed_dom

    def android_init(self, dom):
        """
        Android 初始化
        """
        res_dom = {
            "type": "App",
            "children": []
        }

        # 最外层数据处理
        res_dom["rect"] = {
            "x": 0,
            "y": 0,
            "width": self.width,
            "height": self.height,
        }

        if "hierarchy" in dom and "node" in dom["hierarchy"]:
            if type(dom["hierarchy"]["node"]) is list:
                for node in dom["hierarchy"]["node"]:
                    child = self.recursive_android_dom_elements(node)
                    if child is not None:
                        res_dom["children"].append(child)
            elif type(dom["hierarchy"]["node"]) is dict:
                child = self.recursive_android_dom_elements(dom["hierarchy"]["node"])
                if child is not None:
                    res_dom["children"].append(child)
        return res_dom

    def recursive_gdfe(self, dom, goal_dom_list):
        """
        递归获取制定的元素
        """
        check_res, _ = self.check_element_legal(dom)
        if check_res is True:
        # if True:
            for dom_feature in self.goal_feature_dict:
                if dom_feature in dom and dom[dom_feature] is not None:
                    # 过滤掉空字符串
                    if dom[dom_feature] == "":
                        continue

                    # 有指定值范围的，进行过滤
                    if len(self.goal_feature_dict[dom_feature]) > 0:
                        if dom[dom_feature] not in self.goal_feature_dict[dom_feature]:
                            continue

                    # dfe列表每个dom只用插入一次，避免重复
                    goal_dom_list.append(self.copy_node_exclude(dom, ["children"]))
                    break

        if "children" in dom:
            for child in dom["children"]:
                self.recursive_gdfe(child, goal_dom_list)

    def check_element_legal(self, node):
        """
        判断元素是否合法
        """
        # 判断元素的坐标是否超出屏幕
        if node["rect"]["x"] > self.width or node["rect"]["x"] < 0:
            return False, 1
        if node["rect"]["x"] + node["rect"]["width"] > self.width:
            return False, 2
        if node["rect"]["y"] > self.height or node["rect"]["y"] < 0:
            return False, 3
        if node["rect"]["y"] + node["rect"]["height"] > self.height:
            return False, 4
        
        # 判断元素是否过于小
        if node["rect"]["height"] <= DST.MIN_HEIGHT:
            return False, 5
        if node["rect"]["width"] <= DST.MIN_WIDTH:
            return False, 6

        # 判断元素是否为页面外框
        if node["rect"]["height"] >= self.height and node["rect"]["width"] >= self.width:
            logger.info("找到外边框元素")
            return False, 7

        return True, 0
    
    def get_node_cross_area(self, p, c):
        """
        获取 p 和 c 的相交面积
        """
        p_x1 = p["rect"]["x"]
        p_x2 = p["rect"]["x"] + p["rect"]["width"]
        p_y1 = p["rect"]["y"]
        p_y2 = p["rect"]["y"] + p["rect"]["height"]

        c_x1 = c["rect"]["x"]
        c_x2 = c["rect"]["x"] + c["rect"]["width"]
        c_y1 = c["rect"]["y"]
        c_y2 = c["rect"]["y"] + c["rect"]["height"]
        # 计算重叠部分面积
        maxx = max(p_x1, c_x1)
        minx = min(p_x2, c_x2)
        maxy = max(p_y1, c_y1)
        miny = min(p_y2, c_y2)
        cross_area = 0
        if minx > maxx and miny > maxy:
            cross_area = (maxx - minx) * (maxy - miny)
        return cross_area
    
    def check_elements_overlap(self, node_a, node_b):
        """
        判断两个元素是否交叉
        """
        a_area = node_a["rect"]["width"] * node_a["rect"]["height"]
        b_area = node_b["rect"]["width"] * node_b["rect"]["height"]
        
        # 重合部分面积
        cross_area = self.get_node_cross_area(node_a, node_b)
        
        if a_area > 0 and b_area > 0:
            check_area = min(a_area, b_area)
        else:
            return False

        if 100 * cross_area / check_area > DST.ELEMENT_OVERLAP_RATIO:
            return True
        return False
    
    def check_children_sequence(self, node_list):
        """
        根据顺序，进行节点清理
        """
        del_index_list = []
        for i in range(1, len(node_list)):
            if i in del_index_list:
                continue
            node = node_list[i]
            overlap_flag = False
            sequence_flag = False
            for j in range(i):
                if j in del_index_list:
                    continue
                check_node = node_list[j]
                overlap_flag = self.check_elements_overlap(node, check_node)
                if node["rect"]["y"] <= check_node["rect"]["y"] and \
                    (node["rect"]["y"] + node["rect"]["height"]) <= \
                        (check_node["rect"]["y"] + check_node["rect"]["height"]):
                    sequence_flag = True
            if overlap_flag is True and sequence_flag is True:
                del_index_list.append(i)
        new_node_list = []
        for i in range(len(node_list)):
            if i not in del_index_list:
                new_node_list.append(node_list[i])
        return new_node_list
    
    def remove_illegal_elements_in_dom(self, dom):
        """
        在 dom 中清理不合法元素
        """
        flag, code = self.check_element_legal(dom)
        dom["illegal_flag"] = flag
        dom["illegal_code"] = code
        if flag is False:
            logger.debug("元素不合法，code: {}, dom = {}".format(code, self.copy_node_exclude(dom, ["children"])))

        if "children" not in dom:
            return
        
        for child in dom["children"]:
            self.remove_illegal_elements_in_dom(child)
        
        new_children = []
        for child in dom["children"]:
            if child["illegal_flag"] is True:
                new_children.append(child)
            else:
                # 如果不合法节点是屏幕外框 code=7（之后可拓展），保留其子节点
                if child["illegal_code"] == 7 and "children" in child:
                    for grandchild in child["children"]:
                        new_children.append(grandchild)
            
            # if self.os_type == 1:
            #     dom["children"] = new_children
            # else:
            #     dom["children"] = self.check_children_sequence(new_children)
        dom["children"] = new_children
        return
    
    def adjust_child_node_size(self, raw_dom):
        """
        调整子节点大小
        """
        if "children" not in raw_dom:
            return
        
        for child in raw_dom["children"]:
            if child["rect"]["x"] < raw_dom["rect"]["x"]:
                child["rect"]["x"] = raw_dom["rect"]["x"]
            
            if child["rect"]["y"] < raw_dom["rect"]["y"]:
                child["rect"]["y"] = raw_dom["rect"]["y"]

            if child["rect"]["x"] + child["rect"]["width"] > raw_dom["rect"]["x"] + raw_dom["rect"]["width"]:
                child["rect"]["width"] = raw_dom["rect"]["x"] + raw_dom["rect"]["width"] - child["rect"]["x"]
            
            if child["rect"]["y"] + child["rect"]["height"] > raw_dom["rect"]["y"] + raw_dom["rect"]["height"]:
                child["rect"]["height"] = raw_dom["rect"]["y"] + raw_dom["rect"]["height"] - child["rect"]["y"]
            
            self.adjust_child_node_size(child)

    def get_children_overlap_ratio(self, dom):
        """
        返回当前节点被子节点覆盖的占比
        """
        ratio = 0
        # child_area = 0
        # dom_area = np.zeros((dom["rect"]["height"], dom["rect"]["width"]))
        # for child in dom["children"]:
        #     if child["remain_flag"] is False:
        #         continue
        #     for i in range(child["rect"]["height"]):
        #         for j in range(child["rect"]["width"]):
        #             index_i = child["rect"]["y"] - dom["rect"]["y"] + i
        #             index_j = child["rect"]["x"] - dom["rect"]["x"] + j
        #             if 0 <= index_i and index_i < dom["rect"]["height"] and \
        #                 0 <= index_j and index_j < dom["rect"]["width"]:
        #                 pass
        #             else:
        #                 continue
        #             if dom_area[index_i][index_j] == 0:
        #                 child_area += 1
        #             dom_area[index_i][index_j] = 1

        rect_list = []
        for child in dom["children"]:
            if child["remain_flag"] is False:
                continue
            rect_list.append((
                child["rect"]["x"],
                child["rect"]["y"],
                child["rect"]["x"] + child["rect"]["width"],
                child["rect"]["y"] + child["rect"]["height"],
            ))
        child_area = calculate_area(rect_list)

        ratio = 100.0 * child_area / (dom["rect"]["height"] * dom["rect"]["width"])
        return ratio
    
    def copy_node_exclude(self, copy_node, exclude_keys):
        """
        排除指定属性的复制
        """
        node = {}
        for key in copy_node.keys(): 
            if key not in exclude_keys:
                node[key] = copy_node[key]
        return node

    def recursive_remain_nodes(self, dom, remain_nodes_list):
        """
        递归方式，获取有效节点
        """
        # 叶子节点全部保留
        dom["remain_flag"] = False
        if "children" not in dom or len(dom["children"]) == 0:
            dom["remain_flag"] = True
            remain_nodes_list.append(self.copy_node_exclude(dom, ["children"]))
            return
        
        # 非叶子节点，进行额外处理
        # 先对子节点进行递归处理
        for child in dom["children"]:
            self.recursive_remain_nodes(child, remain_nodes_list)
        
        # 如果子节点中，有不需要保留的节点，进行递归式的删除
        # 既删除该节点，然后查看该节点是否有子节点，保留其中需要保留的子节点，递归删除
        new_children = []
        for child in dom["children"]:
            if child["remain_flag"] is True:
                new_children.append(child)
            else:
                if "children" not in child:
                    continue
                for grandchild in child["children"]:
                    new_children.append(grandchild)
        dom["children"] = new_children

        # 判断当前节点是否需要被保留
        children_overlap_ratio = self.get_children_overlap_ratio(dom)
        if children_overlap_ratio > DST.PARENT_NODE_FILL_RATIO:
            logger.debug("overlapping doe = {}".format(self.copy_node_exclude(dom, ["children"])))
            dom["remain_flag"] = False
        else:
            dom["remain_flag"] = True
            remain_nodes_list.append(self.copy_node_exclude(dom, ["children"]))
        
        return
    
    def remain_nodes(self, dom):
        """
        只保留必要元素
        保留策略：叶子节点全部保留；从叶子节点往上，如果一个节点被其所有子节点覆盖超过一定比例，去除该节点
        """
        remain_nodes_list = []
        self.recursive_remain_nodes(dom, remain_nodes_list)
        return remain_nodes_list
    
    def remove_dfe_elements(self, remain_nodes_list, dfe_list):
        """
        去除在DFE中重复的元素
        """
        dfe_id_list = []
        for node in dfe_list:
            dfe_id_list.append(node["tmp_id"])
        doe_list = []
        for node in remain_nodes_list:
            is_legal, _ = self.check_element_legal(node)
            if is_legal is False:
                continue
            if node["tmp_id"] not in dfe_id_list:
                doe_list.append(node)
        return doe_list
    
    def ios_remove_sequence_abnormal_elements(self, raw_dom):
        """
        清理屏下元素 - 方案：清理顺序异常的cell元素 - iOS下
        """
        if "children" not in raw_dom or len(raw_dom["children"]) == 0:
            return
        new_children = []
        cell_children = []
        check_types = ["Cell"]
        for child in raw_dom["children"]:
            # 判断子元素是否是cell类型
            if child["type"] not in check_types:
                new_children.append(child)
            else:
                cell_children.append(child)
        
        if len(cell_children) > 0:
            # 判断cell子节点是否是顺序的
            # 先压入第一个元素
            new_children.append(cell_children[0])
            for index in range(1, len(cell_children)):
                if cell_children[index]["rect"]["y"] <= new_children[-1]["rect"]["y"]:
                    continue
                else:
                    new_children.append(cell_children[index])

        raw_dom["children"] = new_children
        for child in raw_dom["children"]:
            self.ios_remove_sequence_abnormal_elements(child)
        return
    
    def dom_pre_filter(self, raw_dom):
        """
        前置统一过滤操作
        """
        # 清理屏下元素 - 方案：清理顺序异常的cell元素 - iOS下
        if self.os_type == 2:
            self.ios_remove_sequence_abnormal_elements(raw_dom)

        # 清理不合法节点
        self.remove_illegal_elements_in_dom(raw_dom)
        # logger.debug("raw_dom: {}".format(json.dumps(raw_dom)))

        

    def get_dom_other_element(self, raw_dom, dfe_list):
        """
        获取其它的有效dom元素
        """
        # 已经执行过了
        # dom = self.dom_pre_filter(raw_dom)
        # 调整元素大小
        # self.adjust_child_node_size(raw_dom)

        # 只保留必要元素
        remain_nodes_list = self.remain_nodes(raw_dom)

        # 去除在DFE中重复的元素
        doe_list = []
        doe_list = self.remove_dfe_elements(remain_nodes_list, dfe_list)

        return doe_list