#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@Desc    :
@File    :   replay_widget_model.py
@Time    :   2025-05-31
<AUTHOR>   x<PERSON><PERSON><PERSON>@baidu.com
"""
import json
import traceback

import cv2
import numpy as np

from basics.util import logger
from .basic import PROJECT_VERSION
from .lib import subgraph_search as ss
from .lib.calculate_rect_area import calculate_intersection
from .lib.graph_struct import GraphStruct
from .multi_syne_record import MultiSyneRecordExecute
from .multi_syne_replay import MultiSyneReplayExecute
from .record_widget_model import RecordWidgetModelExecute
from .single_feature_replay import SingleFeatureReplayExecute
from ..base.execute_base import ExecuteBase
from ..base.local_strategies_errcode import ErrCode
from ..base.widget_tag import SINGLE, MULTIPLE, VISUAL, SYSTEM

class ReplayWidgetModelExecute(ExecuteBase):
    """
    通用回放接口，module_name路由具体回放模块
    """

    def __init__(self):
        """
        初始化
        """
        super().__init__()
        self.diff_codes = []

    def params_check(self, params, module_info_params, widget_info_params):
        """
        入参校验
        """
        lost_params = []

        if "module_info" not in params:
            return False, "module_info"

        for module_info in module_info_params:
            if module_info not in params["module_info"]:
                lost_params.append(module_info)

        if "widget_info" in params["module_info"]:
            for widget_info in widget_info_params:
                if widget_info not in params["module_info"]["widget_info"]:
                    lost_params.append(widget_info)

        if len(lost_params) == 0:
            return True, ""
        else:
            return False, ",".join(lost_params)

    def update_widget_list(self, params, record_widget_list):
        """
        格式化输入参数，并返回格式化后的参数。

        :param params: 输入参数
        :return: 格式化后的参数
        """
        correct_params = True
        used_widget_list = []

        widget_info = params["module_info"]["widget_info"]
        used_ocr_info = widget_info["ocr_info"].get("used", False)
        if used_ocr_info:
            # 传入了ocr_info
            used_widget_list.append("ocr_info")

        used_beta_info = widget_info["beta_info"].get("used", False)
        if used_beta_info:
            # 传入了beta_info
            used_widget_list.append("beta_info")

        used_icon_info = widget_info["icon_info"].get("used", False)
        if used_icon_info:
            # 传入了icon_info
            used_widget_list.append("icon_info")

        used_ele_info = widget_info["ele_info"].get("used", False)
        if used_ele_info:
            # 传入了组件ele_info
            used_widget_list.append("ele_info")

        used_dom_info = widget_info["dom_info"].get("used", False)
        if used_dom_info:
            # 传入了dom_info
            used_widget_list.append("dom_info")

        for widget_name in record_widget_list:
            if widget_name not in used_widget_list:
                # 如果建模时使用的widget，回放时没有传入，则返回错误
                correct_params = False
                return used_widget_list, correct_params

        # 将建模时未用到的widget，used参数设置为False，保证回放与建模时一致
        record_widget_set = set(record_widget_list)
        replay_widget_set = set(used_widget_list)
        replay_diff_list = list(replay_widget_set - record_widget_set)
        for diff_widget_name in replay_diff_list:
            params["module_info"]["widget_info"][diff_widget_name]["used"] = False

        return used_widget_list, correct_params

    def tag_satisfy_user_choice(self, user_choice, tags):
        """
        判断控件的tag是否满足用户选择

        :param: 用户选择，控件的tag
        :return: 是否满足用户选择
        """
        satisfy_single_multiple = False
        satisfy_visual_system = False
        for tag in tags:
            if tag == SINGLE or tag == MULTIPLE:
                if tag in user_choice:
                    satisfy_single_multiple = True
            if tag == VISUAL or tag == SYSTEM:
                if tag in user_choice:
                    satisfy_visual_system = True

        return satisfy_single_multiple and satisfy_visual_system


    def get_replay_widget(self, params):
        """
        调建模接口，获取回放页面结果

        :param params: 输入参数
        :return: 格式化后的参数
        """
        rwm = RecordWidgetModelExecute()
        replay_rwm = rwm.main(params)
        if replay_rwm["code"] != ErrCode.Success.Code:
            return None, replay_rwm

        replay_dom = replay_rwm["data"]["dom"]
        return replay_dom, replay_rwm

    def filter_by_user_choice(self, replay_dom, user_choice):
        """
        准备返回result，保留最外层的page，children置空
        """
        result = replay_dom
        children = []

        logger.debug("[Batdom Debug] user_choice: {}".format(user_choice))
        for dom in replay_dom["children"]:
            tags = dom["ext"]["tag"]
            if self.tag_satisfy_user_choice(user_choice, tags):
                children.append(dom)

        result["children"] = children
        return result

    def _add_diff_code(self, result):
        # print(result)
        if 'data' not in result:
            return
        for node in result['data']['match_nodes']:
            node['diffCodes'] = self.diff_codes

    def main(self, params):
        """
        主流程函数
        参数可参考：https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/3LoFUu-M_a/LWyELLqriZ/sXgZHfSSh6uO7u
        params:
            {
                "module_name": "replay_widget_model_v2",
                "module_info": {
                    "widget_info": {
                        "ocr_info": {"used": true, "data": ocr_r['ocr_result']},
                        "icon_info": {"used": true, "data": icon_r['dom_tree']},
                        "ele_info": {"used": true, "data": ele_r['element_res']},
                        "beta_info": {"used": true, "data": beta_r['dom_tree']},
                        "dom_info": {"used": true, "data": {}}
                    },
                    "os_type": 2,
                    "step_info": "${step_info}",
                    "image_path": "${回放截图本地路径}",
                    "case_image_path": "${建模截图本地路径}"
                },
            }
        step_info:
            {
                "findInfo": { // 回放用到的匹配参数
                    "findNode": [{}], // 选择的控件列表
                    "chosenTag": [ // 用户选择的控件涉及的tag
                        "multiple",
                        "visual"
                    ],
                    "findType": 0, // 0-根据序号进行匹配（index模式）、1-根据位置相似度匹配（byDistance模式）
                    "useFastSAM": false, // 是否使用fastSAM图像分割技术进行辅助召回
                    "rect": { // 寻找控件的区域，如果不传默认在全屏查找（不传即为 undefined）
                        "x": 11,
                        "y": 22,
                        "w": 33,
                        "h": 44
                    }
                },
                "recordInfo": { // 建模相关信息
                    "name": "recordWidgetModel",
                    "used_widget_list": [
                        "ocr_info",
                        "beta_info",
                        "icon_info",
                        "ele_info"
                    ],
                    "version": "v10.1.0",
                    "recordResult": { // 建模结果
                    }
                }
            }
        """
        try:
            # 入参校验
            params_check, lost_params = self.params_check(
                params,
                ["widget_info", "step_info", "os_type", "image_path", "case_image_path"],
                ["ocr_info", "icon_info", "ele_info", "beta_info", "dom_info"]
            )
            if params_check is False:
                logger.error("replay_widget_model missing params: {}".format(lost_params))
                result = {
                    "code": ErrCode.ParamsLost.Code,
                    "msg": ErrCode.ParamsLost.Msg.format(lost_params)
                }
                return result

            module_info = params["module_info"]
            # 校验回放的widget_list是否包含录制的widget_list
            record_widget_list = module_info["step_info"]["recordInfo"]["used_widget_list"]
            replay_widget_list, correct_update = self.update_widget_list(params, record_widget_list)
            if not correct_update:
                result = {
                    "code": ErrCode.ParamsLost.Code,
                    "msg": "missing widget, record_widget_list:{}, replay_widget_list:{}"
                    .format(record_widget_list, replay_widget_list)
                }
                return result

            # 如果findNode含有业务专属模型的控件，将product_id设置到参数easy_detect，
            # 表示在建模时再调用一次easyDL接口
            find_node_list = params["module_info"]["step_info"]["findInfo"]["findNode"]
            for node in find_node_list:
                if "product_id" in node["detailFeature"]["ext"]:
                    params["easy_detect"] = node["detailFeature"]["ext"]["product_id"]
                    break

            # 获取回放页面的建模结果
            # 塞一个relaying参数，表示这是回放的使用调用的建模能力，需要保留ocr的charset
            params['replaying'] = True
            if params["module_info"].get("replay_dom", None) is None:
                replay_dom, replay_err_result = self.get_replay_widget(params)
            else:
                # 允许自行传入建模信息
                replay_dom = params["module_info"]["replay_dom"]
                replay_err_result = None
            user_choice = module_info["step_info"]["findInfo"]["chosenTag"]
            # 根据用户选择，过滤不需要的控件
            replay_dom = self.filter_by_user_choice(replay_dom, user_choice)

            logger.debug("[Batdom Debug]replay_dom: {}".format(replay_dom))
            if replay_dom is None:
                # 报错，返回建模失败结果
                return replay_err_result

            # 根据用户选择，调用不同的回放模块
            params["module_info"]["replay_dom"] = replay_dom
            result = None
            if SINGLE in user_choice:
                # 单控件回放
                sfr = SingleFeatureReplayExecute()
                result = sfr.main(params["module_info"])
            if MULTIPLE in user_choice:
                # 多控件回放
                msr = MultiSyneReplayExecute()
                result = msr.main(params["module_info"])

            # 这里对区域查找进行识别，处理
            if 'rect' in module_info["step_info"]["findInfo"] and \
                    module_info["step_info"]["findInfo"]["rect"] is not None:
                base_area = module_info["step_info"]["findInfo"]["rect"]
                # 判断找到的控件在不在目标区域，如果不在，就返回不在目标区域
                # logger.debug("[Batdom Debug] result: {}".format(result))
                if 'data' not in result or result.get('code', -1) != 0:
                    return result
                action_area = result['data']['action_area']
                ratio = calculate_intersection(base_area, action_area) / (action_area['w'] * action_area['h'] + 1e-6)
                if ratio < 0.8:
                    result = {
                        "code": ErrCode.ActionAreaNotInBaseError.Code,
                        "version": PROJECT_VERSION,
                        "msg": ErrCode.ActionAreaNotInBaseError.Msg,
                        "show_msg": ErrCode.ActionAreaNotInBaseError.ShowMsg
                    }
                    return result
            self._add_diff_code(result)
            return result

        except Exception as e:
            logger.error("replay_widget_model uncaught Error: {}".format(traceback.format_exc()))
            result = {
                "code": ErrCode.UnknownError.Code,
                "version": PROJECT_VERSION,
                "msg": ErrCode.UnknownError.Msg,
                "show_msg": ErrCode.UnknownError.ShowMsg
            }
            return result