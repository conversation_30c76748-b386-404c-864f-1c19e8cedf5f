#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@Desc    :   
@File    :   record_widget_webcustomized.py
@Time    :   2024-06-28
<AUTHOR>   xuz<PERSON><PERSON>@baidu.com
"""
import json
import traceback

from basics.text_process import merge_ocr, ocr_pattern
from basics.util import logger
from .basic import PROJECT_VERSION
from .lib.sum_map import SMap
from .record_widget_model import RecordWidgetModelExecute
from ..base.execute_base import ExecuteBase
from ..base.batdom_node_type import BatdomNodeType
from ..base.widget_tag import SINGLE, MULTIPLE, VISUAL, SYSTEM


class RecordWidgetWebcustomizedExecute(ExecuteBase):
    """
    通用建模接口，返回全部输入的控件，供前端自由选择
    """
    def format_card_info(self, card_info):
        """
        格式化卡片信息
        """
        card_list = []
        try:
            detect_res = card_info
            for ret in detect_res:
                iterm = {
                    "type": BatdomNodeType.CARD.name,
                    "rect": {
                        "x": ret["left"],
                        "y": ret["top"],
                        "width": ret["width"],
                        "height": ret["height"]
                    },
                    "conf": ret["confidence"],
                    "name": ret["name"],
                    "tag": [SINGLE, MULTIPLE, VISUAL]
                }
                card_list.append(iterm)
        except:
            logger.error("format_card_info Error: {}".format(traceback.format_exc()))
        return card_list
    
    def get_node_cross_area(self, p, c):
        """
        获取 p 和 c 的相交面积
        """
        p_x1 = p["rect"]["x"]
        p_x2 = p["rect"]["x"] + p["rect"]["w"]
        p_y1 = p["rect"]["y"]
        p_y2 = p["rect"]["y"] + p["rect"]["h"]

        c_x1 = c["rect"]["x"]
        c_x2 = c["rect"]["x"] + c["rect"]["w"]
        c_y1 = c["rect"]["y"]
        c_y2 = c["rect"]["y"] + c["rect"]["h"]
        # 计算重叠部分面积
        maxx = max(p_x1, c_x1)
        minx = min(p_x2, c_x2)
        maxy = max(p_y1, c_y1)
        miny = min(p_y2, c_y2)
        cross_area = 0
        if minx > maxx and miny > maxy:
            cross_area = (maxx - minx) * (maxy - miny)
        return cross_area

    def main(self, params):
        """
        主函数
        """
        rwme = RecordWidgetModelExecute()
        raw_dom = rwme.main(params)
        raw_dom["data"]["info"]["name"] = "webcustomized"

        # 判断是否有card_info
        # 没有直接返回原始信息
        if "card_info" not in params["module_info"]["widget_info"]:
            return raw_dom
        
        # 如果有card_info，则需要对原始信息进行处理
        element_list = raw_dom["data"]["dom"]["children"]
        new_children = []
        card_list = []
        card_raw_list = self.format_card_info(params["module_info"]["widget_info"]["card_info"]["data"]["detect_res"])
        
        for card in card_raw_list:
            element = {
                "type": BatdomNodeType.CARD.name,
                "ext": {
                    "name": "{}".format(card["name"]),
                    "tag": card["tag"],
                },
                "debug": {},
                "rect": {
                    "x": card["rect"]["x"],
                    "y": card["rect"]["y"],
                    "w": card["rect"]["width"],
                    "h": card["rect"]["height"],
                },
                "children": []
            }
            card_list.append(element)

        for item in element_list:
            # 判断item和哪个card存在包含关系：
            # 如果没有，则直接放在根节点
            # 如果有多个，则选择重合最高的那个
            
            inculde_list = []
            for card in card_list:
                cross_area = self.get_node_cross_area(item, card)
                if cross_area > 0:
                    inculde_list.append((card, cross_area))
            if len(inculde_list) > 0:
                inculde_list = sorted(inculde_list, key=lambda x: x[1], reverse=True)
                parent = inculde_list[0][0]
                parent["children"].append(item)
            else:
                new_children.append(item)
        for card in card_list:
            new_children.append(card)
        
        raw_dom["data"]["dom"]["children"] = new_children
        return raw_dom
