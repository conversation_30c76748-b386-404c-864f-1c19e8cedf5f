#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@Desc    :   
@File    :   single_feature_replay.py
@Time    :   2024-02-20
<AUTHOR>   xuz<PERSON><PERSON>@baidu.com
"""
import re
import traceback
import json
import cv2
import numpy as np

from basics.util import logger
from basics.image.ui.img_similarity import is_target_from_similarity
from .lib.const import FIND_BY_INDEX, FIND_BY_DISTANCE
from ..base.execute_base import ExecuteBase
from ..base.local_strategies_errcode import ErrCode
from .single_feature_record import SingleFeatureRecordExecute
from .lib.check_node_detail import (check_node_feature, check_node_self, fallback_check_node_feature,
                                    build_match_cache, check_overlap, find_by_dist, find_by_index)

from .basic import PROJECT_VERSION
from ...cv_services.circle_detect import detect_circles
from ...cv_services.sam_detect import fast_sam_detect
from .lib.dom_split_threshold import DomSplitThreshold as DST


class SingleFeatureReplayExecute(ExecuteBase):
    """
    单特征回放类
    """

    def __init__(self):
        """
        初始化类 
        """
        super().__init__()

    def check_params_by_model_type(self, params, model_type):
        """
        根据model_type判断入参是否完整
        """
        if int(model_type) == 2:
            if "root_elements" not in params:
                return False
        return True

    def get_chosen_nodes(self, step_info):
        """
        获取选择 nodes 的列表
        """
        try:
            # 因为是单控件，所以只会有一个 findNode
            find_node = step_info["findInfo"]["findNode"][0]
            logger.info("find_node: {}".format(json.dumps(find_node)))
            return True, find_node
        except:
            logger.error(traceback.format_exc())
            logger.error("step_info: {}".format(step_info))
            return False, None

    def cut_img_by_rect(self, image, rect):
        """
        根据 rect 裁剪图片
        """
        # 获取裁剪后的图片
        cut_image = np.zeros((
            rect["h"], rect["w"], 3
        ), dtype=np.uint8)
        for line_no in range(rect["h"]):
            for column_no in range(rect["w"]):
                cut_image[line_no][column_no] = \
                    image[line_no + rect["y"]][column_no + rect["x"]]

        return cut_image

    def _get_replay_text_list(self, replay_dom_infos, merge_ocr=False):
        replay_text_list = []
        if merge_ocr is False:
            # 如果没开启OCR合并，就展平合并的OCR框
            for replay_dom_info in replay_dom_infos:
                if replay_dom_info["type"] == "Text":
                    replay_text_list.append(replay_dom_info)
                if replay_dom_info["type"] == "TextArea":
                    for child in replay_dom_info['children']:
                        replay_text_list.append(child)
        else:
            for replay_dom_info in replay_dom_infos:
                if replay_dom_info["type"] in ["Text", "TextArea"]:
                    replay_text_list.append(replay_dom_info)
        # 根据文字排序
        replay_text_list.sort(key=lambda x: (x["rect"]["y"], x["rect"]["x"]))
        return replay_text_list

    def _single_text_match(self, replay_image, replay_dom_infos, find_node, settings, shape_info):
        """
        文本单控件匹配逻辑
        """
        find_type = settings['findType']

        # 文本匹配特殊处理
        # 先获取replay中的文本信息
        replay_text_list = self._get_replay_text_list(replay_dom_infos, settings['mergeOCR'])
        find_text_list = []
        for replay_text_info in replay_text_list:
            def on_ocr_match(param):
                """定义ocr匹配到时的回调"""
                if 'target_ocr_chars' in param:
                    replay_text_info['target_ocr_chars'] = param['target_ocr_chars']

            check_res = check_node_feature(replay_image,
                                           replay_text_info,
                                           find_node["detailFeature"],
                                           on_match=on_ocr_match, index_flag=False)
            if check_res is True:
                find_text_list.append(replay_text_info)
        # 获取index
        logger.debug("replay_text_list: {}".format(json.dumps(replay_text_list)))
        logger.debug("find_text_list: {}".format(json.dumps(find_text_list)))
        if len(find_text_list) == 0:
            return None
        if find_type == FIND_BY_DISTANCE:
            return find_by_dist(find_text_list, find_node["detailFeature"], shape_info)
        else:
            return find_by_index(find_text_list, find_node["detailFeature"])

    def _single_component_match(self, record_image, replay_image, replay_dom_infos, find_node, settings, shape_info):
        """
        组件单控件匹配逻辑
        """
        max_sim = 0
        max_sim_info = None
        match_cache = {}
        # 如果是用户找的是组件，先运行批量特征比对，生成match_cache
        rep_list = [node for node in replay_dom_infos if node['type'] == 'Component']
        build_match_cache(find_node["detailFeature"], rep_list, record_image, replay_image, match_cache)

        for replay_dom_info in replay_dom_infos:
            # 单控件加上节点形状比对
            shape_info = {
                "record": {
                    "height": record_image.shape[0],
                    "width": record_image.shape[1]
                },
                "replay": {
                    "height": replay_image.shape[0],
                    "width": replay_image.shape[1]
                }
            }
            if not check_node_self(find_node, replay_dom_info, shape_info):
                # 这里加入形状比对逻辑
                continue

            # 如果是组件类型，则不会立刻return，而是更新最大相似度和对应的replay节点，循环结束后再返回最大相似度节点
            def callback(params):
                """
                组件匹配时的回调函数，帮助组件类型找到相似度最大的对象
                """
                nonlocal max_sim, max_sim_info

                if 'similarity' in params and params['similarity'] > max_sim and params['res']:
                    max_sim = params['similarity']
                    max_sim_info = params['dom_info']

            check_res = check_node_feature(replay_image, replay_dom_info, find_node["detailFeature"],
                                           on_match=callback, match_cache=match_cache, index_flag=False)

        if max_sim_info is not None:
            return max_sim_info
        else:
            return None

    def _single_default_match(self, record_image, replay_image, replay_dom_infos, find_node, settings, shape_info):
        """
        默认单控件匹配逻辑，适用于：图标、DFE、DOE
        """
        find_type = settings['findType']
        find_list = []
        for replay_dom_info in replay_dom_infos:
            # 单控件加上节点形状比对
            shape_info = {
                "record": {
                    "height": record_image.shape[0],
                    "width": record_image.shape[1]
                },
                "replay": {
                    "height": replay_image.shape[0],
                    "width": replay_image.shape[1]
                }
            }
            if not check_node_self(find_node, replay_dom_info, shape_info):
                # 这里加入形状比对逻辑
                continue
            check_res = check_node_feature(replay_image, replay_dom_info, find_node["detailFeature"], index_flag=False)
            if check_res is True:
                find_list.append(replay_dom_info)
        if len(find_list) == 0:
            return None
        if find_type == FIND_BY_DISTANCE:
            return find_by_dist(find_list, find_node['detailFeature'], shape_info)
        else:
            return find_by_index(find_list, find_node['detailFeature'])

    def single_feature_match(self, record_image, replay_image, replay_dom_infos, find_node, settings=None):
        """
        进行单控件特征匹配
        """
        shape_info = {
            "record": {
                "height": record_image.shape[0],
                "width": record_image.shape[1]
            },
            "replay": {
                "height": replay_image.shape[0],
                "width": replay_image.shape[1]
            }
        }
        if not settings:
            settings = {"mergeOCR": False}
        if find_node["detailFeature"]["type"] in ["Text", "TextArea"]:
            target = self._single_text_match(replay_image, replay_dom_infos, find_node, settings, shape_info)
            if target is not None:
                return target
                # 如果用户选择的控件为文本，判断文本中是否存在易混淆字符“I”、“l”，将其统一替换为“I”
            goal_text = find_node["detailFeature"]["ext"]["text"]
            if 'I' in goal_text or 'l' in goal_text:
                find_node["detailFeature"]["ext"]["text"] = goal_text.replace('l', 'I')
                # 找到dom中所有的文本控件，将l替换成I，同时将下层的字符也替换成I
                for dom in replay_dom_infos:
                    if dom["type"] == "TextArea":
                        dom["ext"]["text"] = dom["ext"]["text"].replace('l', 'I')
                        for child in dom.get('children', []):
                            child["ext"]["text"] = child["ext"]["text"].replace('l', 'I')
                            for c in child.get('charset', []):
                                if c['word'] == 'l':
                                    c['word'] = 'I'
                    elif dom["type"] == "Text":
                        dom["ext"]["text"] = dom["ext"]["text"].replace('l', 'I')
                        for c in dom.get('charset', []):
                            if c['word'] == 'l':
                                c['word'] = 'I'
            return self._single_text_match(replay_image, replay_dom_infos, find_node, settings, shape_info)
        else:
            find_res = None
            if find_node["detailFeature"]['type'] == 'Component':
                find_res = self._single_component_match(record_image, replay_image,
                                                        replay_dom_infos, find_node, settings, shape_info)
            else:
                find_res = self._single_default_match(record_image, replay_image,
                                                      replay_dom_infos, find_node, settings, shape_info)

            if find_res is not None:
                return find_res

            fallback_dom_infos = replay_dom_infos[:]
            circles = detect_circles(replay_image)
            # print('before c', len(circles))
            circles = check_overlap(fallback_dom_infos, circles)
            # print('after c', len(circles))
            fallback_dom_infos.extend(circles)
            if settings['useFastSAM']:
                sam_list = fast_sam_detect(replay_image)
                # print('before s', len(sam_list))
                sam_list = check_overlap(fallback_dom_infos, sam_list)
                # print('after s', len(sam_list))
                fallback_dom_infos.extend(sam_list)

            # 如果第一轮比对失败，运行兜底策略
            return fallback_check_node_feature(replay_image, fallback_dom_infos,
                                               find_node["detailFeature"], record_image, shape_info, settings)
        return None

    def format_result(self, find_node, match_node):
        """
        格式化输出结果
        """

        action_area = match_node["rect"]
        if 'target_ocr_chars' in match_node:
            cs = match_node['target_ocr_chars']
            x1 = min([c['rect']['left'] for c in cs])
            y1 = min([c['rect']['top'] for c in cs])
            x2 = max([c['rect']['left'] + c['rect']['width'] for c in cs])
            y2 = max([c['rect']['top'] + c['rect']['height'] for c in cs])

            action_area = {
                'x': x1,
                'y': y1,
                'w': x2 - x1,
                'h': y2 - y1
            }

        result = {
            "code": 0,
            "msg": "success",
            "data": {
                "match_nodes": [{
                    "id": match_node["debug"]["id"],
                    "rect": match_node["rect"],
                    "record_id": find_node["id"],
                    "record_rect": find_node["detailFeature"]["rect"],
                    "ext": match_node["ext"],
                    "type": match_node["type"]
                }],
                "action_area": action_area,
            }
        }
        return result

    def main(self, params):
        """
        主流程函数
        """
        try:
            # 获取目标元素信息
            get_flag, find_node = self.get_chosen_nodes(params["step_info"])
            if get_flag is False or find_node is {}:
                result = {
                    "code": ErrCode.RecordGoalNodesFindError.Code,
                    "version": PROJECT_VERSION,
                    "msg": ErrCode.RecordGoalNodesFindError.Msg,
                    "show_msg": ErrCode.RecordGoalNodesFindError.ShowMsg
                }
                return result

            # 获取目标元素的区域图片 —— TODO
            record_image = cv2.imread(params["case_image_path"])
            find_node_image = self.cut_img_by_rect(record_image, find_node["detailFeature"]["rect"])
            find_node["detailFeature"]["image"] = find_node_image

            replay_image = cv2.imread(params["image_path"])

            # # debug
            # debug_image_info = {
            #     "record": {
            #         "height": record_image.shape[0],
            #         "width": record_image.shape[1]
            #     }
            # }
            # check_node_self(find_node, None, debug_image_info)

            settings = None
            if "findInfo" in params["step_info"]:
                if 'mergeOCR' not in params["step_info"]["findInfo"]:
                    params["step_info"]["findInfo"]['mergeOCR'] = False
                if 'useFastSAM' not in params["step_info"]["findInfo"]:
                    params["step_info"]["findInfo"]['useFastSAM'] = False
                if 'findType' not in params["step_info"]["findInfo"]:
                    params["step_info"]["findInfo"]['findType'] = FIND_BY_INDEX
                settings = {
                    "mergeOCR": params["step_info"]["findInfo"]["mergeOCR"],
                    "useFastSAM": params["step_info"]["findInfo"]['useFastSAM'],
                    "findType": params["step_info"]["findInfo"]['findType']
                }
            # 进行单控件特征匹配 - 因为打平，所以只会对children进行匹配
            replay_dom_children = params["replay_dom"]["children"]
            logger.debug("find_node: {}".format(find_node))
            logger.debug("replay_dom: {}".format(params["replay_dom"]))
            match_node = self.single_feature_match(record_image, replay_image, replay_dom_children, find_node, settings)
            if match_node is None:
                result = {
                    "code": ErrCode.StrictMatchError.Code,
                    "version": PROJECT_VERSION,
                    "msg": ErrCode.StrictMatchError.Msg,
                    "show_msg": ErrCode.StrictMatchError.ShowMsg
                }
                return result

            result = self.format_result(find_node, match_node)
            return result

        except Exception as e:
            logger.error("structure_match uncaught Error: {}".format(traceback.format_exc()))
            result = {
                "code": ErrCode.UnknownError.Code,
                "version": PROJECT_VERSION,
                "msg": ErrCode.UnknownError.Msg,
                "show_msg": ErrCode.UnknownError.ShowMsg
            }
            return result
