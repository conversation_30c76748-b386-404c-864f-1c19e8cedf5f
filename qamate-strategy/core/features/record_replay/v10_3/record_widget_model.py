#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@Desc    :
@File    :   record_widget_model.py
@Time    :   2024-05-28
<AUTHOR>   <EMAIL>
"""
import json
import re
import traceback

import cv2

from basics.text_process import merge_ocr, ocr_pattern
from basics.util import logger
from .basic import PROJECT_VERSION
from .lib.sum_map import SMap
from .lib.easydl_model import EasyDLModel
from ..base.batdom_node_type import BatdomNodeType
from ..base.execute_base import ExecuteBase
from ..base.local_strategies_errcode import ErrCode
from .lib.name_reflector import get_icon_name_dict, get_beta_icon_name_dict
from .lib.dom_split_threshold import DomSplitThreshold as DST
from .lib.dom_element_handle import DomElementHandle
from ..base.widget_tag import SINGLE, MULTIPLE, VISUAL, SYSTEM


class RecordWidgetModelExecute(ExecuteBase):
    """
    通用建模接口，返回全部输入的控件，供前端自由选择
    """

    def __init__(self):
        """
        初始化
        """
        super().__init__()
        self.width = 0
        self.height = 0
        self.goal_feature_dict = {}
        self.easydl_model = EasyDLModel()

    def extract_basic_info(self, params):
        """
        提取一些基本信息
        """
        img = cv2.imread(params["image_path"])
        self.height = img.shape[0]
        self.width = img.shape[1]

    def params_check(self, params, module_info_params, widget_info_params):
        """
        入参校验
        """
        lost_params = []

        if "module_info" not in params:
            return False, "module_info"

        for module_info in module_info_params:
            if module_info not in params["module_info"]:
                lost_params.append(module_info)

        if "widget_info" in params["module_info"]:
            for widget_info in widget_info_params:
                if widget_info not in params["module_info"]["widget_info"]:
                    lost_params.append(widget_info)

        if len(lost_params) == 0:
            return True, ""
        else:
            return False, ",".join(lost_params)

    def add_raw_id(self, dom, tmp_id):
        """
        添加一个临时id用于去重
        """
        tmp_id[0] += 1
        dom["tmp_id"] = tmp_id[0]
        if "children" in dom:
            for child in dom["children"]:
                self.add_raw_id(child, tmp_id)

    def filter_dfe_info(self, dfe_info, os_type):
        """
        活率dfe元素
        """
        new_dfe_list = []
        if os_type == 2:
            for dom in dfe_info:
                if dom["rect"]["x"] < 0 or dom["rect"]["y"] < 0:
                    continue

                if "type" not in dom or dom["type"] == "Other":
                    continue

                goal_attr_list = ["name", "label", "value"]

                filter_flag = True
                content = ""
                for attr in goal_attr_list:
                    if attr in dom and dom[attr] != "" and dom[attr] is not None:
                        filter_flag = False
                        content = dom[attr]
                        break
                if filter_flag:
                    continue
                dom["content"] = content
                new_dfe_list.append(dom)
        return new_dfe_list

    def get_dom_all_element(self, os_type, root_elements):
        """
        获取特定的有效dom元素
        """
        dom = {}
        if os_type == 1:
            # Android
            self.goal_feature_dict = {
                "text": [],
                "content-desc": [],
                "resource-id": [],
            }
        elif os_type == 2:
            # iOS
            self.goal_feature_dict = {
                "label": [],
                "name": [],
                "type": []
            }
        self.dom_handler = DomElementHandle(self.width, self.height, os_type, self.goal_feature_dict)

        if os_type == 1:
            # Android
            dom = self.dom_handler.android_init(root_elements)
        elif os_type == 2:
            # iOS
            dom = root_elements

        # 添加一个临时id用于去重
        raw_id = [-1]
        self.add_raw_id(dom, raw_id)
        logger.debug("input_raw_dom: {}".format(json.dumps(dom)))

        # 先对dom进行前置过滤
        # self.dom_handler.dom_pre_filter(dom)
        # logger.debug("dom_pre_filter: {}".format(json.dumps(dom)))

        dfe_info = []
        self.dom_handler.recursive_gdfe(dom, dfe_info)
        logger.info("dfe_info_len: {}".format(len(dfe_info)))
        dfe_info = self.filter_dfe_info(dfe_info, os_type)
        logger.info("dfe_info_len: {}".format(len(dfe_info)))
        for dfe in dfe_info:
            logger.info("dfe_info: {}".format(dfe))

        # 对dfe元素进行处理
        logger.debug("dfe_info: {}".format(dfe_info))
        doe_info = []
        # doe_info = self.dom_handler.get_dom_other_element(dom, dfe_info)
        logger.info("doe_info_len: {}".format(len(doe_info)))
        logger.debug("doe_info: {}".format(doe_info))

        return dfe_info, doe_info

    def format_ocr_info(self, ocr_info):
        """
        格式化文本信息
        """
        ocr_list = []
        ext_icon_list = []
        ext_ele_list = []

        try:
            ocr_ret = ocr_info["ret"]
            for ret in ocr_ret:
                word = ret["word"]
                if len(word) == 1:
                    # 如果是单个特殊字符，将类型提升为icon或Component
                    if word in ('x', 'X', '✗', '✘', '×', '✕', '☓', '✖'):
                        iterm = {
                            "type": "Icon",
                            "rect": {
                                "x": ret["rect"]["left"],
                                "y": ret["rect"]["top"],
                                "width": ret["rect"]["width"],
                                "height": ret["rect"]["height"]
                            },
                            "name": "close",
                            "tag": [SINGLE, MULTIPLE, VISUAL]
                        }
                        ext_icon_list.append(iterm)
                        continue
                    if word in ('+', '十', '✚', '✛', '✜', '✝'):
                        iterm = {
                            "type": "Icon",
                            "rect": {
                                "x": ret["rect"]["left"],
                                "y": ret["rect"]["top"],
                                "width": ret["rect"]["width"],
                                "height": ret["rect"]["height"]
                            },
                            "name": "add",
                            "tag": [SINGLE, MULTIPLE, VISUAL]
                        }
                        ext_icon_list.append(iterm)
                        continue
                    if word in ('一', '-', '_', '‐', '‑', '‒', '–', '—', '―', '−', '¯', 'ˉ', '⎯', '₋'):
                        iterm = {
                            "type": "Component",
                            "rect": {
                                "x": ret["rect"]["left"],
                                "y": ret["rect"]["top"],
                                "width": ret["rect"]["width"],
                                "height": ret["rect"]["height"]
                            },
                            "name": "icon",
                            "tag": [SINGLE, MULTIPLE, VISUAL]
                        }
                        ext_ele_list.append(iterm)
                        continue

                w = ocr_pattern.drop_not_in_pattern(word)
                if len(w) == 0:
                    continue
                new_charset = [c for c in ret['charset'] if c['word'] in w]
                iterm = {
                    "type": "Text",
                    "rect": {
                        "x": ret["rect"]["left"],
                        "y": ret["rect"]["top"],
                        "width": ret["rect"]["width"],
                        "height": ret["rect"]["height"]
                    },
                    "word": w,
                    "charset": new_charset,
                    "tag": [SINGLE, MULTIPLE, VISUAL]
                }
                ocr_list.append(iterm)
        except:
            logger.error("format_ocr_info Error: {}".format(traceback.format_exc()))
        return ocr_list, ext_icon_list, ext_ele_list

    def format_beta_info(self, beta_info):
        """
        格式化文本信息
        """
        beta_list = []
        try:
            detect_res = beta_info.get("detect_res", [])
            for ret in detect_res:
                iterm = {
                    "type": BatdomNodeType.ICON.name,
                    "rect": {
                        "x": ret["left"],
                        "y": ret["top"],
                        "width": ret["width"],
                        "height": ret["height"]
                    },
                    "conf": ret["confidence"],
                    "name": ret["name"],
                    "tag": [SINGLE, MULTIPLE, VISUAL]
                }
                beta_list.append(iterm)
        except:
            logger.error("format_beta_info Error: {}".format(traceback.format_exc()))
        return beta_list

    def format_icon_info(self, icon_info, comp_info):
        """
        格式化文本信息
        """
        icon_list = []
        try:
            detect_res = icon_info["detect_res"]
            for ret in detect_res:
                iterm = {
                    "type": "Icon",
                    "rect": {
                        "x": ret["left"],
                        "y": ret["top"],
                        "width": ret["width"] - 1,
                        "height": ret["height"] - 1
                    },
                    "name": ret["name"],
                    "tag": [SINGLE, MULTIPLE, VISUAL]
                }
                icon_list.append(iterm)

            # 组件的search box也算作icon
            for ret in comp_info["detect_res"]:
                if ret["name"] in ("search_box", "input_box", "comment_box", "keyboard"):
                    iterm = {
                        "type": "Icon",
                        "rect": {
                            "x": ret["left"],
                            "y": ret["top"],
                            "width": ret["width"] - 1,
                            "height": ret["height"] - 1
                        },
                        "name": "input_box" if ret["name"] == "comment_box" else ret["name"],
                        "tag": [SINGLE, MULTIPLE, VISUAL]
                    }
                    icon_list.append(iterm)
        except:
            pass
        return icon_list

    def format_model_info(self, model_item_list):
        """
        格式化文本信息
        """
        model_list = []
        try:
            for ret in model_item_list:
                iterm = {
                    "type": "Icon",
                    "rect": ret["rect"],
                    "name": ret["name"],
                    "tag": [SINGLE, MULTIPLE, VISUAL]
                }
                model_list.append(iterm)
        except:
            pass
        return model_list

    def format_component_info(self, component_info):
        """
        格式化文本信息
        """
        component_list = []
        try:
            detect_res = component_info["detect_res"]
            for ret in detect_res:
                if ret["name"] in ("search_box", "input_box", "comment_box", "keyboard"):
                    # 排除search box
                    continue
                iterm = {
                    "type": "Component",
                    "rect": {
                        "x": ret["left"],
                        "y": ret["top"],
                        "width": ret["width"] - 1,
                        "height": ret["height"] - 1
                    },
                    "name": ret["name"],
                    "cname": ret["chinese_name"],
                    "tag": [SINGLE, MULTIPLE, VISUAL]
                }
                component_list.append(iterm)
        except:
            pass
        return component_list

    def format_dfe_info(self, dfe_info):
        """
        格式化dfe信息
        """
        try:
            for dfe in dfe_info:
                dfe["tag"] = [SINGLE, MULTIPLE, SYSTEM]
        except:
            pass

    def format_doe_info(self, doe_info):
        """
        格式化dfe信息
        """
        try:
            for doe in doe_info:
                doe["tag"] = [MULTIPLE, SYSTEM]
        except:
            pass

    def remove_ocr_overlap(self, raw_ocr_info: list, smap):
        """
        检查ocr识别到的文字符是否在icon模型或beta模型中，如果在，就过滤掉这些字符，更新ocr行矩形框
        参考：https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/3LoFUu-M_a/LWyELLqriZ/NXeWhHUhXpQN6m
        smap: beta和icon的所有矩形框构建出来的累加和矩阵,用于减少判断重叠的时间复杂度
        """

        def reshape_ocr(chars):
            """
            重新计算ocr矩形的面积
            """

            x1, y1, x2, y2 = None, None, None, None
            for ch in chars:
                rect = ch["rect"]
                r = [rect["left"], rect["top"], rect["left"] + rect["width"], rect["top"] + rect["height"]]
                if x1 is None:
                    x1, y1, x2, y2 = r
                x1 = min(x1, r[0])
                y1 = min(y1, r[1])
                x2 = max(x2, r[2])
                y2 = max(y2, r[3])
            return x1, y1, x2, y2

        # 待删除列表
        remove_list = []
        # 待添加列表
        add_list = []

        for ocr in raw_ocr_info:
            logger.debug("ocr {}".format(ocr))
            charset = ocr["charset"][:]
            old_len = len(charset)
            groups = [[]]
            for ch in ocr["charset"]:
                rect = ch["rect"]
                r = [rect["left"], rect["top"], rect["left"] + rect["width"], rect["top"] + rect["height"]]
                area = (r[2] - r[0]) * (r[3] - r[1])
                cross_area = smap.cross_area(rect=r)
                ov = cross_area / area
                if ov > DST.MAX_OCR_ICON_OVERLAP:
                    logger.debug("remove ocr char, ocr {}, char {}".format(ocr, ch))
                    charset.remove(ch)
                    # 如果文本被从中间截断，就分成多组charset
                    groups.append([])
                else:
                    groups[-1].append(ch)
            new_len = len(charset)

            if new_len < old_len:
                remove_list.append(ocr)
                for group in groups:
                    if len(group) == 0:
                        continue
                    x1, y1, x2, y2 = reshape_ocr(group)
                    new_ocr = {
                        "rect": {
                            "left": x1,
                            "top": y1,
                            "width": x2 - x1,
                            "height": y2 - y1
                        },
                        "charset": group,
                        "word": "".join([c["word"] for c in group])
                    }
                    add_list.append(new_ocr)

        for rm in remove_list:
            logger.debug("remove ocr line {}".format(rm))
            raw_ocr_info.remove(rm)
        for ad in add_list:
            logger.debug("add ocr line {}".format(ad))
            raw_ocr_info.append(ad)

    def remove_ocr_in_icon(self, icon_info, beta_info, ocr_info):
        """
        除去与icon模型或beta模型检测框重叠的ocr字符，并reshape行矩形框
        """
        tmp_rects = []
        raw_icon_info = icon_info.get("detect_res", [])
        raw_ocr_info = ocr_info.get("ret", [])
        raw_beta_info = beta_info.get("detect_res", [])
        for icon in raw_icon_info:
            logger.debug("icon: {}".format(icon))
            x1, y1, x2, y2 = icon['left'], icon['top'], icon['left'] + icon['width'], icon['top'] + icon['height']
            tmp_rects.append([x1, y1, x2, y2])
        for icon in raw_beta_info:
            x1, y1, x2, y2 = icon['left'], icon['top'], icon['left'] + icon['width'], icon['top'] + icon['height']
            tmp_rects.append([x1, y1, x2, y2])
        # 构建累加和矩阵
        smap = SMap(shape=(self.width, self.height), rects=tmp_rects)
        self.remove_ocr_overlap(raw_ocr_info, smap=smap)

    def remove_low_conf_obj(self, icon_info, beta_info, ele_info):
        """
        FIXME 临时逻辑暂时加在这里，后续会放入更合适的位置
            移除低置信度低的目标检测框，beta、icon、element模型的识别结果都会过滤
        """

        def rm_low_conf_rects(rect_list: list):
            rm_list = [r for r in rect_list if r["confidence"] < DST.MIN_OBJ_DETECT_CONF]
            logger.debug("remove {} object".format(len(rm_list)))
            for rm in rm_list:
                rect_list.remove(rm)

        raw_icon_info = icon_info.get("detect_res", [])
        rm_low_conf_rects(raw_icon_info)
        raw_beta_info = beta_info.get("detect_res", [])
        rm_low_conf_rects(raw_beta_info)
        raw_ele_info = ele_info.get("detect_res", [])
        rm_low_conf_rects(raw_ele_info)

    def remove_charset(self, ocr_info):
        """
        如果是对录制页面建模，就调用这个逻辑，删除所有charset避免json过大
        如果是对回放页面建模，就不调用这个逻辑，为回放的点击坐标提供依据
        """
        for text_area in ocr_info:
            for text in text_area['children']:
                del text['charset']

    def get_node_cross_area(self, p, c):
        """
        获取 p 和 c 的相交面积
        """
        p_x1 = p["rect"]["x"]
        p_x2 = p["rect"]["x"] + p["rect"]["w"]
        p_y1 = p["rect"]["y"]
        p_y2 = p["rect"]["y"] + p["rect"]["h"]

        c_x1 = c["rect"]["x"]
        c_x2 = c["rect"]["x"] + c["rect"]["w"]
        c_y1 = c["rect"]["y"]
        c_y2 = c["rect"]["y"] + c["rect"]["h"]
        # 计算重叠部分面积
        maxx = max(p_x1, c_x1)
        minx = min(p_x2, c_x2)
        maxy = max(p_y1, c_y1)
        miny = min(p_y2, c_y2)
        cross_area = 0
        if minx > maxx and miny > maxy:
            cross_area = (maxx - minx) * (maxy - miny)
        return cross_area

    def check_element_overlap(self, node, word, threshold=DST.ALMOST_OVERLAP_RATIO):
        """
        判断两个元素是否几乎重叠
        """
        node_area = node["rect"]["w"] * node["rect"]["h"]
        word_area = word["rect"]["w"] * word["rect"]["h"]

        # 重合部分面积
        cross_area = self.get_node_cross_area(node, word)

        # 取node和word面积的并集
        union_area = node_area + word_area - cross_area

        word_cross_ratio = 0
        if word_area > 0:
            word_cross_ratio = 100.0 * cross_area / union_area

        if word_cross_ratio >= threshold:
            return True

        # # 如果存在包含的关系，也返回True
        # if cross_area >= word_area or cross_area >= node_area:
        #     return True

        return False

    def check_overlap_with_existing(self, existing_list, element, appointed_types=None):
        """
        检查元素是否与res中的任何元素重叠
        """
        for existing_element in existing_list:
            if appointed_types is not None and existing_element["type"] not in appointed_types:
                continue
            if self.check_element_overlap(element, existing_element):
                return True
        return False

    def get_self_overlap_ratio(self, r1, r2):
        """
        计算两个矩形r1和r2的交集面积占r1面积的百分比
        r1,r2形如{'x': 82, 'y': 44, 'w': 87, 'h': 33}
        """
        x1 = max(r1["x"], r2["x"])
        y1 = max(r1["y"], r2["y"])
        x2 = min(r1["x"] + r1["w"], r2["x"] + r2["w"])
        y2 = min(r1["y"] + r1["h"], r2["y"] + r2["h"])
        if x1 >= x2 or y1 >= y2:
            return 0.0
        else:
            overlap_area = (x2 - x1) * (y2 - y1)
            return overlap_area / (r1["w"] * r1["h"])

    def check_ocr_in_picture_or_icon(self, existing_list, element):
        """
        检查一个文本是否有80%的面积在图片内
        """
        assert element["type"] in ["Text", "TextArea"]
        pic_list = [e for e in existing_list if e.get('type') == 'Icon' or
                    e.get('ext', {}).get('name', None) in ['picture', 'icon']]
        for pic in pic_list:
            if self.get_self_overlap_ratio(element["rect"], pic["rect"]) > 0.8:
                logger.info(f"ocr {element} in picture or icon: {pic}")
                return True
        return False

    def check_ocr_in_keyboard(self, existing_list, element):
        """
        如果纯数字、字母的文本在键盘组件内部，去掉
        """
        assert element["type"] in ["Text", "TextArea"]
        keyboard_list = [e for e in existing_list if e.get('ext', {}).get('name', None) == 'keyboard']

        number_pattern = r'^\d+$'
        alpha_pattern = r'^[a-zA-Z]+$'
        for keyboard in keyboard_list:
            text = element['ext']['text']
            if (re.fullmatch(number_pattern, text) or re.fullmatch(alpha_pattern, text)) and \
                    self.get_self_overlap_ratio(element["rect"], keyboard["rect"]) > 0.8:
                logger.info(f"ocr {element} in picture: {keyboard}")
                return True
        return False

    def check_component_icon_overlap_normal_icon(self, existing_list, element):
        """
        检查一个组件的icon是否与普通icon有重叠
        """
        assert element["type"] == 'Component' and element["ext"]["name"] == 'icon'
        normal_icon_list = [e for e in existing_list if e['type'] == BatdomNodeType.ICON.name]
        for normal_icon in normal_icon_list:
            # if self.get_self_overlap_ratio(element["rect"], normal_icon["rect"]) > 0.8 or \
            #         self.get_self_overlap_ratio(normal_icon["rect"], element["rect"]) > 0.8:
            #     return True
            if self.check_element_overlap(normal_icon, element, threshold=15):
                return True
        return False

    def format_params(self, params):
        """
        格式化输入参数，并返回格式化后的参数。

        :param params: 输入参数
        :return: 格式化后的参数
        """
        new_param = {}
        ocr_info_raw = {}
        beta_info_raw = {}
        icon_info_raw = {}
        ele_info_raw = {}
        dfe_info = []
        doe_info = []
        used_widget_list = []

        # 提取输入组件的原信息
        widget_info = params["module_info"]["widget_info"]
        used_ocr_info = widget_info["ocr_info"].get("used", False)
        if used_ocr_info:
            # 传入了ocr_info
            ocr_info_raw = widget_info["ocr_info"]["data"]
            used_widget_list.append("ocr_info")

        used_beta_info = widget_info["beta_info"].get("used", False)
        if used_beta_info:
            # 传入了beta_info
            beta_info_raw = widget_info["beta_info"]["data"]
            used_widget_list.append("beta_info")

        used_icon_info = widget_info["icon_info"].get("used", False)
        if used_icon_info:
            # 传入了icon_info
            icon_info_raw = widget_info["icon_info"]["data"]
            used_widget_list.append("icon_info")

        used_ele_info = widget_info["ele_info"].get("used", False)
        if used_ele_info:
            # 传入了组件ele_info
            ele_info_raw = widget_info["ele_info"]["data"]
            used_widget_list.append("ele_info")

        used_dom_info = widget_info["dom_info"].get("used", False)
        if used_dom_info:
            # 传入了dom_info，提取dfe, doe控件
            dom_info = widget_info["dom_info"]["data"]
            dfe_info, doe_info = self.get_dom_all_element(params["module_info"]["os_type"], dom_info)
            used_widget_list.append("dom_info")
            logger.info("dfe_info_len: {}, doe_info_len: {}".format(len(dfe_info), len(doe_info)))

        # ocr和图标模型处理
        # 除去与icon模型或beta模型检测框重叠的ocr字符（必须在这个阶段执行，format后会丢失charset信息）
        # self.remove_ocr_in_icon(icon_info_raw, beta_info_raw, ocr_info_raw)
        # FIXME 临时逻辑 过滤beta模型、icon模型、component模型所有低confidence的结果
        # self.remove_low_conf_obj(icon_info_raw, beta_info_raw, ele_info_raw)

        # 格式化输入信息, 加tag
        ocr_info = []
        beta_info = []
        icon_info = []
        ele_info = []
        # 通过ocr提升为icon或ele的列表
        ocr_icon_info = []
        ocr_ele_info = []

        if used_ocr_info:
            ocr_info, ocr_icon_info, ocr_ele_info = self.format_ocr_info(ocr_info_raw)
        if used_beta_info:
            beta_info = self.format_beta_info(beta_info_raw)
        if used_ele_info:
            icon_info = self.format_icon_info(icon_info_raw, ele_info_raw)
        if used_ele_info:
            ele_info = self.format_component_info(ele_info_raw)
        self.format_dfe_info(dfe_info)
        self.format_doe_info(doe_info)

        icon_info.extend(ocr_icon_info)
        ele_info.extend(ocr_ele_info)

        # 合并ocr信息
        ocr_info = merge_ocr.merge_rects_in_origin_format(ocr_info)
        if 'replaying' not in params or not params['replaying']:
            self.remove_charset(ocr_info)

        # 在icon_info中加入业务专属模型
        try:
            model_info = self.format_model_info(self.easydl_model.model_newapp(params["module_info"]["image_path"]))
        except:
            model_info = []
        icon_info.extend(model_info)

        new_param['ocr_info'] = ocr_info
        new_param['beta_info'] = beta_info
        new_param['icon_info'] = icon_info
        new_param['ele_info'] = ele_info
        new_param['dfe_info'] = dfe_info
        new_param['doe_info'] = doe_info

        new_param['image_path'] = params["module_info"]["image_path"]
        new_param['os_type'] = params["module_info"]["os_type"]

        return new_param, used_widget_list

    def insert_beta_info(self, beta_info, merge_list):
        """
        录入beta_info的信息
        """
        beta_icon_name_dict = get_beta_icon_name_dict()
        beta_info.sort(key=lambda x: (x["rect"]["y"], x["rect"]["x"]))
        for beta in beta_info:
            element = {
                "type": BatdomNodeType.ICON.name,
                "ext": {
                    "name": "{}_beta".format(beta["name"]),
                    "cname": "{}_beta".format(beta_icon_name_dict.get(beta["name"], beta["name"])),
                    "tag": beta["tag"],
                },
                "debug": {},
                "rect": {
                    "x": beta["rect"]["x"],
                    "y": beta["rect"]["y"],
                    "w": beta["rect"]["width"],
                    "h": beta["rect"]["height"],
                },
                "children": []
            }
            merge_list.append(element)

    def insert_icon_info(self, icon_info, merge_list):
        """
        录入 icon_info 的信息
        """
        icon_name_dict = get_icon_name_dict()
        icon_info.sort(key=lambda x: (x["rect"]["y"], x["rect"]["x"]))
        for icon in icon_info:
            element = {
                "type": BatdomNodeType.ICON.name,
                "ext": {
                    "name": icon["name"],
                    "cname": icon_name_dict.get(icon["name"], icon["name"]),
                    "tag": icon["tag"],
                },
                "debug": {},
                "rect": {
                    "x": icon["rect"]["x"],
                    "y": icon["rect"]["y"],
                    "w": icon["rect"]["width"],
                    "h": icon["rect"]["height"],
                },
                "children": []
            }
            if not self.check_overlap_with_existing(merge_list, element):
                merge_list.append(element)
            else:
                logger.info("overlapping icon element: {}".format(element))

    def insert_component_info(self, component_info, merge_list):
        """
        录入 element_info 的信息
        """
        # 稳定component_stable_info 的录入
        for component in component_info:
            try:
                element = {
                    "type": BatdomNodeType.COMPONENT.name,
                    "ext": {
                        "name": component["name"],
                        "cname": component["cname"],
                        "tag": component["tag"],
                    },
                    "debug": {},
                    "rect": {
                        "x": component["rect"]["x"],
                        "y": component["rect"]["y"],
                        "w": component["rect"]["width"],
                        "h": component["rect"]["height"],
                    },
                    "children": []
                }
            except:
                logger.error("component_info_add error: {}".format(json.dumps(component)))
                logger.error("错误信息: {}".format(traceback.format_exc()))
            if self.check_overlap_with_existing(merge_list, element):
                logger.info("overlapping component element: {}".format(element))
            elif element['ext']['name'] == 'icon' and \
                    self.check_component_icon_overlap_normal_icon(merge_list, element):
                logger.info("overlapping component-icon with icon element: {}".format(element))
            else:
                merge_list.append(element)

    def insert_ocr_info(self, ocr_info, merge_list):
        """
        录入 ocr_info 的信息
        """
        for ocr in ocr_info:
            element = {
                "type": ocr['type'],
                "ext": {
                    "text": ocr["word"],
                    "tag": ocr["tag"],
                },
                "debug": {},
                "rect": {
                    "x": ocr["rect"]["x"],
                    "y": ocr["rect"]["y"],
                    "w": ocr["rect"]["width"],
                    "h": ocr["rect"]["height"],
                },
                "children": []
            }
            if 'charset' in ocr:
                element['charset'] = ocr['charset']
            if ocr['children'] and len(ocr['children']) > 0:
                for item in ocr['children']:
                    child = {
                        "type": item['type'],
                        "ext": {
                            "text": item["word"],
                            "tag": item["tag"],
                        },
                        "debug": {},
                        "rect": {
                            "x": item["rect"]["x"],
                            "y": item["rect"]["y"],
                            "w": item["rect"]["width"],
                            "h": item["rect"]["height"],
                        },
                        "children": []
                    }
                    element['children'].append(child)
                    if 'charset' in item:
                        child['charset'] = item['charset']
            if self.check_overlap_with_existing(merge_list, element, [BatdomNodeType.ICON.name]):
                logger.info("Single_feature overlapping ocr element: {}".format(element))
            elif self.check_ocr_in_picture_or_icon(merge_list, element):
                logger.info("Single_feature ocr element in image: {}".format(element))
            elif self.check_ocr_in_keyboard(merge_list, element):
                logger.info("Single_feature ocr alpha or number element in keyboard: {}".format(element))
            else:
                merge_list.append(element)

    def insert_dfe_info(self, dfe_info, merge_list):
        """
        录入 dfe_info 的信息
        """
        dfe_info.sort(key=lambda x: (x["rect"]["y"], x["rect"]["x"]))

        # 属性格式转换
        for dom in dfe_info:
            feature_dict = {}
            # for key in self.goal_feature_dict:
            # for key in dom:
            #     if key in dom and dom[key] is not None and dom[key] != "":
            #         feature_dict[key] = {
            #             "data": dom[key],
            #             "chosen": True
            #         }
            feature_dict["system_type"] = {
                "data": dom["type"],
                "chosen": True
            }
            feature_dict["content"] = {
                "data": dom["content"],
                "chosen": True
            }

            element = {
                "type": "DFE",
                "ext": {
                    "feature": feature_dict,
                    "tag": [SINGLE, MULTIPLE, SYSTEM],
                },
                "debug": {},
                "rect": {
                    "x": dom["rect"]["x"],
                    "y": dom["rect"]["y"],
                    "w": dom["rect"]["width"],
                    "h": dom["rect"]["height"],
                },
                "children": []
            }
            merge_list.append(element)

    def remove_illegal_elements(self, merge_list):
        """
        清理不符合要求的元素
        """
        pass

    def build_structure(self, input_data):
        """
        完成建模
        """
        merge_list = []

        # beta_info 的录入
        beta_info = input_data["beta_info"]
        self.insert_beta_info(beta_info, merge_list)

        # icon 的录入
        icon_info = input_data["icon_info"]
        self.insert_icon_info(icon_info, merge_list)

        # 组件 的录入
        element_info = input_data["ele_info"]
        self.insert_component_info(element_info, merge_list)

        # 文本 的录入
        ocr_info = input_data["ocr_info"]
        self.insert_ocr_info(ocr_info, merge_list)

        # dom 的录入
        dfe_info = input_data["dfe_info"]
        self.insert_dfe_info(dfe_info, merge_list)

        # 清理不符合要求的元素
        self.remove_illegal_elements(merge_list)

        # 控件排序
        merge_list.sort(key=lambda x: (x["rect"]["y"], x["rect"]["x"]))

        # 添加唯一标志
        id = 0
        for iterm in merge_list:
            id += 1
            iterm["debug"]["id"] = id
            if "children" in iterm:
                for child in iterm["children"]:
                    id += 1
                    child["debug"]["id"] = id
        return merge_list

    def main(self, params):
        """
        主流程函数
        """
        try:
            # 入参校验
            params_check, lost_params = self.params_check(
                params,
                ["widget_info", "os_type", "image_path"],
                ["ocr_info", "icon_info", "ele_info", "beta_info", "dom_info"]
            )
            if params_check is False:
                logger.error("record_widget_model missing params: {}".format(lost_params))
                result = {
                    "code": ErrCode.ParamsLost.Code,
                    "msg": ErrCode.ParamsLost.Msg.format(lost_params)
                }
                return result

            logger.debug("[Batdom Debug] raw_params: {}".format(params))

            # 格式化输入参数：提取入参，ocr处理、ocr合并，图标模型筛选，添加 id
            self.extract_basic_info(params["module_info"])
            new_param, used_widget_list = self.format_params(params)
            logger.debug("[Batdom Debug] new_params: {}".format(new_param))
            union_dom = []
            union_dom = self.build_structure(new_param)

            # 组合结构, 添加页面信息
            res_data = {
                "type": "Page",
                "ext": {"id": 0},
                "debug": {"id": 0},
                "rect": {
                    "x": 0,
                    "y": 0,
                    "w": self.width,
                    "h": self.height
                },
                "children": union_dom
            }
            result = {
                "code": ErrCode.Success.Code,
                "msg": ErrCode.Success.Msg,
                "data": {
                    "dom": res_data,
                    "info": {
                        "version": PROJECT_VERSION,
                        "used_widget_list": used_widget_list,
                        "name": "recordWidgetModel",
                        "sub_version": "batdom_agent"
                    }
                }
            }
            logger.debug("[Batdom Debug]result: {}".format(json.dumps(result)))

            return result

        except Exception as e:
            logger.error("record_widget_model uncaught Error: {}".format(traceback.format_exc()))
            result = {
                "code": ErrCode.UnknownError.Code,
                "msg": ErrCode.UnknownError.Msg
            }
            return result
