#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@File    : perf.py
<AUTHOR> <PERSON><PERSON><PERSON><PERSON>(renya<PERSON><PERSON>@baidu.com)
@Time    : 2025/7/25 20:01
@Desc    : 
"""

import traceback

from basics.util import logger
from video.stage.split import binary_tendcy
from video import user_custome


def findStage(params):
    """ find """
    stage_list = []
    try:
        frames_dir = params[0]
        thresh = 0.9
        if len(params) > 1:
            thresh = params[1]
        stage_list = binary_tendcy(frames_dir, thresh)
    except Exception as e:
        logger.error(traceback.format_exc())
    return stage_list


def parser_cross(params):
    """
    解析查找十字帧传入参数
    :param params:
    :return:
    """
    frames_dir = params.get("frames_dir")
    start_index = max(params.get("start_index", 0), 5)

    if params.get('action') is None:
        return {"smartIndex": start_index}
    if params.get('finger_points') is None or len(params.get('finger_points')) < 1:
        logger.debug("parser cross , finger point is None")
        return {"smartIndex": start_index}

    info = {'object': 'cross', "action": params.get('action'),
            "finger_points": params.get('finger_points')}

    smart_index = user_custome(frames_dir, info, start_index)
    return {"smartIndex": smart_index}