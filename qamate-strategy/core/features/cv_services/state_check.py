"""
一些带状态的控件的状态检测代码
"""

import cv2


def resize_image_long_side(image, target_long_side):
    """
    将图片的长边缩放到目标长度，并返回缩放后的图像
    """

    if image is None:
        raise Exception("Image is None")

    # 获取图像的原始尺寸
    height, width = image.shape[:2]

    # 确定长边和缩放比例
    if height > width:
        scaling_factor = target_long_side / float(height)
    else:
        scaling_factor = target_long_side / float(width)

    # 计算新的尺寸
    new_size = (int(width * scaling_factor), int(height * scaling_factor))

    # 缩放图像
    resized_image = cv2.resize(image, new_size, interpolation=cv2.INTER_AREA)

    return resized_image


def draw_contour(image, contour):
    """
    调试用代码，绘制单个轮廓
    """
    # print(image.shape)
    if not image.shape[2] == 3:
        # 创建彩色图像以绘制轮廓
        contour_image = cv2.cvtColor(image, cv2.COLOR_GRAY2BGR)
    contour_image = image.copy()

    # 绘制所有轮廓
    cv2.drawContours(contour_image, [contour], -1, (0, 255, 0), 2)

    # 显示轮廓图像
    cv2.imshow('Contours', contour_image)
    cv2.waitKey(0)
    cv2.destroyAllWindows()


def detect_switch_state(image_cv):
    """
    通过传统cv的方式检测开关是开启还是关闭状态，True为开启，False为关闭
    """
    # 复制图像
    image = image_cv.copy()
    # cv2.imwrite(f'/Users/<USER>/lab/xxxx/{uuid.uuid4()}.png', image)

    image = resize_image_long_side(image, 200)

    # 应用高斯模糊以减少噪声
    blurred = cv2.GaussianBlur(image, (5, 5), 0)

    # 使用Canny检测边缘
    edged = cv2.Canny(blurred, 50, 150)

    # 显示边缘图像
    # cv2.imshow('Edges', edged)
    # cv2.waitKey(0)
    # cv2.destroyAllWindows()

    # 找到轮廓
    contours, _ = cv2.findContours(edged, cv2.RETR_LIST, cv2.CHAIN_APPROX_SIMPLE)

    # 初始化变量用于存储开关内圆的边界
    switch_circle = None

    # 遍历所有轮廓
    for contour in contours:
        # 计算轮廓的边界矩形
        x, y, w, h = cv2.boundingRect(contour)
        arcl = cv2.arcLength(contour, True)
        if arcl < 150:
            continue
        rt = w / h
        # draw_contour(image, contour)
        # 通过宽高比例大致判断是否为圆形
        if 0.8 < rt < 1.2:
            switch_circle = (x, y, w, h)
            break

    if switch_circle is None:
        return False

    # 获取图像的中心点
    image_center = image.shape[1] / 2

    # 根据圆的位置判断开关状态
    circle_center = switch_circle[0] + switch_circle[2] / 2

    if circle_center < image_center:
        return False
    else:
        return True


if __name__ == '__main__':
    imgcv = cv2.imread('/Users/<USER>/lab/xxxx/a9810668-83c9-4fa1-bb89-f77159baf6bf.png')
    print(detect_switch_state(imgcv))
