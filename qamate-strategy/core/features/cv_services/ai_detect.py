"""
智能检测相关的函数
"""
import concurrent.futures
import os.path

import cv2

from apps.app_agent.multi_agent.ai_check_agent.multi_img_check_agent.agent import MultiImgAiCheckAgent
from apps.app_agent.multi_agent.ai_check_agent.single_img_check_agent.v1_2.agent import AiCheckAgent
from apps.app_agent.multi_agent.ai_check_agent.video_check_agent.agent import VideoAiCheckAgent
from apps.app_agent.multi_agent.ai_replay_agent.v1_0.agent import AiLocateAgent
from apps.app_agent.utils.task_type import TaskType
from basics.util import logger
from features.record_replay.base.local_strategies_errcode import ErrCode


def get_cost_params(params, task_type):
    """
    根据入参更新成本中心必须的参数（是一个线程局部变量）
    """
    module_id = params.get('module_id', None)
    task_id = params.get('task_id', None)
    if task_id is not None:
        task_type = task_type
    else:
        task_type = None
    return {
        'module_id': module_id,
        'task_id': task_id,
        'task_type': task_type,
    }



def ai_replay(params):
    """
    视频校验
    """
    ctx = get_cost_params(params, task_type=TaskType.AI_LOCATE)
    record_img_path = params.get('record_img_path', None)
    replay_img_path = params.get('replay_img_path', None)
    rect = params.get('rect', None)
    find_desc = params.get('find_desc', '')
    model_name = params.get('model_name', 'doubao-1.5-thinking-vision-pro-250428')

    if record_img_path is None or not os.path.exists(record_img_path):
        logger.error("ai replay Error: record_img_path is null or not exist: {}".format(record_img_path))
        result = {
            "code": ErrCode.ParamsLost.Code,
            "msg": ErrCode.ParamsLost.Msg.format("用例截图为空或不存在"),
            "dom_tree": {}
        }
        return result

    if replay_img_path is None or not os.path.exists(record_img_path):
        logger.error("ai replay Error: replay_img_path is null or not exist: {}".format(replay_img_path))
        result = {
            "code": ErrCode.ParamsLost.Code,
            "msg": ErrCode.ParamsLost.Msg.format("回放截图为空或不存在"),
            "dom_tree": {}
        }
        return result

    if find_desc is None or len(find_desc) == 0:
        logger.error("ai replay Error: find_desc is empty")
        result = {
            "code": ErrCode.ParamsLost.Code,
            "msg": ErrCode.ParamsLost.Msg.format("元素的文字描述为空"),
            "dom_tree": {}
        }
        return result

    timeout = 3 * 60  # 设置超时时间

    executor = concurrent.futures.ThreadPoolExecutor()
    try:
        future = executor.submit(lambda: AiLocateAgent(
            desc=find_desc,
            rect=rect,
            record_img_path=record_img_path,
            replay_img_path=replay_img_path,
            mode_name=model_name,
            ctx=ctx).main()
                                 )
        r = future.result(timeout=timeout)
        if r['result'] is True:
            return {
                'code': 0,
                'msg': '定位成功',
                'match_res': {
                    'reason': r['reason'],
                    'pos': r['pos']
                },
                'cost': {
                    'llm': r['cost'],
                }
            }
        else:
            return {
                'code': ErrCode.UiMatchError.Code,
                'msg': ErrCode.UiMatchError.Msg,
                'match_res': {
                    'reason': r['reason'],
                    'pos': r['pos']
                },
                'cost': {
                    'llm': r['cost'],
                }
            }
    except concurrent.futures.TimeoutError:
        logger.error("ai assert timeout")
        result = {
            "code": ErrCode.ProcTimeoutError.Code,
            "msg": ErrCode.ProcTimeoutError.Msg.format("智能校验超过3min"),
            "dom_tree": {}
        }
        return result
    finally:
        executor.shutdown(wait=False)



def video_ai_assert(params):
    """
    视频校验
    """
    ctx = get_cost_params(params, task_type=TaskType.VIDEO_AI_ASSERT)
    video_url = params.get('video_url', None)
    assert_input = params.get('input_assert', '')
    model_name = params.get('model_name', 'doubao-1.5-vision-pro-250328')

    if video_url is None or len(video_url) == 0:
        logger.error("ai assert Error: video is null")
        result = {
            "code": ErrCode.ParamsLost.Code,
            "msg": ErrCode.ParamsLost.Msg.format("视频链接为空"),
            "dom_tree": {}
        }
        return result

    if assert_input == '':
        logger.error("ai assert Error: input assert is empty")
        result = {
            "code": ErrCode.ParamsLost.Code,
            "msg": ErrCode.ParamsLost.Msg.format("智能断言的input_assert为空"),
            "dom_tree": {}
        }
        return result

    timeout = 3 * 60  # 设置超时时间

    executor = concurrent.futures.ThreadPoolExecutor()
    try:
        future = executor.submit(lambda: VideoAiCheckAgent(check_str=assert_input,
                                                           video_url=video_url,
                                                           mode_name=model_name,
                                                           ctx=ctx).main())
        r = future.result(timeout=timeout)
        return {
            'code': 0,
            'assert_res': {
                'res': r['result'],
                'reason': r['reason']
            },
            'cost': {
                'llm': r['cost'],
            }
        }
    except concurrent.futures.TimeoutError:
        logger.error("ai assert timeout")
        result = {
            "code": ErrCode.ProcTimeoutError.Code,
            "msg": ErrCode.ProcTimeoutError.Msg.format("智能校验超过3min"),
            "dom_tree": {}
        }
        return result
    finally:
        executor.shutdown(wait=False)


def multi_img_ai_assert(params):
    ctx = get_cost_params(params, task_type=TaskType.MULTI_AI_ASSERT)
    proc = params.get('proc', [])
    assert_input = params.get('input_assert', '')
    model_name = params.get('model_name', 'doubao-1.5-vision-pro-250328')
    img_paths = [x['img_path'] for x in proc]

    if len(img_paths) == 0:
        logger.error("ai assert Error: proc is empty")
        result = {
            "code": ErrCode.ParamsLost.Code,
            "msg": ErrCode.ParamsLost.Msg.format("过程图片为空"),
            "dom_tree": {}
        }
        return result

    if assert_input == '':
        logger.error("ai assert Error: input assert is empty")
        result = {
            "code": ErrCode.ParamsLost.Code,
            "msg": ErrCode.ParamsLost.Msg.format("智能断言的input_assert为空"),
            "dom_tree": {}
        }
        return result

    timeout = 3 * 60  # 设置超时时间

    executor = concurrent.futures.ThreadPoolExecutor()
    try:
        future = executor.submit(lambda: MultiImgAiCheckAgent(check_str=assert_input,
                                                              img_paths=img_paths,
                                                              mode_name=model_name,
                                                              ctx=ctx).main())
        r = future.result(timeout=timeout)
        return {
            'code': 0,
            'assert_res': {
                'res': r['result'],
                'reason': r['reason']
            },
            'cost': {
                'llm': r['cost'],
            }
        }
    except concurrent.futures.TimeoutError:
        logger.error("ai assert timeout")
        result = {
            "code": ErrCode.ProcTimeoutError.Code,
            "msg": ErrCode.ProcTimeoutError.Msg.format("智能校验超过3min"),
            "dom_tree": {}
        }
        return result
    finally:
        executor.shutdown(wait=False)


def ai_assert(params):
    """
    智能断言
    """
    ctx = get_cost_params(params, task_type=TaskType.SINGLE_AI_ASSERT)
    current_img_path = params.get('current_img', '')
    assert_input = params.get('input_assert', '')
    model_name = params.get('model_name', 'doubao-1.5-vision-pro-250328')
    cur_imgcv = cv2.imread(current_img_path)
    if cur_imgcv is None or len(cur_imgcv) == 0:
        logger.error("ai assert Error: current image is empty")
        result = {
            "code": ErrCode.InputImageError.Code,
            "msg": ErrCode.InputImageError.Msg.format("图片为空"),
            "dom_tree": {}
        }
        return result
    if assert_input == '':
        logger.error("ai assert Error: input assert is empty")
        result = {
            "code": ErrCode.ParamsLost.Code,
            "msg": ErrCode.ParamsLost.Msg.format("智能断言的input_assert为空"),
            "dom_tree": {}
        }
        return result

    timeout = 3 * 60  # 设置超时时间

    executor = concurrent.futures.ThreadPoolExecutor()
    try:
        # from apps.app_agent.multi_agent.ai_check_agent.single_img_check_agent.v1_2.agent import AiCheckAgent
        future = executor.submit(lambda: AiCheckAgent(check_str=assert_input,
                                                      img_path=current_img_path,
                                                      mode_name=model_name,
                                                      ctx=ctx).main())
        r = future.result(timeout=timeout)
        return {
            'code': 0,
            'assert_res': {
                'res': r['result'],
                'reason': r['reason'],
                'rect_list': r['rects']
            },
            'cost': {
                'record': r['record_cost'],
                'llm': r['llm_cost'],
            }
        }
    except concurrent.futures.TimeoutError:
        logger.error("ai assert timeout")
        result = {
            "code": ErrCode.ProcTimeoutError.Code,
            "msg": ErrCode.ProcTimeoutError.Msg.format("智能校验超过3min"),
            "dom_tree": {}
        }
        return result
    finally:
        executor.shutdown(wait=False)


if __name__ == '__main__':
    import basics.config as config

    config.load_config('/Users/<USER>/batdom_vers/10.1.0/core/profile.json')
    img_path = '/Users/<USER>/Downloads/b74180ccbddd296c2a1f85167.jpg'
    res = ai_assert({'current_img': img_path, 'input_assert':
        '是否存在加号按钮'})
    print(res)
    imgcv = cv2.imread(img_path)
    for rect in res['assert_res']['rect_list']:
        cv2.rectangle(imgcv, (rect['x'], rect['y']), (rect['x'] + rect['w'], rect['y'] + rect['h']), (0, 255, 0), 5)
    cv2.imshow('img', imgcv)
    cv2.waitKey(0)
