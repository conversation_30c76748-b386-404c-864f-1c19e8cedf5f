"""
专门开一个文件记录controller，避免循环引用
"""

import features.cv_services.icon_detect as icon_detect
import features.cv_services.ocr as ocr

import features.cv_services.icon_detect_online as icon_detect_online
import features.cv_services.ocr_online as ocr_online

import features.cv_services.white_ratio as white_ratio
from features.cv_services import popup_detect
from features.cv_services import ai_detect
from features.cv_services import step_repair
from features.cv_services import web_merge_online
from features.cv_services import image_merge

from features.cv_services import perf

CONTROLLER = {

    # step repair
    "step_repair": step_repair.repair_one_step,

    # icon
    "icon_detect": icon_detect.icon_detect,
    "element_detect": icon_detect.element_detect,
    "card_detect": icon_detect.card_detect,
    "beta_detect": icon_detect.beta_detect,

    # ocr
    "ocr_result": ocr.ocr_result,

    # online
    # icon
    "online_icon_detect": icon_detect_online.icon_detect,
    "online_element_detect": icon_detect_online.element_detect,
    "online_card_detect": icon_detect_online.card_detect,
    "online_beta_detect": icon_detect_online.beta_detect,

    # ocr
    "online_ocr_result": ocr_online.ocr_result,

    # white ratio
    "white_ratio": white_ratio.white_ratio,

    # popup detect
    "popup_detect": popup_detect.popup_detect,

    # web customized
    "web_merge_online": web_merge_online.web_merge_online,

    # ai detect
    "ai_assert": ai_detect.ai_assert,
    "multi_img_ai_assert": ai_detect.multi_img_ai_assert,
    "video_ai_assert": ai_detect.video_ai_assert,
    "ai_replay": ai_detect.ai_replay,

    # image_merge
    "image_merge": image_merge.image_merge,

    # 【lazyPerf】
    # 十字帧
    "video.cross": perf.parser_cross,

    # 趋势点
    "frame.find.stage": perf.findStage
}