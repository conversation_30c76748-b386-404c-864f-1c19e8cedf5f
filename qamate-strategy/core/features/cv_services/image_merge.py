#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@File    : image_merge.py
<AUTHOR> <PERSON><PERSON><PERSON><PERSON>(renya<PERSON><PERSON>@baidu.com)
@Time    : 2025/4/11 16:38
@Desc    : 
"""

import cv2

from basics.image_merge.image_merge import ImageMerge
from basics.util import logger
from features.record_replay.base.local_strategies_errcode import ErrCode
from basics.image.ui.image_process import opencvimg_to_base64


def image_merge(params) -> dict:
    """

    :param params:
    :return:
    """

    img_paths = params.get('img_paths', [])
    save_dir = params.get('save_dir', None)

    imgcv_list = []
    for img_path in img_paths:
        imgcv = cv2.imread(img_path)
        if imgcv is None:
            logger.error("ai assert Error: current image is empty")
            result = {
                "code": ErrCode.InputImageError.Code,
                "msg": ErrCode.InputImageError.Msg.format("图片为空"),
                "dom_tree": {}
            }
            return result
        imgcv_list.append(imgcv)

    merge_tool = ImageMerge(imgcv_list=imgcv_list, dir_path=save_dir)
    merge_imgcv = merge_tool.main()

    base64_img = opencvimg_to_base64(merge_imgcv)

    result = {
        "code": 0,
        "result": {
            "base64_img": base64_img
        }
    }
    return result
