# !/usr/bin/python3

# -*- coding: utf-8 -*-

"""
@ModuleName:
@author: <PERSON><PERSON><PERSON><PERSON> (renya<PERSON><PERSON>@baidu.com)
@date: 2022-05-09
@desc:
"""
import json

import cv2

from basics.config import config, load_config
from basics.image.ui.business_exclusive_detect import BusinessExclusiveDetect
from basics.image.ui.math_position import cal_iou
from basics import config
from basics.normal_icon_detect.icon_detect_a import IconDetect
from basics.normal_element_detect.element_detect import ElementDetect
from basics.normal_card_detect.card_detect import CardDetect
from features.record_replay.base.local_strategies_errcode import ErrCode
from basics.util import logger


def icon_detect(params):
    """
    新、旧合并后的icon检测
    """
    old_res = old_icon_detect(params)
    new_res = new_icon_detect(params)
    return merge_detect_res(old_res, new_res)


def merge_detect_res(old_res, new_res):
    """
    合并新、旧模型的检测结果
    1. 如果新、旧模型在相同位置检测结果不一致，取旧模型结果，并在日志记录
    2. 如果旧模型检测出、新模型检测不出，保留旧模型结果，并在日志记录
    3. 如果新模型检测出、旧模型检测不出，保留新模型结果
    """

    def cal_iou(new_icon, old_icon):
        """
        计算两个图标的IoU（交并比）
        :param new_icon: 一个字典，包含新图标的坐标和尺寸信息
        :param old_icon: 一个字典，包含旧图标的坐标和尺寸信息
        :return: IoU值
        """

        # 计算交集区域的坐标
        inter_left = max(new_icon['left'], old_icon['left'])
        inter_top = max(new_icon['top'], old_icon['top'])
        inter_right = min(new_icon['right'], old_icon['right'])
        inter_bottom = min(new_icon['bottom'], old_icon['bottom'])

        # 计算交集区域的宽度和高度
        inter_width = max(0, inter_right - inter_left)
        inter_height = max(0, inter_bottom - inter_top)

        # 计算交集区域的面积
        inter_area = inter_width * inter_height

        # 计算每个图标的面积
        new_icon_area = new_icon['width'] * new_icon['height']
        old_icon_area = old_icon['width'] * old_icon['height']

        # 计算并集区域的面积
        union_area = new_icon_area + old_icon_area - inter_area

        # 计算IoU
        iou = inter_area / union_area if union_area != 0 else 0

        return iou

    detect_res = old_res["dom_tree"]["detect_res"][:]
    for new_icon in new_res["dom_tree"]["detect_res"]:
        ov_icon = None
        for old_icon in old_res["dom_tree"]["detect_res"]:
            iou = cal_iou(new_icon, old_icon)
            if iou > 0.5:
                ov_icon = old_icon
                break
        if ov_icon is not None:
            if new_icon["name"] == ov_icon["name"]:
                # 如果新模型图标与旧模型图标重叠且名称相同，则跳过
                continue
            else:
                # 如果新模型图标与旧模型图标重叠且名称不同，则打印日志
                logger.info(f"conflict: new icon {new_icon['name']} and old icon {ov_icon['name']} "
                            f"in the same position, use old icon")
                ov_icon['diff_code'] = 0
        else:
            # 如果检测不到与新模型图标重叠的图标，则直接加入到结果中
            detect_res.append(new_icon)

    # FIXME 临时逻辑，用于记录旧模型检测得到，新模型检测不到的图标，并输出日志
    for old_icon in old_res["dom_tree"]["detect_res"]:
        ov_icon = None
        for new_icon in new_res["dom_tree"]["detect_res"]:
            iou = cal_iou(new_icon, old_icon)
            if iou > 0.5:
                ov_icon = old_icon
                break
        if ov_icon is None:
            logger.info(f"missing out: old icon {old_icon['name']} not detected by new icon")
            old_icon['diff_code'] = 1

    return {'code': 0, 'dom_tree': {"detect_res": detect_res, "visual_res": None}}


def old_icon_detect(params):
    """
    旧icon检测
    """
    # 参数校验


    img_cv = cv2.imread(params.get("image_path", ""))
    if img_cv is None or len(img_cv) == 0:
        logger.error("icon detect Error: image is empty")
        result = {
            "code": ErrCode.InputImageError.Code,
            "msg": ErrCode.InputImageError.Msg.format("图片为空"),
            "dom_tree": {}
        }
        return result

    detector = IconDetect()
    detect_res = detector.main(params.get("image_path"), params.get("save_path", None))
    detect_res["detect_res"] = [item.to_dict() for item in detect_res["detect_res"]]
    return {'code': 0, 'dom_tree': detect_res}


def new_icon_detect(params):
    """
    新icon检测
    """
    # 参数校验
    img_cv = cv2.imread(params.get("image_path", ""))
    if img_cv is None or len(img_cv) == 0:
        logger.error("icon detect Error: image is empty")
        result = {
            "code": ErrCode.InputImageError.Code,
            "msg": ErrCode.InputImageError.Msg.format("图片为空"),
            "dom_tree": {}
        }
        return result
    # @yanwei确认下这里？
    threshold = params.get("threshold")
    if threshold:
        detector = IconDetect(version="new", conf=threshold)
    else:
        detector = IconDetect(version="new")
    # end
        
    detect_res = detector.main(params.get("image_path"), params.get("save_path", None))
    detect_res["detect_res"] = [item.to_dict() for item in detect_res["detect_res"]]
    return {'code': 0, 'dom_tree': detect_res}


def element_detect(params):
    """
    element检测
    """
    # 参数校验
    img_cv = cv2.imread(params.get("image_path", ""))
    if img_cv is None or len(img_cv) == 0:
        logger.error("element detect Error: image is empty")
        result = {
            "code": ErrCode.InputImageError.Code,
            "msg": ErrCode.InputImageError.Msg.format("图片为空"),
            "element_res": {}
        }
        return result

    threshold = params.get("threshold")
    if threshold:
        detector = ElementDetect(conf=threshold)
    else:
        detector = ElementDetect()
    detect_res = detector.main(params.get("image_path"), params.get("save_path", None))
    detect_res["detect_res"] = [item.to_dict() for item in detect_res["detect_res"]]
    return {'code': 0, 'element_res': detect_res}


def card_detect(params):
    """
    card检测
    """
    # 参数校验
    img_cv = cv2.imread(params.get("image_path", ""))
    if img_cv is None or len(img_cv) == 0:
        logger.error("card detect Error: image is empty")
        result = {
            "code": ErrCode.InputImageError.Code,
            "msg": ErrCode.InputImageError.Msg.format("图片为空"),
            "card_res": {}
        }
        return result

    threshold = params.get("threshold")
    if threshold:
        detector = CardDetect(conf=threshold)
    else:
        detector = CardDetect()
    detect_res = detector.main(params.get("image_path"), params.get("save_path", None))
    detect_res["detect_res"] = [item.to_dict() for item in detect_res["detect_res"]]
    return {'code': 0, 'card_res': detect_res}


def beta_detect(params):
    """
    beta图标模型实现icon检测
    """
    # 参数校验
    img_cv = cv2.imread(params.get("image_path", ""))
    if img_cv is None or len(img_cv) == 0:
        logger.error("icon detect Error: image is empty")
        result = {
            "code": ErrCode.InputImageError.Code,
            "msg": ErrCode.InputImageError.Msg.format("图片为空"),
            "dom_tree": {}
        }
        return result

    threshold = params.get("threshold")
    if threshold:
        detector = IconDetect(version="beta_1_0", conf=threshold)
    else:
        detector = IconDetect(version="beta_1_0")
    detect_res = detector.main(params.get("image_path"), params.get("save_path", None))
    detect_res["detect_res"] = [item.to_dict() for item in detect_res["detect_res"]]

    # 传入productId调用业务专属模型
    extra_detector = BusinessExclusiveDetect(params.get("product_id", None))
    extra_detect_res = extra_detector.main(params.get("image_path"))

    # 对比两个模型的检测结果，优先删除通用模型检测结果中重复的元素，可以避免重新编号
    repeat_list = []
    for extra_item in extra_detect_res:
        for item in detect_res["detect_res"]:
            # 计算两个图标的IoU，如果大于0.5，则认为是重复的
            if cal_iou(extra_item, item) > 0.5:
                repeat_list.append(item)
                break
    for item in repeat_list:
        detect_res["detect_res"].remove(item)
    # 将业务专属模型的结果接续到返回结果中
    detect_res["detect_res"].extend(extra_detect_res)

    return {"code": 0, "msg": "返回成功", "dom_tree": detect_res}


if __name__ == "__main__":
    load_config("../../profile.json")
    result = beta_detect({"image_path": "/Users/<USER>/Downloads/1.png", "product_id": 3})
    print(json.dumps(result, indent=4, ensure_ascii=False))

