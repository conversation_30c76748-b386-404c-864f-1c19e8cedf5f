"""
单步骤修复能力
"""

import os.path

from apps.case_repair.step_repair.repair_step import repair_step_by_info
from basics.util import logger
from features.record_replay.base.local_strategies_errcode import ErrCode


def repair_one_step(params):
    """
    修复步骤
    """
    step_info = params.get('step_info', None)
    new_img_path = params.get('img_path', None)
    force_repair = params.get('force_repair', False)
    if step_info is None:
        logger.error("repair step failed, step_info is None")
        result = {
            "code": ErrCode.ParamsLost.Code,
            "msg": ErrCode.ParamsLost.Msg.format("输入用例为空"),
            "dom_tree": {}
        }
        return result

    if new_img_path is None or len(new_img_path) == 0 or not os.path.exists(new_img_path):
        logger.error("ai assert Error: current image is empty or not exists")
        result = {
            "code": ErrCode.InputImageError.Code,
            "msg": ErrCode.InputImageError.Msg.format("图片为空或不存在"),
            "dom_tree": {}
        }
        return result

    new_step_info = repair_step_by_info(step_info, new_img_path, force_repair=force_repair)
    return {
        'code': 0,
        'step': new_step_info
    }
