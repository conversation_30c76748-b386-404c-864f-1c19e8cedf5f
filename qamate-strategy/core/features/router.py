#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@Desc    :   一级入口路由，根据 method 进行
@File    :   router.py
@Time    :   2023-08-18
<AUTHOR>   x<PERSON><PERSON><PERSON>@baidu.com
"""
import traceback

from basics.util import logger
from features.record_replay.base.local_strategies_errcode import ErrCode


def str_hump(strategy_name):
    """
    将下划线格式转化为驼峰格式
    下划线格式：文件名
    驼峰格式：策略类名
    """
    tmp_list = strategy_name.split("_")
    strategy_class_name = ""
    for t in tmp_list:
        strategy_class_name += t[0].upper() + t[1:]
    return strategy_class_name


def main_router(router_str, params):
    """
    一级入口路由，根据 method 进行
    """
    logger.info("router method: {}".format(router_str))
    # 判断 method 是否存在
    method_list = router_str.split(".")
    if method_list[0] == "record_replay":
        method_class_str = method_list[0]
    else:
        method_class_str = method_list[1]
    # 对ui_split进行下特殊逻辑
    if method_class_str == "ui_split":
        method_file = "features.record_replay.{}.{}_router".format(method_class_str, method_class_str)
    elif method_class_str == "get_ocr_charset":
        method_file = "features.cv_services.{}".format(method_class_str)
    else:
        method_file = "features.{}.{}_router".format(method_class_str, method_class_str)
    method_class_name = str_hump("{}_router".format(method_class_str))
    logger.info("method_class_str:{} method_file:{} method_class_name:{}".format(
        method_class_str, method_file, method_class_name
    ))
    
    try:
        method_import = __import__(method_file, fromlist=True)
        method_class = getattr(method_import, method_class_name)
        method = method_class()
        result = method.main(router_str, params)
    except ModuleNotFoundError: # pylint:disable=undefined-variable
        # 一级方法名称没找到
        logger.error(traceback.format_exc())
        result = {
            "code": ErrCode.MethodNotFound.Code,
            "msg": ErrCode.MethodNotFound.Msg.format(router_str)
        }
    except Exception as e:
        logger.error(e)
        logger.error(traceback.format_exc())
        result = {
            "code": ErrCode.UnknownError.Code,
            "msg": ErrCode.UnknownError.Msg
        }
    finally:
        return result


if __name__ == "__main__":
    main_router(
        "record_replay.record",
        {"version": "v2.0"}
    )
