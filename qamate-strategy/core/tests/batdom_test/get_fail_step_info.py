import json

import requests

from tests.batdom_test.batdom11_plus_test import test_replay
from tests.batdom_test.batdom9_plus_test import paint_match_res
from tests.util import download_img


def get_fail_step(report):
    """
    获取失败步骤
    """
    for case in report:
        for step in case['step']:
            if step.get('result', {}).get('status', 0) == -1:
                return step
    return None


def get_step_info(step):
    """
    从报告步骤信息中，生成batdom11风格步骤格式
    """
    s = step['stepInfo']['params']
    new_s = {
        "findInfo": s['findInfo']['widgetInfo'],
        "recordInfo": {
            "name": s['recordInfo']['domInfo']['name'],
            "used_widget_list": [
                "ocr_info",
                "beta_info",
                "icon_info",
                "ele_info"
            ],
            "version": s['recordInfo']['domInfo']['version'],
            # "version": s['recordInfo']['version'],
            "recordResult": requests.get(s['recordInfo']['dom']).json() if type(s['recordInfo']['dom']) is str else s['recordInfo']['dom']
        }
    }
    rec_img_url = s['recordInfo']['deviceInfo']['screenshot']
    rep_img_url = step['result']['data']['screenshot']
    scale = s['recordInfo']['deviceInfo']['screenSize']['scale']
    scale_step_info(new_s, scale)
    return new_s, rec_img_url, rep_img_url

def parse_from_url(report_url):
    """
    从报告链接解析失败步骤
    """
    report = requests.get(report_url).json()
    fail_step = get_fail_step(report)
    return get_step_info(fail_step)

def scale_tree(tree, scale):
    """
    """
    if 'rect' in tree:
        tree['rect'] = {k: int(v * scale) for k, v in tree['rect'].items()}
    if 'detailFeature' in tree:
        tree['detailFeature']['rect'] = {k: int(v * scale) for k, v in tree['detailFeature']['rect'].items()}
    if 'children' in tree:
        for child in tree['children']:
            scale_tree(child, scale)

def scale_step_info(step_info, scale):
    """
    """
    for node in step_info['findInfo']['findNode']:
        scale_tree(node, scale)
    scale_tree(step_info['recordInfo']['recordResult'], scale)



if __name__ == '__main__':
    report_url = 'https://bj.bcebos.com/newmvp/lazycloud/8129202-result-1749557330116-5f1df300-09c3-472d-b79d-0d39d7fb4600.json'
    step_info, rec_img_url, rep_img_url = parse_from_url(report_url)
    print(rec_img_url, rep_img_url)
    rec_img = download_img(rec_img_url)
    rep_img = download_img(rep_img_url)
    res = test_replay(rec_img_path=rec_img, rep_img_path=rep_img, step_info=step_info)
    print(res)
    paint_match_res(rep_img, res)
