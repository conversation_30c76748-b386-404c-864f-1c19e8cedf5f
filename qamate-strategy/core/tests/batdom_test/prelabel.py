import json
import os

import cv2
from shapely.geometry import box

from tests.batdom_test.batdom9_plus_test import detect_icon, detect_beta_icon


def calculate_iou(box1, box2):
    # 计算两个矩形框的IOU
    iou = box1.intersection(box2).area / box1.union(box2).area
    return iou


def merge_annotations_based_on_iou(original_shapes, new_shapes, iou_threshold=0.2):
    # 合并新旧标注，基于IOU去重
    merged_shapes = original_shapes.copy()
    for new_shape in new_shapes:
        print(new_shape)
        new_box = box(new_shape['points'][0][0], new_shape['points'][0][1], new_shape['points'][1][0],
                      new_shape['points'][1][1])
        overlap = False
        for orig_shape in original_shapes:
            orig_box = box(orig_shape['points'][0][0], orig_shape['points'][0][1], orig_shape['points'][1][0],
                           orig_shape['points'][1][1])
            if calculate_iou(new_box, orig_box) > iou_threshold:
                overlap = True
                break
        if not overlap:
            merged_shapes.append(new_shape)
    return merged_shapes


def convert_to_labelme_format(detect_res):
    # 将detect_icon函数的输出转换为LabelMe格式
    shapes = []
    for item in detect_res:
        points = [
            [item['left'], item['top']],
            [item['right'], item['bottom']]
        ]
        shape = {
            "label": item["name"],
            "points": points,
            "group_id": None,
            "shape_type": "rectangle",
            "flags": {}
        }
        shapes.append(shape)
    return shapes


def process_image_for_labelme(image_path, detect_icon, detect_beta_icon):
    # 主函数：处理图片并生成/合并LabelMe标注
    label_path = os.path.splitext(image_path)[0] + ".json"
    new_anns_icon = detect_icon(image_path, conf=0.7)['dom_tree']['detect_res']
    new_anns_beta_icon = detect_beta_icon(image_path, conf=0.7)['dom_tree']['detect_res']
    # 合并两个检测结果
    new_anns_combined = new_anns_icon + new_anns_beta_icon
    new_labelme_anns = convert_to_labelme_format(new_anns_combined)
    imgcv = cv2.imread(image_path)

    if os.path.exists(label_path):
        with open(label_path, 'r') as f:
            original_labelme_data = json.load(f)
        merged_anns = merge_annotations_based_on_iou(original_labelme_data['shapes'], new_labelme_anns)
        original_labelme_data['shapes'] = merged_anns
        labelme_data = original_labelme_data
    else:
        labelme_data = {
            "version": "5.2.1",
            "flags": {},
            "shapes": new_labelme_anns,
            "imagePath": os.path.basename(image_path),
            "imageData": None,
            "imageHeight": imgcv.shape[0],  # 填写图片的实际高度
            "imageWidth": imgcv.shape[1]  # 填写图片的实际宽度
        }

    with open(label_path, 'w') as f:
        json.dump(labelme_data, f, indent=2)



if __name__ == '__main__':
    base_dir = '/Users/<USER>/lab/icon新数据集制作1w/labelme'


    for i, image_path in enumerate(os.listdir(base_dir)):
        if i < 11252:
            continue
        if 'jpg' not in image_path.lower() and 'png' not in image_path.lower():
            continue
        print(i, 'proc', image_path)
        # 假设detect_icon是你的函数
        process_image_for_labelme(os.path.join(base_dir, image_path), detect_icon, detect_beta_icon)

if __name__ == '__main__1':
    base_dir_1 = '/Users/<USER>/lab/icon新数据集制作1w/labelme2'
    base_dir_2 = '/Users/<USER>/lab/icon新数据集制作1w/labelme'

    for i, image_path in enumerate(os.listdir(base_dir_1)):

        if 'jpg' not in image_path.lower() and 'png' not in image_path.lower():
            continue
        print(i, 'proc', image_path)
        # 假设detect_icon是你的函数
        img = cv2.imread(os.path.join(base_dir_1, image_path))
        cv2.imwrite(os.path.join(base_dir_2, image_path.split('.')[0] + '.jpg'), img)
