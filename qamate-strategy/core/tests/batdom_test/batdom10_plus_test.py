"""
batdom10.0.0+测试方法
"""
import json
import cProfile
import pstats

import cv2

import requests

from basics import config
from tests.batdom_test.batdom9_plus_test import detect_icon, detect_beta_icon, detect_element, detect_ocr, test_main_route, \
    paint_rects, paint_match_res, paint_multi_match_res
from tests.util import cost, download_img

# "ocr_info": ocr_r['ocr_result'],
# "icon_info": icon_r['dom_tree'],
# "ele_info": ele_r['element_res'],
# "beta_info": beta_r['dom_tree'],

config.load_config('/Users/<USER>/batdom_vers/10.1.0/core/profile.json')

@cost
def test_single_feature_record(img_path, version=None):
    """
    建模一把梭函数
    """
    icon_r = detect_icon(img_path)
    beta_r = detect_beta_icon(img_path)
    ele_r = detect_element(img_path)
    ocr_r = detect_ocr(img_path)
    body = {
        "jsonrpc": "xxx",
        "id": 0,
        "method": "v4.record_replay.record",
        "params": [
            {
                "module_name": "record_widget_model",
                "module_info": {
                    "widget_info": {
                        "ocr_info": {"used": True, "data": ocr_r['ocr_result']},
                        "icon_info": {"used": True, "data": icon_r['dom_tree']},
                        "ele_info": {"used": True, "data": ele_r['element_res']},
                        "beta_info": {"used": True, "data": beta_r['dom_tree']},
                        "dom_info": {"used": False, "data": {}}
                    },
                    "os_type": 2,
                    "image_path": img_path
                },
            }
        ]
    }
    if version is not None:
        body['params'][0]['version'] = version
    return test_main_route(body)

@cost
def test_multi_feature_replay(rec_img_path, rep_img_path, step_info, version=None):
    """
    建模一把梭函数
    """
    icon_r = detect_icon(rep_img_path)
    beta_r = detect_beta_icon(rep_img_path)
    ele_r = detect_element(rep_img_path)
    ocr_r = detect_ocr(rep_img_path)
    body = {
        "jsonrpc": "xxx",
        "id": 0,
        "method": "v4.record_replay.replay",
        "params": [
            {
                "module_name": "replay_widget_model",
                "module_info": {
                    "widget_info": {
                        "ocr_info": {"used": True, "data": ocr_r['ocr_result']},
                        "icon_info": {"used": True, "data": icon_r['dom_tree']},
                        "ele_info": {"used": True, "data": ele_r['element_res']},
                        "beta_info": {"used": True, "data": beta_r['dom_tree']},
                        "dom_info": {"used": False, "data": {}}
                    },
                    "os_type": 2,
                    "step_info": step_info,
                    "image_path": rep_img_path,
                    "case_image_path": rec_img_path
                },
            }
        ]
    }
    if version is not None:
        body['params'][0]['version'] = version
    return test_main_route(body)

if __name__ == '__main__1':
    rec_img = download_img('https://bj.bcebos.com/newmvp/appagent/native/img/00008110-001249C82185401E_1729585568802_3988b48f-88f6-4280-a0d4-525e60f8746e_screenshot.jpg')
    # rec_img = '/Users/<USER>/Downloads/5830062-1725822963469-94e7598b-efd1-49bb-bc9b-c702a969613e.jpg'
    rep_img = rec_img
    step_info = json.load(open('/Users/<USER>/batdom_vers/gpu/core/tests/batdom_test/test/s2.json', 'r'))
    res = test_multi_feature_replay(rec_img_path=rec_img, rep_img_path=rep_img, step_info=step_info)
    print(res)

if __name__ == '__main__1':
    # img = download_img('https://bj.bcebos.com/newmvp/lazycloud/1476815-1733415020357-46245b82-e960-4c96-8097-388518e0257d.jpg')
    img = download_img('https://bj.bcebos.com/newmvp/lazy-one/263306-991832-1720178380697-b90ed5ae-b9ce-4cef-84c6-3d0583fc2fc8.jpg')
    # img = '/Users/<USER>/Downloads/1713422170890-98091edd-6aae-49b1-966e-30786bdd652e.png'
    r = test_single_feature_record(img_path=img)
    print(r)
    dom = r['data']['dom']
    json.dump(dom, open('/Users/<USER>/batdom_vers/case_trans/core/tests/batdom_test/step/dom1.json', 'w'),
               ensure_ascii=False, indent=4)
    paint_rects(img_path=img, res=r, step_by_step=False)

if __name__ == '__main__':
    # rec_img = download_img(url='https://bj.bcebos.com/newmvp/lazy-one/263306-991832-1720178380697-b90ed5ae-b9ce-4cef-84c6-3d0583fc2fc8.jpg')
    # rep_img = download_img(url='https://bj.bcebos.com/newmvp/lazycloud/1476815-1733415020357-46245b82-e960-4c96-8097-388518e0257d.jpg')
    rec_img = download_img(url='https://bj.bcebos.com/newmvp/lazycloud/1476815-1733415020357-46245b82-e960-4c96-8097-388518e0257d.jpg')
    rep_img = download_img(url='https://bj.bcebos.com/newmvp/lazy-one/263306-991832-1720178380697-b90ed5ae-b9ce-4cef-84c6-3d0583fc2fc8.jpg')
    step_info = json.load(open('/Users/<USER>/batdom_vers/case_trans/core/tests/batdom_test/step/s5.json', 'r'))
    # step_info = requests.get('https://bj.bcebos.com/newmvp/lazyone/native/img/1721848471135-2b2005c5-1a46-494a-8730-a376f58a8504.json').json()
    res = test_multi_feature_replay(rec_img_path=rec_img, rep_img_path=rep_img, step_info=step_info)
    print(res)
    paint_multi_match_res(rep_img, res)
    # stats = pstats.Stats(profiler)
    # stats.sort_stats('cumulative').print_stats(10)

if __name__ == '__main__1':
    rec_img = download_img(
        url='https://bj.bcebos.com/newmvp/lazy-one/203793-1003924-1720686251220-f24bf1a2-3543-4678-94fc-13e6f1414d5b.jpg')
    res = detect_ocr(rec_img)
    paint_rects(rec_img, res, step_by_step=True)

if __name__ == '__main__1':
    rec_img = download_img(
        url='https://bj.bcebos.com/newmvp/lazy-one/203793-1003924-1720686251220-f24bf1a2-3543-4678-94fc-13e6f1414d5b.jpg')
    imgcv = cv2.imread(rec_img)
    cv2.rectangle(imgcv, (953, 1734), (953 + 89, 1734 + 37), color=(0, 0, 255), thickness=5)
    cv2.imshow("test", imgcv)
    cv2.waitKey(0)
