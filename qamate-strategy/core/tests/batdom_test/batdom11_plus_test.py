import json

from tests.batdom_test.batdom9_plus_test import detect_icon, detect_beta_icon, detect_element, detect_ocr, \
    test_main_route
from tests.util import cost, download_img


@cost
def test_record(img_path, version=None):
    """
    建模一把梭函数
    """
    icon_r = detect_icon(img_path)
    beta_r = detect_beta_icon(img_path)
    ele_r = detect_element(img_path)
    ocr_r = detect_ocr(img_path)
    body = {
        "jsonrpc": "xxx",
        "id": 0,
        "method": "record_replay.record",
        "params": [
            {
                "module_name": "record_widget_model_v2",
                "module_info": {
                    "widget_info": {
                        "ocr_info": {"used": True, "data": ocr_r['ocr_result']},
                        "icon_info": {"used": True, "data": icon_r['dom_tree']},
                        "ele_info": {"used": True, "data": ele_r['element_res']},
                        "beta_info": {"used": True, "data": beta_r['dom_tree']},
                        "dom_info": {"used": False, "data": {}}
                    },
                    "os_type": 2,
                    "image_path": img_path
                },
            }
        ]
    }
    if version is not None:
        body['params'][0]['version'] = version
    return test_main_route(body)

@cost
def test_replay(rec_img_path, rep_img_path, step_info, version=None):
    """
    建模一把梭函数
    """
    icon_r = detect_icon(rep_img_path)
    beta_r = detect_beta_icon(rep_img_path)
    ele_r = detect_element(rep_img_path)
    ocr_r = detect_ocr(rep_img_path)
    body = {
        "jsonrpc": "xxx",
        "id": 0,
        "method": "record_replay.replay",
        "params": [
            {
                "module_name": "replay_widget_model_v2",
                "module_info": {
                    "widget_info": {
                        "ocr_info": {"used": True, "data": ocr_r['ocr_result']},
                        "icon_info": {"used": True, "data": icon_r['dom_tree']},
                        "ele_info": {"used": True, "data": ele_r['element_res']},
                        "beta_info": {"used": True, "data": beta_r['dom_tree']},
                        "dom_info": {"used": False, "data": {}}
                    },
                    "os_type": 2,
                    "step_info": step_info,
                    "image_path": rep_img_path,
                    "case_image_path": rec_img_path
                },
            }
        ]
    }
    if version is not None:
        body['params'][0]['version'] = version
    return test_main_route(body)

if __name__ == '__main__1':
    rec_img = download_img('https://bj.bcebos.com/newmvp/lazy-one/370598-1434415-1727434657142-febe51b3-d3f6-470a-ab63-4c2dd8b68525.jpg')
    # rec_img = '/Users/<USER>/Downloads/5830062-1725822963469-94e7598b-efd1-49bb-bc9b-c702a969613e.jpg'
    rep_img = download_img('https://bj.bcebos.com/newmvp/lazycloud/3495147-1739763738460-c2a940e5-5062-4949-b946-6b2c34f41685.jpg')
    step_info = json.load(open('/Users/<USER>/batdom_vers/case_trans/core/tests/batdom_test/step/s7.json', 'r'))
    res = test_replay(rec_img_path=rec_img, rep_img_path=rep_img, step_info=step_info)
    print(res)

if __name__ == '__main__':
    rec_img = download_img(
        'https://bj.bcebos.com/newmvp/lazy-one/370598-1434415-1727434657142-febe51b3-d3f6-470a-ab63-4c2dd8b68525.jpg')
    res = test_record(rec_img)
    print(res)