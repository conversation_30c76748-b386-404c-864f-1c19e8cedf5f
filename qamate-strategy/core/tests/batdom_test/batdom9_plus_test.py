#!/usr/bin/env python
# -*- encoding: utf-8 -*-

"""
batdom的建模回放一把梭函数，一个函数搞定原子能力调用+建模/回放，让问题排查更丝滑
"""


import json
import os.path
from concurrent.futures import ThreadPoolExecutor

import cv2
from PIL import Image
import time

import basics.config as config
from features.cv_services import icon_detect, popup_detect
from features.cv_services import ocr
from features.router import main_router
from tests.util import cost, download_img, load_url_json

config.load_config('/Users/<USER>/batdom_vers/gpu/core/profile.json')

@cost
def detect_popup(img_path):
    """
    body example:
    {
        "jsonrpc": "xxx",
        "id": 0,
        "method": "popup_detect",
        "params": [{"image_path": "/Users/<USER>/1.png"}]
    }
    """
    return popup_detect.popup_detect({"image_path": img_path})

@cost
def detect_icon(img_path):
    """
    body example:
    {
        "jsonrpc": "xxx",
        "id": 0,
        "method": "icon_detect",
        "params": [{"image_path": "/Users/<USER>/1.png"}]
    }
    """
    return icon_detect.icon_detect({"image_path": img_path})

@cost
def detect_beta_icon(img_path):
    """
    body example:
    {
        "jsonrpc": "xxx",
        "id": 0,
        "method": "beta_detect",
        "params": [{"image_path": "/Users/<USER>/1.png"}]
    }
    """
    return icon_detect.beta_detect({"image_path": img_path})

@cost
def detect_element(img_path):
    """
    body example:
    {
        "jsonrpc": "xxx",
        "id": 0,
        "method": "element_detect",
        "params": [{"image_path": "/Users/<USER>/1.png"}]
    }
    """
    return icon_detect.element_detect({"image_path": img_path})

@cost
def detect_ocr(img_path):
    """
    body example:
    {
        "jsonrpc": "xxx",
        "id": 0,
        "method": "ocr_result",
        "params": [{"image_path": "/Users/<USER>/1.png"}]
    }
    """
    return ocr.ocr_result({"image_path": img_path})

@cost
def test_main_route(body):
    """
    {
        "jsonrpc": "xxx",
        "id": 0,
        "method": "v3.record_replay.single_feature_record",
        "params": [
            {
                "os_type": 2,
                "model_type": 0,
                "image_path": "/xxx/xxx.png",
                "vision_info": {}
            }
        ]
    }
    """
    return main_router(body["method"], body["params"][0])

@cost
def test_single_feature_record(img_path, version=None):
    """
    建模一把梭函数
    """
    icon_r = detect_icon(img_path)
    beta_r = detect_beta_icon(img_path)
    ele_r = detect_element(img_path)
    ocr_r = detect_ocr(img_path)
    body = {
        "jsonrpc": "xxx",
        "id": 0,
        "method": "v3.record_replay.single_feature_record",
        "params": [
            {
                "os_type": 2,
                "model_type": 0,
                "image_path": img_path,
                "vision_info": {
                    "ocr_info": ocr_r['ocr_result'],
                    "icon_info": icon_r['dom_tree'],
                    "ele_info": ele_r['element_res'],
                    "beta_info": beta_r['dom_tree'],
                }
            }
        ]
    }
    if version is not None:
        body['params'][0]['version'] = version
    return test_main_route(body)

@cost
def test_single_feature_replay(record_img_path, replay_img_path, step_info, version=None):
    """
    回放一把梭函数
    """

    icon_r = detect_icon(replay_img_path)
    beta_r = detect_beta_icon(replay_img_path)
    ele_r = detect_element(replay_img_path)
    ocr_r = detect_ocr(replay_img_path)

    body = {
        "jsonrpc": "xxx",
        "id": 0,
        "method": "v3.record_replay.single_feature_replay",
        "params": [
            {
                "os_type": 2,
                "model_type": 0,
                "image_path": replay_img_path,
                "case_image_path": record_img_path,
                "step_info": step_info,
                "vision_info": {
                    "ocr_info": ocr_r['ocr_result'],
                    "icon_info": icon_r['dom_tree'],
                    "ele_info": ele_r['element_res'],
                    "beta_info": beta_r['dom_tree'],
                }
            }
        ]
    }
    if version is not None:
        body['params'][0]['version'] = version
    return test_main_route(body)

@cost
def test_multi_syne_record(img_path, version=None):
    """
    多控件建模一把梭函数
    """
    icon_r = detect_icon(img_path)
    beta_r = detect_beta_icon(img_path)
    ele_r = detect_element(img_path)
    ocr_r = detect_ocr(img_path)
    body = {
        "jsonrpc": "xxx",
        "id": 0,
        "method": "v3.record_replay.multi_syne_record",
        "params": [
            {
                "os_type": 2,
                "model_type": 0,
                "image_path": img_path,
                "vision_info": {
                    "ocr_info": ocr_r['ocr_result'],
                    "icon_info": icon_r['dom_tree'],
                    "ele_info": ele_r['element_res'],
                    "beta_info": beta_r['dom_tree'],
                }
            }
        ]
    }
    if version is not None:
        body['params'][0]['version'] = version
    return test_main_route(body)

@cost
def test_multi_syne_replay(record_img_path, replay_img_path, step_info, version=None):
    """
    多控件回放一把梭函数
    """
    icon_r = detect_icon(replay_img_path)
    beta_r = detect_beta_icon(replay_img_path)
    ele_r = detect_element(replay_img_path)
    ocr_r = detect_ocr(replay_img_path)
    body = {
        "jsonrpc": "xxx",
        "id": 0,
        "method": "v3.record_replay.multi_syne_replay",
        "params": [
            {
                "os_type": 2,
                "model_type": 0,
                "image_path": replay_img_path,
                "case_image_path": record_img_path,
                "step_info": step_info,
                "vision_info": {
                    "ocr_info": ocr_r['ocr_result'],
                    "icon_info": icon_r['dom_tree'],
                    "ele_info": ele_r['element_res'],
                    "beta_info": beta_r['dom_tree'],
                }
            }
        ]
    }
    if version is not None:
        body['params'][0]['version'] = version
    return test_main_route(body)

@cost
def test_multi_syne_replay_url(rep_url, step_url=None, step_path=None):
    rep_path = download_img(rep_url)
    if step_url is not None:
        step_info = load_url_json(step_url)
    if step_path is not None:
        with open(step_path, 'r') as f:
            step_info = json.load(f)

    rec_path = download_img(step_info['deviceInfo']['screenshot'])
    res = test_multi_syne_replay(record_img_path=rec_path, replay_img_path=rep_path, step_info=step_info)
    return res

@cost
def test_single_feature_replay_url(rep_url, step_url):
    rep_path = download_img(rep_url)
    step_info = load_url_json(step_url)
    rec_path = download_img(step_info['deviceInfo']['screenshot'])
    res = test_single_feature_replay(record_img_path=rec_path, replay_img_path=rep_path, step_info=step_info)
    return res

def paint_rects(img_path, res, step_by_step=False):
    img = cv2.imread(img_path)
    if 'data' in res and 'dom' in res['data']:
        dom = res['data']['dom']
        for child in dom['children']:
            print(child)
            rect = child['rect']
            pt1 = (rect['x'], rect['y'])
            pt2 = (rect['x'] + rect['w'], rect['y'] + rect['h'])

            img = cv2.rectangle(img, pt1=pt1, pt2=pt2, color=(0, 255, 0), thickness=2)
            if step_by_step:
                cv2.imshow("test", img)
                cv2.waitKey(0)
    if 'dom_tree' in res and 'detect_res' in res['dom_tree']:
        dom = res['dom_tree']['detect_res']
        for child in dom:
            print(child)
            rect = child
            pt1 = (rect['left'], rect['top'])
            pt2 = (rect['left'] + rect['width'], rect['top'] + rect['height'])

            img = cv2.rectangle(img, pt1=pt1, pt2=pt2, color=(0, 255, 0), thickness=2)
            if step_by_step:
                cv2.imshow("test", img)
                cv2.waitKey(0)
    if 'element_res' in res and 'detect_res' in res['element_res']:
        dom = res['element_res']['detect_res']
        for child in dom:
            print(child)
            rect = child
            pt1 = (rect['left'], rect['top'])
            pt2 = (rect['left'] + rect['width'], rect['top'] + rect['height'])

            img = cv2.rectangle(img, pt1=pt1, pt2=pt2, color=(0, 255, 0), thickness=2)
            if step_by_step:
                cv2.imshow("test", img)
                cv2.waitKey(0)

    if 'ocr_result' in res and 'ret' in res['ocr_result']:
        dom = res['ocr_result']['ret']
        for child in dom:
            print(child)
            rect = child['rect']
            pt1 = (rect['left'], rect['top'])
            pt2 = (rect['left'] + rect['width'], rect['top'] + rect['height'])

            img = cv2.rectangle(img, pt1=pt1, pt2=pt2, color=(0, 255, 0), thickness=2)
            if step_by_step:
                cv2.imshow("test", img)
                cv2.waitKey(0)

    if 'data' in res and 'hit' in res['data']:
        r = res['data']['rect']
        pt1 = (r['x'], r['y'])
        pt2 = (r['x'] + r['w'], r['y'] + r['h'])
        img = cv2.rectangle(img, pt1=pt1, pt2=pt2, color=(0, 255, 0), thickness=2)
    cv2.imshow("test", img)
    cv2.waitKey(0)

def paint_match_res(img_path, replay_res):
    img = cv2.imread(img_path)
    rect = replay_res['data']['action_area']
    pt1 = (rect['x'], rect['y'])
    pt2 = (rect['x'] + rect['w'], rect['y'] + rect['h'])
    img = cv2.rectangle(img, pt1=pt1, pt2=pt2, color=(0, 255, 0), thickness=2)
    cv2.imshow("test", img)
    cv2.waitKey(0)

def paint_multi_match_res(img_path, replay_res):
    img = cv2.imread(img_path)
    for node in replay_res['data']['match_nodes']:
        rect = node['rect']
        pt1 = (rect['x'], rect['y'])
        pt2 = (rect['x'] + rect['w'], rect['y'] + rect['h'])
        img = cv2.rectangle(img, pt1=pt1, pt2=pt2, color=(0, 255, 0), thickness=2)
    cv2.imshow("test", img)
    cv2.waitKey(0)

def pre_label(img_path):
    """预标注"""
    res = test_single_feature_record(img_path)
    dir_path, name = os.path.split(img_path)
    prefix, suffix = os.path.splitext(name)
    label_name = prefix + '.json'
    label_path = os.path.join(dir_path, label_name)
    test_single_feature_record(img_path)
    print(res)

    with Image.open(img_path) as img:
        width, height = img.size
        rgb_image = img.convert('RGB')
        rgb_image.save(img_path, "jpeg")
    assert width is not None and height is not None
    labelme = {
        "version": "5.2.1",
        "flags": {},
        "shapes": [],
        "imageHeight": height,
        "imageWidth": width,
        "imagePath": name,
        'imageData': None
    }
    for c in res['data']['dom']['children']:
        if c['type'] not in ['Icon', 'Component']:
            continue
        type_name = c['ext']['name']
        # assert 'beta' in type_name
        type_name = type_name.replace('_beta', '')
        print(type_name)
        r = c['rect']
        labelme['shapes'].append({
            "shape_type": "rectangle",
            "label": type_name,
            "points": [
                [r['x'], r['y']],
                [r['x'] + r['w'], r['y'] + r['h']]
            ],
        })
    with open(label_path, 'w') as f:
        json.dump(labelme, f)

def paint_step_info(step_url):
    """绘制一个step"""
    step_info = load_url_json(step_url)
    img_url = step_info['deviceInfo']['screenshot']
    img_path = download_img(img_url)
    img = cv2.imread(img_path)
    rect = step_info['findInfo']['findNode'][0]['detailFeature']['rect']
    pt1 = (rect['x'], rect['y'])
    pt2 = (rect['x'] + rect['w'], rect['y'] + rect['h'])
    img = cv2.rectangle(img, pt1=pt1, pt2=pt2, color=(0, 255, 0), thickness=2)
    cv2.imshow("test", img)
    cv2.waitKey(0)


def pre_label_all(dir_path):
    imgs = [os.path.join(dir_path, x) for x in os.listdir(dir_path) if 'jpg' in x.lower() or 'png' in x.lower()]
    for img in imgs:
        pre_label(img)



if __name__ == '__main__1':
    img_path = '/Users/<USER>/batdom930/core/tests/test/test_pack_3/good/1186.png'
    res = detect_popup(img_path=img_path)
    paint_rects(img_path=img_path, res=res)

if __name__ == '__main__1':
    res = test_single_feature_replay_url(rep_url='https://bj.bcebos.com/newmvp/lazyone/native/img/1721848471282-bf807bce-7e36-406f-aed3-9dae2be6b488.png',
                                         step_url='https://bj.bcebos.com/newmvp/lazyone/native/img/1721848471135-2b2005c5-1a46-494a-8730-a376f58a8504.json')
    print(res)

if __name__ == '__main__1':
    step_info = None
    with open('/Users/<USER>/Downloads/reback/3406b2a4-d7d9-4fe3-a5cf-6dbf845e8845/step.json', 'r') as f:
        step_info = json.load(f)

    rec_path = '/Users/<USER>/Downloads/reback/3406b2a4-d7d9-4fe3-a5cf-6dbf845e8845/rec.jpg'
    rep_path = '/Users/<USER>/Downloads/reback/3406b2a4-d7d9-4fe3-a5cf-6dbf845e8845/rep.png'
    res = test_single_feature_replay(record_img_path=rec_path, replay_img_path=rep_path, step_info=step_info)
    print(res)
    paint_match_res(rep_path, res)


if __name__ == '__main__1':
    res = test_multi_syne_replay_url(rep_url='https://bj.bcebos.com/newmvp/appagent/native/img/AP7GVB4103005530_1715829824866_99bf2098-1845-4cce-ba82-2029e60263d0_screenshot.jpg', step_path='./data/st1.json')
    print(res)

if __name__ == '__main__1':
    paint_step_info('https://bj.bcebos.com/newmvp/lazyone/native/img/1711274799339-5d01fd81-8f8d-4a7e-bf85-84b34dccbd81.json')

if __name__ == '__main__1':
    path = download_img("https://bj.bcebos.com/newmvp/lazy-one/202646--1-1713868435437-01469789-54b4-4b94-b7ee-d14225e5a387.jpg")
    # path = '/Users/<USER>/Downloads/78F8BCE3B120D596BF0FA99D8.jpg'
    # path = download_img(url='https://bj.bcebos.com/newmvp/lazycloud/3020546-1716768492835-edf284fe-bd59-4b3e-a6db
    # -c45c085c5222.jpg')
    res = test_single_feature_record(path, version='v9.3.0')
    print(res)
    paint_rects(path, res, step_by_step=False)

if __name__ == '__main__1':
    path = download_img(url='https://bj.bcebos.com/newmvp/appagent/native/img/AP7GVB4103005530_1715829824866_99bf2098-1845-4cce-ba82-2029e60263d0_screenshot.jpg')
    res = test_multi_syne_record(path)
    print(res)
    paint_rects(path, res)

if __name__ == '__main__1':
    # print(detect_beta_icon("/Users/<USER>/1.png"))
    my_dir = '/Users/<USER>/batdom930/core/tests/batdom_test/local_test/20240521162441'
    rec_path = os.path.join(my_dir, 'record.png')
    rep_path = os.path.join(my_dir, 'replay.png')
    step_path = os.path.join(my_dir, 'step_info.json')
    step_info = None
    with open(step_path, 'r') as f:
        step_info = json.load(f)
    res = test_multi_syne_replay(replay_img_path=rep_path, record_img_path=rec_path, step_info=step_info)
    print(res)
    paint_match_res(rep_path, res)

if __name__ == '__main__1':
    # path = "/Users/<USER>/Downloads/123.png"
    path = download_img(url='https://bj.bcebos.com/newmvp/lazycloud/7067423-1728618974766-4046e367-0e32-4555-9e04-aea85bbcc591.jpg')
    begin = time.time()
    res = detect_ocr(path)
    print('all cost {}'.format(time.time() - begin))
    print(res)
    paint_rects(path, res)


if __name__ == '__main__1':
    # p1 = download_img('https://bj.bcebos.com/newmvp/lazyone/native/img/1713422170890-98091edd-6aae-49b1-966e-30786bdd652e.png')
    p = download_img('https://bj.bcebos.com/newmvp/lazycloud/5830062-1725822963469-94e7598b-efd1-49bb-bc9b-c702a969613e.jpg')
    # p = '/Users/<USER>/Downloads/reback/3406b2a4-d7d9-4fe3-a5cf-6dbf845e8845/rep.png'

    # res = detect_element(p)
    res = detect_icon(p)
    print(res)
    paint_rects(p, res)

if __name__ == '__main__1':
    # p1 = download_img('https://bj.bcebos.com/newmvp/lazyone/native/img/1713422170890-98091edd-6aae-49b1-966e-30786bdd652e.png')
    p = download_img(
        'https://bj.bcebos.com/newmvp/lazyone/native/img/1721847702004-c56eac37-f663-4667-90b3-5baa81dd321d.png')
    # p = '/Users/<USER>/Downloads/reback/3406b2a4-d7d9-4fe3-a5cf-6dbf845e8845/rep.png'
    # res = detect_element(p)
    with ThreadPoolExecutor(max_workers=10) as executor:
        for i in range(50):
            time.sleep(0.1)
            executor.submit(detect_icon, p)
    # res = detect_icon(p)
    # print(res)
    # paint_rects(p, res)

if __name__ == '__main__1':
    # pre_label(img_path='/Users/<USER>/Downloads/232995--1-1716175787227-7a09cea7-7de3-44d7-99df-8172823b1765.jpg')
    pre_label_all('/Users/<USER>/data_agu/517/mosaic/beta2_3')


if __name__ == '__main__1':
    p = download_img('http://bj.bcebos.com/icheck-data/strategy/data/image/15300d090a46e9e47a0d777689657568.png')
    res = detect_beta_icon(p)
    paint_rects(p, res, step_by_step=False)