#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@File    : find_stage.py
<AUTHOR> <PERSON><PERSON><PERSON><PERSON>(<EMAIL>)
@Time    : 2025/7/25 19:44
@Desc    : 趋势点
"""

import traceback

from basics.util import logger
from video.stage.split import binary_tendcy


def findStage(params):
    """ find """
    stage_list = []
    try:
        frames_dir = params[0]
        thresh = 0.9
        if len(params) > 1:
            thresh = params[1]
        stage_list = binary_tendcy(frames_dir, thresh)
    except Exception as e:
        logger.error(traceback.format_exc())
    return stage_list


if __name__ == '__main__':
    param = ["/Users/<USER>/Documents/2025/性能评测/十字帧/138389-correct"]
    res = findStage(param)
    print(res)