#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@File    : parser_cross.py
<AUTHOR> <PERSON><PERSON><PERSON><PERSON>(<EMAIL>)
@Time    : 2025/7/25 19:17
@Desc    :  十字帧检测
"""

from video import user_custome
from basics.util import logger


def parser_cross(params):
    """
    解析查找十字帧传入参数
    :param params:
    :return:
    """
    frames_dir = params.get("frames_dir")
    start_index = max(params.get("start_index", 0), 5)

    if params.get('action') is None:
        return {"smartIndex": start_index}
    if params.get('finger_points') is None or len(params.get('finger_points')) < 1:
        logger.debug("parser cross , finger point is None")
        return {"smartIndex": start_index}

    info = {'object': 'cross', "action": params.get('action'),
            "finger_points": params.get('finger_points')}

    smart_index = user_custome(frames_dir, info, start_index)
    return {"smartIndex": smart_index}


if __name__ == '__main__':
    param = {"action": 1,
             "finger_points": [0.31422018348623854, 0.07369473114169216],
             "trace_id": 1753241500430279,
             "frames_dir": "/Users/<USER>/Documents/2025/性能评测/趋势点/138389-stage",
             "start_index": 0}
    res = parser_cross(param)
    print(res)

