#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@File    : test.py
<AUTHOR> <PERSON><PERSON><PERSON><PERSON>(renya<PERSON><PERSON>@baidu.com)
@Time    : 2025/4/11 16:29
@Desc    : 
"""
import os
import base64

import cv2
import numpy as np

from basics.image_merge.image_merge import ImageMerge
from features.cv_services.image_merge import image_merge


def test_image_merge1():
    """

    :return:
    """
    img_paths = [
        "./data/case_0_0.png",
        "./data/case_0_1.png",
        "./data/case_0_2.png",
        "./data/case_0_3.png"
    ]

    from basics.util.config import CACHE_DIR

    imgcvs = [cv2.imread(item) for item in img_paths]

    tool = ImageMerge(imgcv_list=imgcvs, dir_path=CACHE_DIR)
    merge_imgcv = tool.main()

    cv2.imshow("merge_imgcv", merge_imgcv)
    cv2.wait<PERSON>ey(0)


def test_image_merge2():
    """

    :return:
    """

    params = {
        "img_paths": [
            "./data/case_0_0.png",
            "./data/case_0_1.png",
            "./data/case_0_2.png",
            "./data/case_0_3.png"
        ]
    }

    merge_res = image_merge(params)
    print(merge_res)

    image_data = base64.b64decode(merge_res['result'])
    image_array = np.frombuffer(image_data, np.uint8)
    image_matrix = cv2.imdecode(image_array, cv2.IMREAD_COLOR)
    cv2.imshow('Image', image_matrix)
    cv2.waitKey(0)


if __name__ == '__main__':
    # test_image_merge1()
    test_image_merge2()