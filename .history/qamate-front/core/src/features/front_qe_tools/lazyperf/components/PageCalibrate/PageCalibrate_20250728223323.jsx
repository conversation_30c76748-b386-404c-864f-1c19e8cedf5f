import { useState, useEffect, useCallback } from 'react';
import { Card, Pagination, Spin, message, Button, Modal, Input, Space } from 'antd';
import { ExclamationCircleOutlined, UpOutlined, DownOutlined, EyeOutlined, DeleteOutlined, UndoOutlined } from '@ant-design/icons';
import { getSpeedRoundList, updateSpeedRound } from 'COMMON/api/front_qe_tools/lazyperf';
import EventBus from 'COMMON/utils/eventBus';
import FrameViewer from './FrameViewer';
import styles from './PageCalibrate.module.less';

const { confirm } = Modal;
const { TextArea } = Input;

const PageCalibrate = ({ planId, caseNodeId, sceneId, recordList: propRecordList }) => {
    const [loading, setLoading] = useState(false);
    const [recordList, setRecordList] = useState(propRecordList || []);
    const [currentIndex, setCurrentIndex] = useState(0);
    const [invalidModalVisible, setInvalidModalVisible] = useState(false);
    const [invalidComment, setInvalidComment] = useState('');

    // 获取数据列表
    const fetchRecordList = useCallback(async () => {
        if (!planId || !caseNodeId || !sceneId) {
            return;
        }

        setLoading(true);
        try {
            const response = await getSpeedRoundList({
                planId,
                caseNodeId,
                sceneId
            });

            if (response.code === 0) {
                setRecordList(response.data.recordList || []);
            } else {
                message.error(response.msg || '获取数据失败');
            }
        } catch (error) {
            console.error('获取数据失败:', error);
            message.error('获取数据失败');
        } finally {
            setLoading(false);
        }
    }, [planId, caseNodeId, sceneId]);

    // 初始化数据
    useEffect(() => {
        if (!propRecordList) {
            fetchRecordList();
        }
    }, [fetchRecordList, propRecordList]);

    // 监听EventBus事件
    useEffect(() => {
        const handleInvalidateRecord = () => {
            showInvalidConfirm();
        };

        const handleRestoreRecord = () => {
            showRestoreConfirm();
        };

        EventBus.on('invalidateCurrentRecord', handleInvalidateRecord);
        EventBus.on('restoreCurrentRecord', handleRestoreRecord);

        return () => {
            EventBus.off('invalidateCurrentRecord', handleInvalidateRecord);
            EventBus.off('restoreCurrentRecord', handleRestoreRecord);
        };
    }, []);

    // 当前记录
    const currentRecord = recordList[currentIndex];

    // 帧校准回调
    const handleFrameCorrect = useCallback(async (frameIndex, timestamp, type) => {
        if (!currentRecord) return;

        try {
            // 更新本地状态
            const updatedRecord = { ...currentRecord };
            if (type === 'first') {
                updatedRecord.correctDetail.manual.firstFrameIndex = frameIndex;
                updatedRecord.correctDetail.manual.firstFrameTimestamp = timestamp;
                updatedRecord.correctDetail.manual.firstFrameStatus = 1;
            } else {
                updatedRecord.correctDetail.manual.lastFrameIndex = frameIndex;
                updatedRecord.correctDetail.manual.lastFrameTimestamp = timestamp;
                updatedRecord.correctDetail.manual.lastFrameStatus = 1;
            }

            const newRecordList = [...recordList];
            newRecordList[currentIndex] = updatedRecord;
            setRecordList(newRecordList);

            message.success(`${type === 'first' ? '首' : '尾'}帧校准成功`);
        } catch (error) {
            console.error('帧校准失败:', error);
            message.error('帧校准失败');
        }
    }, [currentRecord, recordList, currentIndex]);

    // 废弃记录
    const handleInvalidRecord = useCallback(async () => {
        if (!currentRecord) return;

        try {
            const response = await updateSpeedRound({
                recordSceneId: currentRecord.recordSceneId,
                isValid: 1,
                validComment: invalidComment
            });

            if (response.code === 0) {
                // 更新本地状态
                const updatedRecord = { ...currentRecord };
                updatedRecord.isValid = 1;
                updatedRecord.invalidComment = invalidComment;

                const newRecordList = [...recordList];
                newRecordList[currentIndex] = updatedRecord;
                setRecordList(newRecordList);

                message.success('记录已废弃');
                setInvalidModalVisible(false);
                setInvalidComment('');
            } else {
                message.error(response.msg || '废弃失败');
            }
        } catch (error) {
            console.error('废弃记录失败:', error);
            message.error('废弃记录失败');
        }
    }, [currentRecord, invalidComment, recordList, currentIndex]);

    // 恢复记录
    const handleRestoreRecord = useCallback(async () => {
        if (!currentRecord) return;

        try {
            const response = await updateSpeedRound({
                recordSceneId: currentRecord.recordSceneId,
                isValid: 0,
                validComment: ''
            });

            if (response.code === 0) {
                // 更新本地状态
                const updatedRecord = { ...currentRecord };
                updatedRecord.isValid = 0;
                updatedRecord.invalidComment = '';

                const newRecordList = [...recordList];
                newRecordList[currentIndex] = updatedRecord;
                setRecordList(newRecordList);

                message.success('记录已恢复');
            } else {
                message.error(response.msg || '恢复失败');
            }
        } catch (error) {
            console.error('恢复记录失败:', error);
            message.error('恢复记录失败');
        }
    }, [currentRecord, recordList, currentIndex]);

    // 显示废弃确认弹窗
    const showInvalidConfirm = () => {
        setInvalidModalVisible(true);
    };

    // 显示恢复确认弹窗
    const showRestoreConfirm = () => {
        confirm({
            title: '确认恢复记录？',
            icon: <ExclamationCircleOutlined />,
            content: '恢复后该记录将重新参与校准',
            onOk: handleRestoreRecord,
        });
    };

    if (loading) {
        return (
            <div className={styles.pageCalibrate}>
                <Spin size="large" />
            </div>
        );
    }

    if (!recordList.length) {
        return (
            <div className={styles.pageCalibrate}>
                <Card>
                    <div className={styles.emptyState}>
                        <p>暂无数据</p>
                        <p>请先选择执行用例和执行场景</p>
                    </div>
                </Card>
            </div>
        );
    }

    return (
        <div className={styles.pageCalibrate}>
            {/* 左侧记录分页 */}
            <div className={styles.recordSider}>
                <div className={styles.recordMenu}>
                    {recordList.map((record, index) => {
                        const isActive = index === currentIndex;
                        const isInvalid = record.isValid === 1;

                        return (
                            <div
                                key={index}
                                className={`${styles.recordItem} ${isActive ? styles.active : ''}`}
                                onClick={() => setCurrentIndex(index)}
                                title={isInvalid ? `记录已废弃${record.invalidComment ? ': ' + record.invalidComment : ''}` : `记录 ${index + 1}`}
                            >
                                <span
                                    style={{
                                        color: isInvalid ? '#ff4d4f' : '',
                                        textDecoration: isInvalid ? 'line-through' : 'none'
                                    }}
                                >
                                    {index + 1}
                                </span>
                            </div>
                        );
                    })}
                </div>

                {/* 左侧分页器 */}
                <div className={styles.siderPagination}>
                    <div
                        className={`${styles.paginationBtn} ${currentIndex === 0 ? styles.disabled : ''}`}
                        onClick={() => currentIndex > 0 && setCurrentIndex(currentIndex - 1)}
                    >
                        <UpOutlined />
                    </div>
                    <div className={styles.paginationInfo}>
                        <div className={styles.currentPage}>{currentIndex + 1}</div>
                        <div className={styles.totalInfo}>共 {recordList.length} 条</div>
                    </div>
                    <div
                        className={`${styles.paginationBtn} ${currentIndex === recordList.length - 1 ? styles.disabled : ''}`}
                        onClick={() => currentIndex < recordList.length - 1 && setCurrentIndex(currentIndex + 1)}
                    >
                        <DownOutlined />
                    </div>
                </div>
            </div>

            {/* 主内容区域 */}
            <div className={styles.mainContent}>
                <Card>
                    {/* 头部信息 */}
                    <div className={styles.header}>
                        <div className={styles.recordInfo}>
                            {/* 校准状态标签 */}
                            {currentRecord?.correctDetail?.manual?.firstFrameStatus === 1 ||
                             currentRecord?.correctDetail?.manual?.lastFrameStatus === 1 ? (
                                <span className={styles.calibrateTag}>人工校准</span>
                            ) : (
                                <span className={styles.calibrateTag}>智能校准</span>
                            )}

                            {/* TTI时间显示 */}
                            {(() => {
                                const { correctDetail } = currentRecord || {};
                                const { manual = {}, auto = {} } = correctDetail || {};

                                // 优先显示人工校准的TTI
                                if (manual.firstFrameTimestamp && manual.lastFrameTimestamp) {
                                    const tti = manual.lastFrameTimestamp - manual.firstFrameTimestamp;
                                    return <span className={styles.ttiInfo}>TTI: {tti} ms</span>;
                                }

                                // 其次显示智能校准的TTI
                                if (auto.firstFrameTimestamp && auto.lastFrameTimestamp) {
                                    const tti = auto.lastFrameTimestamp - auto.firstFrameTimestamp;
                                    return <span className={styles.ttiInfo}>TTI: {tti} ms</span>;
                                }

                                return <span className={styles.ttiInfo}>TTI: -- ms</span>;
                            })()}

                            {/* 废弃状态 */}
                            {currentRecord?.isValid === 1 && (
                                <span className={styles.invalidTag}>已废弃</span>
                            )}
                        </div>
                    </div>

                    {/* 帧校准组件 */}
                    {currentRecord && (
                        <FrameViewer
                            record={currentRecord}
                            onFrameCorrect={handleFrameCorrect}
                        />
                    )}

                    {/* 分页器 */}
                    <div className={styles.pagination}>
                        <Pagination
                            current={currentIndex + 1}
                            total={recordList.length}
                            pageSize={1}
                            showSizeChanger={false}
                            showQuickJumper
                            showTotal={(total, range) => `第 ${range[0]} 条，共 ${total} 条`}
                            onChange={(page) => setCurrentIndex(page - 1)}
                        />
                    </div>
                </Card>
            </div>

            {/* 废弃记录弹窗 */}
            <Modal
                title="废弃记录"
                open={invalidModalVisible}
                onOk={handleInvalidRecord}
                onCancel={() => {
                    setInvalidModalVisible(false);
                    setInvalidComment('');
                }}
                okText="确认废弃"
                cancelText="取消"
            >
                <p>确认要废弃这条记录吗？废弃后该记录将不参与校准。</p>
                <TextArea
                    placeholder="请输入废弃原因（可选）"
                    value={invalidComment}
                    onChange={(e) => setInvalidComment(e.target.value)}
                    rows={3}
                />
            </Modal>
        </div>
    );
};

export default PageCalibrate;
