// 按照旧版本的布局样式
.frame_li {
    position: relative;
    display: inline-block;
    margin: 0 5px;
    cursor: pointer;
}

.frame_li:first-child {
    margin-left: 5px;
}

// 全局滚动条样式
:global(.ant-slider) {
    margin: 0 !important;
    position: relative !important;
    z-index: 1 !important;
}

:global(.ant-slider-rail) {
    height: 5px !important;
    background-color: #d7d9e0 !important;
}

:global(.ant-slider-track) {
    height: 5px !important;
}

:global(.ant-slider-handle) {
    margin-top: -6px !important;
}

:global(.ant-slider-dot) {
    top: -4px !important;
    width: 12px !important;
    height: 12px !important;
    border: 2px solid #d7d9e0 !important;
}
