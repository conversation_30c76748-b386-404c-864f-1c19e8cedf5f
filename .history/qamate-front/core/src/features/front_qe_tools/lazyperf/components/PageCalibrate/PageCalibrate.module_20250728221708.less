.pageCalibrate {
    width: 100%;
    height: 100vh;
    position: relative;
    overflow: hidden;

    .recordSider {
        position: absolute;
        top: 0;
        left: 0;
        width: 80px;
        height: 100%;
        background: #fff;
        border-right: 1px solid #d9d9d9;
        z-index: 10;
        box-shadow: 2px 0 8px rgba(0, 0, 0, 0.15);
    }

    .recordMenu {
        height: 100%;
        overflow-y: auto;
        padding-top: 20px;
    }

    .recordItem {
        text-align: center;
        padding: 12px 0;
        cursor: pointer;
        transition: background-color 0.3s;

        &:hover {
            background-color: #f5f5f5;
        }

        &.active {
            background-color: #e6f7ff;
            border-right: 3px solid #1890ff;
        }
    }

    .collapseButton {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        z-index: 11;
        background: #fff;
        border: 1px solid #d9d9d9;
        border-radius: 4px;
        padding: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        cursor: pointer;
        transition: all 0.3s;

        &:hover {
            background: #f5f5f5;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }

        .anticon {
            font-size: 16px;
            color: #666;
        }
    }

    .mainContent {
        width: 100%;
        height: 100%;
        transition: margin-left 0.3s;

        :global(.ant-card) {
            width: 100%;
            height: 100%;

            :global(.ant-card-body) {
                height: calc(100% - 57px);
                padding: 16px;
                display: flex;
                flex-direction: column;
            }
        }
    }

    .emptyState {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        height: 400px;
        color: #999;
        font-size: 16px;

        p {
            margin: 8px 0;
        }
    }

    .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;
        padding-bottom: 16px;
        border-bottom: 1px solid #f0f0f0;

        .recordInfo {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 14px;

            .invalidTag {
                color: #ff4d4f;
                font-weight: 500;
                background: #fff2f0;
                padding: 2px 8px;
                border-radius: 4px;
                border: 1px solid #ffccc7;
            }

            .invalidComment {
                color: #666;
                font-size: 12px;
                max-width: 300px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
        }

        .actions {
            display: flex;
            align-items: center;
            gap: 8px;
        }
    }

    .pagination {
        margin-top: auto;
        padding-top: 16px;
        text-align: center;
        border-top: 1px solid #f0f0f0;

        :global(.ant-pagination) {
            display: flex;
            justify-content: center;
        }
    }
}
