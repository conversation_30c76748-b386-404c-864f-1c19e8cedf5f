.pageCalibrate {
    width: 100%;
    height: 100vh;
    position: relative;
    display: flex;

    .recordSider {
        width: 80px;
        height: 100%;
        background: #fff;
        border-right: 1px solid #d9d9d9;
        flex-shrink: 0;
        display: flex;
        flex-direction: column;
    }

    .recordMenu {
        flex: 1;
        overflow-y: auto;
        padding: 8px 0;
    }

    .recordItem {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 40px;
        margin: 0;
        cursor: pointer;
        font-size: 14px;
        border-bottom: 1px solid #f0f0f0;

        &:hover {
            background-color: #f5f5f5;
        }

        &.active {
            background-color: #e6f7ff;
            border-right: 3px solid #1890ff;
            color: #1890ff;
        }
    }

    .siderPagination {
        padding: 16px 8px;
        border-top: 1px solid #f0f0f0;
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 8px;

        .paginationBtn {
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            cursor: pointer;
            background: #fff;
            transition: all 0.2s;

            &:hover:not(.disabled) {
                border-color: #1890ff;
                color: #1890ff;
            }

            &.disabled {
                color: #d9d9d9;
                cursor: not-allowed;
                background: #f5f5f5;
            }
        }

        .paginationInfo {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;

            .currentPage {
                width: 32px;
                height: 24px;
                display: flex;
                align-items: center;
                justify-content: center;
                border: 1px solid #1890ff;
                border-radius: 4px;
                background: #fff;
                color: #1890ff;
                font-size: 12px;
                font-weight: 500;
            }

            .totalInfo {
                font-size: 10px;
                color: #666;
                text-align: center;
                white-space: nowrap;
            }
        }
    }

    .mainContent {
        flex: 1;
        height: 100%;
        overflow: hidden;

        :global(.ant-card) {
            width: 100%;
            height: 100%;

            :global(.ant-card-body) {
                height: calc(100% - 57px);
                padding: 16px;
                display: flex;
                flex-direction: column;
            }
        }
    }

    .emptyState {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        height: 400px;
        color: #999;
        font-size: 16px;

        p {
            margin: 8px 0;
        }
    }

    .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;
        padding-bottom: 16px;
        border-bottom: 1px solid #f0f0f0;

        .recordInfo {
            display: flex;
            align-items: center;
            gap: 16px;
            font-size: 14px;

            .invalidTag {
                background: #ff4d4f;
                color: white;
                padding: 2px 8px;
                border-radius: 4px;
                font-size: 12px;
            }

            .invalidComment {
                color: #666;
                font-size: 12px;
                max-width: 300px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
        }

        .actions {
            display: flex;
            align-items: center;
            gap: 8px;
        }
    }

    .pagination {
        margin-top: auto;
        padding-top: 16px;
        text-align: center;
        border-top: 1px solid #f0f0f0;

        :global(.ant-pagination) {
            display: flex;
            justify-content: center;
        }
    }
}
