import { useState } from 'react';
import { <PERSON>, But<PERSON>, message, Space } from 'antd';
import PageCalibrate from './PageCalibrate';

// 生成模拟帧数据
const generateMockFrames = (count = 50) => {
    const frames = [];
    for (let i = 0; i < count; i++) {
        frames.push({
            timestamp: 100 + i * 50,
            frame: `data:image/svg+xml;base64,${btoa(`
                <svg width="120" height="80" xmlns="http://www.w3.org/2000/svg">
                    <rect width="120" height="80" fill="${i % 2 === 0 ? '#e6f7ff' : '#f6ffed'}"/>
                    <text x="60" y="40" text-anchor="middle" font-family="Arial" font-size="12" fill="#333">
                        Frame ${i}
                    </text>
                    <text x="60" y="55" text-anchor="middle" font-family="Arial" font-size="10" fill="#666">
                        ${100 + i * 50}ms
                    </text>
                </svg>
            `)}`
        });
    }
    return frames;
};

// 生成模拟记录数据
const generateMockRecords = (count = 10) => {
    const records = [];
    for (let i = 0; i < count; i++) {
        records.push({
            recordSceneId: `record_${i + 1}`,
            frameList: generateMockFrames(),
            stageList: [5, 12, 18, 25, 32, 40, 45], // 转场点
            correctDetail: {
                manual: {
                    firstFrameIndex: 3 + i,
                    lastFrameIndex: 42 - i,
                    firstFrameTimestamp: 250 + i * 50,
                    lastFrameTimestamp: 2200 - i * 50,
                    firstFrameStatus: i % 2, // 一半已校准，一半未校准
                    lastFrameStatus: i % 2
                },
                auto: {
                    firstFrameIndex: 2 + i,
                    lastFrameIndex: 43 - i,
                    firstFrameTimestamp: 200 + i * 50,
                    lastFrameTimestamp: 2250 - i * 50,
                    firstFrameStatus: 0,
                    lastFrameStatus: 0
                }
            },
            isValid: i > 7 ? 1 : 0, // 最后两个记录标记为废弃
            invalidComment: i > 7 ? '测试废弃记录' : '',
            updateUser: `user${i + 1}@baidu.com`,
            updateTime: Date.now() - i * 3600000 // 每个记录相差1小时
        });
    }
    return records;
};

const PageCalibrateDemo = () => {
    const [recordList, setRecordList] = useState(generateMockRecords());

    const resetData = () => {
        setRecordList(generateMockRecords());
        message.info('数据已重置');
    };

    const addRecord = () => {
        const newRecord = {
            recordSceneId: `record_${recordList.length + 1}`,
            frameList: generateMockFrames(),
            stageList: [5, 12, 18, 25, 32, 40, 45],
            correctDetail: {
                manual: {
                    firstFrameIndex: 3,
                    lastFrameIndex: 42,
                    firstFrameTimestamp: 250,
                    lastFrameTimestamp: 2200,
                    firstFrameStatus: 0,
                    lastFrameStatus: 0
                },
                auto: {
                    firstFrameIndex: 2,
                    lastFrameIndex: 43,
                    firstFrameTimestamp: 200,
                    lastFrameTimestamp: 2250,
                    firstFrameStatus: 0,
                    lastFrameStatus: 0
                }
            },
            isValid: 0,
            invalidComment: '',
            updateUser: '<EMAIL>',
            updateTime: Date.now()
        };
        setRecordList(prev => [...prev, newRecord]);
        message.success('新记录已添加');
    };

    return (
        <div style={{ padding: '24px', background: '#f0f2f5', minHeight: '100vh' }}>
            <Card 
                title="分页校准组件演示" 
                style={{ marginBottom: '24px' }}
                extra={
                    <Space>
                        <Button onClick={addRecord}>添加记录</Button>
                        <Button onClick={resetData}>重置数据</Button>
                    </Space>
                }
            >
                <div style={{ marginBottom: '16px' }}>
                    <h4>功能说明：</h4>
                    <ul>
                        <li><strong>左侧分页：</strong>简约的tab样式，显示记录编号（1, 2, 3...），点击可切换记录</li>
                        <li><strong>废弃记录：</strong>废弃的记录在左侧列表中显示为红色删除线</li>
                        <li><strong>帧校准：</strong>双击帧图片可设置首帧/尾帧</li>
                        <li><strong>键盘操作：</strong>W/S切换区域，A/D翻页，Q/E跳转转场点</li>
                    </ul>
                </div>
                
                <div style={{ marginBottom: '16px' }}>
                    <h4>当前状态：</h4>
                    <p>总记录数：{recordList.length}</p>
                    <p>有效记录：{recordList.filter(r => r.isValid === 0).length}</p>
                    <p>废弃记录：{recordList.filter(r => r.isValid === 1).length}</p>
                </div>
            </Card>

            <div style={{ height: 'calc(100vh - 300px)' }}>
                <PageCalibrate 
                    planId="demo_plan"
                    caseNodeId="demo_case"
                    sceneId="demo_scene"
                    recordList={recordList}
                />
            </div>
        </div>
    );
};

export default PageCalibrateDemo;
