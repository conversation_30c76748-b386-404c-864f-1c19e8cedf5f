.pageCalibrate {
    width: 100%;
    height: 100vh;
    position: relative;
    display: flex;

    .recordSider {
        width: 80px;
        height: 100%;
        background: #fff;
        border-right: 1px solid #d9d9d9;
        flex-shrink: 0;
    }

    .recordMenu {
        height: 100%;
        overflow-y: auto;
        padding: 8px 0;
    }

    .recordItem {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 40px;
        margin: 0;
        cursor: pointer;
        font-size: 14px;
        border-bottom: 1px solid #f0f0f0;

        &:hover {
            background-color: #f5f5f5;
        }

        &.active {
            background-color: #e6f7ff;
            border-right: 3px solid #1890ff;
            color: #1890ff;
        }
    }

    .mainContent {
        flex: 1;
        height: 100%;
        overflow: hidden;

        :global(.ant-card) {
            width: 100%;
            height: 100%;

            :global(.ant-card-body) {
                height: calc(100% - 57px);
                padding: 16px;
                display: flex;
                flex-direction: column;
            }
        }
    }

    .emptyState {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        height: 400px;
        color: #999;
        font-size: 16px;

        p {
            margin: 8px 0;
        }
    }

    .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        padding: 16px 20px;
        background: #fafafa;
        border-bottom: 1px solid #e8e8e8;
        border-radius: 8px 8px 0 0;

        .recordInfo {
            display: flex;
            align-items: center;
            gap: 16px;
            font-size: 14px;

            .invalidTag {
                background: linear-gradient(135deg, #ff4d4f 0%, #ff7875 100%);
                color: white;
                padding: 4px 12px;
                border-radius: 12px;
                font-size: 12px;
                font-weight: 500;
                box-shadow: 0 2px 4px rgba(255, 77, 79, 0.3);
            }

            .invalidComment {
                color: #666;
                font-size: 12px;
                max-width: 300px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
        }

        .actions {
            display: flex;
            align-items: center;
            gap: 8px;
        }
    }

    .pagination {
        margin-top: auto;
        padding-top: 16px;
        text-align: center;
        border-top: 1px solid #f0f0f0;

        :global(.ant-pagination) {
            display: flex;
            justify-content: center;
        }
    }
}
