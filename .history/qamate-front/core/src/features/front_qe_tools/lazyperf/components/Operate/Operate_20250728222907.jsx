import { Select, Button, message, Segmented, Form, Space } from 'antd';
import { FormOutlined, AppstoreOutlined, BarsOutlined, DeleteOutlined, UndoOutlined } from '@ant-design/icons';
import { useState, useEffect, useMemo, useRef } from 'react';
import EventBus from 'COMMON/utils/eventBus';
import { connectModel } from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import deviceModel from 'COMMON/models/deviceModel';
import { FORM_ITEM_STYLE } from 'FEATURES/front_qe_tools/device/const';
import CalibrateTable from '../../FramePage/components/CalibrateTable';
import PageCalibrate from '../PageCalibrate';
import SlideCalibrate from '../SlideCalibrate';

const Operate = (props) => {
    const { setCurSceneId, caseNodeOptions, setCurCaseNodeId, sceneListOptions, recordList, planId } =
        props;
    const [messageApi, contextHolder] = message.useMessage();
    const [searchForm] = Form.useForm();
    const [viewMode, setViewMode] = useState('List');
    const [curCaseNodeId, setCurCaseNodeIdState] = useState(null);
    const [curSceneId, setCurSceneIdState] = useState(null);

    return (
        <div>
            {contextHolder}
            <div style={{ display: 'flex', alignItems: 'center', minHeight: '42px' }}>
                <div style={{ flex: 1 }}>
                    {viewMode === 'List' ? (
                        <Form
                            form={searchForm}
                            name="basic"
                            layout="inline"
                            labelAlign="right"
                            labelCol={{ span: 8 }}
                            colon={false}
                            autoComplete="off"
                            style={{ marginBottom: 0 }}
                        >
                            <Form.Item name="caseNodeIdList" label="执行用例" style={{ ...FORM_ITEM_STYLE, marginBottom: 0 }}>
                                <Select
                                    placeholder="请选择执行用例"
                                    allowClear
                                    options={caseNodeOptions}
                                    onChange={(value) => {
                                        console.log('value', value);
                                        setCurCaseNodeId(value);
                                        setCurCaseNodeIdState(value);
                                    }}
                                />
                            </Form.Item>
                            <Form.Item name="sceneIdList" label="执行场景" style={{ ...FORM_ITEM_STYLE, marginBottom: 0 }}>
                                <Select
                                    placeholder="请选择执行场景"
                                    allowClear
                                    options={sceneListOptions}
                                    onChange={(value) => {
                                        setCurSceneId(value);
                                        setCurSceneIdState(value);
                                    }}
                                />
                            </Form.Item>
                        </Form>
                    ) : null}
                </div>
                <Segmented
                    style={{ marginLeft: 'auto' }}
                    value={viewMode}
                    onChange={setViewMode}
                    options={[
                        {
                            label: '列表模式',
                            value: 'List',
                            icon: <BarsOutlined />
                        },
                        { label: '分页校准', value: 'Kanban', icon: <FormOutlined /> },
                        {
                            label: '滑动校准',
                            value: 'Kanban1',
                            icon: <AppstoreOutlined />
                        }
                    ]}
                />
            </div>
            {viewMode === 'List' && <CalibrateTable recordList={recordList} />}
            {viewMode === 'Kanban' && (
                <PageCalibrate
                    planId={planId}
                    caseNodeId={curCaseNodeId}
                    sceneId={curSceneId}
                    recordList={recordList}
                />
            )}
            {viewMode === 'Kanban1' && (
                <SlideCalibrate
                    planId={planId}
                    caseNodeId={curCaseNodeId}
                    sceneId={curSceneId}
                />
            )}
        </div>
    );
};

export default connectModel([baseModel, deviceModel], (state) => ({
    currentSpace: state.common.base.currentSpace,
    userAccess: state.common.base.userAccess,
    poolList: state.common.device.poolList
}))(Operate);
