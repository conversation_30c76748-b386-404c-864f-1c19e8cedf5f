.pageCalibrate {
    width: 100%;
    height: 100vh;
    position: relative;
    display: flex;
    background: #f5f5f5;

    .recordSider {
        width: 80px;
        height: 100%;
        background: #fff;
        border-right: 1px solid #e8e8e8;
        box-shadow: 2px 0 8px rgba(0, 0, 0, 0.06);
        z-index: 10;
        flex-shrink: 0;
    }

    .recordMenu {
        height: 100%;
        overflow-y: auto;
        padding: 16px 0;

        /* 自定义滚动条 */
        &::-webkit-scrollbar {
            width: 4px;
        }

        &::-webkit-scrollbar-track {
            background: #f1f1f1;
        }

        &::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 2px;
        }

        &::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }
    }

    .recordItem {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 48px;
        margin: 2px 8px;
        border-radius: 6px;
        cursor: pointer;
        transition: all 0.2s ease;
        font-size: 14px;
        font-weight: 500;
        position: relative;

        &:hover {
            background-color: #f0f0f0;
            transform: translateX(2px);
        }

        &.active {
            background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
            color: #fff;
            box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
            transform: translateX(4px);

            span {
                color: #fff !important;
                text-decoration: none !important;
            }
        }

        /* 废弃记录的特殊样式 */
        &.invalid {
            background-color: #fff2f0;
            border: 1px solid #ffccc7;

            &:hover {
                background-color: #fff1f0;
            }

            &.active {
                background: linear-gradient(135deg, #ff4d4f 0%, #ff7875 100%);
                border: none;
            }
        }
    }

    .mainContent {
        width: 100%;
        height: 100%;
        transition: margin-left 0.3s;

        :global(.ant-card) {
            width: 100%;
            height: 100%;

            :global(.ant-card-body) {
                height: calc(100% - 57px);
                padding: 16px;
                display: flex;
                flex-direction: column;
            }
        }
    }

    .emptyState {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        height: 400px;
        color: #999;
        font-size: 16px;

        p {
            margin: 8px 0;
        }
    }

    .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;
        padding-bottom: 16px;
        border-bottom: 1px solid #f0f0f0;

        .recordInfo {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 14px;

            .invalidTag {
                color: #ff4d4f;
                font-weight: 500;
                background: #fff2f0;
                padding: 2px 8px;
                border-radius: 4px;
                border: 1px solid #ffccc7;
            }

            .invalidComment {
                color: #666;
                font-size: 12px;
                max-width: 300px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
        }

        .actions {
            display: flex;
            align-items: center;
            gap: 8px;
        }
    }

    .pagination {
        margin-top: auto;
        padding-top: 16px;
        text-align: center;
        border-top: 1px solid #f0f0f0;

        :global(.ant-pagination) {
            display: flex;
            justify-content: center;
        }
    }
}
