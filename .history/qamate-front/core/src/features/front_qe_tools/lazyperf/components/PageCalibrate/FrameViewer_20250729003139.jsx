import React, { useState, useEffect, useCallback } from 'react';
import { Image, Tooltip, message, Button, Slider, Tag } from 'antd';
import {
    CaretLeftFilled,
    CaretRightFilled,
    EyeOutlined,
    RotateLeftOutlined,
    RotateRightOutlined
} from '@ant-design/icons';
import styles from './FrameViewer.module.less';

const FrameViewer = ({ record, onFrameCorrect }) => {
    const [firstPage, setFirstPage] = useState(0);
    const [lastPage, setLastPage] = useState(0);
    const [activeSection, setActiveSection] = useState('first'); // 'first' or 'last'
    const [previewIndex, setPreviewIndex] = useState(-1);

    // 动态计算每页显示的帧数
    const calculateFramePerPage = () => {
        const imageWidth = 150; // 图片宽度
        const imageMargin = 10; // 图片边距 (左右各5px)
        const itemWidth = imageWidth + imageMargin; // 每个图片项的总宽度
        const controlWidth = 80; // 左右控制按钮的总宽度
        const containerWidth = 1200; // 假设容器宽度
        const availableWidth = containerWidth - controlWidth;
        const calculated = Math.floor(availableWidth / itemWidth);
        // 为了保险起见，减1确保不会换行
        return Math.max(5, calculated - 1);
    };

    const framePerPage = calculateFramePerPage();
    const frames = record?.frameList || [];
    const totalFrames = frames.length;
    const stageList = record?.stageList || [];

    // 初始化页面位置 - 按照旧代码逻辑，页面值就是起始帧索引
    useEffect(() => {
        if (frames.length > 0) {
            const firstFrameIndex = record?.correctDetail?.manual?.firstFrameIndex ||
                                  record?.correctDetail?.auto?.firstFrameIndex || 0;
            const lastFrameIndex = record?.correctDetail?.manual?.lastFrameIndex ||
                                 record?.correctDetail?.auto?.lastFrameIndex ||
                                 frames.length - 1;

            // 按照旧代码逻辑，页面值直接是起始帧的索引
            // 为了让目标帧显示在中间位置，我们减去changePage(2)
            const changePage = 2;
            setFirstPage(Math.max(0, firstFrameIndex - changePage));
            setLastPage(Math.max(0, lastFrameIndex - changePage));
        }
    }, [record, frames.length]);

    // 键盘事件处理
    const handleKeyDown = useCallback((event) => {
        if (event.target.tagName === 'INPUT' || event.target.tagName === 'TEXTAREA') {
            return; // 在输入框中不处理键盘事件
        }

        const maxStartFrameIndex = totalFrames - framePerPage - 1;
        const pageNum = 2; // 每次翻页的帧数

        switch (event.key.toLowerCase()) {
            case 'a': // 左翻页
                if (activeSection === 'first') {
                    setFirstPage(prev => Math.max(0, prev - pageNum));
                } else {
                    setLastPage(prev => Math.max(0, prev - pageNum));
                }
                break;
            case 'd': // 右翻页
                if (activeSection === 'first') {
                    setFirstPage(prev => Math.min(maxStartFrameIndex, prev + pageNum));
                } else {
                    setLastPage(prev => Math.min(maxStartFrameIndex, prev + pageNum));
                }
                break;
            case 'w': // 切换到首帧区域
                setActiveSection('first');
                break;
            case 's': // 切换到尾帧区域
                setActiveSection('last');
                break;
            case 'q': // 跳转到上一个转场点
                jumpToStagePoint(-1);
                break;
            case 'e': // 跳转到下一个转场点
                jumpToStagePoint(1);
                break;
            case 'escape': // 回到主帧位置
                resetToMainFrame();
                break;
            default:
                break;
        }
    }, [activeSection, totalFrames, framePerPage]);

    // 绑定键盘事件
    useEffect(() => {
        document.addEventListener('keydown', handleKeyDown);
        return () => {
            document.removeEventListener('keydown', handleKeyDown);
        };
    }, [handleKeyDown]);

    // 跳转到转场点 - 按照旧代码逻辑
    const jumpToStagePoint = (direction) => {
        if (!stageList.length) {
            message.info('无转场点数据');
            return;
        }

        const currentPage = activeSection === 'first' ? firstPage : lastPage;
        const currentStartIndex = currentPage; // 现在currentPage就是起始帧索引

        let targetStage = -1;

        if (direction === 1) { // 下一个转场点
            targetStage = stageList.find(stage => stage > currentStartIndex + framePerPage - 1);
        } else { // 上一个转场点
            for (let i = stageList.length - 1; i >= 0; i--) {
                if (stageList[i] < currentStartIndex) {
                    targetStage = stageList[i];
                    break;
                }
            }
        }

        if (targetStage !== -1) {
            // 按照旧代码逻辑，转场点前2帧开始显示
            const targetStartIndex = Math.max(0, targetStage - 2);

            if (activeSection === 'first') {
                setFirstPage(targetStartIndex);
            } else {
                setLastPage(targetStartIndex);
            }
        } else {
            message.info(direction === 1 ? '已到最后一个转场点' : '已到第一个转场点');
        }
    };

    // 重置到主帧位置
    const resetToMainFrame = () => {
        const firstFrameIndex = record?.correctDetail?.manual?.firstFrameIndex ||
                              record?.correctDetail?.auto?.firstFrameIndex || 0;
        const lastFrameIndex = record?.correctDetail?.manual?.lastFrameIndex ||
                             record?.correctDetail?.auto?.lastFrameIndex ||
                             frames.length - 1;

        // 按照旧代码逻辑，页面值直接是起始帧的索引
        // 为了让目标帧显示在中间位置，我们减去changePage(2)
        const changePage = 2;
        setFirstPage(Math.max(0, firstFrameIndex - changePage));
        setLastPage(Math.max(0, lastFrameIndex - changePage));
    };

    // 双击设置首帧/尾帧
    const handleFrameDoubleClick = (frameIndex, isFirstSection) => {
        if (record?.isValid === 1) {
            message.info('该记录已废弃，无法校准');
            return;
        }

        const frameData = frames[frameIndex];
        if (frameData) {
            const correctionType = isFirstSection ? 'first' : 'last';
            onFrameCorrect?.(frameIndex, frameData.timestamp, correctionType);
        }
    };

    // 渲染帧列表 - 按照旧代码样式
    const renderFrames = (type) => {
        const isFirstSection = type === 'first';
        const currentPage = isFirstSection ? firstPage : lastPage;
        // 按照旧代码逻辑，currentPage就是起始帧的索引
        const startIndex = currentPage;
        const endIndex = Math.min(startIndex + framePerPage, totalFrames);

        const frameElements = [];

        for (let index = startIndex; index < endIndex; index++) {
            const frame = frames[index];
            if (!frame) continue;

            // 确定边框样式和标签 - 完全按照旧代码逻辑
            let border = '1px solid';
            let maskMsg = '';
            let id = '';

            // 转场点标记
            if (stageList.includes(index)) {
                border = '3px dashed orange';
                maskMsg = '转场点';
            }

            // 智能首尾帧标记
            if (isFirstSection) {
                if (index === record?.correctDetail?.auto?.firstFrameIndex) {
                    border = '3px dashed #ff0000';
                    maskMsg = '智能首帧';
                    id = `first-frame-${index}`;
                }
                if (index === record?.correctDetail?.manual?.firstFrameIndex) {
                    border = '3px solid #ff0000';
                    maskMsg = '人工首帧';
                    id = `first-frame-${index}`;
                }
            } else {
                if (index === record?.correctDetail?.auto?.lastFrameIndex) {
                    border = '3px dashed #ff0000';
                    maskMsg = '智能尾帧';
                    id = `last-frame-${index}`;
                }
                if (index === record?.correctDetail?.manual?.lastFrameIndex) {
                    border = '3px solid #ff0000';
                    maskMsg = '人工尾帧';
                    id = `last-frame-${index}`;
                }
            }

            frameElements.push(
                <Tooltip
                    key={index}
                    placement="top"
                    title={
                        <div>
                            <div>index: {index}</div>
                            <div>{maskMsg}</div>
                        </div>
                    }
                >
                    <li
                        id={id}
                        className={styles.frame_li}
                    >


                        <div style={
                            {
                                position: 'relative'
                            }
                        }>
                            <Image
                            placeholder
                            width={150}
                            height={250}
                            style={{
                                border: border,
                                objectFit: 'cover'
                            }}
                            src={frame.frame}
                            preview={{
                                mask: (
                                    <Button
                                        shape='round'
                                        icon={<EyeOutlined />}
                                        style={{
                                            position: 'absolute',
                                            bottom: 35
                                        }}
                                        onClick={() => setPreviewIndex(index)}
                                    >
                                        预览
                                    </Button>
                                ),
                                visible: previewIndex === index,
                                onVisibleChange: (visible) => {
                                    if (!visible) setPreviewIndex(-1);
                                }
                            }}
                            onClick={() => setActiveSection(type)}
                            onDoubleClick={() => handleFrameDoubleClick(index, isFirstSection)}
                        />

                        {/* 标签 - 按照旧代码样式 */}
                        {maskMsg && (
                            <div
                                style={{
                                    textAlign: 'center',
                                    width: '100%',
                                    zIndex: 998,
                                    background: 'rgba(0, 0, 0, 0.5)',
                                    color: 'white',
                                    position: 'absolute',
                                    bottom: 0
                                }}
                            >
                                {maskMsg}
                            </div>
                        )}
                        </div>

                        {/* 时间戳 - 在图片下方 */}
                        <div style={{
                            display: 'flex',
                            marginTop: 5,
                            justifyContent: 'center',
                        }}>
                            <Tag color="orange">
                            {frame.timestamp - frames[0].timestamp} ms
                        </Tag>
                        </div>
                    </li>
                </Tooltip>
            );
        }

        return frameElements;
    };

    // 渲染左侧控制 - 使用flexbox布局
    const renderLeftControl = (type) => {
        const currentPage = type === 'first' ? firstPage : lastPage;
        const setPage = type === 'first' ? setFirstPage : setLastPage;
        const pageNum = 2; // 每次翻页数量（帧数）

        return (
            <div
                style={{
                    position: 'relative',
                    width: 40,
                    minHeight: 280,
                    flexShrink: 0,
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: 'center',
                    alignItems: 'center'
                }}
            >
                {/* 跳转上一个转场点 */}
                {stageList.length > 0 && (
                    <Tooltip title={'跳转上一个转场趋势点'}>
                        <RotateLeftOutlined
                            style={{
                                fontSize: 20,
                                cursor: 'pointer',
                                marginBottom: 10
                            }}
                            onClick={() => jumpToStagePoint(-1)}
                        />
                    </Tooltip>
                )}

                {/* 跳转上页 */}
                <CaretLeftFilled
                    style={{
                        fontSize: 20,
                        cursor: 'pointer'
                    }}
                    onClick={() => {
                        // 按照旧代码逻辑，每次减少pageNum帧
                        setPage(Math.max(0, currentPage - pageNum));
                    }}
                />
            </div>
        );
    };

    // 渲染右侧控制 - 使用flexbox布局
    const renderRightControl = (type) => {
        const currentPage = type === 'first' ? firstPage : lastPage;
        const setPage = type === 'first' ? setFirstPage : setLastPage;
        const pageNum = 2; // 每次翻页数量（帧数）
        const maxStartFrameIndex = totalFrames - framePerPage - 1; // 最后一页的起始帧索引

        return (
            <div
                style={{
                    position: 'relative',
                    width: 40,
                    minHeight: 280,
                    flexShrink: 0,
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: 'center',
                    alignItems: 'center'
                }}
            >
                {/* 跳转下一个转场点 */}
                {stageList.length > 0 && (
                    <Tooltip title={'跳转下一个转场趋势点'}>
                        <RotateRightOutlined
                            style={{
                                fontSize: 20,
                                cursor: 'pointer',
                                marginBottom: 10
                            }}
                            onClick={() => jumpToStagePoint(1)}
                        />
                    </Tooltip>
                )}

                {/* 跳转后页 */}
                <CaretRightFilled
                    style={{
                        fontSize: 20,
                        cursor: 'pointer'
                    }}
                    onClick={() => {
                        // 按照旧代码逻辑，每次增加pageNum帧
                        setPage(Math.min(maxStartFrameIndex, currentPage + pageNum));
                    }}
                />
            </div>
        );
    };

    // 渲染进度条 - 完全按照旧代码BarSlider样式
    const renderBarSlider = (type) => {
        const isFirstSection = type === 'first';
        const currentPage = isFirstSection ? firstPage : lastPage;
        const setPage = isFirstSection ? setFirstPage : setLastPage;
        const isActive = activeSection === type;

        // 按照旧代码的逻辑：pageNum = 7, changePage = 2
        const pageNum = framePerPage; // 7
        const changePage = 2;

        // 转场点标记 - 完全按照旧代码逻辑
        const marks = {};
        if (stageList && stageList.length > 0) {
            for (let stage of stageList) {
                if (stage > changePage) {
                    marks[stage - changePage] = ' ';
                }
            }
        }

        // 按照旧代码：value是当前显示的第一帧的索引，max是总帧数减去每页帧数再减1
        // 这样滑动条就可以一张张滑动了
        const currentStartFrameIndex = currentPage; // 当前显示的第一帧索引
        const maxStartFrameIndex = totalFrames - pageNum - 1; // 最后一页的第一帧索引

        return (
            <div
                style={{
                    width: '80%',
                    marginLeft: '10%',
                    padding: '10px 5px',
                    marginTop: '10px',
                    marginBottom: '10px'
                }}
                onClick={() => setActiveSection(type)}
            >
                <Slider
                    marks={marks}
                    disabled={!isActive}
                    min={0}
                    max={Math.max(0, maxStartFrameIndex)}
                    value={currentStartFrameIndex}
                    onChange={(frameIndex) => {
                        // frameIndex是要显示的第一帧的索引
                        // 我们需要将其转换为页面索引
                        setPage(frameIndex);
                    }}
                />
            </div>
        );
    };

    if (!frames.length) {
        return (
            <div style={{ width: '100%', marginTop: -10, backgroundColor: '#fff' }}>
                <div style={{ textAlign: 'center', padding: '50px', color: '#999' }}>
                    暂无帧数据
                </div>
            </div>
        );
    }

    return (
        <div
            id={'check-correct-list'}
            style={{ width: '100%', marginTop: -10, backgroundColor: '#fff' }}
        >
            {/* 首帧区域 - 使用flexbox布局 */}
            {frames.length > 0 && (
                <div
                    style={{
                        display: 'flex',
                        alignItems: 'flex-start',
                        width: '100%',
                        minHeight: 280,
                        marginTop: 0
                    }}
                >
                    {renderLeftControl('first')}
                    <div style={{ flex: 1, minWidth: 0 }}>
                        <ul
                            style={{
                                width: '100%',
                                padding: 0,
                                margin: 0,
                                opacity: activeSection === 'last' ? 0.2 : 1,
                                display: 'flex',
                                flexWrap: 'wrap',
                                justifyContent: 'flex-start',
                                listStyle: 'none'
                            }}
                        >
                            {renderFrames('first')}
                        </ul>
                    </div>
                    {renderRightControl('first')}
                </div>
            )}

            {/* 首帧进度条 */}
            {frames.length > 0 && renderBarSlider('first')}

            {/* 尾帧区域 - 使用flexbox布局 */}
            {frames.length > 0 && (
                <div
                    style={{
                        display: 'flex',
                        alignItems: 'flex-start',
                        width: '100%',
                        minHeight: 280,
                        marginTop: 3
                    }}
                >
                    {renderLeftControl('last')}
                    <div style={{ flex: 1, minWidth: 0 }}>
                        <ul
                            style={{
                                width: '100%',
                                padding: 0,
                                margin: 0,
                                opacity: activeSection === 'first' ? 0.2 : 1,
                                display: 'flex',
                                flexWrap: 'wrap',
                                justifyContent: 'flex-start',
                                listStyle: 'none'
                            }}
                        >
                            {renderFrames('last')}
                        </ul>
                    </div>
                    {renderRightControl('last')}
                </div>
            )}

            {/* 尾帧进度条 */}
            {frames.length > 0 && renderBarSlider('last')}
        </div>
    );
};

export default FrameViewer;
